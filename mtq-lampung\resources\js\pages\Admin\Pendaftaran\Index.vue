<script setup lang="ts">
import { ref } from 'vue'
import { router } from '@inertiajs/vue3'
import AppLayout from '@/layouts/AppLayout.vue'
import { Head } from '@inertiajs/vue3'
import Heading from '@/components/Heading.vue'
import Button from '@/components/ui/button/Button.vue'
import Select from '@/components/ui/select/Select.vue'
import SelectContent from '@/components/ui/select/SelectContent.vue'
import SelectItem from '@/components/ui/select/SelectItem.vue'
import SelectTrigger from '@/components/ui/select/SelectTrigger.vue'
import SelectValue from '@/components/ui/select/SelectValue.vue'
import Avatar from '@/components/ui/avatar/Avatar.vue'
import AvatarFallback from '@/components/ui/avatar/AvatarFallback.vue'
import Card from '@/components/ui/card/Card.vue'
import CardHeader from '@/components/ui/card/CardHeader.vue'
import CardTitle from '@/components/ui/card/CardTitle.vue'
import CardDescription from '@/components/ui/card/CardDescription.vue'
import CardContent from '@/components/ui/card/CardContent.vue'
import Input from '@/components/ui/input/Input.vue'
import Label from '@/components/ui/label/Label.vue'
import Badge from '@/components/ui/badge/Badge.vue'
import Icon from '@/components/Icon.vue'
import TextLink from '@/components/TextLink.vue'
import { type BreadcrumbItem } from '@/types'
import { formatDate } from '@/utils/date'
import { getInitials } from '@/utils/string'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger, DropdownMenuSeparator } from '@/components/ui/dropdown-menu'


const breadcrumbItems = [
    {
        title: 'Dashboard Admin',
        href: '/admin/dashboard',
    },
];

const props = defineProps<{
    pendaftaran: any,
    golongan: any,
    filters: any,
}>()

const form = ref({
    search: props.filters.search || '',
    status: props.filters.status || 'all',
    golongan: props.filters.golongan || 'all'
})

function search() {
    router.get(route('admin.pendaftaran.index'), form.value, {
        preserveState: true,
        replace: true
    })
}

function resetFilters() {
    form.value.search = ''
    form.value.status = 'all'
    form.value.golongan = 'all'
    search()
}

function getStatusVariant(status: string) {
    const variants = {
        draft: 'secondary',
        submitted: 'secondary',
        payment_pending: 'secondary',
        paid: 'secondary',
        verified: 'secondary',
        approved: 'secondary',
        rejected: 'secondary'
    }
    return variants[status as keyof typeof variants] || 'secondary'
}

function getStatusText(status: string) {
    const texts = {
        draft: 'Draft',
        submitted: 'Disubmit',
        payment_pending: 'Menunggu Pembayaran',
        paid: 'Dibayar',
        verified: 'Diverifikasi',
        approved: 'Disetujui',
        rejected: 'Ditolak'
    }
    return texts[status as keyof typeof texts] || status
}

</script>

<template>
    <AppLayout :breadcrumbs="breadcrumbItems">
        <Head title="Manajemen Pendaftaran" />
        <div class="space-y-6">
            <!-- Header Section -->
            <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                <div>
                    <Heading title="Manajemen Pendaftaran" description="Kelola data pendaftaran peserta" />
                </div>
                <Button as-child>
                    <TextLink :href="route('admin.pendaftaran.create')">
                        <Icon name="plus" class="w-4 h-4 mr-2" />
                        Tambah Pendaftaran
                    </TextLink>
                </Button>
            </div>
            <!-- Enhanced Filters -->
            <Card class="pb-3">
                <CardHeader>
                    <CardTitle class="flex items-center gap-2">
                        <Icon name="filter" class="w-4 h-4" />
                        Filter & Pencarian
                    </CardTitle>
                </CardHeader>
                <CardContent class="space-y-4">
                    <form @submit.prevent="search" class="space-y-2">
                        <div class="grid grid-cols-1 md:grid-cols-4 gap-2">
                            <div class="space-y-2">
                                <Label for="search">Pencarian</Label>
                                <div class="relative">
                                    <Icon name="search" class="absolute left-3 top-3 w-4 h-4 text-muted-foreground" />
                                    <Input id="search" v-model="form.search" placeholder="Nama, NIK, atau Email..."
                                        class="pl-9" />
                                </div>
                            </div>
                            <div class="space-y-2">
                                <Label for="status">Status</Label>
                                <Select v-model="form.status">
                                    <SelectTrigger class="w-full">
                                        <SelectValue placeholder="Semua Status" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="all">Semua Status</SelectItem>
                                        <SelectItem value="draft">Draft</SelectItem>
                                        <SelectItem value="submitted">Disubmit</SelectItem>
                                        <SelectItem value="payment_pending">Menunggu Pembayaran</SelectItem>
                                        <SelectItem value="paid">Dibayar</SelectItem>
                                        <SelectItem value="verified">Diverifikasi</SelectItem>
                                        <SelectItem value="approved">Disetujui</SelectItem>
                                        <SelectItem value="rejected">Ditolak</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>
                            <div class="space-y-2">
                                <Label for="golongan">Golongan</Label>
                                <Select v-model="form.golongan">
                                    <SelectTrigger class="w-full">
                                        <SelectValue placeholder="Semua Golongan" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="all">Semua Golongan</SelectItem>
                                        <SelectItem v-for="g in golongan" :key="g.id_golongan"
                                            :value="g.id_golongan.toString()">
                                            {{ g.nama_golongan }}
                                        </SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>
                        </div>
                        <div class="flex flex-col sm:flex-row gap-2 sm:justify-between">
                            <div class="flex gap-2">
                                <Button type="submit" variant="secondary">
                                    <Icon name="search" class="w-4 h-4 mr-2" />
                                    Cari
                                </Button>
                                <Button type="button" variant="outline" @click="resetFilters">
                                    <Icon name="x" class="w-4 h-4 mr-2" />
                                    Reset
                                </Button>
                            </div>
                            <div class="flex gap-2">
                                <Button variant="outline" size="sm">
                                    <Icon name="download" class="w-4 h-4 mr-2" />
                                    Export
                                </Button>
                                <Button variant="outline" size="sm">
                                    <Icon name="upload" class="w-4 h-4 mr-2" />
                                    Import
                                </Button>
                            </div>
                        </div>
                    </form>
                </CardContent>
            </Card>

            <!-- Enhanced Table -->
            <Card class="gap-0">
                <CardHeader>
                    <div class="flex justify-between items-center">
                        <CardTitle>Daftar Pendaftaran</CardTitle>
                        <div class="flex items-center gap-2 text-sm text-muted-foreground">
                            {{ pendaftaran.data.length }} dari {{ pendaftaran.total || 0 }} pendaftaran
                        </div>
                    </div>
                </CardHeader>
                <CardContent class="p-0">
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead>
                                <tr class="border-b bg-slate-50">
                                    <th class="text-left p-4 font-medium">Peserta</th>
                                    <th class="text-left p-4 font-medium">Golongan</th>
                                    <th class="text-left p-4 font-medium">Status</th>
                                    <th class="text-left p-4 font-medium">Terdaftar</th>
                                    <th class="text-right p-4 font-medium">Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-for="p in pendaftaran.data" :key="p.id_pendaftaran"
                                    class="border-b hover:bg-white transition-colors bg-slate-100">
                                    <td class="py-1 px-3">
                                        <div class="flex items-center gap-3">
                                            <Avatar class="h-8 w-8 shadow">
                                                <AvatarFallback>{{ getInitials(p.peserta.nama_lengkap) }}
                                                </AvatarFallback>
                                            </Avatar>
                                            <div>
                                                <div class="font-medium">{{ p.peserta.nama_lengkap }}</div>
                                                <div class="text-sm text-muted-foreground">{{ p.peserta.email }}</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="p-4">
                                        <div class="flex items-center gap-2">
                                            <Icon name="map-pin" class="w-4 h-4 text-muted-foreground" />
                                            <div>
                                                <div class="font-medium">{{ p.golongan.nama_golongan }}</div>
                                                <div class="text-sm text-muted-foreground">
                                                    {{ p.golongan.cabang_lomba.nama_cabang }}</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="p-4">
                                        <Badge :variant="getStatusVariant(p.status_pendaftaran)">
                                            {{ getStatusText(p.status_pendaftaran) }}
                                        </Badge>
                                    </td>
                                    <td class="p-4 text-muted-foreground">
                                        {{ formatDate(p.created_at) }}
                                    </td>
                                    <td class="px-2">
                                        <div class="flex justify-end gap-1">
                                            <Button variant="outline" size="sm" as-child>
                                                <TextLink :href="route('admin.pendaftaran.show', p.id_pendaftaran)">
                                                    <Icon name="eye" class="w-3 h-3" />
                                                </TextLink>
                                            </Button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </CardContent>
            </Card>
        </div>
    </AppLayout>

</template>
