import{a as m}from"./useForwardExpose-CO14IhkA.js";import{u as f}from"./RovingFocusGroup-lsWyU4xZ.js";import{P as g}from"./AppLogoIcon.vue_vue_type_script_setup_true_lang-wzz4PWna.js";import{W as _}from"./AppLayout.vue_vue_type_script_setup_true_lang-BRf8lUiM.js";import{d as s,J as v,c as i,o as l,w as p,D as d,E as u,u as o}from"./app-B_pmlBSQ.js";const h=s({__name:"DialogTrigger",props:{asChild:{type:Boolean},as:{default:"button"}},setup(a){const t=a,e=_(),{forwardRef:r,currentElement:c}=m();return e.contentId||(e.contentId=f(void 0,"reka-dialog-content")),v(()=>{e.triggerElement.value=c.value}),(n,C)=>(l(),i(o(g),u(t,{ref:o(r),type:n.as==="button"?"button":void 0,"aria-haspopup":"dialog","aria-expanded":o(e).open.value||!1,"aria-controls":o(e).open.value?o(e).contentId:void 0,"data-state":o(e).open.value?"open":"closed",onClick:o(e).onOpenToggle}),{default:p(()=>[d(n.$slots,"default")]),_:3},16,["type","aria-expanded","aria-controls","data-state","onClick"]))}}),B=s({__name:"DialogTrigger",props:{asChild:{type:Boolean},as:{type:[String,Object,Function]}},setup(a){const t=a;return(e,r)=>(l(),i(o(h),u({"data-slot":"dialog-trigger"},t),{default:p(()=>[d(e.$slots,"default")]),_:3},16))}});export{B as _};
