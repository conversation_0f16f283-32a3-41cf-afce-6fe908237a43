<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('verifikasi_dokumen', function (Blueprint $table) {
            $table->id('id_verifikasi_dokumen');
            $table->unsignedBigInteger('id_dokumen');
            $table->unsignedBigInteger('id_jenis_dokumen');
            $table->unsignedBigInteger('verified_by');
            
            $table->enum('status_verifikasi', ['pending', 'sesuai', 'tidak_sesuai', 'perlu_perbaikan'])->default('pending');
            $table->text('catatan_verifikasi')->nullable();
            $table->json('detail_verifikasi')->nullable(); // Store detailed verification criteria
            $table->timestamp('verified_at')->nullable();
            
            // Quality check fields
            $table->boolean('kualitas_gambar')->nullable();
            $table->boolean('kelengkapan_data')->nullable();
            $table->boolean('kejelasan_teks')->nullable();
            
            $table->timestamps();
            
            // Foreign keys
            $table->foreign('id_dokumen')->references('id_dokumen')->on('dokumen_peserta')->onDelete('cascade');
            $table->foreign('id_jenis_dokumen')->references('id_jenis_dokumen')->on('jenis_dokumen')->onDelete('restrict');
            $table->foreign('verified_by')->references('id_user')->on('users')->onDelete('restrict');
            
            // Indexes
            $table->index(['status_verifikasi']);
            $table->index(['verified_by']);
            $table->index(['verified_at']);
            $table->unique(['id_dokumen']); // One verification record per document
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('verifikasi_dokumen');
    }
};
