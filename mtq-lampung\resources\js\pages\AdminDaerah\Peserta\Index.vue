<script setup lang="ts">
import { ref } from 'vue'
import { router } from '@inertiajs/vue3'
import AppLayout from '@/layouts/AppLayout.vue'
import { Head } from '@inertiajs/vue3'
import Heading from '@/components/Heading.vue'
import Button from '@/components/ui/button/Button.vue'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import Icon from '@/components/Icon.vue'
import TextLink from '@/components/TextLink.vue'

interface User {
  username: string
  email: string
}

interface Wilayah {
  nama_wilayah: string
}

interface Pendaftaran {
  nomor_pendaftaran: string
  status_pendaftaran: string
  golongan: {
    nama_golongan: string
    cabang_lomba: {
      nama_cabang: string
    }
  }
}

interface Peserta {
  id_peserta: number
  nama_lengkap: string
  nik: string
  jenis_kelamin: string
  tanggal_lahir: string
  status_peserta: string
  registration_type: string
  created_at: string
  user: User
  wilayah: Wilayah
  pendaftaran: Pendaftaran[]
}

interface Stats {
  total_peserta: number
  peserta_approved: number
  peserta_pending: number
  total_pendaftaran: number
}

interface PaginationData {
  data: Peserta[]
  current_page: number
  last_page: number
  per_page: number
  total: number
  from: number
  to: number
}

const props = defineProps<{
  peserta: PaginationData
  stats: Stats
}>()

const searchForm = ref({
  search: '',
  status: 'all'
})

function search() {
  router.get(route('admin-daerah.peserta.index'), searchForm.value, {
    preserveState: true,
    replace: true
  })
}

function getStatusColor(status: string): string {
  const colors = {
    draft: 'bg-gray-100 text-gray-800',
    submitted: 'bg-blue-100 text-blue-800',
    verified: 'bg-indigo-100 text-indigo-800',
    approved: 'bg-green-100 text-green-800',
    rejected: 'bg-red-100 text-red-800'
  }
  return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800'
}

function getStatusText(status: string): string {
  const texts = {
    draft: 'Draft',
    submitted: 'Disubmit',
    verified: 'Terverifikasi',
    approved: 'Disetujui',
    rejected: 'Ditolak'
  }
  return texts[status as keyof typeof texts] || status
}

function getRegistrationTypeText(type: string): string {
  const types = {
    mandiri: 'Mandiri',
    admin_daerah: 'Admin Daerah'
  }
  return types[type as keyof typeof types] || type
}

function formatDate(dateString: string): string {
  return new Date(dateString).toLocaleDateString('id-ID', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

function calculateAge(birthDate: string): number {
  const today = new Date()
  const birth = new Date(birthDate)
  let age = today.getFullYear() - birth.getFullYear()
  const monthDiff = today.getMonth() - birth.getMonth()

  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
    age--
  }

  return age
}
</script>

<template>
  <AppLayout>

      <div class="flex items-center justify-between">
        <Heading title="Kelola Peserta Daerah"/>
        <Button as-child>
          <TextLink :href="route('admin-daerah.peserta.create')">
            <Icon name="plus" class="w-4 h-4 mr-2" />
            Daftarkan Peserta Baru
          </TextLink>
        </Button>
      </div>


    <Head title="Kelola Peserta Daerah" />

    <div class="space-y-6">
      <!-- Statistics Cards -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent class="p-6">
            <div class="flex items-center space-x-2">
              <Icon name="users" class="h-8 w-8 text-blue-600" />
              <div>
                <p class="text-2xl font-bold">{{ stats.total_peserta }}</p>
                <p class="text-sm text-gray-600">Total Peserta</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent class="p-6">
            <div class="flex items-center space-x-2">
              <Icon name="check-circle" class="h-8 w-8 text-green-600" />
              <div>
                <p class="text-2xl font-bold">{{ stats.peserta_approved }}</p>
                <p class="text-sm text-gray-600">Disetujui</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent class="p-6">
            <div class="flex items-center space-x-2">
              <Icon name="clock" class="h-8 w-8 text-yellow-600" />
              <div>
                <p class="text-2xl font-bold">{{ stats.peserta_pending }}</p>
                <p class="text-sm text-gray-600">Menunggu</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent class="p-6">
            <div class="flex items-center space-x-2">
              <Icon name="clipboard-list" class="h-8 w-8 text-purple-600" />
              <div>
                <p class="text-2xl font-bold">{{ stats.total_pendaftaran }}</p>
                <p class="text-sm text-gray-600">Total Pendaftaran</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <!-- Search and Filter -->
      <Card>
        <CardContent class="p-6">
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="md:col-span-2">
              <Label for="search">Cari Peserta</Label>
              <Input
                id="search"
                v-model="searchForm.search"
                placeholder="Cari berdasarkan nama, NIK, atau email..."
                @input="search"
              />
            </div>
            <div>
              <Label for="status">Filter Status</Label>
              <select
                id="status"
                v-model="searchForm.status"
                @change="search"
                class="flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50"
              >
                <option value="all">Semua Status</option>
                <option value="draft">Draft</option>
                <option value="submitted">Disubmit</option>
                <option value="verified">Terverifikasi</option>
                <option value="approved">Disetujui</option>
                <option value="rejected">Ditolak</option>
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- Peserta Table -->
      <Card>
        <CardHeader>
          <CardTitle>Daftar Peserta</CardTitle>
          <CardDescription>
            Menampilkan {{ peserta.from }}-{{ peserta.to }} dari {{ peserta.total }} peserta
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div class="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Peserta</TableHead>
                  <TableHead>NIK</TableHead>
                  <TableHead>Jenis Kelamin</TableHead>
                  <TableHead>Usia</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Jenis Daftar</TableHead>
                  <TableHead>Pendaftaran</TableHead>
                  <TableHead>Aksi</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                <TableRow v-for="p in peserta.data" :key="p.id_peserta">
                  <TableCell>
                    <div>
                      <div class="font-medium">{{ p.nama_lengkap }}</div>
                      <div class="text-sm text-gray-500">{{ p.user.email }}</div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <span class="font-mono text-sm">{{ p.nik }}</span>
                  </TableCell>
                  <TableCell>
                    <Badge :class="p.jenis_kelamin === 'L' ? 'bg-blue-100 text-blue-800' : 'bg-pink-100 text-pink-800'">
                      {{ p.jenis_kelamin === 'L' ? 'Laki-laki' : 'Perempuan' }}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    {{ calculateAge(p.tanggal_lahir) }} tahun
                  </TableCell>
                  <TableCell>
                    <Badge :class="getStatusColor(p.status_peserta)">
                      {{ getStatusText(p.status_peserta) }}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <span class="text-sm">{{ getRegistrationTypeText(p.registration_type) }}</span>
                  </TableCell>
                  <TableCell>
                    <div class="text-sm">
                      <div class="font-medium">{{ p.pendaftaran.length }} lomba</div>
                      <div v-if="p.pendaftaran.length > 0" class="text-gray-500">
                        {{ p.pendaftaran[0].golongan.nama_golongan }}
                        <span v-if="p.pendaftaran.length > 1">+{{ p.pendaftaran.length - 1 }} lainnya</span>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div class="flex space-x-2">
                      <Button
                        as-child
                        size="sm"
                        variant="outline"
                      >
                        <TextLink :href="route('admin-daerah.peserta.show', p.id_peserta)">
                          <Icon name="eye" class="w-4 h-4" />
                        </TextLink>
                      </Button>
                      <Button
                        as-child
                        size="sm"
                        variant="outline"
                      >
                        <TextLink :href="route('admin-daerah.peserta.edit', p.id_peserta)">
                          <Icon name="edit" class="w-4 h-4" />
                        </TextLink>
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </div>

          <!-- Pagination -->
          <div v-if="peserta.last_page > 1" class="flex items-center justify-between mt-6">
            <div class="text-sm text-gray-500">
              Menampilkan {{ peserta.from }}-{{ peserta.to }} dari {{ peserta.total }} peserta
            </div>
            <div class="flex space-x-2">
              <Button
                v-for="page in peserta.last_page"
                :key="page"
                :variant="page === peserta.current_page ? 'default' : 'outline'"
                size="sm"
                @click="router.get(route('admin-daerah.peserta.index', { page }))"
              >
                {{ page }}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  </AppLayout>
</template>
