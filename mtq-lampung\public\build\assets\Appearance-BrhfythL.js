import{d as i,bU as b,h as l,o as a,F as g,m as f,b as n,c as m,V as h,t as k,n as x,u as r,w as p,a as e,g as v}from"./app-B_pmlBSQ.js";import{S as y,a as A,M as C}from"./sun-CHIV9RJ8.js";import{_ as $,a as w}from"./Layout.vue_vue_type_script_setup_true_lang-DmO1Dm59.js";import{_ as B}from"./AppLayout.vue_vue_type_script_setup_true_lang-BRf8lUiM.js";import"./AppLogoIcon.vue_vue_type_script_setup_true_lang-wzz4PWna.js";import"./Heading.vue_vue_type_script_setup_true_lang-is4VBSCS.js";import"./useForwardExpose-CO14IhkA.js";import"./RovingFocusGroup-lsWyU4xZ.js";const I={class:"inline-flex gap-1 rounded-lg bg-neutral-100 p-1 dark:bg-neutral-800"},S=["onClick"],D={class:"ml-1.5 text-sm"},M=i({__name:"AppearanceTabs",setup(_){const{appearance:t,updateAppearance:o}=b(),c=[{value:"light",Icon:y,label:"Light"},{value:"dark",Icon:A,label:"Dark"},{value:"system",Icon:C,label:"System"}];return(F,L)=>(a(),l("div",I,[(a(),l(g,null,f(c,({value:s,Icon:u,label:d})=>n("button",{key:s,onClick:N=>r(o)(s),class:x(["flex items-center rounded-md px-3.5 py-1.5 transition-colors",r(t)===s?"bg-white shadow-xs dark:bg-neutral-700 dark:text-neutral-100":"text-neutral-500 hover:bg-neutral-200/60 hover:text-black dark:text-neutral-400 dark:hover:bg-neutral-700/60"])},[(a(),m(h(u),{class:"-ml-1 h-4 w-4"})),n("span",D,k(d),1)],10,S)),64))]))}}),V={class:"space-y-6"},J=i({__name:"Appearance",setup(_){const t=[{title:"Appearance settings",href:"/settings/appearance"}];return(o,c)=>(a(),m(B,{breadcrumbs:t},{default:p(()=>[e(r(v),{title:"Appearance settings"}),e($,null,{default:p(()=>[n("div",V,[e(w,{title:"Appearance settings",description:"Update your account's appearance settings"}),e(M)])]),_:1})]),_:1}))}});export{J as default};
