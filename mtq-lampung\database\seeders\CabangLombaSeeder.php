<?php

namespace Database\Seeders;

use App\Models\CabangLomba;
use App\Models\Golongan;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class CabangLombaSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Cabang Lomba Tilawah
        $tilawah = CabangLomba::create([
            'kode_cabang' => 'TIL',
            'nama_cabang' => 'Tilawah',
            'deskripsi' => 'Lomba membaca Al-Quran dengan tartil, tajwid yang benar, dan lagu yang indah',
            'status' => 'aktif'
        ]);

        // Golongan untuk Tilawah
        Golongan::create([
            'kode_golongan' => 'TIL-AP',
            'id_cabang' => $tilawah->id_cabang,
            'nama_golongan' => 'Tilawah Anak-anak Putra',
            'jenis_kelamin' => 'L',
            'batas_umur_min' => 6,
            'batas_umur_max' => 12,
            'kuota_max' => 50,
            'biaya_pendaftaran' => 100000,
            'nomor_urut_awal' => 1,
            'nomor_urut_akhir' => 50,
            'status' => 'aktif'
        ]);

        Golongan::create([
            'kode_golongan' => 'TIL-APT',
            'id_cabang' => $tilawah->id_cabang,
            'nama_golongan' => 'Tilawah Anak-anak Putri',
            'jenis_kelamin' => 'P',
            'batas_umur_min' => 6,
            'batas_umur_max' => 12,
            'kuota_max' => 50,
            'biaya_pendaftaran' => 100000,
            'nomor_urut_awal' => 51,
            'nomor_urut_akhir' => 100,
            'status' => 'aktif'
        ]);

        Golongan::create([
            'kode_golongan' => 'TIL-RP',
            'id_cabang' => $tilawah->id_cabang,
            'nama_golongan' => 'Tilawah Remaja Putra',
            'jenis_kelamin' => 'L',
            'batas_umur_min' => 13,
            'batas_umur_max' => 17,
            'kuota_max' => 50,
            'biaya_pendaftaran' => 150000,
            'nomor_urut_awal' => 101,
            'nomor_urut_akhir' => 150,
            'status' => 'aktif'
        ]);

        Golongan::create([
            'kode_golongan' => 'TIL-RPT',
            'id_cabang' => $tilawah->id_cabang,
            'nama_golongan' => 'Tilawah Remaja Putri',
            'jenis_kelamin' => 'P',
            'batas_umur_min' => 13,
            'batas_umur_max' => 17,
            'kuota_max' => 50,
            'biaya_pendaftaran' => 150000,
            'nomor_urut_awal' => 151,
            'nomor_urut_akhir' => 200,
            'status' => 'aktif'
        ]);

        Golongan::create([
            'kode_golongan' => 'TIL-DP',
            'id_cabang' => $tilawah->id_cabang,
            'nama_golongan' => 'Tilawah Dewasa Putra',
            'jenis_kelamin' => 'L',
            'batas_umur_min' => 18,
            'batas_umur_max' => 99,
            'kuota_max' => 50,
            'biaya_pendaftaran' => 200000,
            'nomor_urut_awal' => 201,
            'nomor_urut_akhir' => 250,
            'status' => 'aktif'
        ]);

        Golongan::create([
            'kode_golongan' => 'TIL-DPT',
            'id_cabang' => $tilawah->id_cabang,
            'nama_golongan' => 'Tilawah Dewasa Putri',
            'jenis_kelamin' => 'P',
            'batas_umur_min' => 18,
            'batas_umur_max' => 99,
            'kuota_max' => 50,
            'biaya_pendaftaran' => 200000,
            'nomor_urut_awal' => 251,
            'nomor_urut_akhir' => 300,
            'status' => 'aktif'
        ]);

        // Cabang Lomba Tahfidz
        $tahfidz = CabangLomba::create([
            'kode_cabang' => 'THF',
            'nama_cabang' => 'Tahfidz',
            'deskripsi' => 'Lomba menghafal Al-Quran dengan berbagai kategori jumlah hafalan',
            'status' => 'aktif'
        ]);

        // Golongan untuk Tahfidz
        Golongan::create([
            'kode_golongan' => 'THF-1P',
            'id_cabang' => $tahfidz->id_cabang,
            'nama_golongan' => 'Tahfidz 1 Juz Putra',
            'jenis_kelamin' => 'L',
            'batas_umur_min' => 6,
            'batas_umur_max' => 99,
            'kuota_max' => 30,
            'biaya_pendaftaran' => 150000,
            'nomor_urut_awal' => 301,
            'nomor_urut_akhir' => 330,
            'status' => 'aktif'
        ]);

        Golongan::create([
            'kode_golongan' => 'THF-1PT',
            'id_cabang' => $tahfidz->id_cabang,
            'nama_golongan' => 'Tahfidz 1 Juz Putri',
            'jenis_kelamin' => 'P',
            'batas_umur_min' => 6,
            'batas_umur_max' => 99,
            'kuota_max' => 30,
            'biaya_pendaftaran' => 150000,
            'nomor_urut_awal' => 331,
            'nomor_urut_akhir' => 360,
            'status' => 'aktif'
        ]);

        Golongan::create([
            'kode_golongan' => 'THF-30P',
            'id_cabang' => $tahfidz->id_cabang,
            'nama_golongan' => 'Tahfidz 30 Juz Putra',
            'jenis_kelamin' => 'L',
            'batas_umur_min' => 15,
            'batas_umur_max' => 99,
            'kuota_max' => 20,
            'biaya_pendaftaran' => 300000,
            'nomor_urut_awal' => 361,
            'nomor_urut_akhir' => 380,
            'status' => 'aktif'
        ]);

        Golongan::create([
            'kode_golongan' => 'THF-30PT',
            'id_cabang' => $tahfidz->id_cabang,
            'nama_golongan' => 'Tahfidz 30 Juz Putri',
            'jenis_kelamin' => 'P',
            'batas_umur_min' => 15,
            'batas_umur_max' => 99,
            'kuota_max' => 20,
            'biaya_pendaftaran' => 300000,
            'nomor_urut_awal' => 381,
            'nomor_urut_akhir' => 400,
            'status' => 'aktif'
        ]);

        // Cabang Lomba Fahmil Quran
        $fahmil = CabangLomba::create([
            'kode_cabang' => 'FHM',
            'nama_cabang' => 'Fahmil Quran',
            'deskripsi' => 'Lomba pemahaman dan penguasaan isi kandungan Al-Quran',
            'status' => 'aktif'
        ]);

        // Golongan untuk Fahmil Quran
        Golongan::create([
            'kode_golongan' => 'FHM-P',
            'id_cabang' => $fahmil->id_cabang,
            'nama_golongan' => 'Fahmil Quran Putra',
            'jenis_kelamin' => 'L',
            'batas_umur_min' => 17,
            'batas_umur_max' => 99,
            'kuota_max' => 25,
            'biaya_pendaftaran' => 250000,
            'nomor_urut_awal' => 401,
            'nomor_urut_akhir' => 425,
            'status' => 'aktif'
        ]);

        Golongan::create([
            'kode_golongan' => 'FHM-PT',
            'id_cabang' => $fahmil->id_cabang,
            'nama_golongan' => 'Fahmil Quran Putri',
            'jenis_kelamin' => 'P',
            'batas_umur_min' => 17,
            'batas_umur_max' => 99,
            'kuota_max' => 25,
            'biaya_pendaftaran' => 250000,
            'nomor_urut_awal' => 426,
            'nomor_urut_akhir' => 450,
            'status' => 'aktif'
        ]);
    }
}
