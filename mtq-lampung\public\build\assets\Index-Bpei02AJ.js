import{d as B,l as F,c as x,o as p,w as e,a,b as s,u as g,g as G,e as n,f as N,h as v,F as y,m as w,t as i,W as T}from"./app-B_pmlBSQ.js";import{e as z,f as E,_ as K}from"./AppLayout.vue_vue_type_script_setup_true_lang-BRf8lUiM.js";import{_ as L}from"./Heading.vue_vue_type_script_setup_true_lang-is4VBSCS.js";import{_}from"./AppLogoIcon.vue_vue_type_script_setup_true_lang-wzz4PWna.js";import{_ as $,a as D,b as S,c as k,d}from"./SelectValue.vue_vue_type_script_setup_true_lang-IzlIronZ.js";import{_ as V,a as j}from"./CardContent.vue_vue_type_script_setup_true_lang-ClvLNBXC.js";import{_ as P,a as C}from"./CardTitle.vue_vue_type_script_setup_true_lang-5HO6oSoB.js";import{_ as W}from"./Input.vue_vue_type_script_setup_true_lang-DB1-RsTn.js";import{_ as b}from"./Label.vue_vue_type_script_setup_true_lang-rj_fhE7R.js";import{_ as R}from"./index-CMGr3-bt.js";import{_ as u}from"./Icon.vue_vue_type_script_setup_true_lang-BuYzjckr.js";import{_ as I}from"./TextLink.vue_vue_type_script_setup_true_lang-CC2Og0lP.js";import"./useForwardExpose-CO14IhkA.js";import"./RovingFocusGroup-lsWyU4xZ.js";import"./check-DtmHjdxf.js";import"./useFormControl-mZRLifGx.js";import"./loader-circle-6d8QrWFr.js";import"./sun-CHIV9RJ8.js";function q(m){return new Date(m).toLocaleDateString("id-ID",{year:"numeric",month:"short",day:"numeric"})}function H(m){if(!m)return"";const f=m.trim().split(" ");return f.length===0?"":f.length===1?f[0].charAt(0).toUpperCase():`${f[0].charAt(0)}${f[f.length-1].charAt(0)}`.toUpperCase()}const J={class:"space-y-6"},O={class:"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4"},Q={class:"grid grid-cols-1 md:grid-cols-4 gap-2"},X={class:"space-y-2"},Y={class:"relative"},Z={class:"space-y-2"},aa={class:"space-y-2"},ta={class:"flex flex-col sm:flex-row gap-2 sm:justify-between"},ea={class:"flex gap-2"},sa={class:"flex gap-2"},na={class:"flex justify-between items-center"},la={class:"flex items-center gap-2 text-sm text-muted-foreground"},oa={class:"overflow-x-auto"},ra={class:"w-full"},ia={class:"py-1 px-3"},da={class:"flex items-center gap-3"},ua={class:"font-medium"},fa={class:"text-sm text-muted-foreground"},ma={class:"p-4"},_a={class:"flex items-center gap-2"},pa={class:"font-medium"},ca={class:"text-sm text-muted-foreground"},ga={class:"p-4"},va={class:"p-4 text-muted-foreground"},ba={class:"px-2"},ha={class:"flex justify-end gap-1"},Na=B({__name:"Index",props:{pendaftaran:{},golongan:{},filters:{}},setup(m){const f=[{title:"Dashboard Admin",href:"/admin/dashboard"}],c=m,r=F({search:c.filters.search||"",status:c.filters.status||"all",golongan:c.filters.golongan||"all"});function h(){T.get(route("admin.pendaftaran.index"),r.value,{preserveState:!0,replace:!0})}function A(){r.value.search="",r.value.status="all",r.value.golongan="all",h()}function M(o){return{draft:"secondary",submitted:"secondary",payment_pending:"secondary",paid:"secondary",verified:"secondary",approved:"secondary",rejected:"secondary"}[o]||"secondary"}function U(o){return{draft:"Draft",submitted:"Disubmit",payment_pending:"Menunggu Pembayaran",paid:"Dibayar",verified:"Diverifikasi",approved:"Disetujui",rejected:"Ditolak"}[o]||o}return(o,t)=>(p(),x(K,{breadcrumbs:f},{default:e(()=>[a(g(G),{title:"Manajemen Pendaftaran"}),s("div",J,[s("div",O,[s("div",null,[a(L,{title:"Manajemen Pendaftaran",description:"Kelola data pendaftaran peserta"})]),a(_,{"as-child":""},{default:e(()=>[a(I,{href:o.route("admin.pendaftaran.create")},{default:e(()=>[a(u,{name:"plus",class:"w-4 h-4 mr-2"}),t[3]||(t[3]=n(" Tambah Pendaftaran "))]),_:1,__:[3]},8,["href"])]),_:1})]),a(V,{class:"pb-3"},{default:e(()=>[a(P,null,{default:e(()=>[a(C,{class:"flex items-center gap-2"},{default:e(()=>[a(u,{name:"filter",class:"w-4 h-4"}),t[4]||(t[4]=n(" Filter & Pencarian "))]),_:1,__:[4]})]),_:1}),a(j,{class:"space-y-4"},{default:e(()=>[s("form",{onSubmit:N(h,["prevent"]),class:"space-y-2"},[s("div",Q,[s("div",X,[a(b,{for:"search"},{default:e(()=>t[5]||(t[5]=[n("Pencarian")])),_:1,__:[5]}),s("div",Y,[a(u,{name:"search",class:"absolute left-3 top-3 w-4 h-4 text-muted-foreground"}),a(W,{id:"search",modelValue:r.value.search,"onUpdate:modelValue":t[0]||(t[0]=l=>r.value.search=l),placeholder:"Nama, NIK, atau Email...",class:"pl-9"},null,8,["modelValue"])])]),s("div",Z,[a(b,{for:"status"},{default:e(()=>t[6]||(t[6]=[n("Status")])),_:1,__:[6]}),a($,{modelValue:r.value.status,"onUpdate:modelValue":t[1]||(t[1]=l=>r.value.status=l)},{default:e(()=>[a(D,{class:"w-full"},{default:e(()=>[a(S,{placeholder:"Semua Status"})]),_:1}),a(k,null,{default:e(()=>[a(d,{value:"all"},{default:e(()=>t[7]||(t[7]=[n("Semua Status")])),_:1,__:[7]}),a(d,{value:"draft"},{default:e(()=>t[8]||(t[8]=[n("Draft")])),_:1,__:[8]}),a(d,{value:"submitted"},{default:e(()=>t[9]||(t[9]=[n("Disubmit")])),_:1,__:[9]}),a(d,{value:"payment_pending"},{default:e(()=>t[10]||(t[10]=[n("Menunggu Pembayaran")])),_:1,__:[10]}),a(d,{value:"paid"},{default:e(()=>t[11]||(t[11]=[n("Dibayar")])),_:1,__:[11]}),a(d,{value:"verified"},{default:e(()=>t[12]||(t[12]=[n("Diverifikasi")])),_:1,__:[12]}),a(d,{value:"approved"},{default:e(()=>t[13]||(t[13]=[n("Disetujui")])),_:1,__:[13]}),a(d,{value:"rejected"},{default:e(()=>t[14]||(t[14]=[n("Ditolak")])),_:1,__:[14]})]),_:1})]),_:1},8,["modelValue"])]),s("div",aa,[a(b,{for:"golongan"},{default:e(()=>t[15]||(t[15]=[n("Golongan")])),_:1,__:[15]}),a($,{modelValue:r.value.golongan,"onUpdate:modelValue":t[2]||(t[2]=l=>r.value.golongan=l)},{default:e(()=>[a(D,{class:"w-full"},{default:e(()=>[a(S,{placeholder:"Semua Golongan"})]),_:1}),a(k,null,{default:e(()=>[a(d,{value:"all"},{default:e(()=>t[16]||(t[16]=[n("Semua Golongan")])),_:1,__:[16]}),(p(!0),v(y,null,w(o.golongan,l=>(p(),x(d,{key:l.id_golongan,value:l.id_golongan.toString()},{default:e(()=>[n(i(l.nama_golongan),1)]),_:2},1032,["value"]))),128))]),_:1})]),_:1},8,["modelValue"])])]),s("div",ta,[s("div",ea,[a(_,{type:"submit",variant:"secondary"},{default:e(()=>[a(u,{name:"search",class:"w-4 h-4 mr-2"}),t[17]||(t[17]=n(" Cari "))]),_:1,__:[17]}),a(_,{type:"button",variant:"outline",onClick:A},{default:e(()=>[a(u,{name:"x",class:"w-4 h-4 mr-2"}),t[18]||(t[18]=n(" Reset "))]),_:1,__:[18]})]),s("div",sa,[a(_,{variant:"outline",size:"sm"},{default:e(()=>[a(u,{name:"download",class:"w-4 h-4 mr-2"}),t[19]||(t[19]=n(" Export "))]),_:1,__:[19]}),a(_,{variant:"outline",size:"sm"},{default:e(()=>[a(u,{name:"upload",class:"w-4 h-4 mr-2"}),t[20]||(t[20]=n(" Import "))]),_:1,__:[20]})])])],32)]),_:1})]),_:1}),a(V,{class:"gap-0"},{default:e(()=>[a(P,null,{default:e(()=>[s("div",na,[a(C,null,{default:e(()=>t[21]||(t[21]=[n("Daftar Pendaftaran")])),_:1,__:[21]}),s("div",la,i(o.pendaftaran.data.length)+" dari "+i(o.pendaftaran.total||0)+" pendaftaran ",1)])]),_:1}),a(j,{class:"p-0"},{default:e(()=>[s("div",oa,[s("table",ra,[t[22]||(t[22]=s("thead",null,[s("tr",{class:"border-b bg-slate-50"},[s("th",{class:"text-left p-4 font-medium"},"Peserta"),s("th",{class:"text-left p-4 font-medium"},"Golongan"),s("th",{class:"text-left p-4 font-medium"},"Status"),s("th",{class:"text-left p-4 font-medium"},"Terdaftar"),s("th",{class:"text-right p-4 font-medium"},"Aksi")])],-1)),s("tbody",null,[(p(!0),v(y,null,w(o.pendaftaran.data,l=>(p(),v("tr",{key:l.id_pendaftaran,class:"border-b hover:bg-white transition-colors bg-slate-100"},[s("td",ia,[s("div",da,[a(z,{class:"h-8 w-8 shadow"},{default:e(()=>[a(E,null,{default:e(()=>[n(i(g(H)(l.peserta.nama_lengkap)),1)]),_:2},1024)]),_:2},1024),s("div",null,[s("div",ua,i(l.peserta.nama_lengkap),1),s("div",fa,i(l.peserta.email),1)])])]),s("td",ma,[s("div",_a,[a(u,{name:"map-pin",class:"w-4 h-4 text-muted-foreground"}),s("div",null,[s("div",pa,i(l.golongan.nama_golongan),1),s("div",ca,i(l.golongan.cabang_lomba.nama_cabang),1)])])]),s("td",ga,[a(R,{variant:M(l.status_pendaftaran)},{default:e(()=>[n(i(U(l.status_pendaftaran)),1)]),_:2},1032,["variant"])]),s("td",va,i(g(q)(l.created_at)),1),s("td",ba,[s("div",ha,[a(_,{variant:"outline",size:"sm","as-child":""},{default:e(()=>[a(I,{href:o.route("admin.pendaftaran.show",l.id_pendaftaran)},{default:e(()=>[a(u,{name:"eye",class:"w-3 h-3"})]),_:2},1032,["href"])]),_:2},1024)])])]))),128))])])])]),_:1})]),_:1})])]),_:1}))}});export{Na as default};
