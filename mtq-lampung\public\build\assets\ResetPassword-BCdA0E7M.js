import{d as c,x as g,c as f,o as w,w as t,a,b as l,u as s,g as V,f as b,e as i,i as k}from"./app-B_pmlBSQ.js";import{_ as d}from"./InputError.vue_vue_type_script_setup_true_lang-A-vdzbq9.js";import{_ as y}from"./AppLogoIcon.vue_vue_type_script_setup_true_lang-wzz4PWna.js";import{_ as m}from"./Input.vue_vue_type_script_setup_true_lang-DB1-RsTn.js";import{_ as n}from"./Label.vue_vue_type_script_setup_true_lang-rj_fhE7R.js";import{_ as C}from"./AuthLayout.vue_vue_type_script_setup_true_lang-DMSGHxRq.js";import{L as v}from"./loader-circle-6d8QrWFr.js";import"./useForwardExpose-CO14IhkA.js";const x={class:"grid gap-6"},P={class:"grid gap-2"},$={class:"grid gap-2"},N={class:"grid gap-2"},j=c({__name:"ResetPassword",props:{token:{},email:{}},setup(u){const p=u,e=g({token:p.token,email:p.email,password:"",password_confirmation:""}),_=()=>{e.post(route("password.store"),{onFinish:()=>{e.reset("password","password_confirmation")}})};return(R,o)=>(w(),f(C,{title:"Reset password",description:"Please enter your new password below"},{default:t(()=>[a(s(V),{title:"Reset password"}),l("form",{onSubmit:b(_,["prevent"])},[l("div",x,[l("div",P,[a(s(n),{for:"email"},{default:t(()=>o[3]||(o[3]=[i("Email")])),_:1,__:[3]}),a(s(m),{id:"email",type:"email",name:"email",autocomplete:"email",modelValue:s(e).email,"onUpdate:modelValue":o[0]||(o[0]=r=>s(e).email=r),class:"mt-1 block w-full",readonly:""},null,8,["modelValue"]),a(d,{message:s(e).errors.email,class:"mt-2"},null,8,["message"])]),l("div",$,[a(s(n),{for:"password"},{default:t(()=>o[4]||(o[4]=[i("Password")])),_:1,__:[4]}),a(s(m),{id:"password",type:"password",name:"password",autocomplete:"new-password",modelValue:s(e).password,"onUpdate:modelValue":o[1]||(o[1]=r=>s(e).password=r),class:"mt-1 block w-full",autofocus:"",placeholder:"Password"},null,8,["modelValue"]),a(d,{message:s(e).errors.password},null,8,["message"])]),l("div",N,[a(s(n),{for:"password_confirmation"},{default:t(()=>o[5]||(o[5]=[i(" Confirm Password ")])),_:1,__:[5]}),a(s(m),{id:"password_confirmation",type:"password",name:"password_confirmation",autocomplete:"new-password",modelValue:s(e).password_confirmation,"onUpdate:modelValue":o[2]||(o[2]=r=>s(e).password_confirmation=r),class:"mt-1 block w-full",placeholder:"Confirm password"},null,8,["modelValue"]),a(d,{message:s(e).errors.password_confirmation},null,8,["message"])]),a(s(y),{type:"submit",class:"mt-4 w-full",disabled:s(e).processing},{default:t(()=>[s(e).processing?(w(),f(s(v),{key:0,class:"h-4 w-4 animate-spin"})):k("",!0),o[6]||(o[6]=i(" Reset password "))]),_:1,__:[6]},8,["disabled"])])],32)]),_:1}))}});export{j as default};
