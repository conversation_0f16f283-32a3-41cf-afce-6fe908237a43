import{_ as B}from"./AppLayout.vue_vue_type_script_setup_true_lang-BRf8lUiM.js";import{_ as I}from"./Heading.vue_vue_type_script_setup_true_lang-is4VBSCS.js";import{_ as p}from"./AppLogoIcon.vue_vue_type_script_setup_true_lang-wzz4PWna.js";import{_ as m,a as u}from"./CardContent.vue_vue_type_script_setup_true_lang-ClvLNBXC.js";import{_,a as g}from"./CardTitle.vue_vue_type_script_setup_true_lang-5HO6oSoB.js";import{_ as b}from"./index-CMGr3-bt.js";import{_ as i}from"./Label.vue_vue_type_script_setup_true_lang-rj_fhE7R.js";import{_ as f}from"./Icon.vue_vue_type_script_setup_true_lang-BuYzjckr.js";import{d as S,c as x,o as v,w as n,b as s,a as e,i as D,u as l,e as r,t as d,h as w,F as V,m as T}from"./app-B_pmlBSQ.js";import"./useForwardExpose-CO14IhkA.js";import"./RovingFocusGroup-lsWyU4xZ.js";import"./check-DtmHjdxf.js";import"./loader-circle-6d8QrWFr.js";import"./sun-CHIV9RJ8.js";const K={class:"max-w-4xl mx-auto space-y-6"},E={class:"grid grid-cols-2 gap-6"},F={class:"text-lg font-semibold"},J={class:"text-lg font-semibold text-green-600"},z={class:"text-lg"},G={class:"grid grid-cols-2 gap-6"},R={class:"text-lg font-semibold"},U={class:"text-lg"},A={class:"text-lg"},M={class:"text-lg"},q={class:"text-lg"},H={class:"text-lg"},O={class:"col-span-2"},Q={class:"text-lg"},W={class:"grid grid-cols-2 gap-6"},X={class:"text-lg font-semibold"},Y={class:"text-lg"},Z={class:"text-lg"},h={class:"text-lg"},aa={class:"grid grid-cols-2 gap-6"},ta={class:"text-lg font-semibold text-green-600"},ea={class:"text-lg capitalize"},na={class:"text-lg"},sa={class:"space-y-3"},la={class:"flex items-center space-x-3"},ra={class:"font-medium"},da={class:"text-sm text-gray-500"},ia={class:"flex items-center space-x-2"},oa={class:"flex flex-wrap gap-4"},ja=S({__name:"Show",props:{pendaftaran:{}},setup(ma){const P=t=>({draft:"secondary",submitted:"default",verified:"default",approved:"default",rejected:"destructive"})[t]||"secondary",c=t=>({draft:"Draft",submitted:"Disubmit",verified:"Diverifikasi",approved:"Disetujui",rejected:"Ditolak"})[t]||t,$=t=>({pending:"secondary",approved:"default",rejected:"destructive"})[t]||"secondary",j=t=>({pending:"Pending",approved:"Lunas",rejected:"Ditolak"})[t]||t,L=t=>({pending:"secondary",approved:"default",rejected:"destructive"})[t]||"secondary",C=t=>({pending:"Pending",approved:"Disetujui",rejected:"Ditolak"})[t]||t,y=t=>new Date(t).toLocaleDateString("id-ID",{day:"numeric",month:"long",year:"numeric"}),k=t=>new Intl.NumberFormat("id-ID").format(t),N=t=>{window.open(`/storage/${t}`,"_blank")};return(t,a)=>(v(),x(B,{title:"Detail Pendaftaran"},{header:n(()=>[e(I,null,{default:n(()=>a[2]||(a[2]=[r("Detail Pendaftaran")])),_:1,__:[2]})]),default:n(()=>[s("div",K,[e(l(m),null,{default:n(()=>[e(l(_),null,{default:n(()=>[e(l(g),null,{default:n(()=>a[3]||(a[3]=[r("Informasi Pendaftaran")])),_:1,__:[3]})]),_:1}),e(l(u),{class:"space-y-4"},{default:n(()=>[s("div",E,[s("div",null,[e(l(i),{class:"text-sm font-medium text-gray-500"},{default:n(()=>a[4]||(a[4]=[r("Nomor Urut")])),_:1,__:[4]}),s("p",F,d(t.pendaftaran.nomor_urut),1)]),s("div",null,[e(l(i),{class:"text-sm font-medium text-gray-500"},{default:n(()=>a[5]||(a[5]=[r("Status Pendaftaran")])),_:1,__:[5]}),e(l(b),{variant:P(t.pendaftaran.status_pendaftaran),class:"mt-1"},{default:n(()=>[r(d(c(t.pendaftaran.status_pendaftaran)),1)]),_:1},8,["variant"])]),s("div",null,[e(l(i),{class:"text-sm font-medium text-gray-500"},{default:n(()=>a[6]||(a[6]=[r("Biaya Pendaftaran")])),_:1,__:[6]}),s("p",J," Rp "+d(k(t.pendaftaran.biaya_pendaftaran)),1)]),s("div",null,[e(l(i),{class:"text-sm font-medium text-gray-500"},{default:n(()=>a[7]||(a[7]=[r("Tanggal Daftar")])),_:1,__:[7]}),s("p",z,d(y(t.pendaftaran.created_at)),1)])])]),_:1})]),_:1}),e(l(m),null,{default:n(()=>[e(l(_),null,{default:n(()=>[e(l(g),null,{default:n(()=>a[8]||(a[8]=[r("Informasi Peserta")])),_:1,__:[8]})]),_:1}),e(l(u),{class:"space-y-4"},{default:n(()=>{var o;return[s("div",G,[s("div",null,[e(l(i),{class:"text-sm font-medium text-gray-500"},{default:n(()=>a[9]||(a[9]=[r("Nama Lengkap")])),_:1,__:[9]}),s("p",R,d(t.pendaftaran.peserta.nama_lengkap),1)]),s("div",null,[e(l(i),{class:"text-sm font-medium text-gray-500"},{default:n(()=>a[10]||(a[10]=[r("NIK")])),_:1,__:[10]}),s("p",U,d(t.pendaftaran.peserta.nik),1)]),s("div",null,[e(l(i),{class:"text-sm font-medium text-gray-500"},{default:n(()=>a[11]||(a[11]=[r("Email")])),_:1,__:[11]}),s("p",A,d((o=t.pendaftaran.peserta.user)==null?void 0:o.email),1)]),s("div",null,[e(l(i),{class:"text-sm font-medium text-gray-500"},{default:n(()=>a[12]||(a[12]=[r("No. Telepon")])),_:1,__:[12]}),s("p",M,d(t.pendaftaran.peserta.no_telepon||"-"),1)]),s("div",null,[e(l(i),{class:"text-sm font-medium text-gray-500"},{default:n(()=>a[13]||(a[13]=[r("Tempat, Tanggal Lahir")])),_:1,__:[13]}),s("p",q,d(t.pendaftaran.peserta.tempat_lahir)+", "+d(y(t.pendaftaran.peserta.tanggal_lahir)),1)]),s("div",null,[e(l(i),{class:"text-sm font-medium text-gray-500"},{default:n(()=>a[14]||(a[14]=[r("Jenis Kelamin")])),_:1,__:[14]}),s("p",H,d(t.pendaftaran.peserta.jenis_kelamin==="L"?"Laki-laki":"Perempuan"),1)]),s("div",O,[e(l(i),{class:"text-sm font-medium text-gray-500"},{default:n(()=>a[15]||(a[15]=[r("Alamat")])),_:1,__:[15]}),s("p",Q,d(t.pendaftaran.peserta.alamat),1)])])]}),_:1})]),_:1}),e(l(m),null,{default:n(()=>[e(l(_),null,{default:n(()=>[e(l(g),null,{default:n(()=>a[16]||(a[16]=[r("Informasi Golongan")])),_:1,__:[16]})]),_:1}),e(l(u),{class:"space-y-4"},{default:n(()=>{var o;return[s("div",W,[s("div",null,[e(l(i),{class:"text-sm font-medium text-gray-500"},{default:n(()=>a[17]||(a[17]=[r("Cabang Lomba")])),_:1,__:[17]}),s("p",X,d((o=t.pendaftaran.golongan.cabang_lomba)==null?void 0:o.nama_cabang),1)]),s("div",null,[e(l(i),{class:"text-sm font-medium text-gray-500"},{default:n(()=>a[18]||(a[18]=[r("Nama Golongan")])),_:1,__:[18]}),s("p",Y,d(t.pendaftaran.golongan.nama_golongan),1)]),s("div",null,[e(l(i),{class:"text-sm font-medium text-gray-500"},{default:n(()=>a[19]||(a[19]=[r("Jenis Kelamin")])),_:1,__:[19]}),s("p",Z,d(t.pendaftaran.golongan.jenis_kelamin==="L"?"Putra":"Putri"),1)]),s("div",null,[e(l(i),{class:"text-sm font-medium text-gray-500"},{default:n(()=>a[20]||(a[20]=[r("Batas Umur")])),_:1,__:[20]}),s("p",h,d(t.pendaftaran.golongan.batas_umur_min)+" - "+d(t.pendaftaran.golongan.batas_umur_max)+" tahun",1)])])]}),_:1})]),_:1}),t.pendaftaran.pembayaran?(v(),x(l(m),{key:0},{default:n(()=>[e(l(_),null,{default:n(()=>[e(l(g),null,{default:n(()=>a[21]||(a[21]=[r("Informasi Pembayaran")])),_:1,__:[21]})]),_:1}),e(l(u),{class:"space-y-4"},{default:n(()=>[s("div",aa,[s("div",null,[e(l(i),{class:"text-sm font-medium text-gray-500"},{default:n(()=>a[22]||(a[22]=[r("Status Pembayaran")])),_:1,__:[22]}),e(l(b),{variant:$(t.pendaftaran.pembayaran.status_pembayaran),class:"mt-1"},{default:n(()=>[r(d(j(t.pendaftaran.pembayaran.status_pembayaran)),1)]),_:1},8,["variant"])]),s("div",null,[e(l(i),{class:"text-sm font-medium text-gray-500"},{default:n(()=>a[23]||(a[23]=[r("Jumlah Bayar")])),_:1,__:[23]}),s("p",ta," Rp "+d(k(t.pendaftaran.pembayaran.jumlah_bayar)),1)]),s("div",null,[e(l(i),{class:"text-sm font-medium text-gray-500"},{default:n(()=>a[24]||(a[24]=[r("Metode Pembayaran")])),_:1,__:[24]}),s("p",ea,d(t.pendaftaran.pembayaran.metode_pembayaran),1)]),s("div",null,[e(l(i),{class:"text-sm font-medium text-gray-500"},{default:n(()=>a[25]||(a[25]=[r("Tanggal Bayar")])),_:1,__:[25]}),s("p",na,d(y(t.pendaftaran.pembayaran.tanggal_bayar)),1)])])]),_:1})]),_:1})):D("",!0),t.pendaftaran.dokumen_peserta&&t.pendaftaran.dokumen_peserta.length>0?(v(),x(l(m),{key:1},{default:n(()=>[e(l(_),null,{default:n(()=>[e(l(g),null,{default:n(()=>a[26]||(a[26]=[r("Dokumen Peserta")])),_:1,__:[26]})]),_:1}),e(l(u),null,{default:n(()=>[s("div",sa,[(v(!0),w(V,null,T(t.pendaftaran.dokumen_peserta,o=>(v(),w("div",{key:o.id_dokumen,class:"flex items-center justify-between p-3 border rounded-lg"},[s("div",la,[e(f,{name:"file-text",class:"w-5 h-5 text-gray-400"}),s("div",null,[s("p",ra,d(o.jenis_dokumen),1),s("p",da,d(o.nama_file),1)])]),s("div",ia,[e(l(b),{variant:L(o.status_verifikasi)},{default:n(()=>[r(d(C(o.status_verifikasi)),1)]),_:2},1032,["variant"]),e(p,{variant:"outline",size:"sm",onClick:ua=>N(o.file_path)},{default:n(()=>[e(f,{name:"download",class:"w-4 h-4"})]),_:2},1032,["onClick"])])]))),128))])]),_:1})]),_:1})):D("",!0),e(l(m),null,{default:n(()=>[e(l(u),{class:"p-6"},{default:n(()=>[s("div",oa,[e(p,{as:"link",href:t.route("admin-daerah.pendaftaran.edit",t.pendaftaran.id_pendaftaran)},{default:n(()=>[e(f,{name:"edit",class:"w-4 h-4 mr-2"}),a[27]||(a[27]=r(" Edit Pendaftaran "))]),_:1,__:[27]},8,["href"]),e(p,{as:"link",href:t.route("admin-daerah.pendaftaran.documents",t.pendaftaran.id_pendaftaran),variant:"outline"},{default:n(()=>[e(f,{name:"file-text",class:"w-4 h-4 mr-2"}),a[28]||(a[28]=r(" Kelola Dokumen "))]),_:1,__:[28]},8,["href"]),e(p,{variant:"outline",onClick:a[0]||(a[0]=o=>t.window.print())},{default:n(()=>[e(f,{name:"printer",class:"w-4 h-4 mr-2"}),a[29]||(a[29]=r(" Cetak Data "))]),_:1,__:[29]}),e(p,{variant:"outline",onClick:a[1]||(a[1]=o=>t.$inertia.visit(t.route("admin-daerah.pendaftaran.index")))},{default:n(()=>[e(f,{name:"arrow-left",class:"w-4 h-4 mr-2"}),a[30]||(a[30]=r(" Kembali "))]),_:1,__:[30]})])]),_:1})]),_:1})])]),_:1}))}});export{ja as default};
