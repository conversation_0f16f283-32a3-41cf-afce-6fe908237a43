# MTQ Verification System Implementation

## Overview
Implementasi sistem verifikasi pendaftaran MTQ dengan workflow admin daerah → admin provinsi yang telah diselesaikan.

## 1. Identifier Relationships

### **nomor_urut** (Sequential Number)
- **Purpose**: Range allocation dalam tabel `golongan` (`nomor_urut_awal`, `nomor_urut_akhir`)
- **Function**: Menentukan range nomor urut untuk setiap kategori lomba
- **Usage**: Digenerate otomatis menggunakan stored procedure di background
- **Example**: Jika kategori memiliki range 1-100, peserta mendapat nomor urut dalam range tersebut

### **nomor_pendaftaran** (Registration Number)
- **Purpose**: Identifier unik pendaftaran per peserta per kategori per wilayah per tahun
- **Format**: `YYYY-KODE_WILAYAH-KODE_GOLONGAN-XXXX` (e.g., "2025-LP-THA-0001")
- **Function**: Nomor tracking administratif untuk record pendaftaran
- **Scope**: Unik di seluruh sistem
- **Generation**: Menggunakan stored procedure `generate_nomor_pendaftaran()`

### **nomor_peserta** (Participant Number)
- **Purpose**: Nomor peserta lomba untuk kategori tertentu
- **Format**: `KODE_GOLONGAN-YYYY-XXX` (e.g., "THA-2025-001")
- **Function**: Digunakan saat lomba untuk identifikasi peserta
- **Scope**: Unik per kategori per tahun
- **Generation**: Menggunakan stored procedure `generate_nomor_peserta()`

## 2. Database Schema

### New Tables Created:

#### `jenis_dokumen`
```sql
- id_jenis_dokumen (PK)
- kode_dokumen (UNIQUE)
- nama_dokumen
- deskripsi
- kategori (identitas, pendidikan, rekomendasi, lainnya)
- wajib (boolean)
- format_file (pdf,jpg,png)
- ukuran_max_kb
- status (aktif, non_aktif)
- urutan
```

#### `verifikasi_pendaftaran`
```sql
- id_verifikasi (PK)
- id_pendaftaran (FK, UNIQUE)
- verified_by (FK to users)
- status_nik (pending, sesuai, tidak_sesuai)
- catatan_nik
- verified_nik_at
- status_dokumen (pending, berkas_sesuai, berkas_tidak_sesuai)
- catatan_dokumen
- verified_dokumen_at
- status_verifikasi (pending, approved, rejected)
- catatan_verifikasi
- verified_at
```

#### `verifikasi_dokumen`
```sql
- id_verifikasi_dokumen (PK)
- id_dokumen (FK, UNIQUE)
- id_jenis_dokumen (FK)
- verified_by (FK to users)
- status_verifikasi (pending, sesuai, tidak_sesuai, perlu_perbaikan)
- catatan_verifikasi
- detail_verifikasi (JSON)
- verified_at
- kualitas_gambar (boolean)
- kelengkapan_data (boolean)
- kejelasan_teks (boolean)
```

## 3. Registration Workflow

### Admin Daerah Registration Process:
1. **Direct Registration**: Admin daerah dapat langsung mendaftarkan peserta
2. **Auto-Verification**: Status otomatis diset ke `verified` untuk:
   - `status_peserta` = 'verified'
   - `status_pendaftaran` = 'verified'
3. **Number Generation**: Nomor digenerate otomatis di background:
   - `nomor_pendaftaran` menggunakan stored procedure
   - `nomor_peserta` menggunakan stored procedure
   - `nomor_urut` dihitung berdasarkan range di golongan

### Admin Provinsi Verification Process:
1. **Filter Eligible**: Hanya pendaftaran dengan status 'verified' yang muncul
2. **Dual Verification**:
   - **NIK Verification**: Radio button (sesuai/tidak sesuai) + catatan
   - **Document Verification**: Radio button (berkas sesuai/berkas tidak sesuai) + catatan
3. **Document Preview**: Modal preview untuk melihat dokumen
4. **Single Page Process**: Verifikasi dilakukan dalam satu halaman dengan modal
5. **Final Status**: 
   - Approved jika NIK sesuai DAN dokumen sesuai
   - Rejected jika salah satu tidak sesuai

## 4. Controllers Implemented

### AdminProvinsi\DashboardController
- Dashboard statistik verifikasi
- Overview pendaftaran per wilayah
- Quick actions untuk verifikasi

### AdminProvinsi\VerifikasiController
- `index()`: List pendaftaran yang perlu diverifikasi
- `show()`: Detail pendaftaran dengan dokumen
- `verify()`: Proses verifikasi NIK dan dokumen
- `previewDocument()`: Preview dokumen dalam modal
- `downloadDocument()`: Download dokumen

### Updated AdminDaerah\PendaftaranController
- Auto-set status to 'verified' saat registrasi
- Improved number generation dengan stored procedures
- Background processing untuk nomor_urut

## 5. Vue.js Components

### AdminProvinsi/Dashboard.vue
- Statistics cards untuk verifikasi
- Recent registrations table
- Verification progress by region

### AdminProvinsi/Verifikasi/Index.vue
- Filterable list pendaftaran
- Inline verification modal
- Quick verification actions

### AdminProvinsi/Verifikasi/Show.vue
- Detailed participant information
- Document preview functionality
- Complete verification interface

## 6. Routes Added

```php
// Provincial Verification (Admin Provinsi functionality)
Route::prefix('verifikasi-provinsi')->name('verifikasi-provinsi.')->group(function () {
    Route::get('/', [VerifikasiController::class, 'index'])->name('index');
    Route::get('/{pendaftaran}', [VerifikasiController::class, 'show'])->name('show');
    Route::post('/{pendaftaran}/verify', [VerifikasiController::class, 'verify'])->name('verify');
    Route::get('/dokumen/{dokumen}/preview', [VerifikasiController::class, 'previewDocument'])->name('dokumen.preview');
    Route::get('/dokumen/{dokumen}/download', [VerifikasiController::class, 'downloadDocument'])->name('dokumen.download');
});
```

## 7. Models Created

### JenisDokumen
- Manages document types and requirements
- Validation rules for file formats and sizes

### VerifikasiPendaftaran
- Tracks registration verification status
- Separate tracking for NIK and document verification

### VerifikasiDokumen
- Individual document verification records
- Quality assessment fields

## 8. Key Features

### Document Preview System
- Modal-based document preview
- Support for PDF and image files
- Download functionality

### Verification Interface
- Radio button controls for verification decisions
- Optional notes for each verification step
- Single-page verification process

### Auto-Status Management
- Admin daerah registrations automatically verified
- Status propagation through the system
- Proper workflow enforcement

## 9. Testing Workflow

### Complete Flow Test:
1. **Admin Daerah**: Register participant → Auto-verified status
2. **Admin Provinsi**: View verified registrations → Verify NIK and documents → Approve/Reject
3. **System**: Update all related statuses automatically

### Key Test Points:
- Number generation works correctly
- Status transitions are proper
- Document preview functions
- Verification saves correctly
- Filtering works as expected

## 10. Next Steps

1. Run migrations: `php artisan migrate`
2. Run seeders: `php artisan db:seed --class=JenisDokumenSeeder`
3. Test admin daerah registration
4. Test admin provinsi verification
5. Verify document preview functionality

## 11. Security Considerations

- Role-based access control enforced
- Document access restricted to authorized users
- Verification audit trail maintained
- File upload validation implemented

## 12. Performance Optimizations

- Eager loading for related models
- Indexed database fields for filtering
- Efficient pagination for large datasets
- Cached document type lookups
