import{d as M,r as P,W as $,c as V,o as f,w as s,a,b as n,u as e,g as z,e as i,h as c,F as C,m as S,t as r}from"./app-B_pmlBSQ.js";import{a as D,b as E,c as T,d as W,_ as H}from"./AppLayout.vue_vue_type_script_setup_true_lang-BRf8lUiM.js";import{_ as J}from"./Heading.vue_vue_type_script_setup_true_lang-is4VBSCS.js";import{_ as g}from"./AppLogoIcon.vue_vue_type_script_setup_true_lang-wzz4PWna.js";import{_ as j,a as L}from"./CardContent.vue_vue_type_script_setup_true_lang-ClvLNBXC.js";import{_ as U}from"./index-CMGr3-bt.js";import{_ as R}from"./Input.vue_vue_type_script_setup_true_lang-DB1-RsTn.js";import{_ as p}from"./Label.vue_vue_type_script_setup_true_lang-rj_fhE7R.js";import{_ as x,a as v,b as k,c as b,d as u}from"./SelectValue.vue_vue_type_script_setup_true_lang-IzlIronZ.js";import{_ as m}from"./Icon.vue_vue_type_script_setup_true_lang-BuYzjckr.js";import{l as q,_ as O}from"./lodash-Cvgo55ys.js";import"./useForwardExpose-CO14IhkA.js";import"./RovingFocusGroup-lsWyU4xZ.js";import"./check-DtmHjdxf.js";import"./useFormControl-mZRLifGx.js";import"./loader-circle-6d8QrWFr.js";import"./sun-CHIV9RJ8.js";import"./TextLink.vue_vue_type_script_setup_true_lang-CC2Og0lP.js";const Q={class:"space-y-6"},X={class:"grid grid-cols-1 md:grid-cols-5 gap-4"},Y={class:"flex items-end"},Z={class:"flex justify-between items-center"},aa={class:"text-sm text-gray-600"},ta={class:"overflow-x-auto"},ea={class:"w-full"},sa={class:"bg-white divide-y divide-gray-200"},na={class:"px-6 py-4 whitespace-nowrap"},la={class:"text-sm font-medium text-gray-900"},oa={class:"text-sm text-gray-500"},ia={class:"px-6 py-4 whitespace-nowrap"},ra={class:"text-sm text-gray-900"},da={class:"text-sm text-gray-500"},ua={class:"px-6 py-4 whitespace-nowrap"},ma={class:"space-y-1"},_a={class:"text-sm text-gray-500"},fa={class:"px-6 py-4 whitespace-nowrap"},ga={class:"text-sm text-gray-900"},pa={class:"text-sm font-medium text-green-600"},ca={class:"px-6 py-4 whitespace-nowrap"},xa={class:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium space-x-2"},Ka=M({__name:"Index",props:{golongan:{},filters:{},cabangLomba:{}},setup(A){const N=[{title:"Dashboard",href:"/admin/dashboard"},{title:"Manajemen Golongan",href:"/admin/golongan"}],d=P({...A.filters}),_=q.debounce(()=>{$.get(route("admin.golongan.index"),d,{preserveState:!0,replace:!0})},300),G=()=>{d.search="",d.cabang="all",d.jenis_kelamin="all",d.status="all",_()},I=o=>{confirm(`Apakah Anda yakin ingin menghapus golongan ${o.nama_golongan}?`)&&$.delete(route("admin.golongan.destroy",o.id_golongan))},B=o=>({aktif:"default",non_aktif:"secondary"})[o]||"secondary",F=o=>({aktif:"Aktif",non_aktif:"Non Aktif"})[o]||o,K=o=>new Intl.NumberFormat("id-ID").format(o);return(o,t)=>(f(),V(H,{breadcrumbs:N},{default:s(()=>[a(e(z),{title:"Manajemen Golongan"}),a(J,{title:"Manajemen Golongan"}),n("div",Q,[a(e(j),null,{default:s(()=>[a(e(L),{class:"p-6"},{default:s(()=>[n("div",X,[n("div",null,[a(e(p),{for:"search"},{default:s(()=>t[5]||(t[5]=[i("Pencarian")])),_:1,__:[5]}),a(e(R),{id:"search",modelValue:d.search,"onUpdate:modelValue":t[0]||(t[0]=l=>d.search=l),placeholder:"Nama golongan, kode...",onInput:e(_)},null,8,["modelValue","onInput"])]),n("div",null,[a(e(p),{for:"cabang"},{default:s(()=>t[6]||(t[6]=[i("Cabang Lomba")])),_:1,__:[6]}),a(e(x),{modelValue:d.cabang,"onUpdate:modelValue":[t[1]||(t[1]=l=>d.cabang=l),e(_)]},{default:s(()=>[a(e(v),null,{default:s(()=>[a(e(k),{placeholder:"Semua Cabang"})]),_:1}),a(e(b),null,{default:s(()=>[a(e(u),{value:"all"},{default:s(()=>t[7]||(t[7]=[i("Semua Cabang")])),_:1,__:[7]}),(f(!0),c(C,null,S(o.cabangLomba,l=>(f(),V(e(u),{key:l.id_cabang,value:l.id_cabang.toString()},{default:s(()=>[i(r(l.nama_cabang),1)]),_:2},1032,["value"]))),128))]),_:1})]),_:1},8,["modelValue","onUpdate:modelValue"])]),n("div",null,[a(e(p),{for:"jenis_kelamin"},{default:s(()=>t[8]||(t[8]=[i("Jenis Kelamin")])),_:1,__:[8]}),a(e(x),{modelValue:d.jenis_kelamin,"onUpdate:modelValue":[t[2]||(t[2]=l=>d.jenis_kelamin=l),e(_)]},{default:s(()=>[a(e(v),null,{default:s(()=>[a(e(k),{placeholder:"Semua"})]),_:1}),a(e(b),null,{default:s(()=>[a(e(u),{value:"all"},{default:s(()=>t[9]||(t[9]=[i("Semua")])),_:1,__:[9]}),a(e(u),{value:"L"},{default:s(()=>t[10]||(t[10]=[i("Laki-laki")])),_:1,__:[10]}),a(e(u),{value:"P"},{default:s(()=>t[11]||(t[11]=[i("Perempuan")])),_:1,__:[11]})]),_:1})]),_:1},8,["modelValue","onUpdate:modelValue"])]),n("div",null,[a(e(p),{for:"status"},{default:s(()=>t[12]||(t[12]=[i("Status")])),_:1,__:[12]}),a(e(x),{modelValue:d.status,"onUpdate:modelValue":[t[3]||(t[3]=l=>d.status=l),e(_)]},{default:s(()=>[a(e(v),null,{default:s(()=>[a(e(k),{placeholder:"Semua Status"})]),_:1}),a(e(b),null,{default:s(()=>[a(e(u),{value:"all"},{default:s(()=>t[13]||(t[13]=[i("Semua Status")])),_:1,__:[13]}),a(e(u),{value:"aktif"},{default:s(()=>t[14]||(t[14]=[i("Aktif")])),_:1,__:[14]}),a(e(u),{value:"non_aktif"},{default:s(()=>t[15]||(t[15]=[i("Non Aktif")])),_:1,__:[15]})]),_:1})]),_:1},8,["modelValue","onUpdate:modelValue"])]),n("div",Y,[a(g,{onClick:G,variant:"outline",class:"w-full"},{default:s(()=>[a(m,{name:"x",class:"w-4 h-4 mr-2"}),t[16]||(t[16]=i(" Clear "))]),_:1,__:[16]})])])]),_:1})]),_:1}),n("div",Z,[n("div",aa," Menampilkan "+r(o.golongan.from)+" - "+r(o.golongan.to)+" dari "+r(o.golongan.total)+" golongan ",1),a(g,{onClick:t[4]||(t[4]=l=>o.$inertia.visit(o.route("admin.golongan.create")))},{default:s(()=>[a(m,{name:"plus",class:"w-4 h-4 mr-2"}),t[17]||(t[17]=i(" Tambah Golongan "))]),_:1,__:[17]})]),a(e(j),null,{default:s(()=>[a(e(L),{class:"p-0"},{default:s(()=>[n("div",ta,[n("table",ea,[t[19]||(t[19]=n("thead",{class:"bg-gray-50 border-b"},[n("tr",null,[n("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Golongan "),n("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Cabang Lomba "),n("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Kriteria "),n("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Kuota & Biaya "),n("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Status "),n("th",{class:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"}," Aksi ")])],-1)),n("tbody",sa,[(f(!0),c(C,null,S(o.golongan.data,l=>{var y,h;return f(),c("tr",{key:l.id_golongan,class:"hover:bg-gray-50"},[n("td",na,[n("div",null,[n("div",la,r(l.nama_golongan),1),n("div",oa,r(l.kode_golongan),1)])]),n("td",ia,[n("div",ra,r((y=l.cabang_lomba)==null?void 0:y.nama_cabang),1),n("div",da,r((h=l.cabang_lomba)==null?void 0:h.kode_cabang),1)]),n("td",ua,[n("div",ma,[a(e(U),{variant:l.jenis_kelamin==="L"?"default":"secondary"},{default:s(()=>[i(r(l.jenis_kelamin==="L"?"Laki-laki":"Perempuan"),1)]),_:2},1032,["variant"]),n("div",_a," Usia "+r(l.batas_umur_min)+"-"+r(l.batas_umur_max)+" tahun ",1)])]),n("td",fa,[n("div",ga,"Kuota: "+r(l.kuota_max),1),n("div",pa," Rp "+r(K(l.biaya_pendaftaran)),1)]),n("td",ca,[a(e(U),{variant:B(l.status)},{default:s(()=>[i(r(F(l.status)),1)]),_:2},1032,["variant"])]),n("td",xa,[a(g,{variant:"ghost",size:"sm",onClick:w=>o.$inertia.visit(o.route("admin.golongan.show",l.id_golongan))},{default:s(()=>[a(m,{name:"eye",class:"w-4 h-4"})]),_:2},1032,["onClick"]),a(g,{variant:"ghost",size:"sm",onClick:w=>o.$inertia.visit(o.route("admin.golongan.edit",l.id_golongan))},{default:s(()=>[a(m,{name:"edit",class:"w-4 h-4"})]),_:2},1032,["onClick"]),a(e(D),null,{default:s(()=>[a(e(E),{"as-child":""},{default:s(()=>[a(g,{variant:"ghost",size:"sm"},{default:s(()=>[a(m,{name:"more-vertical",class:"w-4 h-4"})]),_:1})]),_:1}),a(e(T),{align:"end"},{default:s(()=>[a(e(W),{onClick:w=>I(l),class:"text-red-600"},{default:s(()=>[a(m,{name:"trash",class:"w-4 h-4 mr-2"}),t[18]||(t[18]=i(" Hapus "))]),_:2,__:[18]},1032,["onClick"])]),_:2},1024)]),_:2},1024)])])}),128))])])])]),_:1})]),_:1}),a(O,{links:o.golongan.links},null,8,["links"])])]),_:1}))}});export{Ka as default};
