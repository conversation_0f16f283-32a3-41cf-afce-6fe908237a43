var c=t=>{throw TypeError(t)};var h=(t,n,o)=>n.has(t)||c("Cannot "+o);var l=(t,n,o)=>(h(t,n,"read from private field"),o?o.call(t):n.get(t)),f=(t,n,o)=>n.has(t)?c("Cannot add the same private member more than once"):n instanceof WeakSet?n.add(t):n.set(t,o);import{q as y,b as g}from"./useForwardExpose-CO14IhkA.js";import{k as m}from"./app-B_pmlBSQ.js";function $(t){return typeof t=="string"?`'${t}'`:new z().serialize(t)}const z=function(){var n;class t{constructor(){f(this,n,new Map)}compare(e,r){const i=typeof e,s=typeof r;return i==="string"&&s==="string"?e.localeCompare(r):i==="number"&&s==="number"?e-r:String.prototype.localeCompare.call(this.serialize(e,!0),this.serialize(r,!0))}serialize(e,r){if(e===null)return"null";switch(typeof e){case"string":return r?e:`'${e}'`;case"bigint":return`${e}n`;case"object":return this.$object(e);case"function":return this.$function(e)}return String(e)}serializeObject(e){const r=Object.prototype.toString.call(e);if(r!=="[object Object]")return this.serializeBuiltInType(r.length<10?`unknown:${r}`:r.slice(8,-1),e);const i=e.constructor,s=i===Object||i===void 0?"":i.name;if(s!==""&&globalThis[s]===i)return this.serializeBuiltInType(s,e);if(typeof e.toJSON=="function"){const a=e.toJSON();return s+(a!==null&&typeof a=="object"?this.$object(a):`(${this.serialize(a)})`)}return this.serializeObjectEntries(s,Object.entries(e))}serializeBuiltInType(e,r){const i=this["$"+e];if(i)return i.call(this,r);if(typeof(r==null?void 0:r.entries)=="function")return this.serializeObjectEntries(e,r.entries());throw new Error(`Cannot serialize ${e}`)}serializeObjectEntries(e,r){const i=Array.from(r).sort((a,u)=>this.compare(a[0],u[0]));let s=`${e}{`;for(let a=0;a<i.length;a++){const[u,p]=i[a];s+=`${this.serialize(u,!0)}:${this.serialize(p)}`,a<i.length-1&&(s+=",")}return s+"}"}$object(e){let r=l(this,n).get(e);return r===void 0&&(l(this,n).set(e,`#${l(this,n).size}`),r=this.serializeObject(e),l(this,n).set(e,r)),r}$function(e){const r=Function.prototype.toString.call(e);return r.slice(-15)==="[native code] }"?`${e.name||""}()[native]`:`${e.name}(${e.length})${r.replace(/\s*\n\s*/g,"")}`}$Array(e){let r="[";for(let i=0;i<e.length;i++)r+=this.serialize(e[i]),i<e.length-1&&(r+=",");return r+"]"}$Date(e){try{return`Date(${e.toISOString()})`}catch{return"Date(null)"}}$ArrayBuffer(e){return`ArrayBuffer[${new Uint8Array(e).join(",")}]`}$Set(e){return`Set${this.$Array(Array.from(e).sort((r,i)=>this.compare(r,i)))}`}$Map(e){return this.serializeObjectEntries("Map",e.entries())}}n=new WeakMap;for(const o of["Error","RegExp","URL"])t.prototype["$"+o]=function(e){return`${o}(${e})`};for(const o of["Int8Array","Uint8Array","Uint8ClampedArray","Int16Array","Uint16Array","Int32Array","Uint32Array","Float32Array","Float64Array"])t.prototype["$"+o]=function(e){return`${o}[${e.join(",")}]`};for(const o of["BigInt64Array","BigUint64Array"])t.prototype["$"+o]=function(e){return`${o}[${e.join("n,")}${e.length>0?"n":""}]`};return t}();function O(t,n){return t===n||$(t)===$(n)}function S(t){return t==null}function E(t){return m(()=>{var n;return y(t)?!!((n=g(t))!=null&&n.closest("form")):!0})}export{S as a,O as i,E as u};
