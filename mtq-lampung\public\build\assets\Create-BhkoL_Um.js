import{d as P,x as $,k as h,c,o,w as r,a as n,b as s,u as a,g as w,e as d,f as S,h as i,i as u,n as _,t as m,F as Q,m as C}from"./app-B_pmlBSQ.js";import{_ as q}from"./AppLayout.vue_vue_type_script_setup_true_lang-BRf8lUiM.js";import{_ as B}from"./Heading.vue_vue_type_script_setup_true_lang-is4VBSCS.js";import{_ as y}from"./AppLogoIcon.vue_vue_type_script_setup_true_lang-wzz4PWna.js";import{_ as U,a as I}from"./CardContent.vue_vue_type_script_setup_true_lang-ClvLNBXC.js";import{_ as j}from"./CardDescription.vue_vue_type_script_setup_true_lang-DboywBIK.js";import{_ as L,a as N}from"./CardTitle.vue_vue_type_script_setup_true_lang-5HO6oSoB.js";import{_ as p}from"./Input.vue_vue_type_script_setup_true_lang-DB1-RsTn.js";import{_ as g}from"./Label.vue_vue_type_script_setup_true_lang-rj_fhE7R.js";import{_ as E,a as F,b as A,c as z,d as x}from"./SelectValue.vue_vue_type_script_setup_true_lang-IzlIronZ.js";import{_ as k}from"./Icon.vue_vue_type_script_setup_true_lang-BuYzjckr.js";import"./useForwardExpose-CO14IhkA.js";import"./RovingFocusGroup-lsWyU4xZ.js";import"./check-DtmHjdxf.js";import"./useFormControl-mZRLifGx.js";import"./loader-circle-6d8QrWFr.js";import"./sun-CHIV9RJ8.js";const G={class:"max-w-4xl mx-auto"},H={class:"space-y-4"},O={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},R={key:0,class:"text-sm text-red-600 mt-1"},Y={key:0,class:"text-sm text-red-600 mt-1"},J={key:0,class:"text-sm text-red-600 mt-1"},K={key:0,class:"text-sm text-red-600 mt-1"},W={class:"space-y-4"},X={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},Z={key:0,class:"text-sm text-red-600 mt-1"},aa={key:0,class:"text-sm text-red-600 mt-1"},ta={key:0,class:"bg-blue-50 p-3 rounded-lg"},ea={class:"text-sm text-blue-800"},sa={class:"space-y-4"},na={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},la={key:0,class:"text-sm text-red-600 mt-1"},ra={key:0,class:"text-sm text-red-600 mt-1"},da={key:0,class:"bg-green-50 p-3 rounded-lg"},oa={class:"text-sm text-green-800"},ia={key:0,class:"bg-red-50 border border-red-200 p-4 rounded-lg"},ua={class:"flex"},ma={class:"text-sm text-red-700 mt-1 list-disc list-inside"},_a={class:"flex justify-end space-x-4 pt-6 border-t"},Qa=P({__name:"Create",setup(ga){const v=[{title:"Dashboard",href:"/admin/dashboard"},{title:"Manajemen Pelaksanaan MTQ",href:"/admin/pelaksanaan"},{title:"Tambah Pelaksanaan MTQ",href:"/admin/pelaksanaan/create"}],e=$({tahun:new Date().getFullYear().toString(),tema:"",tempat:"",tanggal_mulai:"",tanggal_selesai:"",tanggal_buka_pendaftaran:"",tanggal_tutup_pendaftaran:"",status:"draft"}),V=h(()=>Object.keys(e.errors).length>0),T=()=>{if(!e.tanggal_mulai||!e.tanggal_selesai)return 0;const f=new Date(e.tanggal_mulai),t=new Date(e.tanggal_selesai),l=Math.abs(t.getTime()-f.getTime());return Math.ceil(l/(1e3*60*60*24))+1},D=()=>{if(!e.tanggal_buka_pendaftaran||!e.tanggal_tutup_pendaftaran)return 0;const f=new Date(e.tanggal_buka_pendaftaran),t=new Date(e.tanggal_tutup_pendaftaran),l=Math.abs(t.getTime()-f.getTime());return Math.ceil(l/(1e3*60*60*24))+1},M=()=>{e.post(route("admin.pelaksanaan.store"),{onSuccess:()=>{}})};return(f,t)=>(o(),c(q,{breadcrumbs:v},{default:r(()=>[n(a(w),{title:"Tambah Pelaksanaan MTQ"}),n(B,{title:"Tambah Pelaksanaan MTQ"}),s("div",G,[n(a(U),null,{default:r(()=>[n(a(L),null,{default:r(()=>[n(a(N),null,{default:r(()=>t[9]||(t[9]=[d("Informasi Pelaksanaan MTQ Baru")])),_:1,__:[9]}),n(a(j),null,{default:r(()=>t[10]||(t[10]=[d(" Lengkapi form di bawah untuk menambahkan pelaksanaan MTQ baru ")])),_:1,__:[10]})]),_:1}),n(a(I),null,{default:r(()=>[s("form",{onSubmit:S(M,["prevent"]),class:"space-y-8"},[s("div",H,[t[18]||(t[18]=s("h3",{class:"text-lg font-medium"},"Informasi Dasar",-1)),s("div",O,[s("div",null,[n(a(g),{for:"tahun"},{default:r(()=>t[11]||(t[11]=[d("Tahun *")])),_:1,__:[11]}),n(a(p),{id:"tahun",modelValue:a(e).tahun,"onUpdate:modelValue":t[0]||(t[0]=l=>a(e).tahun=l),type:"number",required:"",min:"2020",max:"2050",placeholder:"2024",class:_({"border-red-500":a(e).errors.tahun})},null,8,["modelValue","class"]),a(e).errors.tahun?(o(),i("p",R,m(a(e).errors.tahun),1)):u("",!0)]),s("div",null,[n(a(g),{for:"status"},{default:r(()=>t[12]||(t[12]=[d("Status *")])),_:1,__:[12]}),n(a(E),{modelValue:a(e).status,"onUpdate:modelValue":t[1]||(t[1]=l=>a(e).status=l),required:""},{default:r(()=>[n(a(F),{class:_({"border-red-500":a(e).errors.status})},{default:r(()=>[n(a(A),{placeholder:"Pilih Status"})]),_:1},8,["class"]),n(a(z),null,{default:r(()=>[n(a(x),{value:"draft"},{default:r(()=>t[13]||(t[13]=[d("Draft")])),_:1,__:[13]}),n(a(x),{value:"aktif"},{default:r(()=>t[14]||(t[14]=[d("Aktif")])),_:1,__:[14]}),n(a(x),{value:"selesai"},{default:r(()=>t[15]||(t[15]=[d("Selesai")])),_:1,__:[15]})]),_:1})]),_:1},8,["modelValue"]),a(e).errors.status?(o(),i("p",Y,m(a(e).errors.status),1)):u("",!0)])]),s("div",null,[n(a(g),{for:"tema"},{default:r(()=>t[16]||(t[16]=[d("Tema MTQ *")])),_:1,__:[16]}),n(a(p),{id:"tema",modelValue:a(e).tema,"onUpdate:modelValue":t[2]||(t[2]=l=>a(e).tema=l),type:"text",required:"",placeholder:"Contoh: Membangun Generasi Qurani Menuju Indonesia Emas 2045",class:_({"border-red-500":a(e).errors.tema})},null,8,["modelValue","class"]),a(e).errors.tema?(o(),i("p",J,m(a(e).errors.tema),1)):u("",!0)]),s("div",null,[n(a(g),{for:"tempat"},{default:r(()=>t[17]||(t[17]=[d("Tempat Pelaksanaan *")])),_:1,__:[17]}),n(a(p),{id:"tempat",modelValue:a(e).tempat,"onUpdate:modelValue":t[3]||(t[3]=l=>a(e).tempat=l),type:"text",required:"",placeholder:"Contoh: Bandar Lampung, Lampung",class:_({"border-red-500":a(e).errors.tempat})},null,8,["modelValue","class"]),a(e).errors.tempat?(o(),i("p",K,m(a(e).errors.tempat),1)):u("",!0)])]),s("div",W,[t[21]||(t[21]=s("h3",{class:"text-lg font-medium"},"Periode Pelaksanaan",-1)),s("div",X,[s("div",null,[n(a(g),{for:"tanggal_mulai"},{default:r(()=>t[19]||(t[19]=[d("Tanggal Mulai *")])),_:1,__:[19]}),n(a(p),{id:"tanggal_mulai",modelValue:a(e).tanggal_mulai,"onUpdate:modelValue":t[4]||(t[4]=l=>a(e).tanggal_mulai=l),type:"date",required:"",class:_({"border-red-500":a(e).errors.tanggal_mulai})},null,8,["modelValue","class"]),a(e).errors.tanggal_mulai?(o(),i("p",Z,m(a(e).errors.tanggal_mulai),1)):u("",!0)]),s("div",null,[n(a(g),{for:"tanggal_selesai"},{default:r(()=>t[20]||(t[20]=[d("Tanggal Selesai *")])),_:1,__:[20]}),n(a(p),{id:"tanggal_selesai",modelValue:a(e).tanggal_selesai,"onUpdate:modelValue":t[5]||(t[5]=l=>a(e).tanggal_selesai=l),type:"date",required:"",class:_({"border-red-500":a(e).errors.tanggal_selesai})},null,8,["modelValue","class"]),a(e).errors.tanggal_selesai?(o(),i("p",aa,m(a(e).errors.tanggal_selesai),1)):u("",!0)])]),a(e).tanggal_mulai&&a(e).tanggal_selesai?(o(),i("div",ta,[s("p",ea,[n(k,{name:"calendar",class:"w-4 h-4 inline mr-1"}),d(" Durasi pelaksanaan: "+m(T())+" hari ",1)])])):u("",!0)]),s("div",sa,[t[24]||(t[24]=s("h3",{class:"text-lg font-medium"},"Periode Pendaftaran",-1)),s("div",na,[s("div",null,[n(a(g),{for:"tanggal_buka_pendaftaran"},{default:r(()=>t[22]||(t[22]=[d("Tanggal Buka Pendaftaran *")])),_:1,__:[22]}),n(a(p),{id:"tanggal_buka_pendaftaran",modelValue:a(e).tanggal_buka_pendaftaran,"onUpdate:modelValue":t[6]||(t[6]=l=>a(e).tanggal_buka_pendaftaran=l),type:"date",required:"",class:_({"border-red-500":a(e).errors.tanggal_buka_pendaftaran})},null,8,["modelValue","class"]),a(e).errors.tanggal_buka_pendaftaran?(o(),i("p",la,m(a(e).errors.tanggal_buka_pendaftaran),1)):u("",!0)]),s("div",null,[n(a(g),{for:"tanggal_tutup_pendaftaran"},{default:r(()=>t[23]||(t[23]=[d("Tanggal Tutup Pendaftaran *")])),_:1,__:[23]}),n(a(p),{id:"tanggal_tutup_pendaftaran",modelValue:a(e).tanggal_tutup_pendaftaran,"onUpdate:modelValue":t[7]||(t[7]=l=>a(e).tanggal_tutup_pendaftaran=l),type:"date",required:"",class:_({"border-red-500":a(e).errors.tanggal_tutup_pendaftaran})},null,8,["modelValue","class"]),a(e).errors.tanggal_tutup_pendaftaran?(o(),i("p",ra,m(a(e).errors.tanggal_tutup_pendaftaran),1)):u("",!0)])]),a(e).tanggal_buka_pendaftaran&&a(e).tanggal_tutup_pendaftaran?(o(),i("div",da,[s("p",oa,[n(k,{name:"calendar-check",class:"w-4 h-4 inline mr-1"}),d(" Durasi pendaftaran: "+m(D())+" hari ",1)])])):u("",!0)]),t[28]||(t[28]=s("div",{class:"bg-blue-50 p-4 rounded-lg"},[s("h4",{class:"font-medium text-blue-900 mb-2"},"Informasi Pelaksanaan MTQ"),s("div",{class:"text-sm text-blue-800 space-y-1"},[s("p",null,[s("strong",null,"Status Draft:"),d(" Pelaksanaan belum aktif, tidak dapat digunakan untuk pendaftaran")]),s("p",null,[s("strong",null,"Status Aktif:"),d(" Pelaksanaan sedang berlangsung dan dapat menerima pendaftaran")]),s("p",null,[s("strong",null,"Status Selesai:"),d(" Pelaksanaan telah selesai, tidak dapat menerima pendaftaran baru")]),s("p",null,[s("strong",null,"Catatan:"),d(" Hanya satu pelaksanaan yang dapat aktif dalam satu waktu")])])],-1)),V.value?(o(),i("div",ia,[s("div",ua,[n(k,{name:"alert-circle",class:"w-5 h-5 text-red-600 mr-2 mt-0.5"}),s("div",null,[t[25]||(t[25]=s("h4",{class:"font-medium text-red-800"},"Terdapat kesalahan pada form:",-1)),s("ul",ma,[(o(!0),i(Q,null,C(a(e).errors,(l,b)=>(o(),i("li",{key:b},m(l),1))),128))])])])])):u("",!0),s("div",_a,[n(y,{type:"button",variant:"outline",onClick:t[8]||(t[8]=l=>f.$inertia.visit(f.route("admin.pelaksanaan.index")))},{default:r(()=>t[26]||(t[26]=[d(" Batal ")])),_:1,__:[26]}),n(y,{type:"submit",disabled:a(e).processing},{default:r(()=>[a(e).processing?(o(),c(k,{key:0,name:"loader-2",class:"w-4 h-4 mr-2 animate-spin"})):u("",!0),t[27]||(t[27]=d(" Simpan Pelaksanaan MTQ "))]),_:1,__:[27]},8,["disabled"])])],32)]),_:1})]),_:1})])]),_:1}))}});export{Qa as default};
