<script setup lang="ts">
import { ref } from 'vue'
import { router } from '@inertiajs/vue3'
import AppLayout from '@/layouts/AppLayout.vue'
import { Head } from '@inertiajs/vue3'
import Heading from '@/components/Heading.vue'
import Button from '@/components/ui/button/Button.vue'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import Icon from '@/components/Icon.vue'
import { type BreadcrumbItem } from '@/types'

interface Golongan {
    id_golongan: number
    nama_golongan: string
    jenis_kelamin: string
    batas_umur_min: number
    batas_umur_max: number
    kuota_max: number
    biaya_pendaftaran: number
    status: string
}

interface CabangLomba {
    id_cabang: number
    nama_cabang: string
    deskripsi: string
    status: string
    golongan: Golongan[]
}

interface Stats {
    total_cabang: number
    total_golongan: number
    total_golongan_laki: number
    total_golongan_perempuan: number
}

const props = defineProps<{
    cabangLomba: CabangLomba[]
    stats: Stats
    filters: {
        search?: string
        jenis_kelamin?: string
    },
    auth: any;
}>()

const form = ref({
    search: props.filters.search || '',
    jenis_kelamin: props.filters.jenis_kelamin || 'all'
})

function search() {
    router.get(route('competition.index'), form.value, {
        preserveState: true,
        replace: true
    })
}

function formatCurrency(amount: number): string {
    return new Intl.NumberFormat('id-ID', {
        style: 'currency',
        currency: 'IDR',
        minimumFractionDigits: 0
    }).format(amount)
}

function getGenderBadgeColor(gender: string): string {
    return gender === 'L' ? 'bg-blue-100 text-blue-800' : 'bg-pink-100 text-pink-800'
}

const breadcrumbItems: BreadcrumbItem[] = [
    {
        title: 'Cabang Lomba',
        href: route('competition.index'),
    },
];
</script>

<template>
    <AppLayout :breadcrumbs="breadcrumbItems">
            <div class="flex items-center justify-between">
                <Heading title="Cabang Lomba MTQ" />
                {{ props.auth.user }}
                <Button
                    v-if="props.auth.user "
                    as="link"
                    :href="route('peserta.dashboard')"
                    variant="outline"
                >
                    <Icon name="user" class="w-4 h-4 mr-2" />
                    Dashboard Saya
                </Button>
            </div>

        <Head title="Cabang Lomba MTQ" />

        <div class="space-y-6">
            <!-- Statistics Cards -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <Card>
                    <CardContent class="p-6">
                        <div class="flex items-center space-x-2">
                            <Icon name="trophy" class="h-8 w-8 text-yellow-600" />
                            <div>
                                <p class="text-2xl font-bold">{{ stats.total_cabang }}</p>
                                <p class="text-sm text-gray-600">Cabang Lomba</p>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardContent class="p-6">
                        <div class="flex items-center space-x-2">
                            <Icon name="users" class="h-8 w-8 text-blue-600" />
                            <div>
                                <p class="text-2xl font-bold">{{ stats.total_golongan }}</p>
                                <p class="text-sm text-gray-600">Total Golongan</p>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardContent class="p-6">
                        <div class="flex items-center space-x-2">
                            <Icon name="user" class="h-8 w-8 text-blue-600" />
                            <div>
                                <p class="text-2xl font-bold">{{ stats.total_golongan_laki }}</p>
                                <p class="text-sm text-gray-600">Golongan Putra</p>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardContent class="p-6">
                        <div class="flex items-center space-x-2">
                            <Icon name="user" class="h-8 w-8 text-pink-600" />
                            <div>
                                <p class="text-2xl font-bold">{{ stats.total_golongan_perempuan }}</p>
                                <p class="text-sm text-gray-600">Golongan Putri</p>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>

            <!-- Search and Filter -->
            <Card>
                <CardContent class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div class="md:col-span-2">
                            <Label for="search">Cari Cabang Lomba</Label>
                            <Input
                                id="search"
                                v-model="form.search"
                                placeholder="Cari berdasarkan nama cabang atau golongan..."
                                @input="search"
                            />
                        </div>
                        <div>
                            <Label for="jenis_kelamin">Filter Jenis Kelamin</Label>
                            <select
                                id="jenis_kelamin"
                                v-model="form.jenis_kelamin"
                                @change="search"
                                class="flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50"
                            >
                                <option value="all">Semua</option>
                                <option value="L">Putra</option>
                                <option value="P">Putri</option>
                            </select>
                        </div>
                    </div>
                </CardContent>
            </Card>

            <!-- Competition List -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card v-for="cabang in cabangLomba" :key="cabang.id_cabang" class="hover:shadow-lg transition-shadow">
                    <CardHeader>
                        <CardTitle class="flex items-center justify-between">
                            {{ cabang.nama_cabang }}
                            <Badge variant="secondary">{{ cabang.golongan.length }} Golongan</Badge>
                        </CardTitle>
                        <CardDescription>{{ cabang.deskripsi }}</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div class="space-y-3">
                            <div v-for="golongan in cabang.golongan" :key="golongan.id_golongan"
                                 class="p-3 border rounded-lg hover:bg-gray-50 transition-colors">
                                <div class="flex items-center justify-between mb-2">
                                    <h4 class="font-medium">{{ golongan.nama_golongan }}</h4>
                                    <Badge :class="getGenderBadgeColor(golongan.jenis_kelamin)">
                                        {{ golongan.jenis_kelamin === 'L' ? 'Putra' : 'Putri' }}
                                    </Badge>
                                </div>
                                <div class="text-sm text-gray-600 space-y-1">
                                    <p>Usia: {{ golongan.batas_umur_min }} - {{ golongan.batas_umur_max }} tahun</p>
                                    <p>Kuota: {{ golongan.kuota_max }} peserta</p>
                                    <p class="font-medium text-green-600">{{ formatCurrency(golongan.biaya_pendaftaran) }}</p>
                                </div>
                                <div class="mt-3">
                                    <Button
                                        as="link"
                                        :href="route('competition.golongan', golongan.id_golongan)"
                                        size="sm"
                                        class="w-full"
                                    >
                                        Lihat Detail & Daftar
                                    </Button>
                                </div>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>

            <!-- Empty State -->
            <div v-if="cabangLomba.length === 0" class="text-center py-12">
                <Icon name="search" class="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 class="text-lg font-medium text-gray-900 mb-2">Tidak ada cabang lomba ditemukan</h3>
                <p class="text-gray-600">Coba ubah kata kunci pencarian atau filter yang Anda gunakan.</p>
            </div>
        </div>
    </AppLayout>
</template>
