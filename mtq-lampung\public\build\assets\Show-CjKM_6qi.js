import{_ as B}from"./AppLayout.vue_vue_type_script_setup_true_lang-BRf8lUiM.js";import{d as z,c as h,o as d,w as r,a as s,b as t,u as i,g as A,e as o,t as n,n as _,h as m,i as p,F as w,m as T}from"./app-B_pmlBSQ.js";import{_ as V}from"./Heading.vue_vue_type_script_setup_true_lang-is4VBSCS.js";import{_ as y}from"./AppLogoIcon.vue_vue_type_script_setup_true_lang-wzz4PWna.js";import{_ as b,a as c}from"./CardContent.vue_vue_type_script_setup_true_lang-ClvLNBXC.js";import{_ as j}from"./CardDescription.vue_vue_type_script_setup_true_lang-DboywBIK.js";import{_ as P,a as $}from"./CardTitle.vue_vue_type_script_setup_true_lang-5HO6oSoB.js";import{_ as f}from"./index-CMGr3-bt.js";import{_ as g}from"./Icon.vue_vue_type_script_setup_true_lang-BuYzjckr.js";import"./useForwardExpose-CO14IhkA.js";import"./RovingFocusGroup-lsWyU4xZ.js";import"./check-DtmHjdxf.js";import"./loader-circle-6d8QrWFr.js";import"./sun-CHIV9RJ8.js";const E={class:"flex items-center space-x-4"},M={class:"space-y-6"},R={class:"flex items-center justify-between"},J={class:"flex space-x-2"},U={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"},Y={class:"space-y-4"},q={class:"font-mono"},G={class:"space-y-4"},H={key:0},O={class:"space-y-4"},Q={class:"font-mono"},W={key:0,class:"mt-6 pt-6 border-t"},X={key:1,class:"mt-4"},Z={class:"text-sm"},tt={key:0,class:"text-center py-8"},et={key:1,class:"space-y-4"},at={class:"flex items-center justify-between mb-3"},st={class:"font-medium"},nt={class:"text-sm text-gray-600"},rt={class:"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm"},lt={class:"font-mono"},it={class:"font-mono"},ot={key:0,class:"mt-4 p-3 bg-gray-50 rounded-lg"},dt={class:"flex items-center justify-between mb-2"},mt={class:"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm"},ut={class:"font-mono"},pt={class:"font-medium"},ft={key:0,class:"mt-2 text-xs text-gray-600"},gt={key:1,class:"mt-4"},_t={class:"text-sm font-medium mb-2"},yt={class:"flex flex-wrap gap-2"},bt={class:"flex flex-wrap gap-4"},Ft=z({__name:"Show",props:{peserta:{}},setup(ct){function x(a){return{draft:"bg-gray-100 text-gray-800",submitted:"bg-blue-100 text-blue-800",verified:"bg-indigo-100 text-indigo-800",approved:"bg-green-100 text-green-800",rejected:"bg-red-100 text-red-800"}[a]||"bg-gray-100 text-gray-800"}function v(a){return{draft:"Draft",submitted:"Disubmit",verified:"Terverifikasi",approved:"Disetujui",rejected:"Ditolak"}[a]||a}function C(a){return{pending:"bg-yellow-100 text-yellow-800",paid:"bg-green-100 text-green-800",failed:"bg-red-100 text-red-800",expired:"bg-gray-100 text-gray-800"}[a]||"bg-gray-100 text-gray-800"}function N(a){return{pending:"bg-yellow-100 text-yellow-800",approved:"bg-green-100 text-green-800",rejected:"bg-red-100 text-red-800"}[a]||"bg-gray-100 text-gray-800"}function S(a){return new Intl.NumberFormat("id-ID",{style:"currency",currency:"IDR",minimumFractionDigits:0}).format(a)}function L(a){return new Date(a).toLocaleDateString("id-ID",{year:"numeric",month:"long",day:"numeric"})}function k(a){return new Date(a).toLocaleDateString("id-ID",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"})}function F(a){const e=new Date,l=new Date(a);let u=e.getFullYear()-l.getFullYear();const D=e.getMonth()-l.getMonth();return(D<0||D===0&&e.getDate()<l.getDate())&&u--,u}function I(a){return{foto:"Foto",ktp:"KTP",kartu_keluarga:"Kartu Keluarga",surat_rekomendasi:"Surat Rekomendasi",ijazah:"Ijazah",sertifikat:"Sertifikat",lainnya:"Lainnya"}[a]||a}function K(a){return{mandiri:"Mandiri",admin_daerah:"Admin Daerah"}[a]||a}return(a,e)=>(d(),h(B,null,{header:r(()=>[t("div",E,[s(y,{as:"link",href:a.route("admin-daerah.peserta.index"),variant:"ghost",size:"sm"},{default:r(()=>[s(g,{name:"arrow-left",class:"w-4 h-4 mr-2"}),e[1]||(e[1]=o(" Kembali "))]),_:1,__:[1]},8,["href"]),s(V,null,{default:r(()=>e[2]||(e[2]=[o("Detail Peserta")])),_:1,__:[2]})])]),default:r(()=>[s(i(A),{title:"Detail Peserta"}),t("div",M,[s(i(b),null,{default:r(()=>[s(i(P),null,{default:r(()=>[t("div",R,[s(i($),null,{default:r(()=>[o(n(a.peserta.nama_lengkap),1)]),_:1}),t("div",J,[s(i(f),{class:_(x(a.peserta.status_peserta))},{default:r(()=>[o(n(v(a.peserta.status_peserta)),1)]),_:1},8,["class"]),s(i(f),{variant:"outline"},{default:r(()=>[o(n(K(a.peserta.registration_type)),1)]),_:1})])]),s(i(j),null,{default:r(()=>[o(n(a.peserta.wilayah.nama_wilayah),1)]),_:1})]),_:1}),s(i(c),null,{default:r(()=>[t("div",U,[t("div",Y,[t("div",null,[e[3]||(e[3]=t("p",{class:"text-sm font-medium text-gray-500"},"NIK",-1)),t("p",q,n(a.peserta.nik),1)]),t("div",null,[e[4]||(e[4]=t("p",{class:"text-sm font-medium text-gray-500"},"Tempat, Tanggal Lahir",-1)),t("p",null,n(a.peserta.tempat_lahir)+", "+n(L(a.peserta.tanggal_lahir)),1)]),t("div",null,[e[5]||(e[5]=t("p",{class:"text-sm font-medium text-gray-500"},"Usia",-1)),t("p",null,n(F(a.peserta.tanggal_lahir))+" tahun",1)])]),t("div",G,[t("div",null,[e[6]||(e[6]=t("p",{class:"text-sm font-medium text-gray-500"},"Jenis Kelamin",-1)),t("p",null,n(a.peserta.jenis_kelamin==="L"?"Laki-laki":"Perempuan"),1)]),t("div",null,[e[7]||(e[7]=t("p",{class:"text-sm font-medium text-gray-500"},"Email",-1)),t("p",null,n(a.peserta.email),1)]),a.peserta.no_telepon?(d(),m("div",H,[e[8]||(e[8]=t("p",{class:"text-sm font-medium text-gray-500"},"No. Telepon",-1)),t("p",null,n(a.peserta.no_telepon),1)])):p("",!0)]),t("div",O,[t("div",null,[e[9]||(e[9]=t("p",{class:"text-sm font-medium text-gray-500"},"Username",-1)),t("p",Q,n(a.peserta.user.username),1)]),t("div",null,[e[10]||(e[10]=t("p",{class:"text-sm font-medium text-gray-500"},"Tanggal Daftar",-1)),t("p",null,n(k(a.peserta.created_at)),1)])])]),a.peserta.alamat?(d(),m("div",W,[e[11]||(e[11]=t("p",{class:"text-sm font-medium text-gray-500 mb-2"},"Alamat",-1)),t("p",null,n(a.peserta.alamat),1)])):p("",!0),a.peserta.keterangan?(d(),m("div",X,[e[12]||(e[12]=t("p",{class:"text-sm font-medium text-gray-500 mb-2"},"Keterangan",-1)),t("p",Z,n(a.peserta.keterangan),1)])):p("",!0)]),_:1})]),_:1}),s(i(b),null,{default:r(()=>[s(i(P),null,{default:r(()=>[s(i($),null,{default:r(()=>e[13]||(e[13]=[o("Pendaftaran Lomba")])),_:1,__:[13]}),s(i(j),null,{default:r(()=>e[14]||(e[14]=[o("Daftar lomba yang diikuti peserta")])),_:1,__:[14]})]),_:1}),s(i(c),null,{default:r(()=>[a.peserta.pendaftaran.length===0?(d(),m("div",tt,[s(g,{name:"clipboard-list",class:"h-12 w-12 text-gray-400 mx-auto mb-4"}),e[15]||(e[15]=t("h3",{class:"text-lg font-medium text-gray-900 mb-2"},"Belum Ada Pendaftaran",-1)),e[16]||(e[16]=t("p",{class:"text-gray-600"},"Peserta belum mendaftar untuk lomba apapun.",-1))])):(d(),m("div",et,[(d(!0),m(w,null,T(a.peserta.pendaftaran,l=>(d(),m("div",{key:l.id_pendaftaran,class:"p-4 border rounded-lg"},[t("div",at,[t("div",null,[t("h4",st,n(l.golongan.nama_golongan),1),t("p",nt,n(l.golongan.cabang_lomba.nama_cabang),1)]),s(i(f),{class:_(x(l.status_pendaftaran))},{default:r(()=>[o(n(v(l.status_pendaftaran)),1)]),_:2},1032,["class"])]),t("div",rt,[t("div",null,[e[17]||(e[17]=t("p",{class:"font-medium text-gray-500"},"No. Pendaftaran",-1)),t("p",lt,n(l.nomor_pendaftaran),1)]),t("div",null,[e[18]||(e[18]=t("p",{class:"font-medium text-gray-500"},"No. Peserta",-1)),t("p",it,n(l.nomor_peserta),1)]),t("div",null,[e[19]||(e[19]=t("p",{class:"font-medium text-gray-500"},"Tahun",-1)),t("p",null,n(l.tahun_pendaftaran),1)])]),l.pembayaran?(d(),m("div",ot,[t("div",dt,[e[20]||(e[20]=t("span",{class:"text-sm font-medium"},"Pembayaran",-1)),s(i(f),{class:_(C(l.pembayaran.status_pembayaran))},{default:r(()=>[o(n(l.pembayaran.status_pembayaran),1)]),_:2},1032,["class"])]),t("div",mt,[t("div",null,[e[21]||(e[21]=t("p",{class:"text-gray-500"},"No. Transaksi",-1)),t("p",ut,n(l.pembayaran.nomor_transaksi),1)]),t("div",null,[e[22]||(e[22]=t("p",{class:"text-gray-500"},"Jumlah",-1)),t("p",pt,n(S(l.pembayaran.jumlah_bayar)),1)])]),l.pembayaran.tanggal_bayar?(d(),m("div",ft," Dibayar: "+n(k(l.pembayaran.tanggal_bayar)),1)):p("",!0)])):p("",!0),l.dokumen_peserta.length>0?(d(),m("div",gt,[t("p",_t,"Dokumen ("+n(l.dokumen_peserta.length)+")",1),t("div",yt,[(d(!0),m(w,null,T(l.dokumen_peserta,u=>(d(),h(i(f),{key:u.jenis_dokumen,class:_([N(u.status_verifikasi),"text-xs"]),variant:"outline"},{default:r(()=>[o(n(I(u.jenis_dokumen)),1)]),_:2},1032,["class"]))),128))])])):p("",!0)]))),128))]))]),_:1})]),_:1}),s(i(b),null,{default:r(()=>[s(i(c),{class:"p-6"},{default:r(()=>[t("div",bt,[s(y,{as:"link",href:a.route("admin-daerah.peserta.edit",a.peserta.id_peserta)},{default:r(()=>[s(g,{name:"edit",class:"w-4 h-4 mr-2"}),e[23]||(e[23]=o(" Edit Peserta "))]),_:1,__:[23]},8,["href"]),s(y,{as:"link",href:a.route("admin-daerah.pendaftaran.create",{peserta:a.peserta.id_peserta}),variant:"outline"},{default:r(()=>[s(g,{name:"plus",class:"w-4 h-4 mr-2"}),e[24]||(e[24]=o(" Daftarkan ke Lomba "))]),_:1,__:[24]},8,["href"]),s(y,{variant:"outline",onClick:e[0]||(e[0]=l=>a.window.print())},{default:r(()=>[s(g,{name:"printer",class:"w-4 h-4 mr-2"}),e[25]||(e[25]=o(" Cetak Data "))]),_:1,__:[25]})])]),_:1})]),_:1})])]),_:1}))}});export{Ft as default};
