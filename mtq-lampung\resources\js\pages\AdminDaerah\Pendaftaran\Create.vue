<template>
  <AppLayout title="Daftarkan Peserta ke Lomba">
    <template #header>
      <Heading>Daftarkan Peserta ke Lomba</Heading>
    </template>

    <div class="max-w-4xl mx-auto space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Form Pendaftaran Lomba</CardTitle>
          <CardDescription>
            Daftarkan peserta dari wilayah Anda ke golongan lomba yang tersedia
          </CardDescription>
        </CardHeader>
        <CardContent class="space-y-6">
          <form @submit.prevent="submit">
            <!-- Peserta Selection -->
            <div class="space-y-2">
              <Label for="id_peserta">Pilih Peserta *</Label>
              <Select v-model="form.id_peserta" required>
                <SelectTrigger>
                  <SelectValue placeholder="Pilih peserta yang akan didaftarkan" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem v-for="p in peserta" :key="p.id_peserta" :value="p.id_peserta.toString()">
                    <div class="flex flex-col">
                      <span class="font-medium">{{ p.nama_lengkap }}</span>
                      <span class="text-sm text-gray-500">{{ p.nik }} - {{ p.user?.email }}</span>
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
              <p v-if="errors.id_peserta" class="text-sm text-red-600">{{ errors.id_peserta }}</p>
            </div>

            <!-- Selected Peserta Info -->
            <div v-if="selectedPesertaInfo" class="p-4 bg-blue-50 rounded-lg">
              <h4 class="font-medium text-blue-900 mb-2">Informasi Peserta Terpilih</h4>
              <div class="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span class="text-blue-700">Nama:</span>
                  <span class="ml-2 font-medium">{{ selectedPesertaInfo.nama_lengkap }}</span>
                </div>
                <div>
                  <span class="text-blue-700">NIK:</span>
                  <span class="ml-2 font-medium">{{ selectedPesertaInfo.nik }}</span>
                </div>
                <div>
                  <span class="text-blue-700">Jenis Kelamin:</span>
                  <span class="ml-2 font-medium">{{ selectedPesertaInfo.jenis_kelamin === 'L' ? 'Laki-laki' : 'Perempuan' }}</span>
                </div>
                <div>
                  <span class="text-blue-700">Tanggal Lahir:</span>
                  <span class="ml-2 font-medium">{{ formatDate(selectedPesertaInfo.tanggal_lahir) }}</span>
                </div>
              </div>
            </div>

            <!-- Golongan Selection -->
            <div class="space-y-2">
              <Label for="id_golongan">Pilih Golongan *</Label>
              <Select v-model="form.id_golongan" required>
                <SelectTrigger>
                  <SelectValue placeholder="Pilih golongan lomba" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem v-for="g in availableGolongan" :key="g.id_golongan" :value="g.id_golongan.toString()">
                    <div class="flex flex-col">
                      <span class="font-medium">{{ g.nama_golongan }}</span>
                      <span class="text-sm text-gray-500">
                        {{ g.cabang_lomba?.nama_cabang }} -
                        {{ g.jenis_kelamin === 'L' ? 'Putra' : 'Putri' }} -
                        Umur {{ g.batas_umur_min }}-{{ g.batas_umur_max }} tahun
                      </span>
                      <span class="text-sm text-green-600">
                        Biaya: Rp {{ formatCurrency(g.biaya_pendaftaran) }}
                      </span>
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
              <p v-if="errors.id_golongan" class="text-sm text-red-600">{{ errors.id_golongan }}</p>
            </div>

            <!-- Selected Golongan Info -->
            <div v-if="selectedGolonganInfo" class="p-4 bg-green-50 rounded-lg">
              <h4 class="font-medium text-green-900 mb-2">Informasi Golongan Terpilih</h4>
              <div class="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span class="text-green-700">Cabang Lomba:</span>
                  <span class="ml-2 font-medium">{{ selectedGolonganInfo.cabang_lomba?.nama_cabang }}</span>
                </div>
                <div>
                  <span class="text-green-700">Golongan:</span>
                  <span class="ml-2 font-medium">{{ selectedGolonganInfo.nama_golongan }}</span>
                </div>
                <div>
                  <span class="text-green-700">Jenis Kelamin:</span>
                  <span class="ml-2 font-medium">{{ selectedGolonganInfo.jenis_kelamin === 'L' ? 'Putra' : 'Putri' }}</span>
                </div>
                <div>
                  <span class="text-green-700">Batas Umur:</span>
                  <span class="ml-2 font-medium">{{ selectedGolonganInfo.batas_umur_min }}-{{ selectedGolonganInfo.batas_umur_max }} tahun</span>
                </div>
                <div>
                  <span class="text-green-700">Biaya Pendaftaran:</span>
                  <span class="ml-2 font-medium text-lg">Rp {{ formatCurrency(selectedGolonganInfo.biaya_pendaftaran) }}</span>
                </div>
              </div>
            </div>

            <!-- Keterangan (Optional) -->
            <div class="space-y-2">
              <Label for="keterangan">Keterangan (Opsional)</Label>
              <Textarea
                id="keterangan"
                v-model="form.keterangan"
                placeholder="Tambahkan keterangan atau catatan khusus untuk pendaftaran ini"
                rows="3"
              />
              <p class="text-sm text-gray-500">
                Catatan tambahan untuk pendaftaran peserta ini
              </p>
              <p v-if="errors.keterangan" class="text-sm text-red-600">{{ errors.keterangan }}</p>
            </div>

            <!-- Age Validation Warning -->
            <div v-if="ageValidationMessage" class="p-4 rounded-lg" :class="ageValidationMessage.type === 'error' ? 'bg-red-50 text-red-700' : 'bg-yellow-50 text-yellow-700'">
              <div class="flex">
                <Icon :name="ageValidationMessage.type === 'error' ? 'alert-circle' : 'alert-triangle'" class="w-5 h-5 mr-2 mt-0.5" />
                <div>
                  <p class="font-medium">{{ ageValidationMessage.title }}</p>
                  <p class="text-sm">{{ ageValidationMessage.message }}</p>
                </div>
              </div>
            </div>

            <!-- Form Actions -->
            <div class="flex justify-between pt-6">
              <Button
                type="button"
                variant="outline"
                @click="$inertia.visit(route('admin-daerah.pendaftaran.index'))"
              >
                <Icon name="arrow-left" class="w-4 h-4 mr-2" />
                Kembali
              </Button>
              <Button
                type="submit"
                :disabled="processing || !canSubmit"
                :class="{ 'opacity-50 cursor-not-allowed': processing || !canSubmit }"
              >
                <Icon name="save" class="w-4 h-4 mr-2" />
                {{ processing ? 'Menyimpan...' : 'Daftarkan Peserta' }}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import { computed, reactive, watch } from 'vue'
import { Head, useForm } from '@inertiajs/vue3'
import AppLayout from '@/layouts/AppLayout.vue'
import Heading from '@/components/Heading.vue'
import Button from '@/components/ui/button/Button.vue'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import Icon from '@/components/Icon.vue'

interface Props {
  peserta: any[]
  golongan: any[]
  selectedPeserta?: any
  errors: Record<string, string>
}

const props = defineProps<Props>()

const form = useForm({
  id_peserta: props.selectedPeserta?.id_peserta?.toString() || '',
  id_golongan: '',
  keterangan: ''
})

const processing = computed(() => form.processing)

const selectedPesertaInfo = computed(() => {
  if (!form.id_peserta) return null
  return props.peserta.find(p => p.id_peserta.toString() === form.id_peserta)
})

const selectedGolonganInfo = computed(() => {
  if (!form.id_golongan) return null
  return props.golongan.find(g => g.id_golongan.toString() === form.id_golongan)
})

const availableGolongan = computed(() => {
  if (!selectedPesertaInfo.value) return props.golongan

  // Filter golongan based on gender
  return props.golongan.filter(g => g.jenis_kelamin === selectedPesertaInfo.value.jenis_kelamin)
})

const ageValidationMessage = computed(() => {
  if (!selectedPesertaInfo.value || !selectedGolonganInfo.value) return null

  const birthDate = new Date(selectedPesertaInfo.value.tanggal_lahir)
  const today = new Date()
  const age = today.getFullYear() - birthDate.getFullYear()
  const monthDiff = today.getMonth() - birthDate.getMonth()

  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
    age--
  }

  const minAge = selectedGolonganInfo.value.batas_umur_min
  const maxAge = selectedGolonganInfo.value.batas_umur_max

  if (age < minAge || age > maxAge) {
    return {
      type: 'error',
      title: 'Umur Tidak Sesuai',
      message: `Peserta berumur ${age} tahun, sedangkan golongan ini untuk umur ${minAge}-${maxAge} tahun.`
    }
  }

  return null
})

const canSubmit = computed(() => {
  return form.id_peserta && form.id_golongan && !ageValidationMessage.value?.type === 'error'
})

// Reset golongan when peserta changes
watch(() => form.id_peserta, () => {
  form.id_golongan = ''
})

const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString('id-ID', {
    day: 'numeric',
    month: 'long',
    year: 'numeric'
  })
}

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('id-ID').format(amount)
}

const submit = () => {
  form.post(route('admin-daerah.pendaftaran.store'))
}
</script>
