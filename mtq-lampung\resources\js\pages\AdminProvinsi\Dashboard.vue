<template>
  <AppLayout>
    <Head title="Dashboard Admin Provinsi" />

    <div class="space-y-6">
      <!-- Header -->
      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-2xl font-bold text-gray-900">Dashboard Admin Provinsi</h1>
            <p class="text-gray-600 mt-1">Verifikasi pendaftaran peserta MTQ Lampung</p>
          </div>
          <div class="flex items-center space-x-4">
            <div class="text-right">
              <p class="text-sm text-gray-500">Selamat datang,</p>
              <p class="font-semibold text-gray-900">{{ $page.props.auth.user.nama_lengkap }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Statistics Cards -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div class="bg-white rounded-lg shadow p-6">
          <div class="flex items-center">
            <div class="p-3 rounded-full bg-blue-100">
              <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
              </svg>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-500">Total Pendaftaran Terverifikasi</p>
              <p class="text-2xl font-bold text-gray-900">{{ stats.total_pendaftaran_verified }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
          <div class="flex items-center">
            <div class="p-3 rounded-full bg-yellow-100">
              <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-500">Menunggu Verifikasi</p>
              <p class="text-2xl font-bold text-gray-900">{{ stats.pending_verification }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
          <div class="flex items-center">
            <div class="p-3 rounded-full bg-green-100">
              <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-500">Disetujui</p>
              <p class="text-2xl font-bold text-gray-900">{{ stats.verification_approved }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
          <div class="flex items-center">
            <div class="p-3 rounded-full bg-orange-100">
              <svg class="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
              </svg>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-500">NIK Pending</p>
              <p class="text-2xl font-bold text-gray-900">{{ stats.nik_pending }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
          <div class="flex items-center">
            <div class="p-3 rounded-full bg-purple-100">
              <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
              </svg>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-500">Dokumen Pending</p>
              <p class="text-2xl font-bold text-gray-900">{{ stats.dokumen_pending }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
          <div class="flex items-center">
            <div class="p-3 rounded-full bg-red-100">
              <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-500">Ditolak</p>
              <p class="text-2xl font-bold text-gray-900">{{ stats.verification_rejected }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Quick Actions -->
      <div class="bg-white rounded-lg shadow p-6">
        <h2 class="text-lg font-semibold text-gray-900 mb-4">Aksi Cepat</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <Link :href="route('admin.verifikasi-provinsi.index')"
                class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
            <div class="p-2 bg-emerald-100 rounded-lg">
              <svg class="w-5 h-5 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
            <div class="ml-3">
              <p class="font-medium text-gray-900">Verifikasi Pendaftaran</p>
              <p class="text-sm text-gray-500">Verifikasi NIK dan dokumen peserta</p>
            </div>
          </Link>
        </div>
      </div>

      <!-- Recent Registrations -->
      <div class="bg-white rounded-lg shadow">
        <div class="p-6 border-b border-gray-200">
          <div class="flex items-center justify-between">
            <h2 class="text-lg font-semibold text-gray-900">Pendaftaran Terbaru</h2>
            <Link :href="route('admin.verifikasi-provinsi.index')"
                  class="text-sm text-emerald-600 hover:text-emerald-700">
              Lihat Semua
            </Link>
          </div>
        </div>
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Peserta</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Wilayah</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Golongan</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tanggal</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Aksi</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-for="pendaftaran in recentPendaftaran" :key="pendaftaran.id_pendaftaran">
                <td class="px-6 py-4 whitespace-nowrap">
                  <div>
                    <div class="text-sm font-medium text-gray-900">{{ pendaftaran.peserta.nama_lengkap }}</div>
                    <div class="text-sm text-gray-500">{{ pendaftaran.nomor_pendaftaran }}</div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {{ pendaftaran.peserta.wilayah.nama_wilayah }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {{ pendaftaran.golongan.nama_golongan }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span v-if="!pendaftaran.verifikasi_pendaftaran"
                        class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
                    Menunggu Verifikasi
                  </span>
                  <span v-else-if="pendaftaran.verifikasi_pendaftaran.status_verifikasi === 'approved'"
                        class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                    Disetujui
                  </span>
                  <span v-else-if="pendaftaran.verifikasi_pendaftaran.status_verifikasi === 'rejected'"
                        class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
                    Ditolak
                  </span>
                  <span v-else
                        class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
                    Pending
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {{ new Date(pendaftaran.created_at).toLocaleDateString('id-ID') }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <Link :href="route('admin.verifikasi-provinsi.show', pendaftaran.id_pendaftaran)"
                        class="text-emerald-600 hover:text-emerald-900">
                    Verifikasi
                  </Link>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- Verification by Region -->
      <div class="bg-white rounded-lg shadow p-6">
        <h2 class="text-lg font-semibold text-gray-900 mb-4">Verifikasi per Wilayah</h2>
        <div class="space-y-4">
          <div v-for="region in verificationByRegion" :key="region.wilayah"
               class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
            <div>
              <p class="font-medium text-gray-900">{{ region.wilayah }}</p>
              <p class="text-sm text-gray-500">{{ region.verified }}/{{ region.total }} terverifikasi</p>
            </div>
            <div class="text-right">
              <p class="text-lg font-bold text-gray-900">{{ region.percentage }}%</p>
              <div class="w-24 bg-gray-200 rounded-full h-2 mt-1">
                <div class="bg-emerald-600 h-2 rounded-full" :style="{ width: region.percentage + '%' }"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import { Head, Link } from '@inertiajs/vue3'
import AppLayout from '@/layouts/AppLayout.vue'

interface Stats {
  total_pendaftaran_verified: number
  pending_verification: number
  nik_pending: number
  dokumen_pending: number
  verification_approved: number
  verification_rejected: number
}

interface Pendaftaran {
  id_pendaftaran: number
  nomor_pendaftaran: string
  created_at: string
  peserta: {
    nama_lengkap: string
    wilayah: {
      nama_wilayah: string
    }
  }
  golongan: {
    nama_golongan: string
  }
  verifikasi_pendaftaran?: {
    status_verifikasi: string
  }
}

interface RegionVerification {
  wilayah: string
  total: number
  verified: number
  pending: number
  percentage: number
}

defineProps<{
  stats: Stats
  recentPendaftaran: Pendaftaran[]
  verificationByRegion: RegionVerification[]
}>()
</script>
