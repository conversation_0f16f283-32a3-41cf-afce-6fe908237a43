import{d as c,k as f,c as h,o as r,w as d,D as g,E as y,u as s,O as x,P as w,h as u,b as i,i as z,t as m,Y as B,a as p,F as P,m as $,Z as S,e as C,n as b}from"./app-B_pmlBSQ.js";import{_ as k}from"./Heading.vue_vue_type_script_setup_true_lang-is4VBSCS.js";import{P as N,a as O,_ as V}from"./AppLogoIcon.vue_vue_type_script_setup_true_lang-wzz4PWna.js";import{l as E}from"./useForwardExpose-CO14IhkA.js";const I=c({__name:"BaseSeparator",props:{orientation:{default:"horizontal"},decorative:{type:Boolean},asChild:{type:Boolean},as:{}},setup(o){const e=o,t=["horizontal","vertical"];function l(a){return t.includes(a)}const n=f(()=>l(e.orientation)?e.orientation:"horizontal"),_=f(()=>n.value==="vertical"?e.orientation:void 0),v=f(()=>e.decorative?{role:"none"}:{"aria-orientation":_.value,role:"separator"});return(a,Y)=>(r(),h(s(N),y({as:a.as,"as-child":a.asChild,"data-orientation":n.value},v.value),{default:d(()=>[g(a.$slots,"default")]),_:3},16,["as","as-child","data-orientation"]))}}),L=c({__name:"Separator",props:{orientation:{default:"horizontal"},decorative:{type:Boolean},asChild:{type:Boolean},as:{}},setup(o){const e=o;return(t,l)=>(r(),h(I,x(w(e)),{default:d(()=>[g(t.$slots,"default")]),_:3},16))}}),R=c({__name:"Separator",props:{orientation:{default:"horizontal"},decorative:{type:Boolean,default:!0},asChild:{type:Boolean},as:{},class:{}},setup(o){const e=o,t=E(e,"class");return(l,n)=>(r(),h(s(L),y({"data-slot":"separator-root"},s(t),{class:s(O)("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px",e.class)}),null,16,["class"]))}}),T={class:"mb-0.5 text-base font-medium"},A={key:0,class:"text-sm text-muted-foreground"},K=c({__name:"HeadingSmall",props:{title:{},description:{}},setup(o){return(e,t)=>(r(),u("header",null,[i("h3",T,m(e.title),1),e.description?(r(),u("p",A,m(e.description),1)):z("",!0)]))}}),D={class:"px-4 py-6"},F={class:"flex flex-col space-y-8 md:space-y-0 lg:flex-row lg:space-y-0 lg:space-x-12"},j={class:"w-full max-w-xl lg:w-48"},H={class:"flex flex-col space-y-1 space-x-0"},M={class:"flex-1 md:max-w-2xl"},U={class:"max-w-xl space-y-12"},Q=c({__name:"Layout",setup(o){var n;const e=[{title:"Profile",href:"/settings/profile"},{title:"Password",href:"/settings/password"},{title:"Appearance",href:"/settings/appearance"}],t=B(),l=(n=t.props.ziggy)!=null&&n.location?new URL(t.props.ziggy.location).pathname:"";return(_,v)=>(r(),u("div",D,[p(k,{title:"Settings",description:"Manage your profile and account settings"}),i("div",F,[i("aside",j,[i("nav",H,[(r(),u(P,null,$(e,a=>p(s(V),{key:a.href,variant:"ghost",class:b(["w-full justify-start",{"bg-muted":s(l)===a.href}]),"as-child":""},{default:d(()=>[p(s(S),{href:a.href},{default:d(()=>[C(m(a.title),1)]),_:2},1032,["href"])]),_:2},1032,["class"])),64))])]),p(s(R),{class:"my-6 md:hidden"}),i("div",M,[i("section",U,[g(_.$slots,"default")])])])]))}});export{Q as _,K as a};
