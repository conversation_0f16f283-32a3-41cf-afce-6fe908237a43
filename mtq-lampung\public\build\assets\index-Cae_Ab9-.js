import{a as e,b as p}from"./AppLogoIcon.vue_vue_type_script_setup_true_lang-wzz4PWna.js";import{d as n,h as l,o,D as c,n as i,u as r}from"./app-B_pmlBSQ.js";const f=n({__name:"Alert",props:{class:{},variant:{}},setup(a){const s=a;return(t,d)=>(o(),l("div",{"data-slot":"alert",class:i(r(e)(r(u)({variant:t.variant}),s.class)),role:"alert"},[c(t.$slots,"default")],2))}}),m=n({__name:"AlertDescription",props:{class:{}},setup(a){const s=a;return(t,d)=>(o(),l("div",{"data-slot":"alert-description",class:i(r(e)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",s.class))},[c(t.$slots,"default")],2))}}),u=p("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});export{f as _,m as a};
