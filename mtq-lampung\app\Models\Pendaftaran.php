<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

class Pendaftaran extends Model
{
    protected $table = 'pendaftaran';
    protected $primaryKey = 'id_pendaftaran';

    protected $fillable = [
        'id_peserta',
        'id_golongan',
        'id_mimbar',
        'nomor_pendaftaran',
        'nomor_peserta',
        'tahun_pendaftaran',
        'status_pendaftaran',
        'tanggal_daftar',
        'registered_by',
        'verified_by',
        'verified_at',
        'approved_by',
        'approved_at',
        'catatan_verifikasi',
        'catatan_approval',
        'keterangan'
    ];

    protected $casts = [
        'tahun_pendaftaran' => 'integer',
        'status_pendaftaran' => 'string',
        'tanggal_daftar' => 'datetime',
        'verified_at' => 'datetime',
        'approved_at' => 'datetime'
    ];

    // Relationships
    public function peserta(): BelongsTo
    {
        return $this->belongsTo(Peserta::class, 'id_peserta', 'id_peserta');
    }

    public function golongan(): BelongsTo
    {
        return $this->belongsTo(Golongan::class, 'id_golongan', 'id_golongan');
    }

    public function mimbar(): BelongsTo
    {
        return $this->belongsTo(Mimbar::class, 'id_mimbar', 'id_mimbar');
    }

    public function verifiedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'verified_by', 'id_user');
    }

    public function approvedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by', 'id_user');
    }

    public function registeredBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'registered_by', 'id_user');
    }

    public function dokumenPeserta(): HasMany
    {
        return $this->hasMany(DokumenPeserta::class, 'id_pendaftaran', 'id_pendaftaran');
    }

    public function pembayaran(): HasOne
    {
        return $this->hasOne(Pembayaran::class, 'id_pendaftaran', 'id_pendaftaran');
    }

    public function nilaiPeserta(): HasMany
    {
        return $this->hasMany(NilaiPeserta::class, 'id_pendaftaran', 'id_pendaftaran');
    }

    // Scopes
    public function scopeByStatus($query, $status)
    {
        return $query->where('status_pendaftaran', $status);
    }

    public function scopeByTahun($query, $tahun)
    {
        return $query->where('tahun_pendaftaran', $tahun);
    }

    public function scopeByGolongan($query, $idGolongan)
    {
        return $query->where('id_golongan', $idGolongan);
    }
}
