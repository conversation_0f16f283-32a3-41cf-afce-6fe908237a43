<template>
  <AppLayout :breadcrumbs="breadcrumbItems">
    <Head title="Edit Cabang Lomba" />
    <Heading :title="`Edit Cabang Lomba: ${cabangLomba.nama_cabang}`" />

    <div class="max-w-2xl mx-auto">
      <Card>
        <CardHeader>
          <CardTitle>Edit Informasi Cabang Lomba</CardTitle>
          <CardDescription>
            Perbarui informasi cabang lomba di bawah ini
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form @submit.prevent="submit" class="space-y-6">
            <!-- Basic Information -->
            <div class="space-y-4">
              <h3 class="text-lg font-medium">Informasi Dasar</h3>
              
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label for="kode_cabang">Kode Cabang *</Label>
                  <Input
                    id="kode_cabang"
                    v-model="form.kode_cabang"
                    type="text"
                    required
                    placeholder="Contoh: TIL, TAH, FAH"
                    :class="{ 'border-red-500': form.errors.kode_cabang }"
                  />
                  <p v-if="form.errors.kode_cabang" class="text-sm text-red-600 mt-1">
                    {{ form.errors.kode_cabang }}
                  </p>
                </div>

                <div>
                  <Label for="status">Status *</Label>
                  <Select v-model="form.status" required>
                    <SelectTrigger :class="{ 'border-red-500': form.errors.status }">
                      <SelectValue placeholder="Pilih Status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="aktif">Aktif</SelectItem>
                      <SelectItem value="non_aktif">Non Aktif</SelectItem>
                    </SelectContent>
                  </Select>
                  <p v-if="form.errors.status" class="text-sm text-red-600 mt-1">
                    {{ form.errors.status }}
                  </p>
                </div>
              </div>

              <div>
                <Label for="nama_cabang">Nama Cabang Lomba *</Label>
                <Input
                  id="nama_cabang"
                  v-model="form.nama_cabang"
                  type="text"
                  required
                  placeholder="Contoh: Tilawatil Quran, Tahfidzul Quran"
                  :class="{ 'border-red-500': form.errors.nama_cabang }"
                />
                <p v-if="form.errors.nama_cabang" class="text-sm text-red-600 mt-1">
                  {{ form.errors.nama_cabang }}
                </p>
              </div>

              <div>
                <Label for="deskripsi">Deskripsi</Label>
                <textarea
                  id="deskripsi"
                  v-model="form.deskripsi"
                  rows="4"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Deskripsi cabang lomba..."
                  :class="{ 'border-red-500': form.errors.deskripsi }"
                ></textarea>
                <p v-if="form.errors.deskripsi" class="text-sm text-red-600 mt-1">
                  {{ form.errors.deskripsi }}
                </p>
              </div>
            </div>

            <!-- Current Info -->
            <div class="bg-gray-50 p-4 rounded-lg">
              <h4 class="font-medium text-gray-900 mb-2">Informasi Saat Ini</h4>
              <div class="text-sm text-gray-600 space-y-1">
                <p><strong>Dibuat:</strong> {{ formatDate(cabangLomba.created_at) }}</p>
                <p><strong>Diperbarui:</strong> {{ formatDate(cabangLomba.updated_at) }}</p>
                <p><strong>Jumlah Golongan:</strong> {{ cabangLomba.golongan?.length || 0 }} golongan</p>
              </div>
            </div>

            <!-- Warning for changes -->
            <div v-if="hasGolongan" class="bg-yellow-50 border border-yellow-200 p-4 rounded-lg">
              <div class="flex">
                <Icon name="alert-triangle" class="w-5 h-5 text-yellow-600 mr-2 mt-0.5" />
                <div>
                  <h4 class="font-medium text-yellow-800">Perhatian</h4>
                  <p class="text-sm text-yellow-700 mt-1">
                    Cabang lomba ini memiliki {{ cabangLomba.golongan?.length || 0 }} golongan. 
                    Perubahan status menjadi non-aktif akan mempengaruhi golongan yang terkait.
                  </p>
                </div>
              </div>
            </div>

            <!-- Actions -->
            <div class="flex justify-end space-x-4 pt-6 border-t">
              <Button
                type="button"
                variant="outline"
                @click="$inertia.visit(route('admin.cabang-lomba.index'))"
              >
                Batal
              </Button>
              <Button type="submit" :disabled="form.processing">
                <Icon v-if="form.processing" name="loader-2" class="w-4 h-4 mr-2 animate-spin" />
                Simpan Perubahan
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Head, useForm } from '@inertiajs/vue3'
import AppLayout from '@/layouts/AppLayout.vue'
import Heading from '@/components/Heading.vue'
import Button from '@/components/ui/button/Button.vue'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import Icon from '@/components/Icon.vue'
import { type BreadcrumbItem } from '@/types'

interface CabangLomba {
  id_cabang: number
  kode_cabang: string
  nama_cabang: string
  deskripsi?: string
  status: string
  created_at: string
  updated_at: string
  golongan?: any[]
}

const props = defineProps<{
  cabangLomba: CabangLomba
}>()

const breadcrumbItems: BreadcrumbItem[] = [
  { title: 'Dashboard', href: '/admin/dashboard' },
  { title: 'Manajemen Cabang Lomba', href: '/admin/cabang-lomba' },
  { title: 'Edit Cabang Lomba', href: `/admin/cabang-lomba/${props.cabangLomba.id_cabang}/edit` }
]

const form = useForm({
  kode_cabang: props.cabangLomba.kode_cabang,
  nama_cabang: props.cabangLomba.nama_cabang,
  deskripsi: props.cabangLomba.deskripsi || '',
  status: props.cabangLomba.status
})

const hasGolongan = computed(() => {
  return props.cabangLomba.golongan && props.cabangLomba.golongan.length > 0
})

const submit = () => {
  form.put(route('admin.cabang-lomba.update', props.cabangLomba.id_cabang), {
    onSuccess: () => {
      // Redirect handled by controller
    }
  })
}

const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString('id-ID', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}
</script>
