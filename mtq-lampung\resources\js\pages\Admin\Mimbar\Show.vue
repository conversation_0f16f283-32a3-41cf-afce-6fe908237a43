<template>
  <AppLayout :breadcrumbs="breadcrumbItems">
    <Head :title="`Detail Mimbar: ${mimbar.nama_mimbar}`" />
    <Heading :title="`Detail Mimbar: ${mimbar.nama_mimbar}`" />

    <div class="max-w-4xl mx-auto space-y-6">
      <!-- Mimbar Information -->
      <Card>
        <CardHeader>
          <div class="flex justify-between items-start">
            <div>
              <CardTitle class="flex items-center gap-3">
                <div class="w-12 h-12 bg-indigo-100 rounded-full flex items-center justify-center">
                  <Icon name="building" class="w-6 h-6 text-indigo-600" />
                </div>
                {{ mimbar.nama_mimbar }}
              </CardTitle>
              <CardDescription>
                {{ mimbar.kode_mimbar }} • Kapasitas: {{ mimbar.kapasitas }} orang
              </CardDescription>
            </div>
            <div class="flex gap-2">
              <Badge :variant="getStatusVariant(mimbar.status)">
                {{ getStatusLabel(mimbar.status) }}
              </Badge>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="space-y-4">
              <div>
                <Label class="text-sm font-medium text-gray-500">Kode Mimbar</Label>
                <p class="text-sm">{{ mimbar.kode_mimbar }}</p>
              </div>
              <div>
                <Label class="text-sm font-medium text-gray-500">Nama Mimbar</Label>
                <p class="text-sm">{{ mimbar.nama_mimbar }}</p>
              </div>
              <div>
                <Label class="text-sm font-medium text-gray-500">Kapasitas</Label>
                <p class="text-sm">{{ mimbar.kapasitas }} orang</p>
              </div>
              <div>
                <Label class="text-sm font-medium text-gray-500">Status</Label>
                <p class="text-sm">{{ getStatusLabel(mimbar.status) }}</p>
              </div>
            </div>
            <div class="space-y-4">
              <div>
                <Label class="text-sm font-medium text-gray-500">Keterangan</Label>
                <p class="text-sm">{{ mimbar.keterangan || '-' }}</p>
              </div>
              <div>
                <Label class="text-sm font-medium text-gray-500">Dibuat</Label>
                <p class="text-sm">{{ formatDate(mimbar.created_at) }}</p>
              </div>
              <div>
                <Label class="text-sm font-medium text-gray-500">Diperbarui</Label>
                <p class="text-sm">{{ formatDate(mimbar.updated_at) }}</p>
              </div>
              <div>
                <Label class="text-sm font-medium text-gray-500">Penggunaan</Label>
                <div class="flex items-center gap-2">
                  <div class="flex-1 bg-gray-200 rounded-full h-2">
                    <div 
                      class="bg-blue-600 h-2 rounded-full" 
                      :style="{ width: `${getUsagePercentage()}%` }"
                    ></div>
                  </div>
                  <span class="text-sm">{{ getUsagePercentage() }}%</span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- Statistics -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent class="p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <Icon name="users" class="h-8 w-8 text-blue-600" />
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">Total Pendaftaran</p>
                <p class="text-2xl font-semibold text-gray-900">{{ mimbar.pendaftaran?.length || 0 }}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent class="p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <Icon name="check-circle" class="h-8 w-8 text-green-600" />
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">Disetujui</p>
                <p class="text-2xl font-semibold text-gray-900">
                  {{ getApprovedCount() }}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent class="p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <Icon name="clock" class="h-8 w-8 text-yellow-600" />
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">Menunggu</p>
                <p class="text-2xl font-semibold text-gray-900">
                  {{ getPendingCount() }}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent class="p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <Icon name="percent" class="h-8 w-8 text-purple-600" />
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">Sisa Kapasitas</p>
                <p class="text-2xl font-semibold text-gray-900">
                  {{ getRemainingCapacity() }}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <!-- Pendaftaran List -->
      <Card>
        <CardHeader>
          <div class="flex justify-between items-center">
            <div>
              <CardTitle>Daftar Pendaftaran</CardTitle>
              <CardDescription>
                Peserta yang menggunakan mimbar {{ mimbar.nama_mimbar }}
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div v-if="!mimbar.pendaftaran || mimbar.pendaftaran.length === 0" class="text-center py-8 text-gray-500">
            <Icon name="building" class="w-12 h-12 mx-auto mb-4 text-gray-300" />
            <p>Belum ada pendaftaran yang menggunakan mimbar ini</p>
          </div>
          <div v-else class="space-y-4">
            <div
              v-for="pendaftaran in mimbar.pendaftaran"
              :key="pendaftaran.id_pendaftaran"
              class="border rounded-lg p-4 hover:bg-gray-50"
            >
              <div class="flex justify-between items-start">
                <div class="flex-1">
                  <div class="flex items-center gap-3 mb-2">
                    <h4 class="font-medium">{{ pendaftaran.peserta?.nama_lengkap }}</h4>
                    <Badge :variant="getStatusVariant(pendaftaran.status_pendaftaran)">
                      {{ pendaftaran.status_pendaftaran }}
                    </Badge>
                  </div>
                  <p class="text-sm text-gray-500 mb-2">{{ pendaftaran.nomor_pendaftaran }}</p>
                  <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <span class="text-gray-500">Golongan:</span>
                      <p class="font-medium">{{ pendaftaran.golongan?.nama_golongan }}</p>
                    </div>
                    <div>
                      <span class="text-gray-500">Cabang:</span>
                      <p class="font-medium">{{ pendaftaran.golongan?.cabang_lomba?.nama_cabang }}</p>
                    </div>
                    <div>
                      <span class="text-gray-500">Wilayah:</span>
                      <p class="font-medium">{{ pendaftaran.peserta?.wilayah?.nama_wilayah }}</p>
                    </div>
                    <div>
                      <span class="text-gray-500">Tgl Daftar:</span>
                      <p class="font-medium">{{ formatDate(pendaftaran.created_at) }}</p>
                    </div>
                  </div>
                </div>
                <div class="flex gap-2 ml-4">
                  <Button
                    variant="ghost"
                    size="sm"
                    @click="$inertia.visit(route('admin.pendaftaran.show', pendaftaran.id_pendaftaran))"
                  >
                    <Icon name="eye" class="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- Actions -->
      <div class="flex justify-between">
        <Button
          variant="outline"
          @click="$inertia.visit(route('admin.mimbar.index'))"
        >
          <Icon name="arrow-left" class="w-4 h-4 mr-2" />
          Kembali
        </Button>
        <div class="flex gap-2">
          <Button
            variant="outline"
            @click="toggleStatus"
          >
            <Icon :name="mimbar.status === 'aktif' ? 'pause' : 'play'" class="w-4 h-4 mr-2" />
            {{ mimbar.status === 'aktif' ? 'Non-aktifkan' : 'Aktifkan' }}
          </Button>
          <Button @click="$inertia.visit(route('admin.mimbar.edit', mimbar.id_mimbar))">
            <Icon name="edit" class="w-4 h-4 mr-2" />
            Edit Mimbar
          </Button>
        </div>
      </div>
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import { Head, router } from '@inertiajs/vue3'
import AppLayout from '@/layouts/AppLayout.vue'
import Heading from '@/components/Heading.vue'
import Button from '@/components/ui/button/Button.vue'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Label } from '@/components/ui/label'
import Icon from '@/components/Icon.vue'
import { type BreadcrumbItem } from '@/types'

interface Peserta {
  nama_lengkap: string
  wilayah?: {
    nama_wilayah: string
  }
}

interface Golongan {
  nama_golongan: string
  cabang_lomba?: {
    nama_cabang: string
  }
}

interface Pendaftaran {
  id_pendaftaran: number
  nomor_pendaftaran: string
  status_pendaftaran: string
  created_at: string
  peserta?: Peserta
  golongan?: Golongan
}

interface Mimbar {
  id_mimbar: number
  kode_mimbar: string
  nama_mimbar: string
  keterangan?: string
  kapasitas: number
  status: string
  created_at: string
  updated_at: string
  pendaftaran?: Pendaftaran[]
}

const props = defineProps<{
  mimbar: Mimbar
}>()

const breadcrumbItems: BreadcrumbItem[] = [
  { title: 'Dashboard', href: '/admin/dashboard' },
  { title: 'Manajemen Mimbar', href: '/admin/mimbar' },
  { title: 'Detail Mimbar', href: `/admin/mimbar/${props.mimbar.id_mimbar}` }
]

const toggleStatus = () => {
  router.post(route('admin.mimbar.toggle-status', props.mimbar.id_mimbar), {}, {
    preserveScroll: true
  })
}

const getStatusVariant = (status: string) => {
  const variants: Record<string, string> = {
    aktif: 'default',
    non_aktif: 'secondary',
    approved: 'default',
    pending: 'secondary',
    draft: 'outline'
  }
  return variants[status] || 'secondary'
}

const getStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    aktif: 'Aktif',
    non_aktif: 'Non Aktif',
    approved: 'Disetujui',
    pending: 'Menunggu',
    draft: 'Draft'
  }
  return labels[status] || status
}

const getUsagePercentage = () => {
  if (props.mimbar.kapasitas === 0) return 0
  const usage = props.mimbar.pendaftaran?.length || 0
  return Math.round((usage / props.mimbar.kapasitas) * 100)
}

const getApprovedCount = () => {
  if (!props.mimbar.pendaftaran) return 0
  return props.mimbar.pendaftaran.filter(p => p.status_pendaftaran === 'approved').length
}

const getPendingCount = () => {
  if (!props.mimbar.pendaftaran) return 0
  return props.mimbar.pendaftaran.filter(p => p.status_pendaftaran === 'pending' || p.status_pendaftaran === 'draft').length
}

const getRemainingCapacity = () => {
  const used = props.mimbar.pendaftaran?.length || 0
  return props.mimbar.kapasitas - used
}

const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString('id-ID', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}
</script>
