import{d as j,x as q,c as v,o as _,w as t,b as s,a as l,e as n,f as U,u as r,j as g,q as f,v as M,h as V,F as P,m as $,t as k,i as h}from"./app-B_pmlBSQ.js";import{_ as I}from"./AppLayout.vue_vue_type_script_setup_true_lang-BRf8lUiM.js";import{_ as N}from"./Heading.vue_vue_type_script_setup_true_lang-is4VBSCS.js";import{_ as y}from"./AppLogoIcon.vue_vue_type_script_setup_true_lang-wzz4PWna.js";import{_ as D,a as K}from"./CardContent.vue_vue_type_script_setup_true_lang-ClvLNBXC.js";import{_ as L,a as T}from"./CardTitle.vue_vue_type_script_setup_true_lang-5HO6oSoB.js";import{_ as B}from"./CardDescription.vue_vue_type_script_setup_true_lang-DboywBIK.js";import{_ as u}from"./Input.vue_vue_type_script_setup_true_lang-DB1-RsTn.js";import{_ as d}from"./Label.vue_vue_type_script_setup_true_lang-rj_fhE7R.js";import{_ as m}from"./InputError.vue_vue_type_script_setup_true_lang-A-vdzbq9.js";import{_ as b}from"./Icon.vue_vue_type_script_setup_true_lang-BuYzjckr.js";import"./useForwardExpose-CO14IhkA.js";import"./RovingFocusGroup-lsWyU4xZ.js";import"./check-DtmHjdxf.js";import"./loader-circle-6d8QrWFr.js";import"./sun-CHIV9RJ8.js";const E={class:"flex items-center space-x-4"},S={class:"max-w-4xl mx-auto"},A={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},C={class:"space-y-4"},F={class:"space-y-4"},z={class:"grid grid-cols-2 gap-4"},J={class:"space-y-4"},W={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},G=["value"],H={class:"space-y-4"},O={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},Q={class:"space-y-4"},R={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},X={class:"flex justify-end space-x-4 pt-6 border-t"},_e=j({__name:"Edit",props:{peserta:{},wilayah:{}},setup(x){const o=x,a=q({username:o.peserta.user.username,email:o.peserta.user.email,nik:o.peserta.nik,nama_lengkap:o.peserta.nama_lengkap,tempat_lahir:o.peserta.tempat_lahir,tanggal_lahir:o.peserta.tanggal_lahir,jenis_kelamin:o.peserta.jenis_kelamin,alamat:o.peserta.alamat,id_wilayah:o.peserta.id_wilayah,no_telepon:o.peserta.no_telepon||"",nama_ayah:o.peserta.nama_ayah||"",nama_ibu:o.peserta.nama_ibu||"",pekerjaan:o.peserta.pekerjaan||"",instansi_asal:o.peserta.instansi_asal||"",status_peserta:o.peserta.status_peserta});function w(){a.put(route("admin.peserta.update",o.peserta.id_peserta),{onSuccess:()=>{}})}return(p,e)=>(_(),v(I,null,{header:t(()=>[s("div",E,[l(y,{as:"link",href:p.route("admin.peserta.index"),variant:"ghost",size:"sm"},{default:t(()=>[l(b,{name:"arrow-left",class:"w-4 h-4 mr-2"}),e[15]||(e[15]=n(" Kembali "))]),_:1,__:[15]},8,["href"]),l(N,null,{default:t(()=>[n("Edit Peserta: "+k(p.peserta.nama_lengkap),1)]),_:1})])]),default:t(()=>[s("div",S,[l(D,null,{default:t(()=>[l(L,null,{default:t(()=>[l(T,null,{default:t(()=>e[16]||(e[16]=[n("Edit Informasi Peserta")])),_:1,__:[16]}),l(B,null,{default:t(()=>e[17]||(e[17]=[n(" Perbarui informasi peserta di bawah ini ")])),_:1,__:[17]})]),_:1}),l(K,null,{default:t(()=>[s("form",{onSubmit:U(w,["prevent"]),class:"space-y-6"},[s("div",A,[s("div",C,[e[22]||(e[22]=s("h3",{class:"text-lg font-medium text-gray-900"},"Informasi Akun",-1)),s("div",null,[l(d,{for:"username",required:""},{default:t(()=>e[18]||(e[18]=[n("Username")])),_:1,__:[18]}),l(u,{id:"username",modelValue:r(a).username,"onUpdate:modelValue":e[0]||(e[0]=i=>r(a).username=i),type:"text",error:r(a).errors.username,placeholder:"Masukkan username",class:"mt-1",required:""},null,8,["modelValue","error"]),l(m,{message:r(a).errors.username},null,8,["message"])]),s("div",null,[l(d,{for:"email",required:""},{default:t(()=>e[19]||(e[19]=[n("Email")])),_:1,__:[19]}),l(u,{id:"email",modelValue:r(a).email,"onUpdate:modelValue":e[1]||(e[1]=i=>r(a).email=i),type:"email",error:r(a).errors.email,placeholder:"Masukkan email",class:"mt-1",required:""},null,8,["modelValue","error"]),l(m,{message:r(a).errors.email},null,8,["message"])]),s("div",null,[l(d,{for:"status_peserta",required:""},{default:t(()=>e[20]||(e[20]=[n("Status Peserta")])),_:1,__:[20]}),g(s("select",{id:"status_peserta","onUpdate:modelValue":e[2]||(e[2]=i=>r(a).status_peserta=i),class:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500",required:""},e[21]||(e[21]=[s("option",{value:"draft"},"Draft",-1),s("option",{value:"submitted"},"Disubmit",-1),s("option",{value:"verified"},"Diverifikasi",-1),s("option",{value:"approved"},"Disetujui",-1),s("option",{value:"rejected"},"Ditolak",-1)]),512),[[f,r(a).status_peserta]]),l(m,{message:r(a).errors.status_peserta},null,8,["message"])])]),s("div",F,[e[29]||(e[29]=s("h3",{class:"text-lg font-medium text-gray-900"},"Informasi Pribadi",-1)),s("div",null,[l(d,{for:"nik",required:""},{default:t(()=>e[23]||(e[23]=[n("NIK")])),_:1,__:[23]}),l(u,{id:"nik",modelValue:r(a).nik,"onUpdate:modelValue":e[3]||(e[3]=i=>r(a).nik=i),type:"text",error:r(a).errors.nik,placeholder:"Masukkan NIK (16 digit)",maxlength:"16",class:"mt-1",required:""},null,8,["modelValue","error"]),l(m,{message:r(a).errors.nik},null,8,["message"])]),s("div",null,[l(d,{for:"nama_lengkap",required:""},{default:t(()=>e[24]||(e[24]=[n("Nama Lengkap")])),_:1,__:[24]}),l(u,{id:"nama_lengkap",modelValue:r(a).nama_lengkap,"onUpdate:modelValue":e[4]||(e[4]=i=>r(a).nama_lengkap=i),type:"text",error:r(a).errors.nama_lengkap,placeholder:"Masukkan nama lengkap",class:"mt-1",required:""},null,8,["modelValue","error"]),l(m,{message:r(a).errors.nama_lengkap},null,8,["message"])]),s("div",z,[s("div",null,[l(d,{for:"tempat_lahir",required:""},{default:t(()=>e[25]||(e[25]=[n("Tempat Lahir")])),_:1,__:[25]}),l(u,{id:"tempat_lahir",modelValue:r(a).tempat_lahir,"onUpdate:modelValue":e[5]||(e[5]=i=>r(a).tempat_lahir=i),type:"text",error:r(a).errors.tempat_lahir,placeholder:"Kota lahir",class:"mt-1",required:""},null,8,["modelValue","error"]),l(m,{message:r(a).errors.tempat_lahir},null,8,["message"])]),s("div",null,[l(d,{for:"tanggal_lahir",required:""},{default:t(()=>e[26]||(e[26]=[n("Tanggal Lahir")])),_:1,__:[26]}),l(u,{id:"tanggal_lahir",modelValue:r(a).tanggal_lahir,"onUpdate:modelValue":e[6]||(e[6]=i=>r(a).tanggal_lahir=i),type:"date",error:r(a).errors.tanggal_lahir,class:"mt-1",required:""},null,8,["modelValue","error"]),l(m,{message:r(a).errors.tanggal_lahir},null,8,["message"])])]),s("div",null,[l(d,{for:"jenis_kelamin",required:""},{default:t(()=>e[27]||(e[27]=[n("Jenis Kelamin")])),_:1,__:[27]}),g(s("select",{id:"jenis_kelamin","onUpdate:modelValue":e[7]||(e[7]=i=>r(a).jenis_kelamin=i),class:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500",required:""},e[28]||(e[28]=[s("option",{value:""},"Pilih jenis kelamin",-1),s("option",{value:"L"},"Laki-laki",-1),s("option",{value:"P"},"Perempuan",-1)]),512),[[f,r(a).jenis_kelamin]]),l(m,{message:r(a).errors.jenis_kelamin},null,8,["message"])])])]),s("div",J,[e[34]||(e[34]=s("h3",{class:"text-lg font-medium text-gray-900"},"Informasi Kontak",-1)),s("div",null,[l(d,{for:"alamat",required:""},{default:t(()=>e[30]||(e[30]=[n("Alamat")])),_:1,__:[30]}),g(s("textarea",{id:"alamat","onUpdate:modelValue":e[8]||(e[8]=i=>r(a).alamat=i),rows:"3",class:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500",placeholder:"Masukkan alamat lengkap",required:""},null,512),[[M,r(a).alamat]]),l(m,{message:r(a).errors.alamat},null,8,["message"])]),s("div",W,[s("div",null,[l(d,{for:"id_wilayah",required:""},{default:t(()=>e[31]||(e[31]=[n("Wilayah")])),_:1,__:[31]}),g(s("select",{id:"id_wilayah","onUpdate:modelValue":e[9]||(e[9]=i=>r(a).id_wilayah=i),class:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500",required:""},[e[32]||(e[32]=s("option",{value:""},"Pilih wilayah",-1)),(_(!0),V(P,null,$(p.wilayah,i=>(_(),V("option",{key:i.id_wilayah,value:i.id_wilayah},k(i.nama_wilayah),9,G))),128))],512),[[f,r(a).id_wilayah]]),l(m,{message:r(a).errors.id_wilayah},null,8,["message"])]),s("div",null,[l(d,{for:"no_telepon"},{default:t(()=>e[33]||(e[33]=[n("No. Telepon")])),_:1,__:[33]}),l(u,{id:"no_telepon",modelValue:r(a).no_telepon,"onUpdate:modelValue":e[10]||(e[10]=i=>r(a).no_telepon=i),type:"tel",error:r(a).errors.no_telepon,placeholder:"Masukkan nomor telepon",class:"mt-1"},null,8,["modelValue","error"]),l(m,{message:r(a).errors.no_telepon},null,8,["message"])])])]),s("div",H,[e[37]||(e[37]=s("h3",{class:"text-lg font-medium text-gray-900"},"Informasi Keluarga",-1)),s("div",O,[s("div",null,[l(d,{for:"nama_ayah"},{default:t(()=>e[35]||(e[35]=[n("Nama Ayah")])),_:1,__:[35]}),l(u,{id:"nama_ayah",modelValue:r(a).nama_ayah,"onUpdate:modelValue":e[11]||(e[11]=i=>r(a).nama_ayah=i),type:"text",error:r(a).errors.nama_ayah,placeholder:"Masukkan nama ayah",class:"mt-1"},null,8,["modelValue","error"]),l(m,{message:r(a).errors.nama_ayah},null,8,["message"])]),s("div",null,[l(d,{for:"nama_ibu"},{default:t(()=>e[36]||(e[36]=[n("Nama Ibu")])),_:1,__:[36]}),l(u,{id:"nama_ibu",modelValue:r(a).nama_ibu,"onUpdate:modelValue":e[12]||(e[12]=i=>r(a).nama_ibu=i),type:"text",error:r(a).errors.nama_ibu,placeholder:"Masukkan nama ibu",class:"mt-1"},null,8,["modelValue","error"]),l(m,{message:r(a).errors.nama_ibu},null,8,["message"])])])]),s("div",Q,[e[40]||(e[40]=s("h3",{class:"text-lg font-medium text-gray-900"},"Informasi Tambahan",-1)),s("div",R,[s("div",null,[l(d,{for:"pekerjaan"},{default:t(()=>e[38]||(e[38]=[n("Pekerjaan")])),_:1,__:[38]}),l(u,{id:"pekerjaan",modelValue:r(a).pekerjaan,"onUpdate:modelValue":e[13]||(e[13]=i=>r(a).pekerjaan=i),type:"text",error:r(a).errors.pekerjaan,placeholder:"Masukkan pekerjaan",class:"mt-1"},null,8,["modelValue","error"]),l(m,{message:r(a).errors.pekerjaan},null,8,["message"])]),s("div",null,[l(d,{for:"instansi_asal"},{default:t(()=>e[39]||(e[39]=[n("Instansi Asal")])),_:1,__:[39]}),l(u,{id:"instansi_asal",modelValue:r(a).instansi_asal,"onUpdate:modelValue":e[14]||(e[14]=i=>r(a).instansi_asal=i),type:"text",error:r(a).errors.instansi_asal,placeholder:"Masukkan instansi asal",class:"mt-1"},null,8,["modelValue","error"]),l(m,{message:r(a).errors.instansi_asal},null,8,["message"])])])]),s("div",X,[l(y,{as:"link",href:p.route("admin.peserta.index"),variant:"outline"},{default:t(()=>e[41]||(e[41]=[n(" Batal ")])),_:1,__:[41]},8,["href"]),l(y,{type:"submit",variant:"primary",disabled:r(a).processing},{default:t(()=>[r(a).processing?(_(),v(b,{key:0,name:"loader-2",class:"w-4 h-4 mr-2 animate-spin"})):h("",!0),n(" "+k(r(a).processing?"Menyimpan...":"Perbarui Peserta"),1)]),_:1},8,["disabled"])])],32)]),_:1})]),_:1})])]),_:1}))}});export{_e as default};
