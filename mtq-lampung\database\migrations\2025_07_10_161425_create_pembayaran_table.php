<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('pembayaran', function (Blueprint $table) {
            $table->id('id_pembayaran');
            $table->unsignedBigInteger('id_pendaftaran');
            $table->string('nomor_transaksi', 50)->unique();
            $table->decimal('jumlah_bayar', 10, 2);
            $table->enum('metode_pembayaran', [
                'transfer_manual', 'va_bni', 'va_bri', 'va_mandiri', 'ovo', 'gopay', 'dana'
            ]);
            $table->enum('status_pembayaran', ['pending', 'paid', 'failed', 'expired'])->default('pending');
            $table->timestamp('tanggal_bayar')->nullable();
            $table->string('bukti_pembayaran', 255)->nullable();
            $table->string('reference_number', 100)->nullable();
            $table->unsignedBigInteger('verified_by')->nullable();
            $table->timestamp('verified_at')->nullable();
            $table->text('catatan')->nullable();
            $table->timestamps();

            // Foreign keys
            $table->foreign('id_pendaftaran')->references('id_pendaftaran')->on('pendaftaran')->onDelete('cascade');
            $table->foreign('verified_by')->references('id_user')->on('users')->onDelete('set null');

            // Indexes
            $table->index(['status_pembayaran']);
            $table->index(['nomor_transaksi']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('pembayaran');
    }
};
