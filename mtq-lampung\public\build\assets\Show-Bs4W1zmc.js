import{d as T,c as W,o as p,w as i,a as e,b as t,u as n,g as z,e as m,t as l,p as E,h as g,F,m as I,W as G}from"./app-B_pmlBSQ.js";import{_ as R}from"./AppLayout.vue_vue_type_script_setup_true_lang-BRf8lUiM.js";import{_ as U}from"./Heading.vue_vue_type_script_setup_true_lang-is4VBSCS.js";import{_ as c}from"./AppLogoIcon.vue_vue_type_script_setup_true_lang-wzz4PWna.js";import{_ as f,a as _}from"./CardContent.vue_vue_type_script_setup_true_lang-ClvLNBXC.js";import{_ as M}from"./CardDescription.vue_vue_type_script_setup_true_lang-DboywBIK.js";import{_ as S,a as j}from"./CardTitle.vue_vue_type_script_setup_true_lang-5HO6oSoB.js";import{_ as K}from"./index-CMGr3-bt.js";import{_ as u}from"./Label.vue_vue_type_script_setup_true_lang-rj_fhE7R.js";import{_ as d}from"./Icon.vue_vue_type_script_setup_true_lang-BuYzjckr.js";import"./useForwardExpose-CO14IhkA.js";import"./RovingFocusGroup-lsWyU4xZ.js";import"./check-DtmHjdxf.js";import"./loader-circle-6d8QrWFr.js";import"./sun-CHIV9RJ8.js";const q={class:"max-w-4xl mx-auto space-y-6"},H={class:"flex justify-between items-start"},J={class:"w-12 h-12 bg-indigo-100 rounded-full flex items-center justify-center"},O={class:"flex gap-2"},Q={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},X={class:"space-y-4"},Y={class:"text-sm"},Z={class:"text-sm"},tt={class:"text-sm"},at={class:"text-sm"},st={class:"space-y-4"},et={class:"text-sm"},it={class:"text-sm"},nt={class:"text-sm"},lt={class:"flex items-center gap-2"},mt={class:"flex-1 bg-gray-200 rounded-full h-2"},rt={class:"text-sm"},ot={class:"grid grid-cols-1 md:grid-cols-4 gap-6"},dt={class:"flex items-center"},ut={class:"flex-shrink-0"},ft={class:"ml-4"},_t={class:"text-2xl font-semibold text-gray-900"},pt={class:"flex items-center"},gt={class:"flex-shrink-0"},ct={class:"ml-4"},bt={class:"text-2xl font-semibold text-gray-900"},vt={class:"flex items-center"},xt={class:"flex-shrink-0"},yt={class:"ml-4"},ht={class:"text-2xl font-semibold text-gray-900"},kt={class:"flex items-center"},wt={class:"flex-shrink-0"},$t={class:"ml-4"},Dt={class:"text-2xl font-semibold text-gray-900"},Ct={class:"flex justify-between items-center"},Mt={key:0,class:"text-center py-8 text-gray-500"},St={key:1,class:"space-y-4"},jt={class:"flex justify-between items-start"},Kt={class:"flex-1"},Nt={class:"flex items-center gap-3 mb-2"},Pt={class:"font-medium"},Bt={class:"text-sm text-gray-500 mb-2"},At={class:"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm"},Vt={class:"font-medium"},Lt={class:"font-medium"},Tt={class:"font-medium"},Wt={class:"font-medium"},zt={class:"flex gap-2 ml-4"},Et={class:"flex justify-between"},Ft={class:"flex gap-2"},ia=T({__name:"Show",props:{mimbar:{}},setup(N){const o=N,P=[{title:"Dashboard",href:"/admin/dashboard"},{title:"Manajemen Mimbar",href:"/admin/mimbar"},{title:"Detail Mimbar",href:`/admin/mimbar/${o.mimbar.id_mimbar}`}],B=()=>{G.post(route("admin.mimbar.toggle-status",o.mimbar.id_mimbar),{},{preserveScroll:!0})},v=s=>({aktif:"default",non_aktif:"secondary",approved:"default",pending:"secondary",draft:"outline"})[s]||"secondary",x=s=>({aktif:"Aktif",non_aktif:"Non Aktif",approved:"Disetujui",pending:"Menunggu",draft:"Draft"})[s]||s,y=()=>{var a;if(o.mimbar.kapasitas===0)return 0;const s=((a=o.mimbar.pendaftaran)==null?void 0:a.length)||0;return Math.round(s/o.mimbar.kapasitas*100)},A=()=>o.mimbar.pendaftaran?o.mimbar.pendaftaran.filter(s=>s.status_pendaftaran==="approved").length:0,V=()=>o.mimbar.pendaftaran?o.mimbar.pendaftaran.filter(s=>s.status_pendaftaran==="pending"||s.status_pendaftaran==="draft").length:0,L=()=>{var a;const s=((a=o.mimbar.pendaftaran)==null?void 0:a.length)||0;return o.mimbar.kapasitas-s},b=s=>new Date(s).toLocaleDateString("id-ID",{year:"numeric",month:"short",day:"numeric"});return(s,a)=>(p(),W(R,{breadcrumbs:P},{default:i(()=>[e(n(z),{title:`Detail Mimbar: ${s.mimbar.nama_mimbar}`},null,8,["title"]),e(U,{title:`Detail Mimbar: ${s.mimbar.nama_mimbar}`},null,8,["title"]),t("div",q,[e(n(f),null,{default:i(()=>[e(n(S),null,{default:i(()=>[t("div",H,[t("div",null,[e(n(j),{class:"flex items-center gap-3"},{default:i(()=>[t("div",J,[e(d,{name:"building",class:"w-6 h-6 text-indigo-600"})]),m(" "+l(s.mimbar.nama_mimbar),1)]),_:1}),e(n(M),null,{default:i(()=>[m(l(s.mimbar.kode_mimbar)+" • Kapasitas: "+l(s.mimbar.kapasitas)+" orang ",1)]),_:1})]),t("div",O,[e(n(K),{variant:v(s.mimbar.status)},{default:i(()=>[m(l(x(s.mimbar.status)),1)]),_:1},8,["variant"])])])]),_:1}),e(n(_),null,{default:i(()=>[t("div",Q,[t("div",X,[t("div",null,[e(n(u),{class:"text-sm font-medium text-gray-500"},{default:i(()=>a[2]||(a[2]=[m("Kode Mimbar")])),_:1,__:[2]}),t("p",Y,l(s.mimbar.kode_mimbar),1)]),t("div",null,[e(n(u),{class:"text-sm font-medium text-gray-500"},{default:i(()=>a[3]||(a[3]=[m("Nama Mimbar")])),_:1,__:[3]}),t("p",Z,l(s.mimbar.nama_mimbar),1)]),t("div",null,[e(n(u),{class:"text-sm font-medium text-gray-500"},{default:i(()=>a[4]||(a[4]=[m("Kapasitas")])),_:1,__:[4]}),t("p",tt,l(s.mimbar.kapasitas)+" orang",1)]),t("div",null,[e(n(u),{class:"text-sm font-medium text-gray-500"},{default:i(()=>a[5]||(a[5]=[m("Status")])),_:1,__:[5]}),t("p",at,l(x(s.mimbar.status)),1)])]),t("div",st,[t("div",null,[e(n(u),{class:"text-sm font-medium text-gray-500"},{default:i(()=>a[6]||(a[6]=[m("Keterangan")])),_:1,__:[6]}),t("p",et,l(s.mimbar.keterangan||"-"),1)]),t("div",null,[e(n(u),{class:"text-sm font-medium text-gray-500"},{default:i(()=>a[7]||(a[7]=[m("Dibuat")])),_:1,__:[7]}),t("p",it,l(b(s.mimbar.created_at)),1)]),t("div",null,[e(n(u),{class:"text-sm font-medium text-gray-500"},{default:i(()=>a[8]||(a[8]=[m("Diperbarui")])),_:1,__:[8]}),t("p",nt,l(b(s.mimbar.updated_at)),1)]),t("div",null,[e(n(u),{class:"text-sm font-medium text-gray-500"},{default:i(()=>a[9]||(a[9]=[m("Penggunaan")])),_:1,__:[9]}),t("div",lt,[t("div",mt,[t("div",{class:"bg-blue-600 h-2 rounded-full",style:E({width:`${y()}%`})},null,4)]),t("span",rt,l(y())+"%",1)])])])])]),_:1})]),_:1}),t("div",ot,[e(n(f),null,{default:i(()=>[e(n(_),{class:"p-6"},{default:i(()=>{var r;return[t("div",dt,[t("div",ut,[e(d,{name:"users",class:"h-8 w-8 text-blue-600"})]),t("div",ft,[a[10]||(a[10]=t("p",{class:"text-sm font-medium text-gray-500"},"Total Pendaftaran",-1)),t("p",_t,l(((r=s.mimbar.pendaftaran)==null?void 0:r.length)||0),1)])])]}),_:1})]),_:1}),e(n(f),null,{default:i(()=>[e(n(_),{class:"p-6"},{default:i(()=>[t("div",pt,[t("div",gt,[e(d,{name:"check-circle",class:"h-8 w-8 text-green-600"})]),t("div",ct,[a[11]||(a[11]=t("p",{class:"text-sm font-medium text-gray-500"},"Disetujui",-1)),t("p",bt,l(A()),1)])])]),_:1})]),_:1}),e(n(f),null,{default:i(()=>[e(n(_),{class:"p-6"},{default:i(()=>[t("div",vt,[t("div",xt,[e(d,{name:"clock",class:"h-8 w-8 text-yellow-600"})]),t("div",yt,[a[12]||(a[12]=t("p",{class:"text-sm font-medium text-gray-500"},"Menunggu",-1)),t("p",ht,l(V()),1)])])]),_:1})]),_:1}),e(n(f),null,{default:i(()=>[e(n(_),{class:"p-6"},{default:i(()=>[t("div",kt,[t("div",wt,[e(d,{name:"percent",class:"h-8 w-8 text-purple-600"})]),t("div",$t,[a[13]||(a[13]=t("p",{class:"text-sm font-medium text-gray-500"},"Sisa Kapasitas",-1)),t("p",Dt,l(L()),1)])])]),_:1})]),_:1})]),e(n(f),null,{default:i(()=>[e(n(S),null,{default:i(()=>[t("div",Ct,[t("div",null,[e(n(j),null,{default:i(()=>a[14]||(a[14]=[m("Daftar Pendaftaran")])),_:1,__:[14]}),e(n(M),null,{default:i(()=>[m(" Peserta yang menggunakan mimbar "+l(s.mimbar.nama_mimbar),1)]),_:1})])])]),_:1}),e(n(_),null,{default:i(()=>[!s.mimbar.pendaftaran||s.mimbar.pendaftaran.length===0?(p(),g("div",Mt,[e(d,{name:"building",class:"w-12 h-12 mx-auto mb-4 text-gray-300"}),a[15]||(a[15]=t("p",null,"Belum ada pendaftaran yang menggunakan mimbar ini",-1))])):(p(),g("div",St,[(p(!0),g(F,null,I(s.mimbar.pendaftaran,r=>{var h,k,w,$,D,C;return p(),g("div",{key:r.id_pendaftaran,class:"border rounded-lg p-4 hover:bg-gray-50"},[t("div",jt,[t("div",Kt,[t("div",Nt,[t("h4",Pt,l((h=r.peserta)==null?void 0:h.nama_lengkap),1),e(n(K),{variant:v(r.status_pendaftaran)},{default:i(()=>[m(l(r.status_pendaftaran),1)]),_:2},1032,["variant"])]),t("p",Bt,l(r.nomor_pendaftaran),1),t("div",At,[t("div",null,[a[16]||(a[16]=t("span",{class:"text-gray-500"},"Golongan:",-1)),t("p",Vt,l((k=r.golongan)==null?void 0:k.nama_golongan),1)]),t("div",null,[a[17]||(a[17]=t("span",{class:"text-gray-500"},"Cabang:",-1)),t("p",Lt,l(($=(w=r.golongan)==null?void 0:w.cabang_lomba)==null?void 0:$.nama_cabang),1)]),t("div",null,[a[18]||(a[18]=t("span",{class:"text-gray-500"},"Wilayah:",-1)),t("p",Tt,l((C=(D=r.peserta)==null?void 0:D.wilayah)==null?void 0:C.nama_wilayah),1)]),t("div",null,[a[19]||(a[19]=t("span",{class:"text-gray-500"},"Tgl Daftar:",-1)),t("p",Wt,l(b(r.created_at)),1)])])]),t("div",zt,[e(c,{variant:"ghost",size:"sm",onClick:It=>s.$inertia.visit(s.route("admin.pendaftaran.show",r.id_pendaftaran))},{default:i(()=>[e(d,{name:"eye",class:"w-4 h-4"})]),_:2},1032,["onClick"])])])])}),128))]))]),_:1})]),_:1}),t("div",Et,[e(c,{variant:"outline",onClick:a[0]||(a[0]=r=>s.$inertia.visit(s.route("admin.mimbar.index")))},{default:i(()=>[e(d,{name:"arrow-left",class:"w-4 h-4 mr-2"}),a[20]||(a[20]=m(" Kembali "))]),_:1,__:[20]}),t("div",Ft,[e(c,{variant:"outline",onClick:B},{default:i(()=>[e(d,{name:s.mimbar.status==="aktif"?"pause":"play",class:"w-4 h-4 mr-2"},null,8,["name"]),m(" "+l(s.mimbar.status==="aktif"?"Non-aktifkan":"Aktifkan"),1)]),_:1}),e(c,{onClick:a[1]||(a[1]=r=>s.$inertia.visit(s.route("admin.mimbar.edit",s.mimbar.id_mimbar)))},{default:i(()=>[e(d,{name:"edit",class:"w-4 h-4 mr-2"}),a[21]||(a[21]=m(" Edit Mimbar "))]),_:1,__:[21]})])])])]),_:1}))}});export{ia as default};
