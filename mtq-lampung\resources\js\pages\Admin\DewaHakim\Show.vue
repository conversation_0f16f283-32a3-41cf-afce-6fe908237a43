<template>
  <AppLayout :breadcrumbs="breadcrumbItems">
    <Head :title="`Detail Dewan Hakim: ${dewaHakim.nama_lengkap}`" />
    <Heading :title="`Detail Dewan Hakim: ${dewaHakim.nama_lengkap}`" />

    <div class="max-w-6xl mx-auto space-y-6">
      <!-- <PERSON><PERSON> Information -->
      <Card>
        <CardHeader>
          <div class="flex justify-between items-start">
            <div>
              <CardTitle class="flex items-center gap-3">
                <div class="w-12 h-12 bg-indigo-100 rounded-full flex items-center justify-center">
                  <Icon name="user-check" class="w-6 h-6 text-indigo-600" />
                </div>
                {{ dewaHakim.nama_lengkap }}
              </CardTitle>
              <CardDescription>
                {{ dewaHakim.nik }} • {{ dewaHakim.pekerjaan }}
              </CardDescription>
            </div>
            <div class="flex gap-2">
              <Badge :variant="getTipeVariant(dewaHakim.tipe_hakim)">
                {{ tipeHakim[dewaHakim.tipe_hakim] || dewaHakim.tipe_hakim }}
              </Badge>
              <Badge :variant="getStatusVariant(dewaHakim.status)">
                {{ getStatusLabel(dewaHakim.status) }}
              </Badge>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div class="space-y-4">
              <h4 class="font-medium text-gray-900">Informasi Pribadi</h4>
              <div class="space-y-2">
                <div>
                  <Label class="text-sm font-medium text-gray-500">NIK</Label>
                  <p class="text-sm">{{ dewaHakim.nik }}</p>
                </div>
                <div>
                  <Label class="text-sm font-medium text-gray-500">Tempat, Tanggal Lahir</Label>
                  <p class="text-sm">{{ dewaHakim.tempat_lahir }}, {{ formatDate(dewaHakim.tanggal_lahir) }}</p>
                </div>
                <div>
                  <Label class="text-sm font-medium text-gray-500">No. Telepon</Label>
                  <p class="text-sm">{{ dewaHakim.no_telepon }}</p>
                </div>
                <div>
                  <Label class="text-sm font-medium text-gray-500">Email</Label>
                  <p class="text-sm">{{ dewaHakim.user?.email || '-' }}</p>
                </div>
              </div>
            </div>
            
            <div class="space-y-4">
              <h4 class="font-medium text-gray-900">Informasi Profesi</h4>
              <div class="space-y-2">
                <div>
                  <Label class="text-sm font-medium text-gray-500">Pekerjaan</Label>
                  <p class="text-sm">{{ dewaHakim.pekerjaan }}</p>
                </div>
                <div>
                  <Label class="text-sm font-medium text-gray-500">Unit Kerja</Label>
                  <p class="text-sm">{{ dewaHakim.unit_kerja }}</p>
                </div>
                <div>
                  <Label class="text-sm font-medium text-gray-500">Spesialisasi</Label>
                  <p class="text-sm">{{ dewaHakim.spesialisasi }}</p>
                </div>
                <div>
                  <Label class="text-sm font-medium text-gray-500">Wilayah</Label>
                  <p class="text-sm">{{ dewaHakim.wilayah?.nama_wilayah }}</p>
                </div>
              </div>
            </div>

            <div class="space-y-4">
              <h4 class="font-medium text-gray-900">Alamat</h4>
              <div class="space-y-2">
                <div>
                  <Label class="text-sm font-medium text-gray-500">Alamat Rumah</Label>
                  <p class="text-sm">{{ dewaHakim.alamat_rumah }}</p>
                </div>
                <div v-if="dewaHakim.alamat_kantor">
                  <Label class="text-sm font-medium text-gray-500">Alamat Kantor</Label>
                  <p class="text-sm">{{ dewaHakim.alamat_kantor }}</p>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- Statistics -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent class="p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <Icon name="award" class="h-8 w-8 text-blue-600" />
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">Total Penilaian</p>
                <p class="text-2xl font-semibold text-gray-900">{{ dewaHakim.nilai_peserta?.length || 0 }}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent class="p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <Icon name="graduation-cap" class="h-8 w-8 text-green-600" />
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">Pendidikan</p>
                <p class="text-2xl font-semibold text-gray-900">{{ dewaHakim.pendidikan?.length || 0 }}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent class="p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <Icon name="briefcase" class="h-8 w-8 text-yellow-600" />
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">Pengalaman</p>
                <p class="text-2xl font-semibold text-gray-900">{{ dewaHakim.pengalaman?.length || 0 }}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent class="p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <Icon name="trophy" class="h-8 w-8 text-purple-600" />
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">Prestasi</p>
                <p class="text-2xl font-semibold text-gray-900">{{ dewaHakim.prestasi?.length || 0 }}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <!-- Pendidikan -->
      <Card v-if="dewaHakim.pendidikan && dewaHakim.pendidikan.length > 0">
        <CardHeader>
          <CardTitle>Riwayat Pendidikan</CardTitle>
        </CardHeader>
        <CardContent>
          <div class="space-y-4">
            <div
              v-for="pendidikan in dewaHakim.pendidikan"
              :key="pendidikan.id_pendidikan"
              class="border rounded-lg p-4"
            >
              <div class="flex justify-between items-start">
                <div>
                  <h4 class="font-medium">{{ pendidikan.jenjang_pendidikan }}</h4>
                  <p class="text-sm text-gray-600">{{ pendidikan.nama_institusi }}</p>
                  <p class="text-sm text-gray-500">{{ pendidikan.jurusan }}</p>
                  <p class="text-sm text-gray-500">{{ pendidikan.tahun_lulus }}</p>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- Pengalaman -->
      <Card v-if="dewaHakim.pengalaman && dewaHakim.pengalaman.length > 0">
        <CardHeader>
          <CardTitle>Pengalaman Kerja</CardTitle>
        </CardHeader>
        <CardContent>
          <div class="space-y-4">
            <div
              v-for="pengalaman in dewaHakim.pengalaman"
              :key="pengalaman.id_pengalaman"
              class="border rounded-lg p-4"
            >
              <div class="flex justify-between items-start">
                <div>
                  <h4 class="font-medium">{{ pengalaman.jabatan }}</h4>
                  <p class="text-sm text-gray-600">{{ pengalaman.nama_instansi }}</p>
                  <p class="text-sm text-gray-500">{{ pengalaman.tahun_mulai }} - {{ pengalaman.tahun_selesai || 'Sekarang' }}</p>
                  <p v-if="pengalaman.deskripsi" class="text-sm text-gray-500 mt-2">{{ pengalaman.deskripsi }}</p>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- Prestasi -->
      <Card v-if="dewaHakim.prestasi && dewaHakim.prestasi.length > 0">
        <CardHeader>
          <CardTitle>Prestasi & Penghargaan</CardTitle>
        </CardHeader>
        <CardContent>
          <div class="space-y-4">
            <div
              v-for="prestasi in dewaHakim.prestasi"
              :key="prestasi.id_prestasi"
              class="border rounded-lg p-4"
            >
              <div class="flex justify-between items-start">
                <div>
                  <h4 class="font-medium">{{ prestasi.nama_prestasi }}</h4>
                  <p class="text-sm text-gray-600">{{ prestasi.pemberi_prestasi }}</p>
                  <p class="text-sm text-gray-500">{{ prestasi.tahun_prestasi }}</p>
                  <p v-if="prestasi.deskripsi" class="text-sm text-gray-500 mt-2">{{ prestasi.deskripsi }}</p>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- Recent Penilaian -->
      <Card v-if="dewaHakim.nilai_peserta && dewaHakim.nilai_peserta.length > 0">
        <CardHeader>
          <CardTitle>Penilaian Terbaru</CardTitle>
          <CardDescription>
            Daftar penilaian yang telah diberikan oleh dewan hakim ini
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div class="space-y-4">
            <div
              v-for="nilai in dewaHakim.nilai_peserta.slice(0, 5)"
              :key="nilai.id_nilai"
              class="border rounded-lg p-4"
            >
              <div class="flex justify-between items-start">
                <div>
                  <h4 class="font-medium">{{ nilai.pendaftaran?.peserta?.nama_lengkap }}</h4>
                  <p class="text-sm text-gray-600">{{ nilai.pendaftaran?.nomor_pendaftaran }}</p>
                  <p class="text-sm text-gray-500">Nilai: {{ nilai.nilai_total }}</p>
                </div>
                <div class="text-sm text-gray-500">
                  {{ formatDate(nilai.created_at) }}
                </div>
              </div>
            </div>
            <div v-if="dewaHakim.nilai_peserta.length > 5" class="text-center">
              <Button variant="outline" size="sm">
                Lihat Semua Penilaian ({{ dewaHakim.nilai_peserta.length }})
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- Actions -->
      <div class="flex justify-between">
        <Button
          variant="outline"
          @click="$inertia.visit(route('admin.dewan-hakim.index'))"
        >
          <Icon name="arrow-left" class="w-4 h-4 mr-2" />
          Kembali
        </Button>
        <div class="flex gap-2">
          <Button
            variant="outline"
            @click="$inertia.visit(route('admin.dewan-hakim.profile', dewaHakim.id_dewan_hakim))"
          >
            <Icon name="user" class="w-4 h-4 mr-2" />
            Profil Lengkap
          </Button>
          <Button
            variant="outline"
            @click="toggleStatus"
          >
            <Icon :name="dewaHakim.status === 'aktif' ? 'user-x' : 'user-check'" class="w-4 h-4 mr-2" />
            {{ dewaHakim.status === 'aktif' ? 'Non-aktifkan' : 'Aktifkan' }}
          </Button>
          <Button @click="$inertia.visit(route('admin.dewan-hakim.edit', dewaHakim.id_dewan_hakim))">
            <Icon name="edit" class="w-4 h-4 mr-2" />
            Edit Dewan Hakim
          </Button>
        </div>
      </div>
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import { Head, router } from '@inertiajs/vue3'
import AppLayout from '@/layouts/AppLayout.vue'
import Heading from '@/components/Heading.vue'
import Button from '@/components/ui/button/Button.vue'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Label } from '@/components/ui/label'
import Icon from '@/components/Icon.vue'
import { type BreadcrumbItem } from '@/types'

interface User {
  email: string
}

interface Wilayah {
  nama_wilayah: string
}

interface Pendidikan {
  id_pendidikan: number
  jenjang_pendidikan: string
  nama_institusi: string
  jurusan: string
  tahun_lulus: string
}

interface Pengalaman {
  id_pengalaman: number
  jabatan: string
  nama_instansi: string
  tahun_mulai: string
  tahun_selesai?: string
  deskripsi?: string
}

interface Prestasi {
  id_prestasi: number
  nama_prestasi: string
  pemberi_prestasi: string
  tahun_prestasi: string
  deskripsi?: string
}

interface NilaiPeserta {
  id_nilai: number
  nilai_total: number
  created_at: string
  pendaftaran?: {
    nomor_pendaftaran: string
    peserta?: {
      nama_lengkap: string
    }
  }
}

interface DewaHakim {
  id_dewan_hakim: number
  nik: string
  nama_lengkap: string
  tempat_lahir: string
  tanggal_lahir: string
  pekerjaan: string
  unit_kerja: string
  alamat_rumah: string
  alamat_kantor?: string
  no_telepon: string
  spesialisasi: string
  tipe_hakim: string
  status: string
  user?: User
  wilayah?: Wilayah
  pendidikan?: Pendidikan[]
  pengalaman?: Pengalaman[]
  prestasi?: Prestasi[]
  nilai_peserta?: NilaiPeserta[]
}

const props = defineProps<{
  dewaHakim: DewaHakim
}>()

const breadcrumbItems: BreadcrumbItem[] = [
  { title: 'Dashboard', href: '/admin/dashboard' },
  { title: 'Manajemen Dewan Hakim', href: '/admin/dewan-hakim' },
  { title: 'Detail Dewan Hakim', href: `/admin/dewan-hakim/${props.dewaHakim.id_dewan_hakim}` }
]

const tipeHakim: Record<string, string> = {
  undangan: 'Undangan',
  kabupaten: 'Kabupaten'
}

const toggleStatus = () => {
  router.post(route('admin.dewan-hakim.toggle-status', props.dewaHakim.id_dewan_hakim), {}, {
    preserveScroll: true
  })
}

const getTipeVariant = (tipe: string) => {
  const variants: Record<string, string> = {
    undangan: 'default',
    kabupaten: 'secondary'
  }
  return variants[tipe] || 'secondary'
}

const getStatusVariant = (status: string) => {
  const variants: Record<string, string> = {
    aktif: 'default',
    non_aktif: 'secondary'
  }
  return variants[status] || 'secondary'
}

const getStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    aktif: 'Aktif',
    non_aktif: 'Non Aktif'
  }
  return labels[status] || status
}

const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString('id-ID', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}
</script>
