<?php

namespace App\Http\Controllers\AdminDaerah;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Peserta;
use App\Models\Wilayah;
use App\Models\Golongan;
use App\Models\Pendaftaran;
use App\Models\Pembayaran;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules;
use Inertia\Inertia;
use Inertia\Response;

class PesertaController extends Controller
{
    /**
     * Display a listing of peserta in admin daerah's wilayah
     */
    public function index(): Response
    {
        $adminWilayah = Auth::user()->id_wilayah;
        
        $peserta = Peserta::with(['user', 'wilayah', 'pendaftaran.golongan.cabangLomba'])
            ->where('id_wilayah', $adminWilayah)
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        $stats = [
            'total_peserta' => Peserta::where('id_wilayah', $adminWilayah)->count(),
            'peserta_approved' => Peserta::where('id_wilayah', $adminWilayah)->where('status_peserta', 'approved')->count(),
            'peserta_pending' => Peserta::where('id_wilayah', $adminWilayah)->whereIn('status_peserta', ['draft', 'submitted'])->count(),
            'total_pendaftaran' => Pendaftaran::whereHas('peserta', function($q) use ($adminWilayah) {
                $q->where('id_wilayah', $adminWilayah);
            })->count(),
        ];

        return Inertia::render('AdminDaerah/Peserta/Index', [
            'peserta' => $peserta,
            'stats' => $stats
        ]);
    }

    /**
     * Show the form for creating a new peserta
     */
    public function create(): Response
    {
        $adminWilayah = Auth::user()->id_wilayah;
        
        // Get available golongan for registration
        $golongan = Golongan::with('cabangLomba')
            ->where('status', 'aktif')
            ->orderBy('nama_golongan')
            ->get();

        return Inertia::render('AdminDaerah/Peserta/Create', [
            'golongan' => $golongan,
            'admin_wilayah' => $adminWilayah
        ]);
    }

    /**
     * Store a newly created peserta
     */
    public function store(Request $request)
    {
        $adminWilayah = Auth::user()->id_wilayah;
        
        $validated = $request->validate([
            'username' => 'required|string|max:50|unique:users,username',
            'nama_lengkap' => 'required|string|max:100',
            'email' => 'required|string|lowercase|email|max:255|unique:users,email',
            'password' => ['required', 'confirmed', Rules\Password::defaults()],
            'nik' => 'required|string|size:16|unique:peserta,nik',
            'tempat_lahir' => 'required|string|max:50',
            'tanggal_lahir' => 'required|date|before:today',
            'jenis_kelamin' => 'required|in:L,P',
            'alamat' => 'required|string',
            'no_telepon' => 'nullable|string|max:20',
            'golongan_ids' => 'nullable|array',
            'golongan_ids.*' => 'exists:golongan,id_golongan',
            'auto_approve' => 'boolean',
            'keterangan' => 'nullable|string|max:500'
        ]);

        DB::transaction(function () use ($validated, $adminWilayah) {
            // Create user account
            $user = User::create([
                'username' => $validated['username'],
                'nama_lengkap' => $validated['nama_lengkap'],
                'email' => $validated['email'],
                'password' => Hash::make($validated['password']),
                'role' => 'peserta',
                'status' => 'aktif',
                'id_wilayah' => $adminWilayah
            ]);

            // Create peserta profile
            $peserta = Peserta::create([
                'id_user' => $user->id_user,
                'nik' => $validated['nik'],
                'nama_lengkap' => $validated['nama_lengkap'],
                'tempat_lahir' => $validated['tempat_lahir'],
                'tanggal_lahir' => $validated['tanggal_lahir'],
                'jenis_kelamin' => $validated['jenis_kelamin'],
                'alamat' => $validated['alamat'],
                'id_wilayah' => $adminWilayah,
                'no_telepon' => $validated['no_telepon'],
                'email' => $validated['email'],
                'registration_type' => 'admin_daerah',
                'status_peserta' => $validated['auto_approve'] ? 'approved' : 'submitted',
                'registered_by' => Auth::id(),
                'keterangan' => $validated['keterangan']
            ]);

            // Register for selected golongan if any
            if (!empty($validated['golongan_ids'])) {
                foreach ($validated['golongan_ids'] as $golonganId) {
                    $golongan = Golongan::findOrFail($golonganId);
                    
                    // Check eligibility
                    if ($this->checkEligibility($peserta, $golongan)) {
                        $tahun = date('Y');
                        $nomorPendaftaran = $this->generateNomorPendaftaran($tahun);
                        $nomorPeserta = $this->generateNomorPeserta($golongan, $tahun);

                        $pendaftaran = Pendaftaran::create([
                            'id_peserta' => $peserta->id_peserta,
                            'id_golongan' => $golonganId,
                            'nomor_pendaftaran' => $nomorPendaftaran,
                            'nomor_peserta' => $nomorPeserta,
                            'tahun_pendaftaran' => $tahun,
                            'status_pendaftaran' => $validated['auto_approve'] ? 'approved' : 'submitted',
                            'registered_by' => Auth::id(),
                            'keterangan' => $validated['keterangan']
                        ]);

                        // Create payment record if auto-approved
                        if ($validated['auto_approve']) {
                            Pembayaran::create([
                                'id_pendaftaran' => $pendaftaran->id_pendaftaran,
                                'nomor_transaksi' => $this->generateNomorTransaksi(),
                                'jumlah_bayar' => $golongan->biaya_pendaftaran,
                                'metode_pembayaran' => 'admin_daerah',
                                'status_pembayaran' => 'paid',
                                'tanggal_bayar' => now(),
                                'verified_by' => Auth::id(),
                                'catatan' => 'Pembayaran diproses oleh admin daerah'
                            ]);
                        }
                    }
                }
            }
        });

        return redirect()->route('admin-daerah.peserta.index')
            ->with('success', 'Peserta berhasil didaftarkan.');
    }

    /**
     * Display the specified peserta
     */
    public function show(string $id): Response
    {
        $adminWilayah = Auth::user()->id_wilayah;
        
        $peserta = Peserta::with([
            'user', 
            'wilayah', 
            'pendaftaran.golongan.cabangLomba',
            'pendaftaran.pembayaran',
            'pendaftaran.dokumenPeserta'
        ])
        ->where('id_wilayah', $adminWilayah)
        ->findOrFail($id);

        return Inertia::render('AdminDaerah/Peserta/Show', [
            'peserta' => $peserta
        ]);
    }

    /**
     * Show the form for editing the specified peserta
     */
    public function edit(string $id): Response
    {
        $adminWilayah = Auth::user()->id_wilayah;
        
        $peserta = Peserta::with('user')
            ->where('id_wilayah', $adminWilayah)
            ->findOrFail($id);

        return Inertia::render('AdminDaerah/Peserta/Edit', [
            'peserta' => $peserta
        ]);
    }

    /**
     * Update the specified peserta
     */
    public function update(Request $request, string $id)
    {
        $adminWilayah = Auth::user()->id_wilayah;
        
        $peserta = Peserta::with('user')
            ->where('id_wilayah', $adminWilayah)
            ->findOrFail($id);

        $validated = $request->validate([
            'nama_lengkap' => 'required|string|max:100',
            'email' => 'required|string|lowercase|email|max:255|unique:users,email,' . $peserta->user->id_user . ',id_user',
            'tempat_lahir' => 'required|string|max:50',
            'tanggal_lahir' => 'required|date|before:today',
            'alamat' => 'required|string',
            'no_telepon' => 'nullable|string|max:20',
            'status_peserta' => 'required|in:draft,submitted,verified,approved,rejected',
            'keterangan' => 'nullable|string|max:500'
        ]);

        DB::transaction(function () use ($validated, $peserta) {
            // Update user
            $peserta->user->update([
                'nama_lengkap' => $validated['nama_lengkap'],
                'email' => $validated['email']
            ]);

            // Update peserta
            $peserta->update([
                'nama_lengkap' => $validated['nama_lengkap'],
                'tempat_lahir' => $validated['tempat_lahir'],
                'tanggal_lahir' => $validated['tanggal_lahir'],
                'alamat' => $validated['alamat'],
                'no_telepon' => $validated['no_telepon'],
                'email' => $validated['email'],
                'status_peserta' => $validated['status_peserta'],
                'keterangan' => $validated['keterangan'],
                'verified_by' => Auth::id(),
                'verified_at' => now()
            ]);
        });

        return redirect()->route('admin-daerah.peserta.show', $peserta->id_peserta)
            ->with('success', 'Data peserta berhasil diperbarui.');
    }

    /**
     * Check if peserta is eligible for golongan
     */
    private function checkEligibility(Peserta $peserta, Golongan $golongan): bool
    {
        // Check gender
        if ($peserta->jenis_kelamin !== $golongan->jenis_kelamin) {
            return false;
        }

        // Check age
        $age = $this->calculateAge($peserta->tanggal_lahir);
        if ($age < $golongan->batas_umur_min || $age > $golongan->batas_umur_max) {
            return false;
        }

        return true;
    }

    /**
     * Calculate age from birth date
     */
    private function calculateAge($birthDate): int
    {
        $today = new \DateTime();
        $birth = new \DateTime($birthDate);
        return $today->diff($birth)->y;
    }

    /**
     * Generate unique registration number
     */
    private function generateNomorPendaftaran(int $tahun): string
    {
        $prefix = "REG{$tahun}";
        $lastNumber = Pendaftaran::where('tahun_pendaftaran', $tahun)
            ->where('nomor_pendaftaran', 'like', "{$prefix}%")
            ->count();
        
        return $prefix . str_pad($lastNumber + 1, 6, '0', STR_PAD_LEFT);
    }

    /**
     * Generate unique participant number
     */
    private function generateNomorPeserta(Golongan $golongan, int $tahun): string
    {
        $prefix = $golongan->kode_golongan . $tahun;
        $lastNumber = Pendaftaran::where('id_golongan', $golongan->id_golongan)
            ->where('tahun_pendaftaran', $tahun)
            ->count();
        
        return $prefix . str_pad($lastNumber + 1, 3, '0', STR_PAD_LEFT);
    }

    /**
     * Generate unique transaction number
     */
    private function generateNomorTransaksi(): string
    {
        $prefix = "TRX" . date('Ymd');
        $lastNumber = Pembayaran::where('nomor_transaksi', 'like', "{$prefix}%")
            ->count();
        
        return $prefix . str_pad($lastNumber + 1, 6, '0', STR_PAD_LEFT);
    }
}
