<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class VerifikasiPendaftaran extends Model
{
    use HasFactory;

    protected $table = 'verifikasi_pendaftaran';
    protected $primaryKey = 'id_verifikasi';

    protected $fillable = [
        'id_pendaftaran',
        'verified_by',
        'status_nik',
        'catatan_nik',
        'verified_nik_at',
        'status_dokumen',
        'catatan_dokumen',
        'verified_dokumen_at',
        'status_verifikasi',
        'catatan_verifikasi',
        'verified_at'
    ];

    protected $casts = [
        'status_nik' => 'string',
        'status_dokumen' => 'string',
        'status_verifikasi' => 'string',
        'verified_nik_at' => 'datetime',
        'verified_dokumen_at' => 'datetime',
        'verified_at' => 'datetime'
    ];

    // Relationships
    public function pendaftaran(): BelongsTo
    {
        return $this->belongsTo(Pendaftaran::class, 'id_pendaftaran', 'id_pendaftaran');
    }

    public function verifiedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'verified_by', 'id_user');
    }

    // Scopes
    public function scopePending($query)
    {
        return $query->where('status_verifikasi', 'pending');
    }

    public function scopeApproved($query)
    {
        return $query->where('status_verifikasi', 'approved');
    }

    public function scopeRejected($query)
    {
        return $query->where('status_verifikasi', 'rejected');
    }

    public function scopeNikPending($query)
    {
        return $query->where('status_nik', 'pending');
    }

    public function scopeDokumenPending($query)
    {
        return $query->where('status_dokumen', 'pending');
    }

    // Helper methods
    public function isNikVerified()
    {
        return $this->status_nik === 'sesuai';
    }

    public function isDokumenVerified()
    {
        return $this->status_dokumen === 'berkas_sesuai';
    }

    public function isFullyVerified()
    {
        return $this->isNikVerified() && $this->isDokumenVerified();
    }

    public function canBeApproved()
    {
        return $this->isFullyVerified() && $this->status_verifikasi === 'pending';
    }

    public function getStatusBadgeClass()
    {
        return match($this->status_verifikasi) {
            'pending' => 'bg-yellow-100 text-yellow-800',
            'approved' => 'bg-green-100 text-green-800',
            'rejected' => 'bg-red-100 text-red-800',
            default => 'bg-gray-100 text-gray-800'
        };
    }
}
