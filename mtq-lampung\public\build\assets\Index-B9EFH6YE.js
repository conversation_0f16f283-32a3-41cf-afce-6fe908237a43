import{d as H,r as K,W as g,c as v,o as m,w as n,a as t,b as i,u as l,g as R,e as r,h as x,F as D,m as T,t as d,i as S}from"./app-B_pmlBSQ.js";import{a as q,b as G,c as J,d as h,_ as O}from"./AppLayout.vue_vue_type_script_setup_true_lang-BRf8lUiM.js";import{_ as X}from"./Heading.vue_vue_type_script_setup_true_lang-is4VBSCS.js";import{_}from"./AppLogoIcon.vue_vue_type_script_setup_true_lang-wzz4PWna.js";import{_ as C,a as V}from"./CardContent.vue_vue_type_script_setup_true_lang-ClvLNBXC.js";import{_ as y}from"./index-CMGr3-bt.js";import{_ as Z}from"./Input.vue_vue_type_script_setup_true_lang-DB1-RsTn.js";import{_ as w}from"./Label.vue_vue_type_script_setup_true_lang-rj_fhE7R.js";import{_ as M,a as P,b as A,c as I,d as c}from"./SelectValue.vue_vue_type_script_setup_true_lang-IzlIronZ.js";import{_ as f}from"./Icon.vue_vue_type_script_setup_true_lang-BuYzjckr.js";import{l as aa,_ as ta}from"./lodash-Cvgo55ys.js";import"./useForwardExpose-CO14IhkA.js";import"./RovingFocusGroup-lsWyU4xZ.js";import"./check-DtmHjdxf.js";import"./useFormControl-mZRLifGx.js";import"./loader-circle-6d8QrWFr.js";import"./sun-CHIV9RJ8.js";import"./TextLink.vue_vue_type_script_setup_true_lang-CC2Og0lP.js";const ea={class:"space-y-6"},sa={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},na={class:"flex items-end"},la={class:"flex justify-between items-center"},ia={class:"text-sm text-gray-600"},ra={class:"overflow-x-auto"},oa={class:"w-full"},da={class:"bg-white divide-y divide-gray-200"},ua={class:"px-6 py-4"},fa={class:"text-sm font-medium text-gray-900 flex items-center gap-2"},ma={class:"text-sm text-gray-600 font-medium"},pa={class:"text-sm text-gray-500 flex items-center gap-1"},_a={class:"px-6 py-4 whitespace-nowrap"},ca={class:"text-sm text-gray-900"},ka={class:"text-sm text-gray-500"},ga={class:"px-6 py-4 whitespace-nowrap"},va={class:"text-sm text-gray-900"},xa={class:"text-sm"},ha={class:"px-6 py-4 whitespace-nowrap"},ya={class:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium space-x-2"},Ya=H({__name:"Index",props:{pelaksanaan:{},filters:{},availableYears:{}},setup(B){const Q=[{title:"Dashboard",href:"/admin/dashboard"},{title:"Manajemen Pelaksanaan MTQ",href:"/admin/pelaksanaan"}],u=K({...B.filters}),k=aa.debounce(()=>{g.get(route("admin.pelaksanaan.index"),u,{preserveState:!0,replace:!0})},300),F=()=>{u.search="",u.tahun="",u.status="",k()},L=s=>{confirm(`Apakah Anda yakin ingin mengaktifkan MTQ ${s.tahun}? Pelaksanaan lain akan dinonaktifkan.`)&&g.post(route("admin.pelaksanaan.activate",s.id_pelaksanaan))},j=s=>{confirm(`Apakah Anda yakin ingin menghapus MTQ ${s.tahun}?`)&&g.delete(route("admin.pelaksanaan.destroy",s.id_pelaksanaan))},U=s=>{window.open(route("admin.pelaksanaan.export-participants",s.id_pelaksanaan))},z=s=>{g.visit(route("admin.pelaksanaan.statistics",s.id_pelaksanaan))},N=s=>({draft:"secondary",aktif:"default",selesai:"outline"})[s]||"secondary",Y=s=>({draft:"Draft",aktif:"Aktif",selesai:"Selesai"})[s]||s,$=s=>{const a=new Date,e=new Date(s.tanggal_buka_pendaftaran),o=new Date(s.tanggal_tutup_pendaftaran);return s.status!=="aktif"?"Tidak Aktif":a<e?"Belum Buka":a>o?"Tutup":"Terbuka"},E=s=>{const a=$(s);return{Terbuka:"default","Belum Buka":"secondary",Tutup:"outline","Tidak Aktif":"secondary"}[a]||"secondary"},b=(s,a)=>{const e=new Date(s),o=new Date(a),p={day:"numeric",month:"short",year:"numeric"};return e.getTime()===o.getTime()?e.toLocaleDateString("id-ID",p):e.getMonth()===o.getMonth()&&e.getFullYear()===o.getFullYear()?`${e.getDate()} - ${o.toLocaleDateString("id-ID",p)}`:`${e.toLocaleDateString("id-ID",p)} - ${o.toLocaleDateString("id-ID",p)}`},W=(s,a)=>{const e=new Date(s),o=new Date(a),p=Math.abs(o.getTime()-e.getTime());return Math.ceil(p/(1e3*60*60*24))+1};return(s,a)=>(m(),v(O,{breadcrumbs:Q},{default:n(()=>[t(l(R),{title:"Manajemen Pelaksanaan MTQ"}),t(X,{title:"Manajemen Pelaksanaan MTQ"}),i("div",ea,[t(l(C),null,{default:n(()=>[t(l(V),{class:"p-6"},{default:n(()=>[i("div",sa,[i("div",null,[t(l(w),{for:"search"},{default:n(()=>a[4]||(a[4]=[r("Pencarian")])),_:1,__:[4]}),t(l(Z),{id:"search",modelValue:u.search,"onUpdate:modelValue":a[0]||(a[0]=e=>u.search=e),placeholder:"Tema, tempat, tahun...",onInput:l(k)},null,8,["modelValue","onInput"])]),i("div",null,[t(l(w),{for:"tahun"},{default:n(()=>a[5]||(a[5]=[r("Tahun")])),_:1,__:[5]}),t(l(M),{modelValue:u.tahun,"onUpdate:modelValue":[a[1]||(a[1]=e=>u.tahun=e),l(k)]},{default:n(()=>[t(l(P),null,{default:n(()=>[t(l(A),{placeholder:"Semua Tahun"})]),_:1}),t(l(I),null,{default:n(()=>[t(l(c),{value:"all"},{default:n(()=>a[6]||(a[6]=[r("Semua Tahun")])),_:1,__:[6]}),(m(!0),x(D,null,T(s.availableYears,e=>(m(),v(l(c),{key:e,value:e.toString()},{default:n(()=>[r(d(e),1)]),_:2},1032,["value"]))),128))]),_:1})]),_:1},8,["modelValue","onUpdate:modelValue"])]),i("div",null,[t(l(w),{for:"status"},{default:n(()=>a[7]||(a[7]=[r("Status")])),_:1,__:[7]}),t(l(M),{modelValue:u.status,"onUpdate:modelValue":[a[2]||(a[2]=e=>u.status=e),l(k)]},{default:n(()=>[t(l(P),null,{default:n(()=>[t(l(A),{placeholder:"Semua Status"})]),_:1}),t(l(I),null,{default:n(()=>[t(l(c),{value:"all"},{default:n(()=>a[8]||(a[8]=[r("Semua Status")])),_:1,__:[8]}),t(l(c),{value:"draft"},{default:n(()=>a[9]||(a[9]=[r("Draft")])),_:1,__:[9]}),t(l(c),{value:"aktif"},{default:n(()=>a[10]||(a[10]=[r("Aktif")])),_:1,__:[10]}),t(l(c),{value:"selesai"},{default:n(()=>a[11]||(a[11]=[r("Selesai")])),_:1,__:[11]})]),_:1})]),_:1},8,["modelValue","onUpdate:modelValue"])]),i("div",na,[t(_,{onClick:F,variant:"outline",class:"w-full"},{default:n(()=>[t(f,{name:"x",class:"w-4 h-4 mr-2"}),a[12]||(a[12]=r(" Clear "))]),_:1,__:[12]})])])]),_:1})]),_:1}),i("div",la,[i("div",ia," Menampilkan "+d(s.pelaksanaan.from)+" - "+d(s.pelaksanaan.to)+" dari "+d(s.pelaksanaan.total)+" pelaksanaan ",1),t(_,{onClick:a[3]||(a[3]=e=>s.$inertia.visit(s.route("admin.pelaksanaan.create")))},{default:n(()=>[t(f,{name:"plus",class:"w-4 h-4 mr-2"}),a[13]||(a[13]=r(" Tambah Pelaksanaan MTQ "))]),_:1,__:[13]})]),t(l(C),null,{default:n(()=>[t(l(V),{class:"p-0"},{default:n(()=>[i("div",ra,[i("table",oa,[a[18]||(a[18]=i("thead",{class:"bg-gray-50 border-b"},[i("tr",null,[i("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Pelaksanaan MTQ "),i("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Periode Acara "),i("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Periode Pendaftaran "),i("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Status "),i("th",{class:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"}," Aksi ")])],-1)),i("tbody",da,[(m(!0),x(D,null,T(s.pelaksanaan.data,e=>(m(),x("tr",{key:e.id_pelaksanaan,class:"hover:bg-gray-50"},[i("td",ua,[i("div",null,[i("div",fa,[r(" MTQ "+d(e.tahun)+" ",1),e.status==="aktif"?(m(),v(l(y),{key:0,variant:"default",class:"text-xs"},{default:n(()=>a[14]||(a[14]=[r(" AKTIF ")])),_:1,__:[14]})):S("",!0)]),i("div",ma,d(e.tema),1),i("div",pa,[t(f,{name:"map-pin",class:"w-3 h-3"}),r(" "+d(e.tempat),1)])])]),i("td",_a,[i("div",ca,d(b(e.tanggal_mulai,e.tanggal_selesai)),1),i("div",ka,d(W(e.tanggal_mulai,e.tanggal_selesai))+" hari ",1)]),i("td",ga,[i("div",va,d(b(e.tanggal_buka_pendaftaran,e.tanggal_tutup_pendaftaran)),1),i("div",xa,[t(l(y),{variant:E(e)},{default:n(()=>[r(d($(e)),1)]),_:2},1032,["variant"])])]),i("td",ha,[t(l(y),{variant:N(e.status)},{default:n(()=>[r(d(Y(e.status)),1)]),_:2},1032,["variant"])]),i("td",ya,[t(_,{variant:"ghost",size:"sm",onClick:o=>s.$inertia.visit(s.route("admin.pelaksanaan.show",e.id_pelaksanaan))},{default:n(()=>[t(f,{name:"eye",class:"w-4 h-4"})]),_:2},1032,["onClick"]),t(_,{variant:"ghost",size:"sm",onClick:o=>s.$inertia.visit(s.route("admin.pelaksanaan.edit",e.id_pelaksanaan))},{default:n(()=>[t(f,{name:"edit",class:"w-4 h-4"})]),_:2},1032,["onClick"]),e.status!=="aktif"?(m(),v(_,{key:0,variant:"ghost",size:"sm",onClick:o=>L(e),class:"text-green-600 hover:text-green-700"},{default:n(()=>[t(f,{name:"play",class:"w-4 h-4"})]),_:2},1032,["onClick"])):S("",!0),t(l(q),null,{default:n(()=>[t(l(G),{"as-child":""},{default:n(()=>[t(_,{variant:"ghost",size:"sm"},{default:n(()=>[t(f,{name:"more-vertical",class:"w-4 h-4"})]),_:1})]),_:1}),t(l(J),{align:"end"},{default:n(()=>[t(l(h),{onClick:o=>U(e)},{default:n(()=>[t(f,{name:"download",class:"w-4 h-4 mr-2"}),a[15]||(a[15]=r(" Export Peserta "))]),_:2,__:[15]},1032,["onClick"]),t(l(h),{onClick:o=>z(e)},{default:n(()=>[t(f,{name:"bar-chart",class:"w-4 h-4 mr-2"}),a[16]||(a[16]=r(" Statistik "))]),_:2,__:[16]},1032,["onClick"]),t(l(h),{onClick:o=>j(e),class:"text-red-600"},{default:n(()=>[t(f,{name:"trash",class:"w-4 h-4 mr-2"}),a[17]||(a[17]=r(" Hapus "))]),_:2,__:[17]},1032,["onClick"])]),_:2},1024)]),_:2},1024)])]))),128))])])])]),_:1})]),_:1}),t(ta,{links:s.pelaksanaan.links},null,8,["links"])])]),_:1}))}});export{Ya as default};
