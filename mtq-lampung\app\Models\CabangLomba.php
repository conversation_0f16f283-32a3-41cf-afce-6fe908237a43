<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class CabangLomba extends Model
{
    protected $table = 'cabang_lomba';
    protected $primaryKey = 'id_cabang';

    protected $fillable = [
        'kode_cabang',
        'nama_cabang',
        'deskripsi',
        'status'
    ];

    protected $casts = [
        'status' => 'string'
    ];

    // Relationships
    public function golongan(): HasMany
    {
        return $this->hasMany(Golongan::class, 'id_cabang', 'id_cabang');
    }

    // Scopes
    public function scopeAktif($query)
    {
        return $query->where('status', 'aktif');
    }
}
