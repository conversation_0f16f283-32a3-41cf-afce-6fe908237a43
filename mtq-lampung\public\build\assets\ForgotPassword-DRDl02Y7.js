import{d as u,x as _,c as n,o as m,w as r,a as o,h as c,i as d,b as a,u as s,g,t as w,f as x,e as l}from"./app-B_pmlBSQ.js";import{_ as y}from"./InputError.vue_vue_type_script_setup_true_lang-A-vdzbq9.js";import{_ as k}from"./TextLink.vue_vue_type_script_setup_true_lang-CC2Og0lP.js";import{_ as v}from"./AppLogoIcon.vue_vue_type_script_setup_true_lang-wzz4PWna.js";import{_ as V}from"./Input.vue_vue_type_script_setup_true_lang-DB1-RsTn.js";import{_ as b}from"./Label.vue_vue_type_script_setup_true_lang-rj_fhE7R.js";import{_ as $}from"./AuthLayout.vue_vue_type_script_setup_true_lang-DMSGHxRq.js";import{L as B}from"./loader-circle-6d8QrWFr.js";import"./useForwardExpose-CO14IhkA.js";const C={key:0,class:"mb-4 text-center text-sm font-medium text-green-600"},E={class:"space-y-6"},N={class:"grid gap-2"},h={class:"my-6 flex items-center justify-start"},F={class:"space-x-1 text-center text-sm text-muted-foreground"},z=u({__name:"ForgotPassword",props:{status:{}},setup(L){const t=_({email:""}),p=()=>{t.post(route("password.email"))};return(i,e)=>(m(),n($,{title:"Forgot password",description:"Enter your email to receive a password reset link"},{default:r(()=>[o(s(g),{title:"Forgot password"}),i.status?(m(),c("div",C,w(i.status),1)):d("",!0),a("div",E,[a("form",{onSubmit:x(p,["prevent"])},[a("div",N,[o(s(b),{for:"email"},{default:r(()=>e[1]||(e[1]=[l("Email address")])),_:1,__:[1]}),o(s(V),{id:"email",type:"email",name:"email",autocomplete:"off",modelValue:s(t).email,"onUpdate:modelValue":e[0]||(e[0]=f=>s(t).email=f),autofocus:"",placeholder:"<EMAIL>"},null,8,["modelValue"]),o(y,{message:s(t).errors.email},null,8,["message"])]),a("div",h,[o(s(v),{class:"w-full",disabled:s(t).processing},{default:r(()=>[s(t).processing?(m(),n(s(B),{key:0,class:"h-4 w-4 animate-spin"})):d("",!0),e[2]||(e[2]=l(" Email password reset link "))]),_:1,__:[2]},8,["disabled"])])],32),a("div",F,[e[4]||(e[4]=a("span",null,"Or, return to",-1)),o(k,{href:i.route("login")},{default:r(()=>e[3]||(e[3]=[l("log in")])),_:1,__:[3]},8,["href"])])])]),_:1}))}});export{z as default};
