import{d as K,x as B,k as L,c as w,o as d,w as s,a as r,b as l,u as a,g as z,e as o,f as M,h as u,i as p,n as _,t as n,j as U,v as P,F as q,m as N}from"./app-B_pmlBSQ.js";import{_ as F}from"./AppLayout.vue_vue_type_script_setup_true_lang-BRf8lUiM.js";import{_ as W}from"./Heading.vue_vue_type_script_setup_true_lang-is4VBSCS.js";import{_ as I}from"./AppLogoIcon.vue_vue_type_script_setup_true_lang-wzz4PWna.js";import{_ as G,a as J}from"./CardContent.vue_vue_type_script_setup_true_lang-ClvLNBXC.js";import{_ as R}from"./CardDescription.vue_vue_type_script_setup_true_lang-DboywBIK.js";import{_ as O,a as Q}from"./CardTitle.vue_vue_type_script_setup_true_lang-5HO6oSoB.js";import{_ as g}from"./Input.vue_vue_type_script_setup_true_lang-DB1-RsTn.js";import{_ as k}from"./Label.vue_vue_type_script_setup_true_lang-rj_fhE7R.js";import{_ as x,a as h,b,c as v,d as y}from"./SelectValue.vue_vue_type_script_setup_true_lang-IzlIronZ.js";import{_ as S}from"./Icon.vue_vue_type_script_setup_true_lang-BuYzjckr.js";import"./useForwardExpose-CO14IhkA.js";import"./RovingFocusGroup-lsWyU4xZ.js";import"./check-DtmHjdxf.js";import"./useFormControl-mZRLifGx.js";import"./loader-circle-6d8QrWFr.js";import"./sun-CHIV9RJ8.js";const X={class:"max-w-4xl mx-auto"},Y={class:"space-y-4"},Z={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},c={key:0,class:"text-sm text-red-600 mt-1"},aa={key:0,class:"text-sm text-red-600 mt-1"},ea={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},ta={key:0,class:"text-sm text-red-600 mt-1"},ra={key:0,class:"text-sm text-red-600 mt-1"},la={key:0,class:"text-sm text-red-600 mt-1"},sa={class:"space-y-4"},ia={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},oa={key:0,class:"text-sm text-red-600 mt-1"},da={key:0,class:"text-sm text-red-600 mt-1"},na={key:0,class:"text-sm text-red-600 mt-1"},ma={class:"space-y-4"},ua={key:0,class:"text-sm text-red-600 mt-1"},pa={key:0,class:"text-sm text-red-600 mt-1"},_a={class:"space-y-4"},ka={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},fa={key:0,class:"text-sm text-red-600 mt-1"},ga={key:0,class:"text-sm text-red-600 mt-1"},wa={key:0,class:"text-sm text-red-600 mt-1"},ya={class:"bg-gray-50 p-4 rounded-lg"},xa={class:"text-sm text-gray-600 space-y-1"},ha={key:0,class:"bg-yellow-50 border border-yellow-200 p-4 rounded-lg"},ba={class:"flex"},va={class:"text-sm text-yellow-700 mt-1"},Va={class:"flex justify-end space-x-4 pt-6 border-t"},za=K({__name:"Edit",props:{dewaHakim:{},wilayah:{},tipeHakim:{}},setup(T){const m=T,A=[{title:"Dashboard",href:"/admin/dashboard"},{title:"Manajemen Dewan Hakim",href:"/admin/dewan-hakim"},{title:"Edit Dewan Hakim",href:`/admin/dewan-hakim/${m.dewaHakim.id_dewan_hakim}/edit`}],t=B({nik:m.dewaHakim.nik,nama_lengkap:m.dewaHakim.nama_lengkap,tempat_lahir:m.dewaHakim.tempat_lahir,tanggal_lahir:m.dewaHakim.tanggal_lahir,pekerjaan:m.dewaHakim.pekerjaan,unit_kerja:m.dewaHakim.unit_kerja,alamat_rumah:m.dewaHakim.alamat_rumah,alamat_kantor:m.dewaHakim.alamat_kantor||"",no_telepon:m.dewaHakim.no_telepon,spesialisasi:m.dewaHakim.spesialisasi,tipe_hakim:m.dewaHakim.tipe_hakim,id_wilayah:m.dewaHakim.id_wilayah.toString(),status:m.dewaHakim.status}),C=L(()=>m.dewaHakim.nilai_peserta&&m.dewaHakim.nilai_peserta.length>0),E=()=>{t.put(route("admin.dewan-hakim.update",m.dewaHakim.id_dewan_hakim),{onSuccess:()=>{}})},V=f=>new Date(f).toLocaleDateString("id-ID",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"});return(f,e)=>(d(),w(F,{breadcrumbs:A},{default:s(()=>[r(a(z),{title:"Edit Dewan Hakim"}),r(W,{title:`Edit Dewan Hakim: ${f.dewaHakim.nama_lengkap}`},null,8,["title"]),l("div",X,[r(a(G),null,{default:s(()=>[r(a(O),null,{default:s(()=>[r(a(Q),null,{default:s(()=>e[14]||(e[14]=[o("Edit Informasi Dewan Hakim")])),_:1,__:[14]}),r(a(R),null,{default:s(()=>e[15]||(e[15]=[o(" Perbarui informasi dewan hakim di bawah ini ")])),_:1,__:[15]})]),_:1}),r(a(J),null,{default:s(()=>{var H,j,$;return[l("form",{onSubmit:M(E,["prevent"]),class:"space-y-8"},[l("div",Y,[e[21]||(e[21]=l("h3",{class:"text-lg font-medium"},"Informasi Pribadi",-1)),l("div",Z,[l("div",null,[r(a(k),{for:"nik"},{default:s(()=>e[16]||(e[16]=[o("NIK *")])),_:1,__:[16]}),r(a(g),{id:"nik",modelValue:a(t).nik,"onUpdate:modelValue":e[0]||(e[0]=i=>a(t).nik=i),type:"text",required:"",maxlength:"16",placeholder:"16 digit NIK",class:_({"border-red-500":a(t).errors.nik})},null,8,["modelValue","class"]),a(t).errors.nik?(d(),u("p",c,n(a(t).errors.nik),1)):p("",!0)]),l("div",null,[r(a(k),{for:"nama_lengkap"},{default:s(()=>e[17]||(e[17]=[o("Nama Lengkap *")])),_:1,__:[17]}),r(a(g),{id:"nama_lengkap",modelValue:a(t).nama_lengkap,"onUpdate:modelValue":e[1]||(e[1]=i=>a(t).nama_lengkap=i),type:"text",required:"",placeholder:"Nama lengkap sesuai KTP",class:_({"border-red-500":a(t).errors.nama_lengkap})},null,8,["modelValue","class"]),a(t).errors.nama_lengkap?(d(),u("p",aa,n(a(t).errors.nama_lengkap),1)):p("",!0)])]),l("div",ea,[l("div",null,[r(a(k),{for:"tempat_lahir"},{default:s(()=>e[18]||(e[18]=[o("Tempat Lahir *")])),_:1,__:[18]}),r(a(g),{id:"tempat_lahir",modelValue:a(t).tempat_lahir,"onUpdate:modelValue":e[2]||(e[2]=i=>a(t).tempat_lahir=i),type:"text",required:"",placeholder:"Kota tempat lahir",class:_({"border-red-500":a(t).errors.tempat_lahir})},null,8,["modelValue","class"]),a(t).errors.tempat_lahir?(d(),u("p",ta,n(a(t).errors.tempat_lahir),1)):p("",!0)]),l("div",null,[r(a(k),{for:"tanggal_lahir"},{default:s(()=>e[19]||(e[19]=[o("Tanggal Lahir *")])),_:1,__:[19]}),r(a(g),{id:"tanggal_lahir",modelValue:a(t).tanggal_lahir,"onUpdate:modelValue":e[3]||(e[3]=i=>a(t).tanggal_lahir=i),type:"date",required:"",class:_({"border-red-500":a(t).errors.tanggal_lahir})},null,8,["modelValue","class"]),a(t).errors.tanggal_lahir?(d(),u("p",ra,n(a(t).errors.tanggal_lahir),1)):p("",!0)])]),l("div",null,[r(a(k),{for:"no_telepon"},{default:s(()=>e[20]||(e[20]=[o("No. Telepon *")])),_:1,__:[20]}),r(a(g),{id:"no_telepon",modelValue:a(t).no_telepon,"onUpdate:modelValue":e[4]||(e[4]=i=>a(t).no_telepon=i),type:"tel",required:"",placeholder:"08xxxxxxxxxx",class:_({"border-red-500":a(t).errors.no_telepon})},null,8,["modelValue","class"]),a(t).errors.no_telepon?(d(),u("p",la,n(a(t).errors.no_telepon),1)):p("",!0)])]),l("div",sa,[e[25]||(e[25]=l("h3",{class:"text-lg font-medium"},"Informasi Profesi",-1)),l("div",ia,[l("div",null,[r(a(k),{for:"pekerjaan"},{default:s(()=>e[22]||(e[22]=[o("Pekerjaan *")])),_:1,__:[22]}),r(a(g),{id:"pekerjaan",modelValue:a(t).pekerjaan,"onUpdate:modelValue":e[5]||(e[5]=i=>a(t).pekerjaan=i),type:"text",required:"",placeholder:"Contoh: Guru, Dosen, Ustadz",class:_({"border-red-500":a(t).errors.pekerjaan})},null,8,["modelValue","class"]),a(t).errors.pekerjaan?(d(),u("p",oa,n(a(t).errors.pekerjaan),1)):p("",!0)]),l("div",null,[r(a(k),{for:"unit_kerja"},{default:s(()=>e[23]||(e[23]=[o("Unit Kerja *")])),_:1,__:[23]}),r(a(g),{id:"unit_kerja",modelValue:a(t).unit_kerja,"onUpdate:modelValue":e[6]||(e[6]=i=>a(t).unit_kerja=i),type:"text",required:"",placeholder:"Nama instansi/lembaga",class:_({"border-red-500":a(t).errors.unit_kerja})},null,8,["modelValue","class"]),a(t).errors.unit_kerja?(d(),u("p",da,n(a(t).errors.unit_kerja),1)):p("",!0)])]),l("div",null,[r(a(k),{for:"spesialisasi"},{default:s(()=>e[24]||(e[24]=[o("Spesialisasi *")])),_:1,__:[24]}),r(a(g),{id:"spesialisasi",modelValue:a(t).spesialisasi,"onUpdate:modelValue":e[7]||(e[7]=i=>a(t).spesialisasi=i),type:"text",required:"",placeholder:"Contoh: Tilawah, Tahfidz, Tafsir",class:_({"border-red-500":a(t).errors.spesialisasi})},null,8,["modelValue","class"]),a(t).errors.spesialisasi?(d(),u("p",na,n(a(t).errors.spesialisasi),1)):p("",!0)])]),l("div",ma,[e[28]||(e[28]=l("h3",{class:"text-lg font-medium"},"Informasi Alamat",-1)),l("div",null,[r(a(k),{for:"alamat_rumah"},{default:s(()=>e[26]||(e[26]=[o("Alamat Rumah *")])),_:1,__:[26]}),U(l("textarea",{id:"alamat_rumah","onUpdate:modelValue":e[8]||(e[8]=i=>a(t).alamat_rumah=i),rows:"3",required:"",class:_(["w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",{"border-red-500":a(t).errors.alamat_rumah}]),placeholder:"Alamat lengkap tempat tinggal"},null,2),[[P,a(t).alamat_rumah]]),a(t).errors.alamat_rumah?(d(),u("p",ua,n(a(t).errors.alamat_rumah),1)):p("",!0)]),l("div",null,[r(a(k),{for:"alamat_kantor"},{default:s(()=>e[27]||(e[27]=[o("Alamat Kantor")])),_:1,__:[27]}),U(l("textarea",{id:"alamat_kantor","onUpdate:modelValue":e[9]||(e[9]=i=>a(t).alamat_kantor=i),rows:"3",class:_(["w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",{"border-red-500":a(t).errors.alamat_kantor}]),placeholder:"Alamat tempat bekerja (opsional)"},null,2),[[P,a(t).alamat_kantor]]),a(t).errors.alamat_kantor?(d(),u("p",pa,n(a(t).errors.alamat_kantor),1)):p("",!0)])]),l("div",_a,[e[34]||(e[34]=l("h3",{class:"text-lg font-medium"},"Informasi Hakim",-1)),l("div",ka,[l("div",null,[r(a(k),{for:"tipe_hakim"},{default:s(()=>e[29]||(e[29]=[o("Tipe Hakim *")])),_:1,__:[29]}),r(a(x),{modelValue:a(t).tipe_hakim,"onUpdate:modelValue":e[10]||(e[10]=i=>a(t).tipe_hakim=i),required:""},{default:s(()=>[r(a(h),{class:_({"border-red-500":a(t).errors.tipe_hakim})},{default:s(()=>[r(a(b),{placeholder:"Pilih Tipe"})]),_:1},8,["class"]),r(a(v),null,{default:s(()=>[(d(!0),u(q,null,N(f.tipeHakim,(i,D)=>(d(),w(a(y),{key:D,value:D},{default:s(()=>[o(n(i),1)]),_:2},1032,["value"]))),128))]),_:1})]),_:1},8,["modelValue"]),a(t).errors.tipe_hakim?(d(),u("p",fa,n(a(t).errors.tipe_hakim),1)):p("",!0)]),l("div",null,[r(a(k),{for:"id_wilayah"},{default:s(()=>e[30]||(e[30]=[o("Wilayah *")])),_:1,__:[30]}),r(a(x),{modelValue:a(t).id_wilayah,"onUpdate:modelValue":e[11]||(e[11]=i=>a(t).id_wilayah=i),required:""},{default:s(()=>[r(a(h),{class:_({"border-red-500":a(t).errors.id_wilayah})},{default:s(()=>[r(a(b),{placeholder:"Pilih Wilayah"})]),_:1},8,["class"]),r(a(v),null,{default:s(()=>[(d(!0),u(q,null,N(f.wilayah,i=>(d(),w(a(y),{key:i.id_wilayah,value:i.id_wilayah.toString()},{default:s(()=>[o(n(i.nama_wilayah),1)]),_:2},1032,["value"]))),128))]),_:1})]),_:1},8,["modelValue"]),a(t).errors.id_wilayah?(d(),u("p",ga,n(a(t).errors.id_wilayah),1)):p("",!0)]),l("div",null,[r(a(k),{for:"status"},{default:s(()=>e[31]||(e[31]=[o("Status *")])),_:1,__:[31]}),r(a(x),{modelValue:a(t).status,"onUpdate:modelValue":e[12]||(e[12]=i=>a(t).status=i),required:""},{default:s(()=>[r(a(h),{class:_({"border-red-500":a(t).errors.status})},{default:s(()=>[r(a(b),{placeholder:"Pilih Status"})]),_:1},8,["class"]),r(a(v),null,{default:s(()=>[r(a(y),{value:"aktif"},{default:s(()=>e[32]||(e[32]=[o("Aktif")])),_:1,__:[32]}),r(a(y),{value:"non_aktif"},{default:s(()=>e[33]||(e[33]=[o("Non Aktif")])),_:1,__:[33]})]),_:1})]),_:1},8,["modelValue"]),a(t).errors.status?(d(),u("p",wa,n(a(t).errors.status),1)):p("",!0)])])]),l("div",ya,[e[39]||(e[39]=l("h4",{class:"font-medium text-gray-900 mb-2"},"Informasi Saat Ini",-1)),l("div",xa,[l("p",null,[e[35]||(e[35]=l("strong",null,"User Account:",-1)),o(" "+n((H=f.dewaHakim.user)==null?void 0:H.email),1)]),l("p",null,[e[36]||(e[36]=l("strong",null,"Dibuat:",-1)),o(" "+n(V(f.dewaHakim.created_at)),1)]),l("p",null,[e[37]||(e[37]=l("strong",null,"Diperbarui:",-1)),o(" "+n(V(f.dewaHakim.updated_at)),1)]),l("p",null,[e[38]||(e[38]=l("strong",null,"Jumlah Penilaian:",-1)),o(" "+n(((j=f.dewaHakim.nilai_peserta)==null?void 0:j.length)||0)+" penilaian",1)])])]),C.value?(d(),u("div",ha,[l("div",ba,[r(S,{name:"alert-triangle",class:"w-5 h-5 text-yellow-600 mr-2 mt-0.5"}),l("div",null,[e[40]||(e[40]=l("h4",{class:"font-medium text-yellow-800"},"Perhatian",-1)),l("p",va," Dewan hakim ini memiliki "+n((($=f.dewaHakim.nilai_peserta)==null?void 0:$.length)||0)+" data penilaian. Perubahan status dapat mempengaruhi proses penilaian yang sedang berlangsung. ",1)])])])):p("",!0),l("div",Va,[r(I,{type:"button",variant:"outline",onClick:e[13]||(e[13]=i=>f.$inertia.visit(f.route("admin.dewan-hakim.index")))},{default:s(()=>e[41]||(e[41]=[o(" Batal ")])),_:1,__:[41]}),r(I,{type:"submit",disabled:a(t).processing},{default:s(()=>[a(t).processing?(d(),w(S,{key:0,name:"loader-2",class:"w-4 h-4 mr-2 animate-spin"})):p("",!0),e[42]||(e[42]=o(" Simpan Perubahan "))]),_:1,__:[42]},8,["disabled"])])],32)]}),_:1})]),_:1})])]),_:1}))}});export{za as default};
