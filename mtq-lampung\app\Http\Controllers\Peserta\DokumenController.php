<?php

namespace App\Http\Controllers\Peserta;

use App\Http\Controllers\Controller;
use App\Models\DokumenPeserta;
use App\Models\Pendaftaran;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Inertia\Inertia;
use Inertia\Response;

class DokumenController extends Controller
{
    /**
     * Display documents for a specific registration
     */
    public function index(string $pendaftaranId): Response
    {
        $peserta = Auth::user()->peserta;

        $pendaftaran = Pendaftaran::with(['golongan.cabangLomba', 'dokumenPeserta'])
            ->where('id_peserta', $peserta->id_peserta)
            ->findOrFail($pendaftaranId);

        $requiredDocuments = [
            'foto' => 'Foto Peserta',
            'ktp' => 'KTP',
            'kartu_keluarga' => 'Kartu Keluarga',
            'surat_rekomendasi' => 'Surat Rekomendasi',
            'ijazah' => 'Ijazah Terakhir'
        ];

        return Inertia::render('Peserta/Dokumen/Index', [
            'pendaftaran' => $pendaftaran,
            'requiredDocuments' => $requiredDocuments
        ]);
    }

    /**
     * Store a newly uploaded document
     */
    public function store(Request $request, string $pendaftaranId)
    {
        $peserta = Auth::user()->peserta;

        $pendaftaran = Pendaftaran::where('id_peserta', $peserta->id_peserta)
            ->findOrFail($pendaftaranId);

        $validated = $request->validate([
            'jenis_dokumen' => 'required|in:foto,ktp,kartu_keluarga,surat_rekomendasi,ijazah,sertifikat,lainnya',
            'file' => 'required|file|mimes:jpg,jpeg,png,pdf|max:5120', // 5MB max
            'keterangan' => 'nullable|string|max:255'
        ]);

        // Check if document already exists
        $existingDocument = DokumenPeserta::where('id_pendaftaran', $pendaftaran->id_pendaftaran)
            ->where('jenis_dokumen', $validated['jenis_dokumen'])
            ->first();

        if ($existingDocument) {
            return back()->withErrors(['file' => 'Dokumen jenis ini sudah ada. Silakan hapus yang lama terlebih dahulu.']);
        }

        // Store file
        $file = $request->file('file');
        $fileName = $this->generateFileName($file, $validated['jenis_dokumen'], $pendaftaran);
        $filePath = $file->storeAs('dokumen-peserta', $fileName, 'public');

        // Create document record
        DokumenPeserta::create([
            'id_pendaftaran' => $pendaftaran->id_pendaftaran,
            'jenis_dokumen' => $validated['jenis_dokumen'],
            'nama_file' => $fileName,
            'path_file' => $filePath,
            'ukuran_file' => $file->getSize(),
            'mime_type' => $file->getMimeType(),
            'status_verifikasi' => 'pending',
            'uploaded_by' => Auth::id(),
            'keterangan' => $validated['keterangan']
        ]);

        return back()->with('success', 'Dokumen berhasil diupload.');
    }

    /**
     * Download a document
     */
    public function download(string $pendaftaranId, string $dokumentId)
    {
        $peserta = Auth::user()->peserta;

        $pendaftaran = Pendaftaran::where('id_peserta', $peserta->id_peserta)
            ->findOrFail($pendaftaranId);

        $dokumen = DokumenPeserta::where('id_pendaftaran', $pendaftaran->id_pendaftaran)
            ->findOrFail($dokumentId);

        if (!Storage::disk('public')->exists($dokumen->path_file)) {
            abort(404, 'File tidak ditemukan.');
        }

        return response()->download(
            Storage::disk('public')->path($dokumen->path_file),
            $dokumen->nama_file
        );
    }

    /**
     * Delete a document
     */
    public function destroy(string $pendaftaranId, string $dokumentId)
    {
        $peserta = Auth::user()->peserta;

        $pendaftaran = Pendaftaran::where('id_peserta', $peserta->id_peserta)
            ->findOrFail($pendaftaranId);

        $dokumen = DokumenPeserta::where('id_pendaftaran', $pendaftaran->id_pendaftaran)
            ->findOrFail($dokumentId);

        // Check if document can be deleted (not verified yet)
        if ($dokumen->status_verifikasi === 'approved') {
            return back()->withErrors(['error' => 'Dokumen yang sudah diverifikasi tidak dapat dihapus.']);
        }

        // Delete file from storage
        if (Storage::disk('public')->exists($dokumen->path_file)) {
            Storage::disk('public')->delete($dokumen->path_file);
        }

        // Delete record
        $dokumen->delete();

        return back()->with('success', 'Dokumen berhasil dihapus.');
    }

    /**
     * Replace an existing document
     */
    public function replace(Request $request, string $pendaftaranId, string $dokumentId)
    {
        $peserta = Auth::user()->peserta;

        $pendaftaran = Pendaftaran::where('id_peserta', $peserta->id_peserta)
            ->findOrFail($pendaftaranId);

        $dokumen = DokumenPeserta::where('id_pendaftaran', $pendaftaran->id_pendaftaran)
            ->findOrFail($dokumentId);

        // Check if document can be replaced
        if ($dokumen->status_verifikasi === 'approved') {
            return back()->withErrors(['error' => 'Dokumen yang sudah diverifikasi tidak dapat diganti.']);
        }

        $validated = $request->validate([
            'file' => 'required|file|mimes:jpg,jpeg,png,pdf|max:5120', // 5MB max
            'keterangan' => 'nullable|string|max:255'
        ]);

        // Delete old file
        if (Storage::disk('public')->exists($dokumen->path_file)) {
            Storage::disk('public')->delete($dokumen->path_file);
        }

        // Store new file
        $file = $request->file('file');
        $fileName = $this->generateFileName($file, $dokumen->jenis_dokumen, $pendaftaran);
        $filePath = $file->storeAs('dokumen-peserta', $fileName, 'public');

        // Update document record
        $dokumen->update([
            'nama_file' => $fileName,
            'path_file' => $filePath,
            'ukuran_file' => $file->getSize(),
            'mime_type' => $file->getMimeType(),
            'status_verifikasi' => 'pending',
            'keterangan' => $validated['keterangan'],
            'catatan_verifikasi' => null,
            'verified_at' => null,
            'verified_by' => null
        ]);

        return back()->with('success', 'Dokumen berhasil diganti.');
    }

    /**
     * Generate unique filename for uploaded document
     */
    private function generateFileName($file, string $jenisDoc, Pendaftaran $pendaftaran): string
    {
        $extension = $file->getClientOriginalExtension();
        $timestamp = date('YmdHis');
        $random = Str::random(6);

        return "{$pendaftaran->nomor_pendaftaran}_{$jenisDoc}_{$timestamp}_{$random}.{$extension}";
    }
}
