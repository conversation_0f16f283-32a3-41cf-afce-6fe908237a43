<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('mimbar', function (Blueprint $table) {
            $table->id('id_mimbar');
            $table->string('kode_mimbar', 10)->unique();
            $table->string('nama_mimbar', 100);
            $table->text('keterangan')->nullable();
            $table->integer('kapasitas')->default(0);
            $table->enum('status', ['aktif', 'non_aktif'])->default('aktif');
            $table->timestamps();

            $table->index('status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('mimbar');
    }
};
