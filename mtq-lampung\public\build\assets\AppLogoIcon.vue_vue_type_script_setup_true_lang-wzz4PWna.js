import{F as Oe,d as Y,U as Be,E as Ce,_ as Fe,$ as j,c as $e,o as Se,u as te,n as Ze,w as Ue,D as We,h as qe,b as Ke}from"./app-B_pmlBSQ.js";function Ae(e){var t,r,o="";if(typeof e=="string"||typeof e=="number")o+=e;else if(typeof e=="object")if(Array.isArray(e)){var a=e.length;for(t=0;t<a;t++)e[t]&&(r=Ae(e[t]))&&(o&&(o+=" "),o+=r)}else for(r in e)e[r]&&(o&&(o+=" "),o+=r);return o}function Me(){for(var e,t,r=0,o="",a=arguments.length;r<a;r++)(e=arguments[r])&&(t=Ae(e))&&(o&&(o+=" "),o+=t);return o}const ve=e=>typeof e=="boolean"?`${e}`:e===0?"0":e,xe=Me,De=(e,t)=>r=>{var o;if((t==null?void 0:t.variants)==null)return xe(e,r==null?void 0:r.class,r==null?void 0:r.className);const{variants:a,defaultVariants:i}=t,l=Object.keys(a).map(f=>{const g=r==null?void 0:r[f],x=i==null?void 0:i[f];if(g===null)return null;const z=ve(g)||ve(x);return a[f][z]}),p=r&&Object.entries(r).reduce((f,g)=>{let[x,z]=g;return z===void 0||(f[x]=z),f},{}),d=t==null||(o=t.compoundVariants)===null||o===void 0?void 0:o.reduce((f,g)=>{let{class:x,className:z,...P}=g;return Object.entries(P).every(w=>{let[k,C]=w;return Array.isArray(C)?C.includes({...i,...p}[k]):{...i,...p}[k]===C})?[...f,x,z]:f},[]);return xe(e,l,d,r==null?void 0:r.class,r==null?void 0:r.className)},ce="-",He=e=>{const t=Xe(e),{conflictingClassGroups:r,conflictingClassGroupModifiers:o}=e;return{getClassGroupId:l=>{const p=l.split(ce);return p[0]===""&&p.length!==1&&p.shift(),Ie(p,t)||Je(l)},getConflictingClassGroupIds:(l,p)=>{const d=r[l]||[];return p&&o[l]?[...d,...o[l]]:d}}},Ie=(e,t)=>{var l;if(e.length===0)return t.classGroupId;const r=e[0],o=t.nextPart.get(r),a=o?Ie(e.slice(1),o):void 0;if(a)return a;if(t.validators.length===0)return;const i=e.join(ce);return(l=t.validators.find(({validator:p})=>p(i)))==null?void 0:l.classGroupId},we=/^\[(.+)\]$/,Je=e=>{if(we.test(e)){const t=we.exec(e)[1],r=t==null?void 0:t.substring(0,t.indexOf(":"));if(r)return"arbitrary.."+r}},Xe=e=>{const{theme:t,classGroups:r}=e,o={nextPart:new Map,validators:[]};for(const a in r)ae(r[a],o,a,t);return o},ae=(e,t,r,o)=>{e.forEach(a=>{if(typeof a=="string"){const i=a===""?t:ke(t,a);i.classGroupId=r;return}if(typeof a=="function"){if(Qe(a)){ae(a(o),t,r,o);return}t.validators.push({validator:a,classGroupId:r});return}Object.entries(a).forEach(([i,l])=>{ae(l,ke(t,i),r,o)})})},ke=(e,t)=>{let r=e;return t.split(ce).forEach(o=>{r.nextPart.has(o)||r.nextPart.set(o,{nextPart:new Map,validators:[]}),r=r.nextPart.get(o)}),r},Qe=e=>e.isThemeGetter,Ye=e=>{if(e<1)return{get:()=>{},set:()=>{}};let t=0,r=new Map,o=new Map;const a=(i,l)=>{r.set(i,l),t++,t>e&&(t=0,o=r,r=new Map)};return{get(i){let l=r.get(i);if(l!==void 0)return l;if((l=o.get(i))!==void 0)return a(i,l),l},set(i,l){r.has(i)?r.set(i,l):a(i,l)}}},ie="!",le=":",er=le.length,rr=e=>{const{prefix:t,experimentalParseClassName:r}=e;let o=a=>{const i=[];let l=0,p=0,d=0,f;for(let w=0;w<a.length;w++){let k=a[w];if(l===0&&p===0){if(k===le){i.push(a.slice(d,w)),d=w+er;continue}if(k==="/"){f=w;continue}}k==="["?l++:k==="]"?l--:k==="("?p++:k===")"&&p--}const g=i.length===0?a:a.substring(d),x=or(g),z=x!==g,P=f&&f>d?f-d:void 0;return{modifiers:i,hasImportantModifier:z,baseClassName:x,maybePostfixModifierPosition:P}};if(t){const a=t+le,i=o;o=l=>l.startsWith(a)?i(l.substring(a.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:l,maybePostfixModifierPosition:void 0}}if(r){const a=o;o=i=>r({className:i,parseClassName:a})}return o},or=e=>e.endsWith(ie)?e.substring(0,e.length-1):e.startsWith(ie)?e.substring(1):e,tr=e=>{const t=Object.fromEntries(e.orderSensitiveModifiers.map(o=>[o,!0]));return o=>{if(o.length<=1)return o;const a=[];let i=[];return o.forEach(l=>{l[0]==="["||t[l]?(a.push(...i.sort(),l),i=[]):i.push(l)}),a.push(...i.sort()),a}},sr=e=>({cache:Ye(e.cacheSize),parseClassName:rr(e),sortModifiers:tr(e),...He(e)}),nr=/\s+/,ar=(e,t)=>{const{parseClassName:r,getClassGroupId:o,getConflictingClassGroupIds:a,sortModifiers:i}=t,l=[],p=e.trim().split(nr);let d="";for(let f=p.length-1;f>=0;f-=1){const g=p[f],{isExternal:x,modifiers:z,hasImportantModifier:P,baseClassName:w,maybePostfixModifierPosition:k}=r(g);if(x){d=g+(d.length>0?" "+d:d);continue}let C=!!k,R=o(C?w.substring(0,k):w);if(!R){if(!C){d=g+(d.length>0?" "+d:d);continue}if(R=o(w),!R){d=g+(d.length>0?" "+d:d);continue}C=!1}const U=i(z).join(":"),F=P?U+ie:U,T=F+R;if(l.includes(T))continue;l.push(T);const _=a(R,C);for(let V=0;V<_.length;++V){const $=_[V];l.push(F+$)}d=g+(d.length>0?" "+d:d)}return d};function ir(){let e=0,t,r,o="";for(;e<arguments.length;)(t=arguments[e++])&&(r=Pe(t))&&(o&&(o+=" "),o+=r);return o}const Pe=e=>{if(typeof e=="string")return e;let t,r="";for(let o=0;o<e.length;o++)e[o]&&(t=Pe(e[o]))&&(r&&(r+=" "),r+=t);return r};function lr(e,...t){let r,o,a,i=l;function l(d){const f=t.reduce((g,x)=>x(g),e());return r=sr(f),o=r.cache.get,a=r.cache.set,i=p,p(d)}function p(d){const f=o(d);if(f)return f;const g=ar(d,r);return a(d,g),g}return function(){return i(ir.apply(null,arguments))}}const b=e=>{const t=r=>r[e]||[];return t.isThemeGetter=!0,t},Re=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,Ve=/^\((?:(\w[\w-]*):)?(.+)\)$/i,cr=/^\d+\/\d+$/,dr=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,mr=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,ur=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,pr=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,fr=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,E=e=>cr.test(e),u=e=>!!e&&!Number.isNaN(Number(e)),I=e=>!!e&&Number.isInteger(Number(e)),se=e=>e.endsWith("%")&&u(e.slice(0,-1)),M=e=>dr.test(e),gr=()=>!0,br=e=>mr.test(e)&&!ur.test(e),Ge=()=>!1,hr=e=>pr.test(e),vr=e=>fr.test(e),xr=e=>!s(e)&&!n(e),wr=e=>O(e,_e,Ge),s=e=>Re.test(e),N=e=>O(e,Le,br),ne=e=>O(e,Sr,u),ye=e=>O(e,Ne,Ge),kr=e=>O(e,Te,vr),J=e=>O(e,Ee,hr),n=e=>Ve.test(e),Z=e=>B(e,Le),yr=e=>B(e,Ar),ze=e=>B(e,Ne),zr=e=>B(e,_e),Cr=e=>B(e,Te),X=e=>B(e,Ee,!0),O=(e,t,r)=>{const o=Re.exec(e);return o?o[1]?t(o[1]):r(o[2]):!1},B=(e,t,r=!1)=>{const o=Ve.exec(e);return o?o[1]?t(o[1]):r:!1},Ne=e=>e==="position"||e==="percentage",Te=e=>e==="image"||e==="url",_e=e=>e==="length"||e==="size"||e==="bg-size",Le=e=>e==="length",Sr=e=>e==="number",Ar=e=>e==="family-name",Ee=e=>e==="shadow",Mr=()=>{const e=b("color"),t=b("font"),r=b("text"),o=b("font-weight"),a=b("tracking"),i=b("leading"),l=b("breakpoint"),p=b("container"),d=b("spacing"),f=b("radius"),g=b("shadow"),x=b("inset-shadow"),z=b("text-shadow"),P=b("drop-shadow"),w=b("blur"),k=b("perspective"),C=b("aspect"),R=b("ease"),U=b("animate"),F=()=>["auto","avoid","all","avoid-page","page","left","right","column"],T=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],_=()=>[...T(),n,s],V=()=>["auto","hidden","clip","visible","scroll"],$=()=>["auto","contain","none"],m=()=>[n,s,d],S=()=>[E,"full","auto",...m()],de=()=>[I,"none","subgrid",n,s],me=()=>["auto",{span:["full",I,n,s]},I,n,s],W=()=>[I,"auto",n,s],ue=()=>["auto","min","max","fr",n,s],ee=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],L=()=>["start","end","center","stretch","center-safe","end-safe"],A=()=>["auto",...m()],G=()=>[E,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...m()],c=()=>[e,n,s],pe=()=>[...T(),ze,ye,{position:[n,s]}],fe=()=>["no-repeat",{repeat:["","x","y","space","round"]}],ge=()=>["auto","cover","contain",zr,wr,{size:[n,s]}],re=()=>[se,Z,N],v=()=>["","none","full",f,n,s],y=()=>["",u,Z,N],q=()=>["solid","dashed","dotted","double"],be=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],h=()=>[u,se,ze,ye],he=()=>["","none",w,n,s],K=()=>["none",u,n,s],D=()=>["none",u,n,s],oe=()=>[u,n,s],H=()=>[E,"full",...m()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[M],breakpoint:[M],color:[gr],container:[M],"drop-shadow":[M],ease:["in","out","in-out"],font:[xr],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[M],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[M],shadow:[M],spacing:["px",u],text:[M],"text-shadow":[M],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",E,s,n,C]}],container:["container"],columns:[{columns:[u,s,n,p]}],"break-after":[{"break-after":F()}],"break-before":[{"break-before":F()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:_()}],overflow:[{overflow:V()}],"overflow-x":[{"overflow-x":V()}],"overflow-y":[{"overflow-y":V()}],overscroll:[{overscroll:$()}],"overscroll-x":[{"overscroll-x":$()}],"overscroll-y":[{"overscroll-y":$()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:S()}],"inset-x":[{"inset-x":S()}],"inset-y":[{"inset-y":S()}],start:[{start:S()}],end:[{end:S()}],top:[{top:S()}],right:[{right:S()}],bottom:[{bottom:S()}],left:[{left:S()}],visibility:["visible","invisible","collapse"],z:[{z:[I,"auto",n,s]}],basis:[{basis:[E,"full","auto",p,...m()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[u,E,"auto","initial","none",s]}],grow:[{grow:["",u,n,s]}],shrink:[{shrink:["",u,n,s]}],order:[{order:[I,"first","last","none",n,s]}],"grid-cols":[{"grid-cols":de()}],"col-start-end":[{col:me()}],"col-start":[{"col-start":W()}],"col-end":[{"col-end":W()}],"grid-rows":[{"grid-rows":de()}],"row-start-end":[{row:me()}],"row-start":[{"row-start":W()}],"row-end":[{"row-end":W()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":ue()}],"auto-rows":[{"auto-rows":ue()}],gap:[{gap:m()}],"gap-x":[{"gap-x":m()}],"gap-y":[{"gap-y":m()}],"justify-content":[{justify:[...ee(),"normal"]}],"justify-items":[{"justify-items":[...L(),"normal"]}],"justify-self":[{"justify-self":["auto",...L()]}],"align-content":[{content:["normal",...ee()]}],"align-items":[{items:[...L(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...L(),{baseline:["","last"]}]}],"place-content":[{"place-content":ee()}],"place-items":[{"place-items":[...L(),"baseline"]}],"place-self":[{"place-self":["auto",...L()]}],p:[{p:m()}],px:[{px:m()}],py:[{py:m()}],ps:[{ps:m()}],pe:[{pe:m()}],pt:[{pt:m()}],pr:[{pr:m()}],pb:[{pb:m()}],pl:[{pl:m()}],m:[{m:A()}],mx:[{mx:A()}],my:[{my:A()}],ms:[{ms:A()}],me:[{me:A()}],mt:[{mt:A()}],mr:[{mr:A()}],mb:[{mb:A()}],ml:[{ml:A()}],"space-x":[{"space-x":m()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":m()}],"space-y-reverse":["space-y-reverse"],size:[{size:G()}],w:[{w:[p,"screen",...G()]}],"min-w":[{"min-w":[p,"screen","none",...G()]}],"max-w":[{"max-w":[p,"screen","none","prose",{screen:[l]},...G()]}],h:[{h:["screen","lh",...G()]}],"min-h":[{"min-h":["screen","lh","none",...G()]}],"max-h":[{"max-h":["screen","lh",...G()]}],"font-size":[{text:["base",r,Z,N]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[o,n,ne]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",se,s]}],"font-family":[{font:[yr,s,t]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[a,n,s]}],"line-clamp":[{"line-clamp":[u,"none",n,ne]}],leading:[{leading:[i,...m()]}],"list-image":[{"list-image":["none",n,s]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",n,s]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:c()}],"text-color":[{text:c()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...q(),"wavy"]}],"text-decoration-thickness":[{decoration:[u,"from-font","auto",n,N]}],"text-decoration-color":[{decoration:c()}],"underline-offset":[{"underline-offset":[u,"auto",n,s]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:m()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",n,s]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",n,s]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:pe()}],"bg-repeat":[{bg:fe()}],"bg-size":[{bg:ge()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},I,n,s],radial:["",n,s],conic:[I,n,s]},Cr,kr]}],"bg-color":[{bg:c()}],"gradient-from-pos":[{from:re()}],"gradient-via-pos":[{via:re()}],"gradient-to-pos":[{to:re()}],"gradient-from":[{from:c()}],"gradient-via":[{via:c()}],"gradient-to":[{to:c()}],rounded:[{rounded:v()}],"rounded-s":[{"rounded-s":v()}],"rounded-e":[{"rounded-e":v()}],"rounded-t":[{"rounded-t":v()}],"rounded-r":[{"rounded-r":v()}],"rounded-b":[{"rounded-b":v()}],"rounded-l":[{"rounded-l":v()}],"rounded-ss":[{"rounded-ss":v()}],"rounded-se":[{"rounded-se":v()}],"rounded-ee":[{"rounded-ee":v()}],"rounded-es":[{"rounded-es":v()}],"rounded-tl":[{"rounded-tl":v()}],"rounded-tr":[{"rounded-tr":v()}],"rounded-br":[{"rounded-br":v()}],"rounded-bl":[{"rounded-bl":v()}],"border-w":[{border:y()}],"border-w-x":[{"border-x":y()}],"border-w-y":[{"border-y":y()}],"border-w-s":[{"border-s":y()}],"border-w-e":[{"border-e":y()}],"border-w-t":[{"border-t":y()}],"border-w-r":[{"border-r":y()}],"border-w-b":[{"border-b":y()}],"border-w-l":[{"border-l":y()}],"divide-x":[{"divide-x":y()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":y()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...q(),"hidden","none"]}],"divide-style":[{divide:[...q(),"hidden","none"]}],"border-color":[{border:c()}],"border-color-x":[{"border-x":c()}],"border-color-y":[{"border-y":c()}],"border-color-s":[{"border-s":c()}],"border-color-e":[{"border-e":c()}],"border-color-t":[{"border-t":c()}],"border-color-r":[{"border-r":c()}],"border-color-b":[{"border-b":c()}],"border-color-l":[{"border-l":c()}],"divide-color":[{divide:c()}],"outline-style":[{outline:[...q(),"none","hidden"]}],"outline-offset":[{"outline-offset":[u,n,s]}],"outline-w":[{outline:["",u,Z,N]}],"outline-color":[{outline:c()}],shadow:[{shadow:["","none",g,X,J]}],"shadow-color":[{shadow:c()}],"inset-shadow":[{"inset-shadow":["none",x,X,J]}],"inset-shadow-color":[{"inset-shadow":c()}],"ring-w":[{ring:y()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:c()}],"ring-offset-w":[{"ring-offset":[u,N]}],"ring-offset-color":[{"ring-offset":c()}],"inset-ring-w":[{"inset-ring":y()}],"inset-ring-color":[{"inset-ring":c()}],"text-shadow":[{"text-shadow":["none",z,X,J]}],"text-shadow-color":[{"text-shadow":c()}],opacity:[{opacity:[u,n,s]}],"mix-blend":[{"mix-blend":[...be(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":be()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[u]}],"mask-image-linear-from-pos":[{"mask-linear-from":h()}],"mask-image-linear-to-pos":[{"mask-linear-to":h()}],"mask-image-linear-from-color":[{"mask-linear-from":c()}],"mask-image-linear-to-color":[{"mask-linear-to":c()}],"mask-image-t-from-pos":[{"mask-t-from":h()}],"mask-image-t-to-pos":[{"mask-t-to":h()}],"mask-image-t-from-color":[{"mask-t-from":c()}],"mask-image-t-to-color":[{"mask-t-to":c()}],"mask-image-r-from-pos":[{"mask-r-from":h()}],"mask-image-r-to-pos":[{"mask-r-to":h()}],"mask-image-r-from-color":[{"mask-r-from":c()}],"mask-image-r-to-color":[{"mask-r-to":c()}],"mask-image-b-from-pos":[{"mask-b-from":h()}],"mask-image-b-to-pos":[{"mask-b-to":h()}],"mask-image-b-from-color":[{"mask-b-from":c()}],"mask-image-b-to-color":[{"mask-b-to":c()}],"mask-image-l-from-pos":[{"mask-l-from":h()}],"mask-image-l-to-pos":[{"mask-l-to":h()}],"mask-image-l-from-color":[{"mask-l-from":c()}],"mask-image-l-to-color":[{"mask-l-to":c()}],"mask-image-x-from-pos":[{"mask-x-from":h()}],"mask-image-x-to-pos":[{"mask-x-to":h()}],"mask-image-x-from-color":[{"mask-x-from":c()}],"mask-image-x-to-color":[{"mask-x-to":c()}],"mask-image-y-from-pos":[{"mask-y-from":h()}],"mask-image-y-to-pos":[{"mask-y-to":h()}],"mask-image-y-from-color":[{"mask-y-from":c()}],"mask-image-y-to-color":[{"mask-y-to":c()}],"mask-image-radial":[{"mask-radial":[n,s]}],"mask-image-radial-from-pos":[{"mask-radial-from":h()}],"mask-image-radial-to-pos":[{"mask-radial-to":h()}],"mask-image-radial-from-color":[{"mask-radial-from":c()}],"mask-image-radial-to-color":[{"mask-radial-to":c()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":T()}],"mask-image-conic-pos":[{"mask-conic":[u]}],"mask-image-conic-from-pos":[{"mask-conic-from":h()}],"mask-image-conic-to-pos":[{"mask-conic-to":h()}],"mask-image-conic-from-color":[{"mask-conic-from":c()}],"mask-image-conic-to-color":[{"mask-conic-to":c()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:pe()}],"mask-repeat":[{mask:fe()}],"mask-size":[{mask:ge()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",n,s]}],filter:[{filter:["","none",n,s]}],blur:[{blur:he()}],brightness:[{brightness:[u,n,s]}],contrast:[{contrast:[u,n,s]}],"drop-shadow":[{"drop-shadow":["","none",P,X,J]}],"drop-shadow-color":[{"drop-shadow":c()}],grayscale:[{grayscale:["",u,n,s]}],"hue-rotate":[{"hue-rotate":[u,n,s]}],invert:[{invert:["",u,n,s]}],saturate:[{saturate:[u,n,s]}],sepia:[{sepia:["",u,n,s]}],"backdrop-filter":[{"backdrop-filter":["","none",n,s]}],"backdrop-blur":[{"backdrop-blur":he()}],"backdrop-brightness":[{"backdrop-brightness":[u,n,s]}],"backdrop-contrast":[{"backdrop-contrast":[u,n,s]}],"backdrop-grayscale":[{"backdrop-grayscale":["",u,n,s]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[u,n,s]}],"backdrop-invert":[{"backdrop-invert":["",u,n,s]}],"backdrop-opacity":[{"backdrop-opacity":[u,n,s]}],"backdrop-saturate":[{"backdrop-saturate":[u,n,s]}],"backdrop-sepia":[{"backdrop-sepia":["",u,n,s]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":m()}],"border-spacing-x":[{"border-spacing-x":m()}],"border-spacing-y":[{"border-spacing-y":m()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",n,s]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[u,"initial",n,s]}],ease:[{ease:["linear","initial",R,n,s]}],delay:[{delay:[u,n,s]}],animate:[{animate:["none",U,n,s]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[k,n,s]}],"perspective-origin":[{"perspective-origin":_()}],rotate:[{rotate:K()}],"rotate-x":[{"rotate-x":K()}],"rotate-y":[{"rotate-y":K()}],"rotate-z":[{"rotate-z":K()}],scale:[{scale:D()}],"scale-x":[{"scale-x":D()}],"scale-y":[{"scale-y":D()}],"scale-z":[{"scale-z":D()}],"scale-3d":["scale-3d"],skew:[{skew:oe()}],"skew-x":[{"skew-x":oe()}],"skew-y":[{"skew-y":oe()}],transform:[{transform:[n,s,"","none","gpu","cpu"]}],"transform-origin":[{origin:_()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:H()}],"translate-x":[{"translate-x":H()}],"translate-y":[{"translate-y":H()}],"translate-z":[{"translate-z":H()}],"translate-none":["translate-none"],accent:[{accent:c()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:c()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",n,s]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":m()}],"scroll-mx":[{"scroll-mx":m()}],"scroll-my":[{"scroll-my":m()}],"scroll-ms":[{"scroll-ms":m()}],"scroll-me":[{"scroll-me":m()}],"scroll-mt":[{"scroll-mt":m()}],"scroll-mr":[{"scroll-mr":m()}],"scroll-mb":[{"scroll-mb":m()}],"scroll-ml":[{"scroll-ml":m()}],"scroll-p":[{"scroll-p":m()}],"scroll-px":[{"scroll-px":m()}],"scroll-py":[{"scroll-py":m()}],"scroll-ps":[{"scroll-ps":m()}],"scroll-pe":[{"scroll-pe":m()}],"scroll-pt":[{"scroll-pt":m()}],"scroll-pr":[{"scroll-pr":m()}],"scroll-pb":[{"scroll-pb":m()}],"scroll-pl":[{"scroll-pl":m()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",n,s]}],fill:[{fill:["none",...c()]}],"stroke-w":[{stroke:[u,Z,N,ne]}],stroke:[{stroke:["none",...c()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}},Ir=lr(Mr);function Pr(...e){return Ir(Me(e))}function je(e){return e?e.flatMap(t=>t.type===Oe?je(t.children):[t]):[]}const Rr=Y({name:"PrimitiveSlot",inheritAttrs:!1,setup(e,{attrs:t,slots:r}){return()=>{var d;if(!r.default)return null;const o=je(r.default()),a=o.findIndex(f=>f.type!==Be);if(a===-1)return o;const i=o[a];(d=i.props)==null||delete d.ref;const l=i.props?Ce(t,i.props):t,p=Fe({...i,props:{}},l);return o.length===1?p:(o[a]=p,o)}}}),Vr=["area","img","input"],Gr=Y({name:"Primitive",inheritAttrs:!1,props:{asChild:{type:Boolean,default:!1},as:{type:[String,Object],default:"div"}},setup(e,{attrs:t,slots:r}){const o=e.asChild?"template":e.as;return typeof o=="string"&&Vr.includes(o)?()=>j(o,t):o!=="template"?()=>j(e.as,t,{default:r.default}):()=>j(Rr,t,{default:r.default})}});/**
 * @license lucide-vue-next v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Nr=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase();/**
 * @license lucide-vue-next v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var Q={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":2,"stroke-linecap":"round","stroke-linejoin":"round"};/**
 * @license lucide-vue-next v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Tr=({size:e,strokeWidth:t=2,absoluteStrokeWidth:r,color:o,iconNode:a,name:i,class:l,...p},{slots:d})=>j("svg",{...Q,width:e||Q.width,height:e||Q.height,stroke:o||Q.stroke,"stroke-width":r?Number(t)*24/Number(e):t,class:["lucide",`lucide-${Nr(i??"icon")}`],...p},[...a.map(f=>j(...f)),...d.default?[d.default()]:[]]);/**
 * @license lucide-vue-next v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Er=(e,t)=>(r,{slots:o})=>j(Tr,{...r,iconNode:t,name:e},o),jr=Y({__name:"Button",props:{variant:{},size:{},class:{},asChild:{type:Boolean},as:{type:[String,Object,Function],default:"button"}},setup(e){const t=e;return(r,o)=>(Se(),$e(te(Gr),{"data-slot":"button",as:r.as,"as-child":r.asChild,class:Ze(te(Pr)(te(_r)({variant:r.variant,size:r.size}),t.class))},{default:Ue(()=>[We(r.$slots,"default")]),_:3},8,["as","as-child","class"]))}}),_r=De("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}}),Or=Y({inheritAttrs:!1,__name:"AppLogoIcon",props:{className:{}},setup(e){return(t,r)=>(Se(),qe("svg",Ce({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 40 42",class:t.className},t.$attrs),r[0]||(r[0]=[Ke("path",{fill:"currentColor","fill-rule":"evenodd","clip-rule":"evenodd",d:"M17.2 5.633 8.6.855 0 5.633v26.51l16.2 9 16.2-9v-8.442l7.6-4.223V9.856l-8.6-4.777-8.6 4.777V18.3l-5.6 3.111V5.633ZM38 18.301l-5.6 3.11v-6.157l5.6-3.11V18.3Zm-1.06-7.856-5.54 3.078-5.54-3.079 5.54-3.078 5.54 3.079ZM24.8 18.3v-6.157l5.6 3.111v6.158L24.8 18.3Zm-1 1.732 5.54 3.078-13.14 7.302-5.54-3.078 13.14-7.3v-.002Zm-16.2 7.89 7.6 4.222V38.3L2 30.966V7.92l5.6 3.111v16.892ZM8.6 9.3 3.06 6.222 8.6 3.143l5.54 3.08L8.6 9.3Zm21.8 15.51-13.2 7.334V38.3l13.2-7.334v-6.156ZM9.6 11.034l5.6-3.11v14.6l-5.6 3.11v-14.6Z"},null,-1)]),16))}});export{Tr as I,Gr as P,Rr as S,jr as _,Pr as a,De as b,Er as c,Or as d,je as r};
