<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('pelaksanaan', function (Blueprint $table) {
            $table->id('id_pelaksanaan');
            $table->string('nama_event', 200);
            $table->string('tema', 200)->nullable();
            $table->text('deskripsi')->nullable();
            $table->year('tahun');
            $table->date('tanggal_mulai');
            $table->date('tanggal_selesai');
            $table->string('tempat', 200);
            $table->text('alamat_lengkap')->nullable();
            $table->date('batas_pendaftaran');
            $table->decimal('biaya_pendaftaran_default', 10, 2)->default(0);
            $table->enum('status', ['draft', 'aktif', 'selesai', 'dibatalkan'])->default('draft');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('pelaksanaan');
    }
};
