import{d as N,c as h,o,w as i,a as t,b as e,u as s,g as I,i as $,e as r,t as d,h as _,F as D,m as W}from"./app-B_pmlBSQ.js";import{_ as P}from"./AppLayout.vue_vue_type_script_setup_true_lang-BRf8lUiM.js";import{_ as B}from"./Heading.vue_vue_type_script_setup_true_lang-is4VBSCS.js";import{_ as x}from"./AppLogoIcon.vue_vue_type_script_setup_true_lang-wzz4PWna.js";import{_ as w,a as v}from"./CardContent.vue_vue_type_script_setup_true_lang-ClvLNBXC.js";import{_ as p}from"./CardDescription.vue_vue_type_script_setup_true_lang-DboywBIK.js";import{_ as g,a as c}from"./CardTitle.vue_vue_type_script_setup_true_lang-5HO6oSoB.js";import{_ as u}from"./index-CMGr3-bt.js";import{_ as m}from"./Label.vue_vue_type_script_setup_true_lang-rj_fhE7R.js";import{_ as k}from"./Icon.vue_vue_type_script_setup_true_lang-BuYzjckr.js";import"./useForwardExpose-CO14IhkA.js";import"./RovingFocusGroup-lsWyU4xZ.js";import"./check-DtmHjdxf.js";import"./loader-circle-6d8QrWFr.js";import"./sun-CHIV9RJ8.js";const K={class:"max-w-4xl mx-auto space-y-6"},E={class:"flex justify-between items-start"},F={class:"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center"},R={class:"flex gap-2"},T={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},H={class:"space-y-4"},M={class:"text-sm"},q={class:"text-sm"},z={class:"text-sm"},G={class:"text-sm"},J={class:"space-y-4"},O={class:"text-sm"},Q={class:"text-sm"},U={class:"text-sm"},X={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"},Y=["onClick"],Z={class:"flex justify-between items-start mb-2"},aa={class:"font-medium"},ta={class:"text-sm text-gray-500"},ea={class:"text-xs text-gray-400 mt-1"},ia={class:"space-y-3"},la={class:"font-medium"},sa={class:"text-sm text-gray-500"},na={class:"flex gap-2"},da={class:"space-y-3"},ra={class:"font-medium"},oa={class:"text-sm text-gray-500"},ua={class:"flex gap-2"},ma={class:"flex justify-between"},_a={class:"flex gap-2"},Aa=N({__name:"Show",props:{wilayah:{}},setup(L){const C=[{title:"Dashboard",href:"/admin/dashboard"},{title:"Manajemen Wilayah",href:"/admin/wilayah"},{title:"Detail Wilayah",href:`/admin/wilayah/${L.wilayah.id_wilayah}`}],y={provinsi:"Provinsi",kabupaten:"Kabupaten",kota:"Kota"},A=a=>({provinsi:"destructive",kabupaten:"default",kota:"secondary"})[a]||"secondary",b=a=>({aktif:"default",non_aktif:"secondary"})[a]||"secondary",f=a=>({aktif:"Aktif",non_aktif:"Non Aktif"})[a]||a,S=a=>({superadmin:"destructive",admin:"default",admin_daerah:"secondary",dewan_hakim:"outline",peserta:"secondary"})[a]||"secondary",V=a=>({superadmin:"Super Admin",admin:"Admin",admin_daerah:"Admin Daerah",dewan_hakim:"Dewan Hakim",peserta:"Peserta"})[a]||a,j=a=>new Date(a).toLocaleDateString("id-ID",{year:"numeric",month:"long",day:"numeric"});return(a,l)=>(o(),h(P,{breadcrumbs:C},{default:i(()=>[t(s(I),{title:`Detail Wilayah: ${a.wilayah.nama_wilayah}`},null,8,["title"]),t(B,{title:`Detail Wilayah: ${a.wilayah.nama_wilayah}`},null,8,["title"]),e("div",K,[t(s(w),null,{default:i(()=>[t(s(g),null,{default:i(()=>[e("div",E,[e("div",null,[t(s(c),{class:"flex items-center gap-3"},{default:i(()=>[e("div",F,[t(k,{name:"map-pin",class:"w-6 h-6 text-blue-600"})]),r(" "+d(a.wilayah.nama_wilayah),1)]),_:1}),t(s(p),null,{default:i(()=>[r(d(a.wilayah.kode_wilayah)+" • "+d(y[a.wilayah.level_wilayah]||a.wilayah.level_wilayah),1)]),_:1})]),e("div",R,[t(s(u),{variant:A(a.wilayah.level_wilayah)},{default:i(()=>[r(d(y[a.wilayah.level_wilayah]||a.wilayah.level_wilayah),1)]),_:1},8,["variant"]),t(s(u),{variant:b(a.wilayah.status)},{default:i(()=>[r(d(f(a.wilayah.status)),1)]),_:1},8,["variant"])])])]),_:1}),t(s(v),null,{default:i(()=>{var n;return[e("div",T,[e("div",H,[e("div",null,[t(s(m),{class:"text-sm font-medium text-gray-500"},{default:i(()=>l[3]||(l[3]=[r("Kode Wilayah")])),_:1,__:[3]}),e("p",M,d(a.wilayah.kode_wilayah),1)]),e("div",null,[t(s(m),{class:"text-sm font-medium text-gray-500"},{default:i(()=>l[4]||(l[4]=[r("Nama Wilayah")])),_:1,__:[4]}),e("p",q,d(a.wilayah.nama_wilayah),1)]),e("div",null,[t(s(m),{class:"text-sm font-medium text-gray-500"},{default:i(()=>l[5]||(l[5]=[r("Level Wilayah")])),_:1,__:[5]}),e("p",z,d(y[a.wilayah.level_wilayah]||a.wilayah.level_wilayah),1)]),e("div",null,[t(s(m),{class:"text-sm font-medium text-gray-500"},{default:i(()=>l[6]||(l[6]=[r("Status")])),_:1,__:[6]}),e("p",G,d(f(a.wilayah.status)),1)])]),e("div",J,[e("div",null,[t(s(m),{class:"text-sm font-medium text-gray-500"},{default:i(()=>l[7]||(l[7]=[r("Wilayah Induk")])),_:1,__:[7]}),e("p",O,d(((n=a.wilayah.parent)==null?void 0:n.nama_wilayah)||"-"),1)]),e("div",null,[t(s(m),{class:"text-sm font-medium text-gray-500"},{default:i(()=>l[8]||(l[8]=[r("Dibuat")])),_:1,__:[8]}),e("p",Q,d(j(a.wilayah.created_at)),1)]),e("div",null,[t(s(m),{class:"text-sm font-medium text-gray-500"},{default:i(()=>l[9]||(l[9]=[r("Diperbarui")])),_:1,__:[9]}),e("p",U,d(j(a.wilayah.updated_at)),1)])])])]}),_:1})]),_:1}),a.wilayah.children&&a.wilayah.children.length>0?(o(),h(s(w),{key:0},{default:i(()=>[t(s(g),null,{default:i(()=>[t(s(c),null,{default:i(()=>l[10]||(l[10]=[r("Anak Wilayah")])),_:1,__:[10]}),t(s(p),null,{default:i(()=>[r(" Wilayah yang berada di bawah "+d(a.wilayah.nama_wilayah),1)]),_:1})]),_:1}),t(s(v),null,{default:i(()=>[e("div",X,[(o(!0),_(D,null,W(a.wilayah.children,n=>(o(),_("div",{key:n.id_wilayah,class:"border rounded-lg p-4 hover:bg-gray-50 cursor-pointer",onClick:fa=>a.$inertia.visit(a.route("admin.wilayah.show",n.id_wilayah))},[e("div",Z,[e("h4",aa,d(n.nama_wilayah),1),t(s(u),{variant:b(n.status),class:"text-xs"},{default:i(()=>[r(d(f(n.status)),1)]),_:2},1032,["variant"])]),e("p",ta,d(n.kode_wilayah),1),e("p",ea,d(y[n.level_wilayah]||n.level_wilayah),1)],8,Y))),128))])]),_:1})]),_:1})):$("",!0),a.wilayah.users&&a.wilayah.users.length>0?(o(),h(s(w),{key:1},{default:i(()=>[t(s(g),null,{default:i(()=>[t(s(c),null,{default:i(()=>l[11]||(l[11]=[r("Pengguna di Wilayah Ini")])),_:1,__:[11]}),t(s(p),null,{default:i(()=>[r(" Daftar pengguna yang terdaftar di "+d(a.wilayah.nama_wilayah),1)]),_:1})]),_:1}),t(s(v),null,{default:i(()=>[e("div",ia,[(o(!0),_(D,null,W(a.wilayah.users,n=>(o(),_("div",{key:n.id_user,class:"flex justify-between items-center p-3 border rounded-lg"},[e("div",null,[e("div",la,d(n.nama_lengkap),1),e("div",sa,d(n.username)+" • "+d(n.email),1)]),e("div",na,[t(s(u),{variant:S(n.role)},{default:i(()=>[r(d(V(n.role)),1)]),_:2},1032,["variant"]),t(s(u),{variant:b(n.status)},{default:i(()=>[r(d(f(n.status)),1)]),_:2},1032,["variant"])])]))),128))])]),_:1})]),_:1})):$("",!0),a.wilayah.peserta&&a.wilayah.peserta.length>0?(o(),h(s(w),{key:2},{default:i(()=>[t(s(g),null,{default:i(()=>[t(s(c),null,{default:i(()=>l[12]||(l[12]=[r("Peserta dari Wilayah Ini")])),_:1,__:[12]}),t(s(p),null,{default:i(()=>[r(" Daftar peserta yang berasal dari "+d(a.wilayah.nama_wilayah),1)]),_:1})]),_:1}),t(s(v),null,{default:i(()=>[e("div",da,[(o(!0),_(D,null,W(a.wilayah.peserta,n=>(o(),_("div",{key:n.id_peserta,class:"flex justify-between items-center p-3 border rounded-lg"},[e("div",null,[e("div",ra,d(n.nama_lengkap),1),e("div",oa,d(n.nik),1)]),e("div",ua,[t(s(u),{variant:n.jenis_kelamin==="L"?"default":"secondary"},{default:i(()=>[r(d(n.jenis_kelamin==="L"?"Laki-laki":"Perempuan"),1)]),_:2},1032,["variant"]),t(s(u),{variant:n.status_peserta==="approved"?"default":"secondary"},{default:i(()=>[r(d(n.status_peserta),1)]),_:2},1032,["variant"])])]))),128))])]),_:1})]),_:1})):$("",!0),e("div",ma,[t(x,{variant:"outline",onClick:l[0]||(l[0]=n=>a.$inertia.visit(a.route("admin.wilayah.index")))},{default:i(()=>[t(k,{name:"arrow-left",class:"w-4 h-4 mr-2"}),l[13]||(l[13]=r(" Kembali "))]),_:1,__:[13]}),e("div",_a,[t(x,{variant:"outline",onClick:l[1]||(l[1]=n=>a.$inertia.visit(a.route("admin.wilayah.create")))},{default:i(()=>[t(k,{name:"plus",class:"w-4 h-4 mr-2"}),l[14]||(l[14]=r(" Tambah Anak Wilayah "))]),_:1,__:[14]}),t(x,{onClick:l[2]||(l[2]=n=>a.$inertia.visit(a.route("admin.wilayah.edit",a.wilayah.id_wilayah)))},{default:i(()=>[t(k,{name:"edit",class:"w-4 h-4 mr-2"}),l[15]||(l[15]=r(" Edit Wilayah "))]),_:1,__:[15]})])])])]),_:1}))}});export{Aa as default};
