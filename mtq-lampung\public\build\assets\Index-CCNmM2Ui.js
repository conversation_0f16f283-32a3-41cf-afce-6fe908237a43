import{d as F,r as P,l as H,W as V,c as C,o as m,w as s,a,b as i,u as l,g as q,e as r,h as _,F as w,m as x,t as o}from"./app-B_pmlBSQ.js";import{a as G,b as J,c as K,d as S,_ as O}from"./AppLayout.vue_vue_type_script_setup_true_lang-BRf8lUiM.js";import{_ as Q}from"./Heading.vue_vue_type_script_setup_true_lang-is4VBSCS.js";import{_ as p}from"./AppLogoIcon.vue_vue_type_script_setup_true_lang-wzz4PWna.js";import{_ as W,a as A}from"./CardContent.vue_vue_type_script_setup_true_lang-ClvLNBXC.js";import{_ as g}from"./index-CMGr3-bt.js";import{_ as R}from"./Input.vue_vue_type_script_setup_true_lang-DB1-RsTn.js";import{_ as k}from"./Label.vue_vue_type_script_setup_true_lang-rj_fhE7R.js";import{_ as D,a as L,b as j,c as N,d as y}from"./SelectValue.vue_vue_type_script_setup_true_lang-IzlIronZ.js";import{_ as X,a as Y,b as Z,c as aa,d as ea}from"./DialogTitle.vue_vue_type_script_setup_true_lang-MyozO0uv.js";import{_ as ta}from"./DialogFooter.vue_vue_type_script_setup_true_lang-D2zTaokr.js";import{_ as f}from"./Icon.vue_vue_type_script_setup_true_lang-BuYzjckr.js";import{l as sa,_ as la}from"./lodash-Cvgo55ys.js";import"./useForwardExpose-CO14IhkA.js";import"./RovingFocusGroup-lsWyU4xZ.js";import"./check-DtmHjdxf.js";import"./useFormControl-mZRLifGx.js";import"./loader-circle-6d8QrWFr.js";import"./sun-CHIV9RJ8.js";import"./TextLink.vue_vue_type_script_setup_true_lang-CC2Og0lP.js";const ia={class:"space-y-6"},na={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},ra={class:"flex items-end"},oa={class:"flex justify-between items-center"},da={class:"text-sm text-gray-600"},ua={class:"overflow-x-auto"},ma={class:"w-full"},_a={class:"bg-white divide-y divide-gray-200"},fa={class:"px-6 py-4 whitespace-nowrap"},pa={class:"text-sm font-medium text-gray-900"},ca={class:"text-sm text-gray-500"},ya={class:"px-6 py-4 whitespace-nowrap"},ha={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},va={class:"px-6 py-4 whitespace-nowrap"},wa={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500"},xa={class:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium space-x-2"},ga={key:0,class:"flex justify-center py-8"},ka={key:1,class:"text-center py-8 text-gray-500"},$a={key:2,class:"space-y-2 max-h-96 overflow-y-auto"},ba={class:"font-medium"},Va={class:"text-sm text-gray-500"},Ka=F({__name:"Index",props:{wilayah:{},filters:{},levels:{}},setup(U){const I=[{title:"Dashboard",href:"/admin/dashboard"},{title:"Manajemen Wilayah",href:"/admin/wilayah"}],u=P({...U.filters}),d=H({show:!1,parent:null,children:[],loading:!1}),h=sa.debounce(()=>{V.get(route("admin.wilayah.index"),u,{preserveState:!0,replace:!0})},300),B=()=>{u.search="",u.level="",u.status="",h()},M=async n=>{d.value.parent=n,d.value.show=!0,d.value.loading=!0,d.value.children=[];try{const t=await(await fetch(route("admin.wilayah.children",n.id_wilayah))).json();d.value.children=t}catch(e){console.error("Error fetching children:",e)}finally{d.value.loading=!1}},T=n=>{confirm(`Apakah Anda yakin ingin menghapus wilayah ${n.nama_wilayah}?`)&&V.delete(route("admin.wilayah.destroy",n.id_wilayah))},z=n=>({provinsi:"destructive",kabupaten:"default",kota:"secondary"})[n]||"secondary",$=n=>({aktif:"default",non_aktif:"secondary"})[n]||"secondary",b=n=>({aktif:"Aktif",non_aktif:"Non Aktif"})[n]||n,E=n=>new Date(n).toLocaleDateString("id-ID",{year:"numeric",month:"short",day:"numeric"});return(n,e)=>(m(),C(O,{breadcrumbs:I},{default:s(()=>[a(l(q),{title:"Manajemen Wilayah"}),a(Q,{title:"Manajemen Wilayah"}),i("div",ia,[a(l(W),null,{default:s(()=>[a(l(A),{class:"p-6"},{default:s(()=>[i("div",na,[i("div",null,[a(l(k),{for:"search"},{default:s(()=>e[6]||(e[6]=[r("Pencarian")])),_:1,__:[6]}),a(l(R),{id:"search",modelValue:u.search,"onUpdate:modelValue":e[0]||(e[0]=t=>u.search=t),placeholder:"Nama wilayah, kode...",onInput:l(h)},null,8,["modelValue","onInput"])]),i("div",null,[a(l(k),{for:"level"},{default:s(()=>e[7]||(e[7]=[r("Level Wilayah")])),_:1,__:[7]}),a(l(D),{modelValue:u.level,"onUpdate:modelValue":[e[1]||(e[1]=t=>u.level=t),l(h)]},{default:s(()=>[a(l(L),null,{default:s(()=>[a(l(j),{placeholder:"Semua Level"})]),_:1}),a(l(N),null,{default:s(()=>[a(l(y),{value:"all"},{default:s(()=>e[8]||(e[8]=[r("Semua Level")])),_:1,__:[8]}),(m(!0),_(w,null,x(n.levels,(t,c)=>(m(),C(l(y),{key:c,value:c},{default:s(()=>[r(o(t),1)]),_:2},1032,["value"]))),128))]),_:1})]),_:1},8,["modelValue","onUpdate:modelValue"])]),i("div",null,[a(l(k),{for:"status"},{default:s(()=>e[9]||(e[9]=[r("Status")])),_:1,__:[9]}),a(l(D),{modelValue:u.status,"onUpdate:modelValue":[e[2]||(e[2]=t=>u.status=t),l(h)]},{default:s(()=>[a(l(L),null,{default:s(()=>[a(l(j),{placeholder:"Semua Status"})]),_:1}),a(l(N),null,{default:s(()=>[a(l(y),{value:"all"},{default:s(()=>e[10]||(e[10]=[r("Semua Status")])),_:1,__:[10]}),a(l(y),{value:"aktif"},{default:s(()=>e[11]||(e[11]=[r("Aktif")])),_:1,__:[11]}),a(l(y),{value:"non_aktif"},{default:s(()=>e[12]||(e[12]=[r("Non Aktif")])),_:1,__:[12]})]),_:1})]),_:1},8,["modelValue","onUpdate:modelValue"])]),i("div",ra,[a(p,{onClick:B,variant:"outline",class:"w-full"},{default:s(()=>[a(f,{name:"x",class:"w-4 h-4 mr-2"}),e[13]||(e[13]=r(" Clear "))]),_:1,__:[13]})])])]),_:1})]),_:1}),i("div",oa,[i("div",da," Menampilkan "+o(n.wilayah.from)+" - "+o(n.wilayah.to)+" dari "+o(n.wilayah.total)+" wilayah ",1),a(p,{onClick:e[3]||(e[3]=t=>n.$inertia.visit(n.route("admin.wilayah.create")))},{default:s(()=>[a(f,{name:"plus",class:"w-4 h-4 mr-2"}),e[14]||(e[14]=r(" Tambah Wilayah "))]),_:1,__:[14]})]),a(l(W),null,{default:s(()=>[a(l(A),{class:"p-0"},{default:s(()=>[i("div",ua,[i("table",ma,[e[17]||(e[17]=i("thead",{class:"bg-gray-50 border-b"},[i("tr",null,[i("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Wilayah "),i("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Level "),i("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Parent "),i("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Status "),i("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Dibuat "),i("th",{class:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"}," Aksi ")])],-1)),i("tbody",_a,[(m(!0),_(w,null,x(n.wilayah.data,t=>{var c;return m(),_("tr",{key:t.id_wilayah,class:"hover:bg-gray-50"},[i("td",fa,[i("div",null,[i("div",pa,o(t.nama_wilayah),1),i("div",ca,o(t.kode_wilayah),1)])]),i("td",ya,[a(l(g),{variant:z(t.level_wilayah)},{default:s(()=>[r(o(n.levels[t.level_wilayah]||t.level_wilayah),1)]),_:2},1032,["variant"])]),i("td",ha,o(((c=t.parent)==null?void 0:c.nama_wilayah)||"-"),1),i("td",va,[a(l(g),{variant:$(t.status)},{default:s(()=>[r(o(b(t.status)),1)]),_:2},1032,["variant"])]),i("td",wa,o(E(t.created_at)),1),i("td",xa,[a(p,{variant:"ghost",size:"sm",onClick:v=>n.$inertia.visit(n.route("admin.wilayah.show",t.id_wilayah))},{default:s(()=>[a(f,{name:"eye",class:"w-4 h-4"})]),_:2},1032,["onClick"]),a(p,{variant:"ghost",size:"sm",onClick:v=>n.$inertia.visit(n.route("admin.wilayah.edit",t.id_wilayah))},{default:s(()=>[a(f,{name:"edit",class:"w-4 h-4"})]),_:2},1032,["onClick"]),a(l(G),null,{default:s(()=>[a(l(J),{"as-child":""},{default:s(()=>[a(p,{variant:"ghost",size:"sm"},{default:s(()=>[a(f,{name:"more-vertical",class:"w-4 h-4"})]),_:1})]),_:1}),a(l(K),{align:"end"},{default:s(()=>[a(l(S),{onClick:v=>M(t)},{default:s(()=>[a(f,{name:"list",class:"w-4 h-4 mr-2"}),e[15]||(e[15]=r(" Lihat Anak Wilayah "))]),_:2,__:[15]},1032,["onClick"]),a(l(S),{onClick:v=>T(t),class:"text-red-600"},{default:s(()=>[a(f,{name:"trash",class:"w-4 h-4 mr-2"}),e[16]||(e[16]=r(" Hapus "))]),_:2,__:[16]},1032,["onClick"])]),_:2},1024)]),_:2},1024)])])}),128))])])])]),_:1})]),_:1}),a(la,{links:n.wilayah.links},null,8,["links"])]),a(l(X),{open:d.value.show,"onUpdate:open":e[5]||(e[5]=t=>d.value.show=t)},{default:s(()=>[a(l(Y),{class:"max-w-2xl"},{default:s(()=>[a(l(Z),null,{default:s(()=>[a(l(aa),null,{default:s(()=>{var t;return[r("Anak Wilayah: "+o((t=d.value.parent)==null?void 0:t.nama_wilayah),1)]}),_:1}),a(l(ea),null,{default:s(()=>{var t;return[r(" Daftar wilayah yang berada di bawah "+o((t=d.value.parent)==null?void 0:t.nama_wilayah),1)]}),_:1})]),_:1}),d.value.loading?(m(),_("div",ga,[a(f,{name:"loader-2",class:"w-6 h-6 animate-spin"})])):d.value.children.length===0?(m(),_("div",ka," Tidak ada anak wilayah ")):(m(),_("div",$a,[(m(!0),_(w,null,x(d.value.children,t=>(m(),_("div",{key:t.id_wilayah,class:"flex justify-between items-center p-3 border rounded-lg"},[i("div",null,[i("div",ba,o(t.nama_wilayah),1),i("div",Va,o(t.kode_wilayah),1)]),a(l(g),{variant:$(t.status)},{default:s(()=>[r(o(b(t.status)),1)]),_:2},1032,["variant"])]))),128))])),a(l(ta),null,{default:s(()=>[a(p,{onClick:e[4]||(e[4]=t=>d.value.show=!1)},{default:s(()=>e[18]||(e[18]=[r("Tutup")])),_:1,__:[18]})]),_:1})]),_:1})]),_:1},8,["open"])]),_:1}))}});export{Ka as default};
