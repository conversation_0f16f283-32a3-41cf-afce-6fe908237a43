import{d as T,r as H,l as K,x as G,W as x,c as h,o as f,w as s,a as e,b as o,u as t,g as J,e as n,h as c,F as $,m as b,t as i,i as O,f as Q}from"./app-B_pmlBSQ.js";import{a as X,b as Y,c as Z,d as R,_ as ee}from"./AppLayout.vue_vue_type_script_setup_true_lang-BRf8lUiM.js";import{_ as ae}from"./Heading.vue_vue_type_script_setup_true_lang-is4VBSCS.js";import{_}from"./AppLogoIcon.vue_vue_type_script_setup_true_lang-wzz4PWna.js";import{_ as D,a as A}from"./CardContent.vue_vue_type_script_setup_true_lang-ClvLNBXC.js";import{_ as N}from"./index-CMGr3-bt.js";import{_ as V}from"./Input.vue_vue_type_script_setup_true_lang-DB1-RsTn.js";import{_ as v}from"./Label.vue_vue_type_script_setup_true_lang-rj_fhE7R.js";import{_ as S,a as U,b as C,c as P,d as p}from"./SelectValue.vue_vue_type_script_setup_true_lang-IzlIronZ.js";import{_ as te,a as se,b as le,c as oe,d as re}from"./DialogTitle.vue_vue_type_script_setup_true_lang-MyozO0uv.js";import{_ as ne}from"./DialogFooter.vue_vue_type_script_setup_true_lang-D2zTaokr.js";import{_ as w}from"./Icon.vue_vue_type_script_setup_true_lang-BuYzjckr.js";import{l as ie,_ as de}from"./lodash-Cvgo55ys.js";import"./useForwardExpose-CO14IhkA.js";import"./RovingFocusGroup-lsWyU4xZ.js";import"./check-DtmHjdxf.js";import"./useFormControl-mZRLifGx.js";import"./loader-circle-6d8QrWFr.js";import"./sun-CHIV9RJ8.js";import"./TextLink.vue_vue_type_script_setup_true_lang-CC2Og0lP.js";const ue={class:"space-y-6"},me={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},fe={class:"flex justify-between items-center"},pe={class:"text-sm text-gray-600"},_e={class:"overflow-x-auto"},we={class:"w-full"},ve={class:"bg-white divide-y divide-gray-200"},ye={class:"px-6 py-4 whitespace-nowrap"},ce={class:"text-sm font-medium text-gray-900"},ge={class:"text-sm text-gray-500"},ke={key:0,class:"text-sm text-gray-500"},xe={class:"px-6 py-4 whitespace-nowrap"},he={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},$e={class:"px-6 py-4 whitespace-nowrap"},be={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500"},Ve={class:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium space-x-2"},Se={class:"space-y-4"},Je=T({__name:"Index",props:{users:{},filters:{},wilayah:{},roles:{}},setup(B){const W=[{title:"Dashboard",href:"/admin/dashboard"},{title:"Manajemen User",href:"/admin/users"}],d=H({...B.filters}),u=K({show:!1,user:null}),m=G({password:"",password_confirmation:""}),g=ie.debounce(()=>{x.get(route("admin.users.index"),d,{preserveState:!0,replace:!0})},300),j=r=>{x.post(route("admin.users.toggle-status",r.id_user),{},{preserveScroll:!0})},I=r=>{u.value.user=r,u.value.show=!0,m.reset()},M=()=>{u.value.user&&m.post(route("admin.users.reset-password",u.value.user.id_user),{onSuccess:()=>{u.value.show=!1,m.reset()}})},z=r=>{confirm(`Apakah Anda yakin ingin menghapus user ${r.nama_lengkap}?`)&&x.delete(route("admin.users.destroy",r.id_user))},F=r=>({superadmin:"destructive",admin:"default",admin_daerah:"secondary",dewan_hakim:"outline",peserta:"secondary"})[r]||"secondary",L=r=>({aktif:"default",non_aktif:"secondary",suspended:"destructive"})[r]||"secondary",q=r=>({aktif:"Aktif",non_aktif:"Non Aktif",suspended:"Suspended"})[r]||r,E=r=>new Date(r).toLocaleDateString("id-ID",{year:"numeric",month:"short",day:"numeric"});return(r,a)=>(f(),h(ee,{breadcrumbs:W},{default:s(()=>[e(t(J),{title:"Manajemen User"}),e(ae,{title:"Manajemen User"}),o("div",ue,[e(t(D),null,{default:s(()=>[e(t(A),{class:"p-6"},{default:s(()=>[o("div",me,[o("div",null,[e(t(v),{for:"search"},{default:s(()=>a[9]||(a[9]=[n("Pencarian")])),_:1,__:[9]}),e(t(V),{id:"search",modelValue:d.search,"onUpdate:modelValue":a[0]||(a[0]=l=>d.search=l),placeholder:"Nama, username, email...",onInput:t(g)},null,8,["modelValue","onInput"])]),o("div",null,[e(t(v),{for:"role"},{default:s(()=>a[10]||(a[10]=[n("Role")])),_:1,__:[10]}),e(t(S),{modelValue:d.role,"onUpdate:modelValue":[a[1]||(a[1]=l=>d.role=l),t(g)]},{default:s(()=>[e(t(U),null,{default:s(()=>[e(t(C),{placeholder:"Semua Role"})]),_:1}),e(t(P),null,{default:s(()=>[e(t(p),{value:"all"},{default:s(()=>a[11]||(a[11]=[n("Semua Role")])),_:1,__:[11]}),(f(!0),c($,null,b(r.roles,(l,y)=>(f(),h(t(p),{key:y,value:y},{default:s(()=>[n(i(l),1)]),_:2},1032,["value"]))),128))]),_:1})]),_:1},8,["modelValue","onUpdate:modelValue"])]),o("div",null,[e(t(v),{for:"status"},{default:s(()=>a[12]||(a[12]=[n("Status")])),_:1,__:[12]}),e(t(S),{modelValue:d.status,"onUpdate:modelValue":[a[2]||(a[2]=l=>d.status=l),t(g)]},{default:s(()=>[e(t(U),null,{default:s(()=>[e(t(C),{placeholder:"Semua Status"})]),_:1}),e(t(P),null,{default:s(()=>[e(t(p),{value:"all"},{default:s(()=>a[13]||(a[13]=[n("Semua Status")])),_:1,__:[13]}),e(t(p),{value:"aktif"},{default:s(()=>a[14]||(a[14]=[n("Aktif")])),_:1,__:[14]}),e(t(p),{value:"non_aktif"},{default:s(()=>a[15]||(a[15]=[n("Non Aktif")])),_:1,__:[15]}),e(t(p),{value:"suspended"},{default:s(()=>a[16]||(a[16]=[n("Suspended")])),_:1,__:[16]})]),_:1})]),_:1},8,["modelValue","onUpdate:modelValue"])]),o("div",null,[e(t(v),{for:"wilayah"},{default:s(()=>a[17]||(a[17]=[n("Wilayah")])),_:1,__:[17]}),e(t(S),{modelValue:d.wilayah,"onUpdate:modelValue":[a[3]||(a[3]=l=>d.wilayah=l),t(g)]},{default:s(()=>[e(t(U),null,{default:s(()=>[e(t(C),{placeholder:"Semua Wilayah"})]),_:1}),e(t(P),null,{default:s(()=>[e(t(p),{value:"all"},{default:s(()=>a[18]||(a[18]=[n("Semua Wilayah")])),_:1,__:[18]}),(f(!0),c($,null,b(r.wilayah,l=>(f(),h(t(p),{key:l.id_wilayah,value:l.id_wilayah.toString()},{default:s(()=>[n(i(l.nama_wilayah),1)]),_:2},1032,["value"]))),128))]),_:1})]),_:1},8,["modelValue","onUpdate:modelValue"])])])]),_:1})]),_:1}),o("div",fe,[o("div",pe," Menampilkan "+i(r.users.from)+" - "+i(r.users.to)+" dari "+i(r.users.total)+" user ",1),e(_,{onClick:a[4]||(a[4]=l=>r.$inertia.visit(r.route("admin.users.create")))},{default:s(()=>[e(w,{name:"plus",class:"w-4 h-4 mr-2"}),a[19]||(a[19]=n(" Tambah User "))]),_:1,__:[19]})]),e(t(D),null,{default:s(()=>[e(t(A),{class:"p-0"},{default:s(()=>[o("div",_e,[o("table",we,[a[22]||(a[22]=o("thead",{class:"bg-gray-50 border-b"},[o("tr",null,[o("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," User "),o("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Role "),o("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Wilayah "),o("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Status "),o("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Dibuat "),o("th",{class:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"}," Aksi ")])],-1)),o("tbody",ve,[(f(!0),c($,null,b(r.users.data,l=>{var y;return f(),c("tr",{key:l.id_user,class:"hover:bg-gray-50"},[o("td",ye,[o("div",null,[o("div",ce,i(l.nama_lengkap),1),o("div",ge,i(l.username)+" • "+i(l.email),1),l.no_telepon?(f(),c("div",ke,i(l.no_telepon),1)):O("",!0)])]),o("td",xe,[e(t(N),{variant:F(l.role)},{default:s(()=>[n(i(r.roles[l.role]||l.role),1)]),_:2},1032,["variant"])]),o("td",he,i(((y=l.wilayah)==null?void 0:y.nama_wilayah)||"-"),1),o("td",$e,[e(t(N),{variant:L(l.status)},{default:s(()=>[n(i(q(l.status)),1)]),_:2},1032,["variant"])]),o("td",be,i(E(l.created_at)),1),o("td",Ve,[e(_,{variant:"ghost",size:"sm",onClick:k=>r.$inertia.visit(r.route("admin.users.show",l.id_user))},{default:s(()=>[e(w,{name:"eye",class:"w-4 h-4"})]),_:2},1032,["onClick"]),e(_,{variant:"ghost",size:"sm",onClick:k=>r.$inertia.visit(r.route("admin.users.edit",l.id_user))},{default:s(()=>[e(w,{name:"edit",class:"w-4 h-4"})]),_:2},1032,["onClick"]),e(_,{variant:"ghost",size:"sm",onClick:k=>j(l),disabled:l.role==="superadmin"},{default:s(()=>[e(w,{name:l.status==="aktif"?"user-x":"user-check",class:"w-4 h-4"},null,8,["name"])]),_:2},1032,["onClick","disabled"]),e(t(X),null,{default:s(()=>[e(t(Y),{"as-child":""},{default:s(()=>[e(_,{variant:"ghost",size:"sm"},{default:s(()=>[e(w,{name:"more-vertical",class:"w-4 h-4"})]),_:1})]),_:1}),e(t(Z),{align:"end"},{default:s(()=>[e(t(R),{onClick:k=>I(l)},{default:s(()=>[e(w,{name:"key",class:"w-4 h-4 mr-2"}),a[20]||(a[20]=n(" Reset Password "))]),_:2,__:[20]},1032,["onClick"]),e(t(R),{onClick:k=>z(l),disabled:l.role==="superadmin",class:"text-red-600"},{default:s(()=>[e(w,{name:"trash",class:"w-4 h-4 mr-2"}),a[21]||(a[21]=n(" Hapus "))]),_:2,__:[21]},1032,["onClick","disabled"])]),_:2},1024)]),_:2},1024)])])}),128))])])])]),_:1})]),_:1}),e(de,{links:r.users.links},null,8,["links"])]),e(t(te),{open:u.value.show,"onUpdate:open":a[8]||(a[8]=l=>u.value.show=l)},{default:s(()=>[e(t(se),null,{default:s(()=>[e(t(le),null,{default:s(()=>[e(t(oe),null,{default:s(()=>a[23]||(a[23]=[n("Reset Password")])),_:1,__:[23]}),e(t(re),null,{default:s(()=>{var l;return[n(" Reset password untuk user: "+i((l=u.value.user)==null?void 0:l.nama_lengkap),1)]}),_:1})]),_:1}),o("form",{onSubmit:Q(M,["prevent"])},[o("div",Se,[o("div",null,[e(t(v),{for:"password"},{default:s(()=>a[24]||(a[24]=[n("Password Baru")])),_:1,__:[24]}),e(t(V),{id:"password",modelValue:t(m).password,"onUpdate:modelValue":a[5]||(a[5]=l=>t(m).password=l),type:"password",required:""},null,8,["modelValue"])]),o("div",null,[e(t(v),{for:"password_confirmation"},{default:s(()=>a[25]||(a[25]=[n("Konfirmasi Password")])),_:1,__:[25]}),e(t(V),{id:"password_confirmation",modelValue:t(m).password_confirmation,"onUpdate:modelValue":a[6]||(a[6]=l=>t(m).password_confirmation=l),type:"password",required:""},null,8,["modelValue"])])]),e(t(ne),{class:"mt-6"},{default:s(()=>[e(_,{type:"button",variant:"outline",onClick:a[7]||(a[7]=l=>u.value.show=!1)},{default:s(()=>a[26]||(a[26]=[n(" Batal ")])),_:1,__:[26]}),e(_,{type:"submit",disabled:t(m).processing},{default:s(()=>a[27]||(a[27]=[n(" Reset Password ")])),_:1,__:[27]},8,["disabled"])]),_:1})],32)]),_:1})]),_:1},8,["open"])]),_:1}))}});export{Je as default};
