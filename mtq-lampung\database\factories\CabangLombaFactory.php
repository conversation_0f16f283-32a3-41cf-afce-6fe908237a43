<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\CabangLomba>
 */
class CabangLombaFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $cabangLomba = [
            ['kode' => 'TIL', 'nama' => 'Tilawatil Quran', 'deskripsi' => 'Seni membaca Al-Quran dengan tartil'],
            ['kode' => 'TAH', 'nama' => 'Tahfidzul Quran', 'deskripsi' => '<PERSON><PERSON><PERSON> Al-Quran'],
            ['kode' => 'FAH', 'nama' => 'Fahmil Quran', 'deskripsi' => 'Pemahaman isi Al-Quran'],
            ['kode' => 'SYA', 'nama' => 'Syarhil Quran', 'deskripsi' => 'Tafsir Al-Quran'],
            ['kode' => 'KAL', 'nama' => 'Kaligra<PERSON>', 'deskripsi' => 'Seni tulis Arab'],
            ['kode' => 'NAS', 'nama' => 'Nasyid', 'deskripsi' => 'Seni musik Islami'],
            ['kode' => 'CER', 'nama' => 'Ceramah', 'deskripsi' => 'Dakwah dan khutbah'],
            ['kode' => 'QIS', 'nama' => 'Qiraatul Kutub', 'deskripsi' => 'Pembacaan kitab kuning'],
        ];

        $selected = fake()->randomElement($cabangLomba);

        return [
            'kode_cabang' => $selected['kode'],
            'nama_cabang' => $selected['nama'],
            'deskripsi' => $selected['deskripsi'],
            'status' => 'aktif',
        ];
    }

    /**
     * Create an active cabang lomba.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'aktif',
        ]);
    }

    /**
     * Create a non-active cabang lomba.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'non_aktif',
        ]);
    }
}
