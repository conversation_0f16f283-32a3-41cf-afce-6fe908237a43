import{d as F,x as V,l as N,c as f,o as d,w as s,a as t,b as e,u as n,g as T,i as g,e as l,t as o,h as p,F as M,m as S,n as w,f as I}from"./app-B_pmlBSQ.js";import{_ as K}from"./AppLayout.vue_vue_type_script_setup_true_lang-BRf8lUiM.js";import{_ as U}from"./Heading.vue_vue_type_script_setup_true_lang-is4VBSCS.js";import{_ as b}from"./AppLogoIcon.vue_vue_type_script_setup_true_lang-wzz4PWna.js";import{_ as z}from"./Label.vue_vue_type_script_setup_true_lang-rj_fhE7R.js";import{_ as Y}from"./Textarea.vue_vue_type_script_setup_true_lang-jqOjZ7RT.js";import{_ as k,a as v}from"./CardContent.vue_vue_type_script_setup_true_lang-ClvLNBXC.js";import{_ as c}from"./CardDescription.vue_vue_type_script_setup_true_lang-DboywBIK.js";import{_ as y,a as x}from"./CardTitle.vue_vue_type_script_setup_true_lang-5HO6oSoB.js";import{_ as B}from"./index-CMGr3-bt.js";import{_ as E,a as J}from"./index-Cae_Ab9-.js";import{_ as O}from"./InputError.vue_vue_type_script_setup_true_lang-A-vdzbq9.js";import{_ as $}from"./Icon.vue_vue_type_script_setup_true_lang-BuYzjckr.js";import"./useForwardExpose-CO14IhkA.js";import"./RovingFocusGroup-lsWyU4xZ.js";import"./check-DtmHjdxf.js";import"./loader-circle-6d8QrWFr.js";import"./sun-CHIV9RJ8.js";const R={class:"flex items-center space-x-4"},q={class:"space-y-6"},H={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},Q={class:"text-lg"},W={class:"text-lg"},X={class:"text-lg"},Z={key:0,class:"text-center py-8"},aa={key:1,class:"grid grid-cols-1 lg:grid-cols-2 gap-4"},ea=["onClick"],ta={class:"flex items-center justify-between mb-2"},na={class:"font-medium"},sa={class:"text-sm text-gray-600 mb-2"},la={class:"text-sm space-y-1"},oa={class:"font-medium text-green-600"},ia={class:"bg-blue-50 p-4 rounded-lg"},ra={class:"flex items-center justify-between mb-3"},ua={class:"text-lg font-semibold"},da={class:"text-gray-700 mb-3"},ma={class:"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm"},_a={class:"text-lg font-bold text-green-600"},fa={class:"grid gap-2"},ga={class:"flex justify-end space-x-4 pt-6 border-t"},Fa=F({__name:"Create",props:{golongan:{},availableGolongan:{},peserta:{}},setup(j){var P;const D=j,m=V({id_golongan:((P=D.golongan)==null?void 0:P.id_golongan)||"",keterangan:""}),u=N(D.golongan);function C(i){u.value=i,m.id_golongan=i.id_golongan}function G(){m.post(route("peserta.pendaftaran.store"))}function L(i){return new Intl.NumberFormat("id-ID",{style:"currency",currency:"IDR",minimumFractionDigits:0}).format(i)}function A(i){const a=new Date,r=new Date(i);let _=a.getFullYear()-r.getFullYear();const h=a.getMonth()-r.getMonth();return(h<0||h===0&&a.getDate()<r.getDate())&&_--,_}return(i,a)=>(d(),f(K,null,{header:s(()=>[e("div",R,[t(b,{as:"link",href:i.route("peserta.pendaftaran.index"),variant:"ghost",size:"sm"},{default:s(()=>[t($,{name:"arrow-left",class:"w-4 h-4 mr-2"}),a[1]||(a[1]=l(" Kembali "))]),_:1,__:[1]},8,["href"]),t(U,null,{default:s(()=>a[2]||(a[2]=[l("Daftar Lomba Baru")])),_:1,__:[2]})])]),default:s(()=>[t(n(T),{title:"Daftar Lomba Baru"}),e("div",q,[t(n(k),null,{default:s(()=>[t(n(y),null,{default:s(()=>[t(n(x),null,{default:s(()=>a[3]||(a[3]=[l("Data Peserta")])),_:1,__:[3]}),t(n(c),null,{default:s(()=>a[4]||(a[4]=[l("Informasi peserta yang akan didaftarkan")])),_:1,__:[4]})]),_:1}),t(n(v),null,{default:s(()=>[e("div",H,[e("div",null,[a[5]||(a[5]=e("p",{class:"text-sm font-medium text-gray-500"},"Nama Lengkap",-1)),e("p",Q,o(i.peserta.nama_lengkap),1)]),e("div",null,[a[6]||(a[6]=e("p",{class:"text-sm font-medium text-gray-500"},"Jenis Kelamin",-1)),e("p",W,o(i.peserta.jenis_kelamin==="L"?"Laki-laki":"Perempuan"),1)]),e("div",null,[a[7]||(a[7]=e("p",{class:"text-sm font-medium text-gray-500"},"Usia",-1)),e("p",X,o(A(i.peserta.tanggal_lahir))+" tahun",1)])])]),_:1})]),_:1}),i.golongan?g("",!0):(d(),f(n(k),{key:0},{default:s(()=>[t(n(y),null,{default:s(()=>[t(n(x),null,{default:s(()=>a[8]||(a[8]=[l("Pilih Golongan")])),_:1,__:[8]}),t(n(c),null,{default:s(()=>a[9]||(a[9]=[l("Pilih golongan lomba yang sesuai dengan kriteria Anda")])),_:1,__:[9]})]),_:1}),t(n(v),null,{default:s(()=>[i.availableGolongan.length===0?(d(),p("div",Z,[t($,{name:"alert-circle",class:"h-12 w-12 text-gray-400 mx-auto mb-4"}),a[11]||(a[11]=e("h3",{class:"text-lg font-medium text-gray-900 mb-2"},"Tidak Ada Golongan yang Tersedia",-1)),a[12]||(a[12]=e("p",{class:"text-gray-600 mb-4"},"Tidak ada golongan lomba yang sesuai dengan profil Anda saat ini.",-1)),t(b,{as:"link",href:i.route("competition.index"),variant:"outline"},{default:s(()=>a[10]||(a[10]=[l(" Lihat Semua Lomba ")])),_:1,__:[10]},8,["href"])])):(d(),p("div",aa,[(d(!0),p(M,null,S(i.availableGolongan,r=>{var _;return d(),p("div",{key:r.id_golongan,onClick:h=>C(r),class:w(["p-4 border rounded-lg cursor-pointer transition-all hover:border-blue-500 hover:shadow-md",((_=u.value)==null?void 0:_.id_golongan)===r.id_golongan?"border-blue-500 bg-blue-50":""])},[e("div",ta,[e("h4",na,o(r.nama_golongan),1),t(n(B),{class:w(r.jenis_kelamin==="L"?"bg-blue-100 text-blue-800":"bg-pink-100 text-pink-800")},{default:s(()=>[l(o(r.jenis_kelamin==="L"?"Putra":"Putri"),1)]),_:2},1032,["class"])]),e("p",sa,o(r.cabang_lomba.nama_cabang),1),e("div",la,[e("p",null,"Usia: "+o(r.batas_umur_min)+" - "+o(r.batas_umur_max)+" tahun",1),e("p",oa,o(L(r.biaya_pendaftaran)),1)])],10,ea)}),128))]))]),_:1})]),_:1})),u.value?(d(),f(n(k),{key:1},{default:s(()=>[t(n(y),null,{default:s(()=>[t(n(x),null,{default:s(()=>a[13]||(a[13]=[l("Golongan Terpilih")])),_:1,__:[13]}),t(n(c),null,{default:s(()=>a[14]||(a[14]=[l("Detail golongan yang akan Anda ikuti")])),_:1,__:[14]})]),_:1}),t(n(v),null,{default:s(()=>[e("div",ia,[e("div",ra,[e("h3",ua,o(u.value.nama_golongan),1),t(n(B),{class:w(u.value.jenis_kelamin==="L"?"bg-blue-100 text-blue-800":"bg-pink-100 text-pink-800")},{default:s(()=>[l(o(u.value.jenis_kelamin==="L"?"Putra":"Putri"),1)]),_:1},8,["class"])]),e("p",da,o(u.value.cabang_lomba.nama_cabang),1),e("div",ma,[e("div",null,[a[15]||(a[15]=e("p",{class:"font-medium"},"Batas Usia",-1)),e("p",null,o(u.value.batas_umur_min)+" - "+o(u.value.batas_umur_max)+" tahun",1)]),e("div",null,[a[16]||(a[16]=e("p",{class:"font-medium"},"Kuota Maksimal",-1)),e("p",null,o(u.value.kuota_max)+" peserta",1)]),e("div",null,[a[17]||(a[17]=e("p",{class:"font-medium"},"Biaya Pendaftaran",-1)),e("p",_a,o(L(u.value.biaya_pendaftaran)),1)])])])]),_:1})]),_:1})):g("",!0),u.value?(d(),f(n(k),{key:2},{default:s(()=>[t(n(y),null,{default:s(()=>[t(n(x),null,{default:s(()=>a[18]||(a[18]=[l("Form Pendaftaran")])),_:1,__:[18]}),t(n(c),null,{default:s(()=>a[19]||(a[19]=[l("Lengkapi informasi pendaftaran Anda")])),_:1,__:[19]})]),_:1}),t(n(v),null,{default:s(()=>[e("form",{onSubmit:I(G,["prevent"]),class:"space-y-6"},[e("div",fa,[t(n(z),{for:"keterangan"},{default:s(()=>a[20]||(a[20]=[l("Keterangan (Opsional)")])),_:1,__:[20]}),t(n(Y),{id:"keterangan",modelValue:n(m).keterangan,"onUpdate:modelValue":a[0]||(a[0]=r=>n(m).keterangan=r),placeholder:"Tambahkan keterangan atau catatan khusus untuk pendaftaran ini...",rows:"4"},null,8,["modelValue"]),t(O,{message:n(m).errors.keterangan},null,8,["message"])]),t(n(E),null,{default:s(()=>[t($,{name:"info",class:"h-4 w-4"}),t(n(J),null,{default:s(()=>a[21]||(a[21]=[l(" Setelah mendaftar, Anda perlu melengkapi dokumen yang diperlukan dan melakukan pembayaran untuk menyelesaikan proses pendaftaran. ")])),_:1,__:[21]})]),_:1}),e("div",ga,[t(b,{as:"link",href:i.route("peserta.pendaftaran.index"),variant:"outline"},{default:s(()=>a[22]||(a[22]=[l(" Batal ")])),_:1,__:[22]},8,["href"]),t(b,{type:"submit",disabled:n(m).processing||!u.value},{default:s(()=>[n(m).processing?(d(),f($,{key:0,name:"loader-2",class:"w-4 h-4 mr-2 animate-spin"})):g("",!0),l(" "+o(n(m).processing?"Mendaftar...":"Daftar Sekarang"),1)]),_:1},8,["disabled"])])],32)]),_:1})]),_:1})):g("",!0)])]),_:1}))}});export{Fa as default};
