import{d as o,c as a,o as r,u as t,Z as n,w as s,D as d}from"./app-B_pmlBSQ.js";const h=o({__name:"TextLink",props:{href:{},tabindex:{},method:{},as:{}},setup(i){return(e,u)=>(r(),a(t(n),{href:e.href,tabindex:e.tabindex,method:e.method,as:e.as,class:"text-foreground decoration-neutral-300 underline-offset-4 transition-colors duration-300 ease-out hover:decoration-current! dark:decoration-neutral-500"},{default:s(()=>[d(e.$slots,"default")]),_:3},8,["href","tabindex","method","as"]))}});export{h as _};
