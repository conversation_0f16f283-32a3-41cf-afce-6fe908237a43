import{d as N,x as $,c as y,o as p,w as r,a as s,b as t,u as l,g as q,e as i,f as I,j as v,n as k,q as h,v as T,h as V,F as K,m as B,t as j}from"./app-B_pmlBSQ.js";import{_ as L}from"./AppLayout.vue_vue_type_script_setup_true_lang-BRf8lUiM.js";import{_ as A}from"./Heading.vue_vue_type_script_setup_true_lang-is4VBSCS.js";import{_ as w}from"./AppLogoIcon.vue_vue_type_script_setup_true_lang-wzz4PWna.js";import{_ as f,a as _}from"./CardContent.vue_vue_type_script_setup_true_lang-ClvLNBXC.js";import{_ as x,a as g}from"./CardTitle.vue_vue_type_script_setup_true_lang-5HO6oSoB.js";import{_ as c}from"./CardDescription.vue_vue_type_script_setup_true_lang-DboywBIK.js";import{_ as m}from"./Input.vue_vue_type_script_setup_true_lang-DB1-RsTn.js";import{_ as n}from"./Label.vue_vue_type_script_setup_true_lang-rj_fhE7R.js";import{_ as d}from"./InputError.vue_vue_type_script_setup_true_lang-A-vdzbq9.js";import{_ as u}from"./Icon.vue_vue_type_script_setup_true_lang-BuYzjckr.js";import{_ as D}from"./TextLink.vue_vue_type_script_setup_true_lang-CC2Og0lP.js";import"./useForwardExpose-CO14IhkA.js";import"./RovingFocusGroup-lsWyU4xZ.js";import"./check-DtmHjdxf.js";import"./loader-circle-6d8QrWFr.js";import"./sun-CHIV9RJ8.js";const M={class:"space-y-4"},S={class:"flex items-center space-x-3"},C={class:"max-w-5xl mx-auto"},E={class:"grid grid-cols-1 lg:grid-cols-2 gap-4"},F={class:"flex items-center space-x-2"},J={class:"w-6 h-6 bg-primary/10 rounded-full flex items-center justify-center"},z={class:"space-y-1"},W={class:"space-y-1"},G={class:"space-y-1"},H={class:"flex items-center space-x-2"},O={class:"w-6 h-6 bg-blue-500/10 rounded-full flex items-center justify-center"},Q={class:"space-y-1"},R={class:"space-y-1"},X={class:"grid grid-cols-2 gap-3"},Y={class:"space-y-1"},Z={class:"space-y-1"},ee={class:"space-y-1"},se={class:"flex items-center space-x-2"},ae={class:"w-6 h-6 bg-green-500/10 rounded-full flex items-center justify-center"},le={class:"space-y-1"},te={class:"grid grid-cols-1 md:grid-cols-2 gap-3"},re={class:"space-y-1"},oe=["value"],ie={class:"space-y-1"},ne={class:"grid grid-cols-1 lg:grid-cols-2 gap-4"},de={class:"flex items-center space-x-2"},me={class:"w-6 h-6 bg-purple-500/10 rounded-full flex items-center justify-center"},ue={class:"space-y-1"},pe={class:"space-y-1"},fe={class:"flex items-center space-x-2"},_e={class:"w-6 h-6 bg-orange-500/10 rounded-full flex items-center justify-center"},xe={class:"space-y-1"},ge={class:"space-y-1"},ce={class:"flex flex-col sm:flex-row justify-between items-center gap-4"},be={class:"flex flex-col sm:flex-row gap-3 w-full sm:w-auto"},De=N({__name:"Create",props:{wilayah:{}},setup(ye){const U=[{title:"Manajemen Peserta",href:"/admin/peserta"},{title:"Tambah Peserta Baru",href:"/admin/peserta/create"}],a=$({username:"",email:"",password:"",nik:"",nama_lengkap:"",tempat_lahir:"",tanggal_lahir:"",jenis_kelamin:"",alamat:"",id_wilayah:"",no_telepon:"",nama_ayah:"",nama_ibu:"",pekerjaan:"",instansi_asal:""});function P(){a.post(route("admin.peserta.store"),{onSuccess:()=>{}})}return(b,e)=>(p(),y(L,{breadcrumbs:U},{default:r(()=>[s(l(q),{title:"Tambah Peserta Baru"}),t("div",M,[t("div",S,[s(w,{"as-child":"",class:"text-muted-foreground bg-gray-100 cursor-pointer hover:text-foreground"},{default:r(()=>[s(D,{href:b.route("admin.peserta.index")},{default:r(()=>[s(u,{name:"arrowLeft",class:"w-4 h-4 mr-2"}),e[15]||(e[15]=i(" Kembali "))]),_:1,__:[15]},8,["href"])]),_:1}),t("div",null,[s(A,{title:"Tambah Peserta Baru",description:"Lengkapi informasi peserta untuk mendaftarkan anggota baru"})])]),t("div",C,[t("form",{onSubmit:I(P,["prevent"]),class:"space-y-4"},[t("div",E,[s(f,null,{default:r(()=>[s(x,{class:"pb-3"},{default:r(()=>[t("div",F,[t("div",J,[s(u,{name:"user",class:"w-3 h-3 text-primary"})]),t("div",null,[s(g,{class:"text-base"},{default:r(()=>e[16]||(e[16]=[i("Informasi Akun")])),_:1,__:[16]}),s(c,{class:"text-xs"},{default:r(()=>e[17]||(e[17]=[i(" Data login dan autentikasi ")])),_:1,__:[17]})])])]),_:1}),s(_,{class:"space-y-3"},{default:r(()=>[t("div",z,[s(n,{for:"username",class:"text-xs font-medium"},{default:r(()=>e[18]||(e[18]=[i(" Username "),t("span",{class:"text-red-500"},"*",-1)])),_:1,__:[18]}),s(m,{id:"username",modelValue:l(a).username,"onUpdate:modelValue":e[0]||(e[0]=o=>l(a).username=o),type:"text",error:!!l(a).errors.username,placeholder:"Username unik",class:"h-8 text-sm",required:""},null,8,["modelValue","error"]),s(d,{message:l(a).errors.username},null,8,["message"])]),t("div",W,[s(n,{for:"email",class:"text-xs font-medium"},{default:r(()=>e[19]||(e[19]=[i(" Email "),t("span",{class:"text-red-500"},"*",-1)])),_:1,__:[19]}),s(m,{id:"email",modelValue:l(a).email,"onUpdate:modelValue":e[1]||(e[1]=o=>l(a).email=o),type:"email",error:!!l(a).errors.email,placeholder:"<EMAIL>",class:"h-8 text-sm",required:""},null,8,["modelValue","error"]),s(d,{message:l(a).errors.email},null,8,["message"])]),t("div",G,[s(n,{for:"password",class:"text-xs font-medium"},{default:r(()=>e[20]||(e[20]=[i(" Password "),t("span",{class:"text-red-500"},"*",-1)])),_:1,__:[20]}),s(m,{id:"password",modelValue:l(a).password,"onUpdate:modelValue":e[2]||(e[2]=o=>l(a).password=o),type:"password",error:!!l(a).errors.password,placeholder:"Min. 8 karakter",class:"h-8 text-sm",required:""},null,8,["modelValue","error"]),s(d,{message:l(a).errors.password},null,8,["message"])])]),_:1})]),_:1}),s(f,null,{default:r(()=>[s(x,{class:"pb-3"},{default:r(()=>[t("div",H,[t("div",O,[s(u,{name:"id-card",class:"w-3 h-3 text-blue-500"})]),t("div",null,[s(g,{class:"text-base"},{default:r(()=>e[21]||(e[21]=[i("Informasi Pribadi")])),_:1,__:[21]}),s(c,{class:"text-xs"},{default:r(()=>e[22]||(e[22]=[i(" Data identitas dan biodata ")])),_:1,__:[22]})])])]),_:1}),s(_,{class:"space-y-3"},{default:r(()=>[t("div",Q,[s(n,{for:"nik",class:"text-xs font-medium"},{default:r(()=>e[23]||(e[23]=[i(" NIK "),t("span",{class:"text-red-500"},"*",-1)])),_:1,__:[23]}),s(m,{id:"nik",modelValue:l(a).nik,"onUpdate:modelValue":e[3]||(e[3]=o=>l(a).nik=o),type:"text",error:!!l(a).errors.nik,placeholder:"16 digit NIK",maxlength:"16",class:"h-8 text-sm font-mono",required:""},null,8,["modelValue","error"]),s(d,{message:l(a).errors.nik},null,8,["message"])]),t("div",R,[s(n,{for:"nama_lengkap",class:"text-xs font-medium"},{default:r(()=>e[24]||(e[24]=[i(" Nama Lengkap "),t("span",{class:"text-red-500"},"*",-1)])),_:1,__:[24]}),s(m,{id:"nama_lengkap",modelValue:l(a).nama_lengkap,"onUpdate:modelValue":e[4]||(e[4]=o=>l(a).nama_lengkap=o),type:"text",error:!!l(a).errors.nama_lengkap,placeholder:"Nama lengkap sesuai KTP",class:"h-8 text-sm",required:""},null,8,["modelValue","error"]),s(d,{message:l(a).errors.nama_lengkap},null,8,["message"])]),t("div",X,[t("div",Y,[s(n,{for:"tempat_lahir",class:"text-xs font-medium"},{default:r(()=>e[25]||(e[25]=[i(" Tempat Lahir "),t("span",{class:"text-red-500"},"*",-1)])),_:1,__:[25]}),s(m,{id:"tempat_lahir",modelValue:l(a).tempat_lahir,"onUpdate:modelValue":e[5]||(e[5]=o=>l(a).tempat_lahir=o),type:"text",error:!!l(a).errors.tempat_lahir,placeholder:"Kota",class:"h-8 text-sm",required:""},null,8,["modelValue","error"]),s(d,{message:l(a).errors.tempat_lahir},null,8,["message"])]),t("div",Z,[s(n,{for:"tanggal_lahir",class:"text-xs font-medium"},{default:r(()=>e[26]||(e[26]=[i(" Tanggal Lahir "),t("span",{class:"text-red-500"},"*",-1)])),_:1,__:[26]}),s(m,{id:"tanggal_lahir",modelValue:l(a).tanggal_lahir,"onUpdate:modelValue":e[6]||(e[6]=o=>l(a).tanggal_lahir=o),type:"date",error:!!l(a).errors.tanggal_lahir,class:"h-8 text-sm",required:""},null,8,["modelValue","error"]),s(d,{message:l(a).errors.tanggal_lahir},null,8,["message"])])]),t("div",ee,[s(n,{for:"jenis_kelamin",class:"text-xs font-medium"},{default:r(()=>e[27]||(e[27]=[i(" Jenis Kelamin "),t("span",{class:"text-red-500"},"*",-1)])),_:1,__:[27]}),v(t("select",{id:"jenis_kelamin","onUpdate:modelValue":e[7]||(e[7]=o=>l(a).jenis_kelamin=o),class:k(["flex h-8 w-full rounded-md border border-input bg-background px-3 py-1 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",{"border-red-500":l(a).errors.jenis_kelamin}]),required:""},e[28]||(e[28]=[t("option",{value:""},"Pilih",-1),t("option",{value:"L"},"Laki-laki",-1),t("option",{value:"P"},"Perempuan",-1)]),2),[[h,l(a).jenis_kelamin]]),s(d,{message:l(a).errors.jenis_kelamin},null,8,["message"])])]),_:1})]),_:1})]),s(f,null,{default:r(()=>[s(x,{class:"pb-3"},{default:r(()=>[t("div",se,[t("div",ae,[s(u,{name:"map-pin",class:"w-3 h-3 text-green-500"})]),t("div",null,[s(g,{class:"text-base"},{default:r(()=>e[29]||(e[29]=[i("Informasi Kontak")])),_:1,__:[29]}),s(c,{class:"text-xs"},{default:r(()=>e[30]||(e[30]=[i(" Alamat dan informasi kontak ")])),_:1,__:[30]})])])]),_:1}),s(_,{class:"space-y-3"},{default:r(()=>[t("div",le,[s(n,{for:"alamat",class:"text-xs font-medium"},{default:r(()=>e[31]||(e[31]=[i(" Alamat "),t("span",{class:"text-red-500"},"*",-1)])),_:1,__:[31]}),v(t("textarea",{id:"alamat","onUpdate:modelValue":e[8]||(e[8]=o=>l(a).alamat=o),rows:"2",class:k(["flex min-h-[60px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",{"border-red-500":l(a).errors.alamat}]),placeholder:"Alamat lengkap sesuai KTP",required:""},null,2),[[T,l(a).alamat]]),s(d,{message:l(a).errors.alamat},null,8,["message"])]),t("div",te,[t("div",re,[s(n,{for:"id_wilayah",class:"text-xs font-medium"},{default:r(()=>e[32]||(e[32]=[i(" Wilayah "),t("span",{class:"text-red-500"},"*",-1)])),_:1,__:[32]}),v(t("select",{id:"id_wilayah","onUpdate:modelValue":e[9]||(e[9]=o=>l(a).id_wilayah=o),class:k(["flex h-8 w-full rounded-md border border-input bg-background px-3 py-1 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",{"border-red-500":l(a).errors.id_wilayah}]),required:""},[e[33]||(e[33]=t("option",{value:""},"Pilih wilayah",-1)),(p(!0),V(K,null,B(b.wilayah,o=>(p(),V("option",{key:o.id_wilayah,value:o.id_wilayah},j(o.nama_wilayah),9,oe))),128))],2),[[h,l(a).id_wilayah]]),s(d,{message:l(a).errors.id_wilayah},null,8,["message"])]),t("div",ie,[s(n,{for:"no_telepon",class:"text-xs font-medium"},{default:r(()=>e[34]||(e[34]=[i(" No. Telepon ")])),_:1,__:[34]}),s(m,{id:"no_telepon",modelValue:l(a).no_telepon,"onUpdate:modelValue":e[10]||(e[10]=o=>l(a).no_telepon=o),type:"tel",error:!!l(a).errors.no_telepon,placeholder:"08xxxxxxxxxx",class:"h-8 text-sm"},null,8,["modelValue","error"]),s(d,{message:l(a).errors.no_telepon},null,8,["message"])])])]),_:1})]),_:1}),t("div",ne,[s(f,null,{default:r(()=>[s(x,{class:"pb-3"},{default:r(()=>[t("div",de,[t("div",me,[s(u,{name:"users",class:"w-3 h-3 text-purple-500"})]),t("div",null,[s(g,{class:"text-base"},{default:r(()=>e[35]||(e[35]=[i("Informasi Keluarga")])),_:1,__:[35]}),s(c,{class:"text-xs"},{default:r(()=>e[36]||(e[36]=[i(" Data keluarga (opsional) ")])),_:1,__:[36]})])])]),_:1}),s(_,{class:"space-y-3"},{default:r(()=>[t("div",ue,[s(n,{for:"nama_ayah",class:"text-xs font-medium"},{default:r(()=>e[37]||(e[37]=[i(" Nama Ayah ")])),_:1,__:[37]}),s(m,{id:"nama_ayah",modelValue:l(a).nama_ayah,"onUpdate:modelValue":e[11]||(e[11]=o=>l(a).nama_ayah=o),type:"text",error:!!l(a).errors.nama_ayah,placeholder:"Nama ayah kandung",class:"h-8 text-sm"},null,8,["modelValue","error"]),s(d,{message:l(a).errors.nama_ayah},null,8,["message"])]),t("div",pe,[s(n,{for:"nama_ibu",class:"text-xs font-medium"},{default:r(()=>e[38]||(e[38]=[i(" Nama Ibu ")])),_:1,__:[38]}),s(m,{id:"nama_ibu",modelValue:l(a).nama_ibu,"onUpdate:modelValue":e[12]||(e[12]=o=>l(a).nama_ibu=o),type:"text",error:!!l(a).errors.nama_ibu,placeholder:"Nama ibu kandung",class:"h-8 text-sm"},null,8,["modelValue","error"]),s(d,{message:l(a).errors.nama_ibu},null,8,["message"])])]),_:1})]),_:1}),s(f,null,{default:r(()=>[s(x,{class:"pb-3"},{default:r(()=>[t("div",fe,[t("div",_e,[s(u,{name:"briefcase",class:"w-3 h-3 text-orange-500"})]),t("div",null,[s(g,{class:"text-base"},{default:r(()=>e[39]||(e[39]=[i("Informasi Profesi")])),_:1,__:[39]}),s(c,{class:"text-xs"},{default:r(()=>e[40]||(e[40]=[i(" Data pekerjaan dan instansi ")])),_:1,__:[40]})])])]),_:1}),s(_,{class:"space-y-3"},{default:r(()=>[t("div",xe,[s(n,{for:"pekerjaan",class:"text-xs font-medium"},{default:r(()=>e[41]||(e[41]=[i(" Pekerjaan ")])),_:1,__:[41]}),s(m,{id:"pekerjaan",modelValue:l(a).pekerjaan,"onUpdate:modelValue":e[13]||(e[13]=o=>l(a).pekerjaan=o),type:"text",error:!!l(a).errors.pekerjaan,placeholder:"Jabatan atau profesi",class:"h-8 text-sm"},null,8,["modelValue","error"]),s(d,{message:l(a).errors.pekerjaan},null,8,["message"])]),t("div",ge,[s(n,{for:"instansi_asal",class:"text-xs font-medium"},{default:r(()=>e[42]||(e[42]=[i(" Instansi Asal ")])),_:1,__:[42]}),s(m,{id:"instansi_asal",modelValue:l(a).instansi_asal,"onUpdate:modelValue":e[14]||(e[14]=o=>l(a).instansi_asal=o),type:"text",error:!!l(a).errors.instansi_asal,placeholder:"Nama perusahaan/instansi",class:"h-8 text-sm"},null,8,["modelValue","error"]),s(d,{message:l(a).errors.instansi_asal},null,8,["message"])])]),_:1})]),_:1})]),t("div",ce,[e[44]||(e[44]=t("div",{class:"text-sm text-muted-foreground"},[t("span",{class:"text-red-500"},"*"),i(" menandakan field wajib diisi ")],-1)),t("div",be,[s(w,{as:"link",href:b.route("admin.peserta.index"),variant:"outline",class:"w-full sm:w-auto"},{default:r(()=>[s(u,{name:"x",class:"w-4 h-4 mr-2"}),e[43]||(e[43]=i(" Batal "))]),_:1,__:[43]},8,["href"]),s(w,{type:"submit",variant:"default",disabled:l(a).processing,class:"w-full sm:w-auto"},{default:r(()=>[l(a).processing?(p(),y(u,{key:0,name:"loader-2",class:"w-4 h-4 mr-2 animate-spin"})):(p(),y(u,{key:1,name:"save",class:"w-4 h-4 mr-2"})),i(" "+j(l(a).processing?"Menyimpan...":"Simpan Peserta"),1)]),_:1},8,["disabled"])])])],32)])])]),_:1}))}});export{De as default};
