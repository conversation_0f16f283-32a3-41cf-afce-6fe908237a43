import{P as E,a as j}from"./AppLogoIcon.vue_vue_type_script_setup_true_lang-wzz4PWna.js";import{j as P,f as T,c as q,k as M,u as x,d as O,l as D,w as H,m as N,P as L,e as z}from"./RovingFocusGroup-lsWyU4xZ.js";import{d as g,k as v,s as J,c as b,o as y,E as k,h as U,m as Q,F as W,J as X,K as Y,w as h,a as w,u as t,D as B,H as Z,i as G,R as ee,f as ae,V as te}from"./app-B_pmlBSQ.js";import{a as R,u as oe}from"./useForwardExpose-CO14IhkA.js";import{a as $,i as I,u as ne}from"./useFormControl-mZRLifGx.js";import{C as le}from"./check-DtmHjdxf.js";function _(r,e){return $(r)?!1:Array.isArray(r)?r.some(a=>I(a,e)):I(r,e)}const A=g({inheritAttrs:!1,__name:"VisuallyHiddenInputBubble",props:{name:{},value:{},checked:{type:Boolean,default:void 0},required:{type:Boolean},disabled:{type:Boolean},feature:{default:"fully-hidden"}},setup(r){const e=r,{primitiveElement:a,currentElement:d}=P(),l=v(()=>e.checked??e.value);return J(l,(i,o)=>{if(!d.value)return;const u=d.value,m=window.HTMLInputElement.prototype,c=Object.getOwnPropertyDescriptor(m,"value").set;if(c&&i!==o){const p=new Event("input",{bubbles:!0}),f=new Event("change",{bubbles:!0});c.call(u,i),u.dispatchEvent(p),u.dispatchEvent(f)}}),(i,o)=>(y(),b(T,k({ref_key:"primitiveElement",ref:a},{...e,...i.$attrs},{as:"input"}),null,16))}}),se=g({inheritAttrs:!1,__name:"VisuallyHiddenInput",props:{name:{},value:{},checked:{type:Boolean,default:void 0},required:{type:Boolean},disabled:{type:Boolean},feature:{default:"fully-hidden"}},setup(r){const e=r,a=v(()=>typeof e.value=="object"&&Array.isArray(e.value)&&e.value.length===0&&e.required),d=v(()=>typeof e.value=="string"||typeof e.value=="number"||typeof e.value=="boolean"?[{name:e.name,value:e.value}]:typeof e.value=="object"&&Array.isArray(e.value)?e.value.flatMap((l,i)=>typeof l=="object"?Object.entries(l).map(([o,u])=>({name:`[${e.name}][${i}][${o}]`,value:u})):{name:`[${e.name}][${i}]`,value:l}):e.value!==null&&typeof e.value=="object"&&!Array.isArray(e.value)?Object.entries(e.value).map(([l,i])=>({name:`[${e.name}][${l}]`,value:i})):[]);return(l,i)=>a.value?(y(),b(A,k({key:l.name},{...e,...l.$attrs},{name:l.name,value:l.value}),null,16,["name","value"])):(y(!0),U(W,{key:1},Q(d.value,o=>(y(),b(A,k({key:o.name,ref_for:!0},{...e,...l.$attrs},{name:o.name,value:o.value}),null,16,["name","value"]))),128))}}),[re,ke]=q("CheckboxGroupRoot");function V(r){return r==="indeterminate"}function K(r){return V(r)?"indeterminate":r?"checked":"unchecked"}const ie=g({__name:"RovingFocusItem",props:{tabStopId:{},focusable:{type:Boolean,default:!0},active:{type:Boolean},allowShiftKey:{type:Boolean},asChild:{type:Boolean},as:{default:"span"}},setup(r){const e=r,a=M(),d=x(),l=v(()=>e.tabStopId||d),i=v(()=>a.currentTabStopId.value===l.value),{getItems:o,CollectionItem:u}=O();X(()=>{e.focusable&&a.onFocusableItemAdd()}),Y(()=>{e.focusable&&a.onFocusableItemRemove()});function m(n){if(n.key==="Tab"&&n.shiftKey){a.onItemShiftTab();return}if(n.target!==n.currentTarget)return;const c=D(n,a.orientation.value,a.dir.value);if(c!==void 0){if(n.metaKey||n.ctrlKey||n.altKey||!e.allowShiftKey&&n.shiftKey)return;n.preventDefault();let p=[...o().map(f=>f.ref).filter(f=>f.dataset.disabled!=="")];if(c==="last")p.reverse();else if(c==="prev"||c==="next"){c==="prev"&&p.reverse();const f=p.indexOf(n.currentTarget);p=a.loop.value?H(p,f+1):p.slice(f+1)}Z(()=>N(p))}}return(n,c)=>(y(),b(t(u),null,{default:h(()=>[w(t(E),{tabindex:i.value?0:-1,"data-orientation":t(a).orientation.value,"data-active":n.active?"":void 0,"data-disabled":n.focusable?void 0:"",as:n.as,"as-child":n.asChild,onMousedown:c[0]||(c[0]=p=>{n.focusable?t(a).onItemFocus(l.value):p.preventDefault()}),onFocus:c[1]||(c[1]=p=>t(a).onItemFocus(l.value)),onKeydown:m},{default:h(()=>[B(n.$slots,"default")]),_:3},8,["tabindex","data-orientation","data-active","data-disabled","as","as-child"])]),_:3}))}}),[ue,de]=q("CheckboxRoot"),ce=g({inheritAttrs:!1,__name:"CheckboxRoot",props:{defaultValue:{type:[Boolean,String]},modelValue:{type:[Boolean,String,null],default:void 0},disabled:{type:Boolean},value:{default:"on"},id:{},asChild:{type:Boolean},as:{default:"button"},name:{},required:{type:Boolean}},emits:["update:modelValue"],setup(r,{emit:e}){const a=r,d=e,{forwardRef:l,currentElement:i}=R(),o=re(null),u=oe(a,"modelValue",d,{defaultValue:a.defaultValue,passive:a.modelValue===void 0}),m=v(()=>(o==null?void 0:o.disabled.value)||a.disabled),n=v(()=>$(o==null?void 0:o.modelValue.value)?u.value==="indeterminate"?"indeterminate":u.value:_(o.modelValue.value,a.value));function c(){if($(o==null?void 0:o.modelValue.value))u.value=V(u.value)?!0:!u.value;else{const s=[...o.modelValue.value||[]];if(_(s,a.value)){const F=s.findIndex(C=>I(C,a.value));s.splice(F,1)}else s.push(a.value);o.modelValue.value=s}}const p=ne(i),f=v(()=>{var s;return a.id&&i.value?(s=document.querySelector(`[for="${a.id}"]`))==null?void 0:s.innerText:void 0});return de({disabled:m,state:n}),(s,F)=>{var C,S;return y(),b(te((C=t(o))!=null&&C.rovingFocus.value?t(ie):t(E)),k(s.$attrs,{id:s.id,ref:t(l),role:"checkbox","as-child":s.asChild,as:s.as,type:s.as==="button"?"button":void 0,"aria-checked":t(V)(n.value)?"mixed":n.value,"aria-required":s.required,"aria-label":s.$attrs["aria-label"]||f.value,"data-state":t(K)(n.value),"data-disabled":m.value?"":void 0,disabled:m.value,focusable:(S=t(o))!=null&&S.rovingFocus.value?!m.value:void 0,onKeydown:ee(ae(()=>{},["prevent"]),["enter"]),onClick:c}),{default:h(()=>[B(s.$slots,"default",{modelValue:t(u),state:n.value}),t(p)&&s.name&&!t(o)?(y(),b(t(se),{key:0,type:"checkbox",checked:!!n.value,name:s.name,value:s.value,disabled:m.value,required:s.required},null,8,["checked","name","value","disabled","required"])):G("",!0)]),_:3},16,["id","as-child","as","type","aria-checked","aria-required","aria-label","data-state","data-disabled","disabled","focusable","onKeydown"])}}}),pe=g({__name:"CheckboxIndicator",props:{forceMount:{type:Boolean},asChild:{type:Boolean},as:{default:"span"}},setup(r){const{forwardRef:e}=R(),a=ue();return(d,l)=>(y(),b(t(L),{present:d.forceMount||t(V)(t(a).state.value)||t(a).state.value===!0},{default:h(()=>[w(t(E),k({ref:t(e),"data-state":t(K)(t(a).state.value),"data-disabled":t(a).disabled.value?"":void 0,style:{pointerEvents:"none"},"as-child":d.asChild,as:d.as},d.$attrs),{default:h(()=>[B(d.$slots,"default")]),_:3},16,["data-state","data-disabled","as-child","as"])]),_:3},8,["present"]))}}),ge=g({__name:"Checkbox",props:{defaultValue:{type:[Boolean,String]},modelValue:{type:[Boolean,String,null]},disabled:{type:Boolean},value:{},id:{},asChild:{type:Boolean},as:{type:[String,Object,Function]},name:{},required:{type:Boolean},class:{}},emits:["update:modelValue"],setup(r,{emit:e}){const a=r,d=e,l=v(()=>{const{class:o,...u}=a;return u}),i=z(l,d);return(o,u)=>(y(),b(t(ce),k({"data-slot":"checkbox"},t(i),{class:t(j)("peer border-input data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",a.class)}),{default:h(()=>[w(t(pe),{"data-slot":"checkbox-indicator",class:"flex items-center justify-center text-current transition-none"},{default:h(()=>[B(o.$slots,"default",{},()=>[w(t(le),{class:"size-3.5"})])]),_:3})]),_:3},16,["class"]))}});export{ge as _};
