import{d as S,x as I,k as h,c as y,o as u,w as n,a as o,b as e,u as t,g as E,e as r,f as N,h as g,i as b,n as _,t as m,j as T,v as B}from"./app-B_pmlBSQ.js";import{_ as P}from"./AppLayout.vue_vue_type_script_setup_true_lang-BRf8lUiM.js";import{_ as j}from"./Heading.vue_vue_type_script_setup_true_lang-is4VBSCS.js";import{_ as x}from"./AppLogoIcon.vue_vue_type_script_setup_true_lang-wzz4PWna.js";import{_ as A,a as U}from"./CardContent.vue_vue_type_script_setup_true_lang-ClvLNBXC.js";import{_ as q}from"./CardDescription.vue_vue_type_script_setup_true_lang-DboywBIK.js";import{_ as M,a as z}from"./CardTitle.vue_vue_type_script_setup_true_lang-5HO6oSoB.js";import{_ as v}from"./Input.vue_vue_type_script_setup_true_lang-DB1-RsTn.js";import{_ as f}from"./Label.vue_vue_type_script_setup_true_lang-rj_fhE7R.js";import{_ as G,a as H,b as Q,c as F,d as L}from"./SelectValue.vue_vue_type_script_setup_true_lang-IzlIronZ.js";import{_ as $}from"./Icon.vue_vue_type_script_setup_true_lang-BuYzjckr.js";import"./useForwardExpose-CO14IhkA.js";import"./RovingFocusGroup-lsWyU4xZ.js";import"./check-DtmHjdxf.js";import"./useFormControl-mZRLifGx.js";import"./loader-circle-6d8QrWFr.js";import"./sun-CHIV9RJ8.js";const J={class:"max-w-2xl mx-auto"},K={class:"space-y-4"},O={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},R={key:0,class:"text-sm text-red-600 mt-1"},W={key:0,class:"text-sm text-red-600 mt-1"},X={key:0,class:"text-sm text-red-600 mt-1"},Y={key:0,class:"text-sm text-red-600 mt-1"},Z={class:"bg-gray-50 p-4 rounded-lg"},aa={class:"text-sm text-gray-600 space-y-1"},ta={key:0,class:"bg-yellow-50 border border-yellow-200 p-4 rounded-lg"},ea={class:"flex"},sa={class:"text-sm text-yellow-700 mt-1"},oa={class:"flex justify-end space-x-4 pt-6 border-t"},La=S({__name:"Edit",props:{cabangLomba:{}},setup(w){const l=w,C=[{title:"Dashboard",href:"/admin/dashboard"},{title:"Manajemen Cabang Lomba",href:"/admin/cabang-lomba"},{title:"Edit Cabang Lomba",href:`/admin/cabang-lomba/${l.cabangLomba.id_cabang}/edit`}],s=I({kode_cabang:l.cabangLomba.kode_cabang,nama_cabang:l.cabangLomba.nama_cabang,deskripsi:l.cabangLomba.deskripsi||"",status:l.cabangLomba.status}),V=h(()=>l.cabangLomba.golongan&&l.cabangLomba.golongan.length>0),D=()=>{s.put(route("admin.cabang-lomba.update",l.cabangLomba.id_cabang),{onSuccess:()=>{}})},p=i=>new Date(i).toLocaleDateString("id-ID",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"});return(i,a)=>(u(),y(P,{breadcrumbs:C},{default:n(()=>[o(t(E),{title:"Edit Cabang Lomba"}),o(j,{title:`Edit Cabang Lomba: ${i.cabangLomba.nama_cabang}`},null,8,["title"]),e("div",J,[o(t(A),null,{default:n(()=>[o(t(M),null,{default:n(()=>[o(t(z),null,{default:n(()=>a[5]||(a[5]=[r("Edit Informasi Cabang Lomba")])),_:1,__:[5]}),o(t(q),null,{default:n(()=>a[6]||(a[6]=[r(" Perbarui informasi cabang lomba di bawah ini ")])),_:1,__:[6]})]),_:1}),o(t(U),null,{default:n(()=>{var c,k;return[e("form",{onSubmit:N(D,["prevent"]),class:"space-y-6"},[e("div",K,[a[13]||(a[13]=e("h3",{class:"text-lg font-medium"},"Informasi Dasar",-1)),e("div",O,[e("div",null,[o(t(f),{for:"kode_cabang"},{default:n(()=>a[7]||(a[7]=[r("Kode Cabang *")])),_:1,__:[7]}),o(t(v),{id:"kode_cabang",modelValue:t(s).kode_cabang,"onUpdate:modelValue":a[0]||(a[0]=d=>t(s).kode_cabang=d),type:"text",required:"",placeholder:"Contoh: TIL, TAH, FAH",class:_({"border-red-500":t(s).errors.kode_cabang})},null,8,["modelValue","class"]),t(s).errors.kode_cabang?(u(),g("p",R,m(t(s).errors.kode_cabang),1)):b("",!0)]),e("div",null,[o(t(f),{for:"status"},{default:n(()=>a[8]||(a[8]=[r("Status *")])),_:1,__:[8]}),o(t(G),{modelValue:t(s).status,"onUpdate:modelValue":a[1]||(a[1]=d=>t(s).status=d),required:""},{default:n(()=>[o(t(H),{class:_({"border-red-500":t(s).errors.status})},{default:n(()=>[o(t(Q),{placeholder:"Pilih Status"})]),_:1},8,["class"]),o(t(F),null,{default:n(()=>[o(t(L),{value:"aktif"},{default:n(()=>a[9]||(a[9]=[r("Aktif")])),_:1,__:[9]}),o(t(L),{value:"non_aktif"},{default:n(()=>a[10]||(a[10]=[r("Non Aktif")])),_:1,__:[10]})]),_:1})]),_:1},8,["modelValue"]),t(s).errors.status?(u(),g("p",W,m(t(s).errors.status),1)):b("",!0)])]),e("div",null,[o(t(f),{for:"nama_cabang"},{default:n(()=>a[11]||(a[11]=[r("Nama Cabang Lomba *")])),_:1,__:[11]}),o(t(v),{id:"nama_cabang",modelValue:t(s).nama_cabang,"onUpdate:modelValue":a[2]||(a[2]=d=>t(s).nama_cabang=d),type:"text",required:"",placeholder:"Contoh: Tilawatil Quran, Tahfidzul Quran",class:_({"border-red-500":t(s).errors.nama_cabang})},null,8,["modelValue","class"]),t(s).errors.nama_cabang?(u(),g("p",X,m(t(s).errors.nama_cabang),1)):b("",!0)]),e("div",null,[o(t(f),{for:"deskripsi"},{default:n(()=>a[12]||(a[12]=[r("Deskripsi")])),_:1,__:[12]}),T(e("textarea",{id:"deskripsi","onUpdate:modelValue":a[3]||(a[3]=d=>t(s).deskripsi=d),rows:"4",class:_(["w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",{"border-red-500":t(s).errors.deskripsi}]),placeholder:"Deskripsi cabang lomba..."},null,2),[[B,t(s).deskripsi]]),t(s).errors.deskripsi?(u(),g("p",Y,m(t(s).errors.deskripsi),1)):b("",!0)])]),e("div",Z,[a[17]||(a[17]=e("h4",{class:"font-medium text-gray-900 mb-2"},"Informasi Saat Ini",-1)),e("div",aa,[e("p",null,[a[14]||(a[14]=e("strong",null,"Dibuat:",-1)),r(" "+m(p(i.cabangLomba.created_at)),1)]),e("p",null,[a[15]||(a[15]=e("strong",null,"Diperbarui:",-1)),r(" "+m(p(i.cabangLomba.updated_at)),1)]),e("p",null,[a[16]||(a[16]=e("strong",null,"Jumlah Golongan:",-1)),r(" "+m(((c=i.cabangLomba.golongan)==null?void 0:c.length)||0)+" golongan",1)])])]),V.value?(u(),g("div",ta,[e("div",ea,[o($,{name:"alert-triangle",class:"w-5 h-5 text-yellow-600 mr-2 mt-0.5"}),e("div",null,[a[18]||(a[18]=e("h4",{class:"font-medium text-yellow-800"},"Perhatian",-1)),e("p",sa," Cabang lomba ini memiliki "+m(((k=i.cabangLomba.golongan)==null?void 0:k.length)||0)+" golongan. Perubahan status menjadi non-aktif akan mempengaruhi golongan yang terkait. ",1)])])])):b("",!0),e("div",oa,[o(x,{type:"button",variant:"outline",onClick:a[4]||(a[4]=d=>i.$inertia.visit(i.route("admin.cabang-lomba.index")))},{default:n(()=>a[19]||(a[19]=[r(" Batal ")])),_:1,__:[19]}),o(x,{type:"submit",disabled:t(s).processing},{default:n(()=>[t(s).processing?(u(),y($,{key:0,name:"loader-2",class:"w-4 h-4 mr-2 animate-spin"})):b("",!0),a[20]||(a[20]=r(" Simpan Perubahan "))]),_:1,__:[20]},8,["disabled"])])],32)]}),_:1})]),_:1})])]),_:1}))}});export{La as default};
