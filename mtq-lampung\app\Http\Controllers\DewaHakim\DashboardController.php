<?php

namespace App\Http\Controllers\DewaHakim;

use App\Http\Controllers\Controller;
use App\Models\DewaHakim;
use App\Models\NilaiPeserta;
use App\Models\Pendaftaran;
use Illuminate\Http\RedirectResponse;
use Inertia\Inertia;
use Inertia\Response;

class DashboardController extends Controller
{
    /**
     * Display the dewan hakim dashboard.
     */
    public function index(): Response|RedirectResponse
    {
        $user = auth()->user();
        $dewaHakim = $user->dewaHakim;

        if (!$dewaHakim) {
            return redirect()->route('dashboard')
                ->with('error', 'Profil dewan hakim belum lengkap. Silakan hubungi administrator.');
        }

        // Get statistics
        $stats = [
            'total_penilaian' => NilaiPeserta::where('id_dewan_hakim', $dewaHakim->id_dewan_hakim)->count(),
            'peserta_dinilai' => NilaiPeserta::where('id_dewan_hakim', $dewaHakim->id_dewan_hakim)
                ->distinct('id_pendaftaran')
                ->count(),
            'pending_penilaian' => $this->getPendingAssignments($dewaHakim),
            'completed_today' => NilaiPeserta::where('id_dewan_hakim', $dewaHakim->id_dewan_hakim)
                ->whereDate('created_at', today())
                ->count()
        ];

        // Get recent scoring activities
        $recentScoring = NilaiPeserta::with([
                'pendaftaran.peserta',
                'pendaftaran.golongan.cabangLomba',
                'jenisNilai'
            ])
            ->where('id_dewan_hakim', $dewaHakim->id_dewan_hakim)
            ->latest()
            ->limit(10)
            ->get();

        // Get upcoming assignments based on judge specialization and regional assignment
        $upcomingAssignments = $this->getUpcomingAssignments($dewaHakim);

        return Inertia::render('DewaHakim/Dashboard', [
            'dewaHakim' => $dewaHakim->load(['wilayah', 'pendidikan', 'pengalaman', 'prestasi']),
            'stats' => $stats,
            'recentScoring' => $recentScoring,
            'upcomingAssignments' => $upcomingAssignments
        ]);
    }

    /**
     * Get pending assignments count for dewan hakim.
     */
    private function getPendingAssignments(DewaHakim $dewaHakim): int
    {
        // Get participants that need scoring based on judge specialization and regional assignment
        $query = Pendaftaran::whereIn('status_pendaftaran', ['approved', 'verified'])
            ->whereDoesntHave('nilaiPeserta', function ($query) use ($dewaHakim) {
                $query->where('id_dewan_hakim', $dewaHakim->id_dewan_hakim);
            });

        // Filter by judge specialization if specified
        if ($dewaHakim->spesialisasi) {
            $query->whereHas('golongan.cabangLomba', function ($q) use ($dewaHakim) {
                $q->where('nama_cabang', 'like', '%' . $dewaHakim->spesialisasi . '%');
            });
        }

        // Regional judges should only score participants from their region
        if ($dewaHakim->tipe_hakim === 'kabupaten' && $dewaHakim->id_wilayah) {
            $query->whereHas('peserta', function ($q) use ($dewaHakim) {
                $q->where('id_wilayah', $dewaHakim->id_wilayah);
            });
        }

        return $query->count();
    }

    /**
     * Get upcoming assignments for dewan hakim.
     */
    private function getUpcomingAssignments(DewaHakim $dewaHakim)
    {
        $query = Pendaftaran::with([
                'peserta.user',
                'peserta.wilayah',
                'golongan.cabangLomba',
                'mimbar'
            ])
            ->whereIn('status_pendaftaran', ['approved', 'verified'])
            ->whereDoesntHave('nilaiPeserta', function ($query) use ($dewaHakim) {
                $query->where('id_dewan_hakim', $dewaHakim->id_dewan_hakim);
            });

        // Apply same filtering logic as pending assignments
        if ($dewaHakim->spesialisasi) {
            $query->whereHas('golongan.cabangLomba', function ($q) use ($dewaHakim) {
                $q->where('nama_cabang', 'like', '%' . $dewaHakim->spesialisasi . '%');
            });
        }

        if ($dewaHakim->tipe_hakim === 'kabupaten' && $dewaHakim->id_wilayah) {
            $query->whereHas('peserta', function ($q) use ($dewaHakim) {
                $q->where('id_wilayah', $dewaHakim->id_wilayah);
            });
        }

        return $query->orderBy('created_at', 'asc')
                    ->limit(10)
                    ->get();
    }
}
