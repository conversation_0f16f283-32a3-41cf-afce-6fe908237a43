import{d as g,c as x,o as r,w as _,a as i,b as t,u,g as h,t as a,h as d,F as f,m as y,n as b}from"./app-B_pmlBSQ.js";import{_ as w}from"./AppLayout.vue_vue_type_script_setup_true_lang-BRf8lUiM.js";import{_ as v}from"./Heading.vue_vue_type_script_setup_true_lang-is4VBSCS.js";import{_ as n}from"./Icon.vue_vue_type_script_setup_true_lang-BuYzjckr.js";import"./AppLogoIcon.vue_vue_type_script_setup_true_lang-wzz4PWna.js";import"./useForwardExpose-CO14IhkA.js";import"./RovingFocusGroup-lsWyU4xZ.js";import"./check-DtmHjdxf.js";import"./loader-circle-6d8QrWFr.js";import"./sun-CHIV9RJ8.js";const k={class:"space-y-6"},D={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"},C={class:"bg-white rounded-lg shadow p-6"},P={class:"flex items-center"},S={class:"flex-shrink-0"},T={class:"ml-4"},j={class:"text-2xl font-semibold text-gray-900"},B={class:"bg-white rounded-lg shadow p-6"},A={class:"flex items-center"},L={class:"flex-shrink-0"},V={class:"ml-4"},$={class:"text-2xl font-semibold text-gray-900"},F={class:"bg-white rounded-lg shadow p-6"},I={class:"flex items-center"},M={class:"flex-shrink-0"},N={class:"ml-4"},z={class:"text-2xl font-semibold text-gray-900"},E={class:"bg-white rounded-lg shadow p-6"},G={class:"flex items-center"},q={class:"flex-shrink-0"},H={class:"ml-4"},J={class:"text-2xl font-semibold text-gray-900"},K={class:"bg-white rounded-lg shadow"},O={class:"overflow-x-auto"},Q={class:"min-w-full divide-y divide-gray-200"},R={class:"bg-white divide-y divide-gray-200"},U={class:"px-6 py-4 whitespace-nowrap"},W={class:"text-sm font-medium text-gray-900"},X={class:"text-sm text-gray-500"},Y={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},Z={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},tt={class:"px-6 py-4 whitespace-nowrap"},et={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500"},gt=g({__name:"Dashboard",props:{stats:{},recent_pendaftaran:{},calon_peserta:{}},setup(st){const l=[{title:"Dashboard Admin",href:"/admin/dashboard"}];function c(s){return{draft:"bg-gray-100 text-gray-800",submitted:"bg-blue-100 text-blue-800",payment_pending:"bg-yellow-100 text-yellow-800",paid:"bg-green-100 text-green-800",verified:"bg-indigo-100 text-indigo-800",approved:"bg-green-100 text-green-800",rejected:"bg-red-100 text-red-800"}[s]||"bg-gray-100 text-gray-800"}function m(s){return{draft:"Draft",submitted:"Disubmit",payment_pending:"Menunggu Pembayaran",paid:"Dibayar",verified:"Diverifikasi",approved:"Disetujui",rejected:"Ditolak"}[s]||s}function p(s){return new Date(s).toLocaleDateString("id-ID",{year:"numeric",month:"short",day:"numeric"})}return(s,e)=>(r(),x(w,{breadcrumbs:l},{default:_(()=>[i(u(h),{title:"Dashboard Admin"}),i(v,{title:"Dashboard Admin"}),t("div",k,[t("div",D,[t("div",C,[t("div",P,[t("div",S,[i(n,{name:"users",class:"h-8 w-8 text-blue-600"})]),t("div",T,[e[0]||(e[0]=t("p",{class:"text-sm font-medium text-gray-500"},"Total Peserta",-1)),t("p",j,a(s.stats.total_peserta),1)])])]),t("div",B,[t("div",A,[t("div",L,[i(n,{name:"file-text",class:"h-8 w-8 text-green-600"})]),t("div",V,[e[1]||(e[1]=t("p",{class:"text-sm font-medium text-gray-500"},"Total Pendaftaran",-1)),t("p",$,a(s.stats.total_pendaftaran),1)])])]),t("div",F,[t("div",I,[t("div",M,[i(n,{name:"clock",class:"h-8 w-8 text-yellow-600"})]),t("div",N,[e[2]||(e[2]=t("p",{class:"text-sm font-medium text-gray-500"},"Menunggu Verifikasi",-1)),t("p",z,a(s.stats.pendaftaran_pending),1)])])]),t("div",E,[t("div",G,[t("div",q,[i(n,{name:"check-circle",class:"h-8 w-8 text-green-600"})]),t("div",H,[e[3]||(e[3]=t("p",{class:"text-sm font-medium text-gray-500"},"Disetujui",-1)),t("p",J,a(s.stats.pendaftaran_approved),1)])])])]),t("div",K,[e[5]||(e[5]=t("div",{class:"px-6 py-4 border-b border-gray-200"},[t("h3",{class:"text-lg font-medium text-gray-900"},"Pendaftaran Terbaru")],-1)),t("div",O,[t("table",Q,[e[4]||(e[4]=t("thead",{class:"bg-gray-50"},[t("tr",null,[t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Peserta "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Cabang Lomba "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Golongan "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Status "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Tanggal ")])],-1)),t("tbody",R,[(r(!0),d(f,null,y(s.recent_pendaftaran,o=>(r(),d("tr",{key:o.id_pendaftaran},[t("td",U,[t("div",W,a(o.peserta.nama_lengkap),1),t("div",X,a(o.nomor_pendaftaran),1)]),t("td",Y,a(o.golongan.cabang_lomba.nama_cabang),1),t("td",Z,a(o.golongan.nama_golongan),1),t("td",tt,[t("span",{class:b([c(o.status_pendaftaran),"inline-flex px-2 py-1 text-xs font-semibold rounded-full"])},a(m(o.status_pendaftaran)),3)]),t("td",et,a(p(o.created_at)),1)]))),128))])])])])])]),_:1}))}});export{gt as default};
