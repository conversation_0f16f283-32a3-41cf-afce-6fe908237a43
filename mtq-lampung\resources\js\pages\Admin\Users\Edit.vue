<template>
  <AppLayout :breadcrumbs="breadcrumbItems">
    <Head title="Edit User" />
    <Heading :title="`Edit User: ${user.nama_lengkap}`" />

    <div class="max-w-2xl mx-auto">
      <Card>
        <CardHeader>
          <CardTitle>Edit Informasi User</CardTitle>
          <CardDescription>
            Perbarui informasi user di bawah ini
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form @submit.prevent="submit" class="space-y-6">
            <!-- Basic Information -->
            <div class="space-y-4">
              <h3 class="text-lg font-medium">Informasi Dasar</h3>
              
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label for="username">Username *</Label>
                  <Input
                    id="username"
                    v-model="form.username"
                    type="text"
                    required
                    :class="{ 'border-red-500': form.errors.username }"
                  />
                  <p v-if="form.errors.username" class="text-sm text-red-600 mt-1">
                    {{ form.errors.username }}
                  </p>
                </div>

                <div>
                  <Label for="email">Email *</Label>
                  <Input
                    id="email"
                    v-model="form.email"
                    type="email"
                    required
                    :class="{ 'border-red-500': form.errors.email }"
                  />
                  <p v-if="form.errors.email" class="text-sm text-red-600 mt-1">
                    {{ form.errors.email }}
                  </p>
                </div>
              </div>

              <div>
                <Label for="nama_lengkap">Nama Lengkap *</Label>
                <Input
                  id="nama_lengkap"
                  v-model="form.nama_lengkap"
                  type="text"
                  required
                  :class="{ 'border-red-500': form.errors.nama_lengkap }"
                />
                <p v-if="form.errors.nama_lengkap" class="text-sm text-red-600 mt-1">
                  {{ form.errors.nama_lengkap }}
                </p>
              </div>

              <div>
                <Label for="no_telepon">No. Telepon</Label>
                <Input
                  id="no_telepon"
                  v-model="form.no_telepon"
                  type="tel"
                  :class="{ 'border-red-500': form.errors.no_telepon }"
                />
                <p v-if="form.errors.no_telepon" class="text-sm text-red-600 mt-1">
                  {{ form.errors.no_telepon }}
                </p>
              </div>
            </div>

            <!-- Role and Access -->
            <div class="space-y-4">
              <h3 class="text-lg font-medium">Role dan Akses</h3>
              
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label for="role">Role *</Label>
                  <Select v-model="form.role" required :disabled="user.role === 'superadmin'">
                    <SelectTrigger :class="{ 'border-red-500': form.errors.role }">
                      <SelectValue placeholder="Pilih Role" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem v-for="(label, value) in roles" :key="value" :value="value">
                        {{ label }}
                      </SelectItem>
                    </SelectContent>
                  </Select>
                  <p v-if="form.errors.role" class="text-sm text-red-600 mt-1">
                    {{ form.errors.role }}
                  </p>
                  <p v-if="user.role === 'superadmin'" class="text-sm text-gray-500 mt-1">
                    Role superadmin tidak dapat diubah
                  </p>
                </div>

                <div>
                  <Label for="status">Status *</Label>
                  <Select v-model="form.status" required :disabled="user.role === 'superadmin'">
                    <SelectTrigger :class="{ 'border-red-500': form.errors.status }">
                      <SelectValue placeholder="Pilih Status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="aktif">Aktif</SelectItem>
                      <SelectItem value="non_aktif">Non Aktif</SelectItem>
                      <SelectItem value="suspended">Suspended</SelectItem>
                    </SelectContent>
                  </Select>
                  <p v-if="form.errors.status" class="text-sm text-red-600 mt-1">
                    {{ form.errors.status }}
                  </p>
                  <p v-if="user.role === 'superadmin'" class="text-sm text-gray-500 mt-1">
                    Status superadmin tidak dapat diubah
                  </p>
                </div>
              </div>

              <div v-if="form.role === 'admin_daerah' || form.role === 'dewan_hakim'">
                <Label for="id_wilayah">Wilayah *</Label>
                <Select v-model="form.id_wilayah" required>
                  <SelectTrigger :class="{ 'border-red-500': form.errors.id_wilayah }">
                    <SelectValue placeholder="Pilih Wilayah" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem v-for="w in wilayah" :key="w.id_wilayah" :value="w.id_wilayah.toString()">
                      {{ w.nama_wilayah }}
                    </SelectItem>
                  </SelectContent>
                </Select>
                <p v-if="form.errors.id_wilayah" class="text-sm text-red-600 mt-1">
                  {{ form.errors.id_wilayah }}
                </p>
              </div>
            </div>

            <!-- User Info -->
            <div class="bg-gray-50 p-4 rounded-lg">
              <h4 class="font-medium text-gray-900 mb-2">Informasi Tambahan</h4>
              <div class="text-sm text-gray-600 space-y-1">
                <p><strong>Dibuat:</strong> {{ formatDate(user.created_at) }}</p>
                <p v-if="user.created_by"><strong>Dibuat oleh:</strong> {{ user.created_by?.nama_lengkap }}</p>
                <p v-if="user.last_login"><strong>Login terakhir:</strong> {{ formatDate(user.last_login) }}</p>
              </div>
            </div>

            <!-- Actions -->
            <div class="flex justify-end space-x-4 pt-6 border-t">
              <Button
                type="button"
                variant="outline"
                @click="$inertia.visit(route('admin.users.index'))"
              >
                Batal
              </Button>
              <Button type="submit" :disabled="form.processing">
                <Icon v-if="form.processing" name="loader-2" class="w-4 h-4 mr-2 animate-spin" />
                Simpan Perubahan
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import { watch } from 'vue'
import { Head, useForm } from '@inertiajs/vue3'
import AppLayout from '@/layouts/AppLayout.vue'
import Heading from '@/components/Heading.vue'
import Button from '@/components/ui/button/Button.vue'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import Icon from '@/components/Icon.vue'
import { type BreadcrumbItem } from '@/types'

interface User {
  id_user: number
  username: string
  email: string
  nama_lengkap: string
  no_telepon?: string
  role: string
  status: string
  id_wilayah?: number
  created_at: string
  last_login?: string
  created_by?: {
    nama_lengkap: string
  }
}

const props = defineProps<{
  user: User
  wilayah: Array<{
    id_wilayah: number
    nama_wilayah: string
  }>
  roles: Record<string, string>
}>()

const breadcrumbItems: BreadcrumbItem[] = [
  { title: 'Dashboard', href: '/admin/dashboard' },
  { title: 'Manajemen User', href: '/admin/users' },
  { title: 'Edit User', href: `/admin/users/${props.user.id_user}/edit` }
]

const form = useForm({
  username: props.user.username,
  email: props.user.email,
  role: props.user.role,
  nama_lengkap: props.user.nama_lengkap,
  no_telepon: props.user.no_telepon || '',
  id_wilayah: props.user.id_wilayah?.toString() || '',
  status: props.user.status
})

// Clear wilayah when role changes to admin
watch(() => form.role, (newRole) => {
  if (newRole === 'admin') {
    form.id_wilayah = ''
  }
})

const submit = () => {
  form.put(route('admin.users.update', props.user.id_user), {
    onSuccess: () => {
      // Redirect handled by controller
    }
  })
}

const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString('id-ID', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}
</script>
