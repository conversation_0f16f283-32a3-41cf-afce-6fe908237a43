<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('dewan_hakim', function (Blueprint $table) {
            $table->id('id_dewan_hakim');
            $table->unsignedBigInteger('id_user')->unique();
            $table->string('nik', 16)->unique();
            $table->string('nama_lengkap', 100);
            $table->string('tempat_lahir', 50);
            $table->date('tanggal_lahir');
            $table->string('pekerjaan', 100)->nullable();
            $table->string('unit_kerja', 100)->nullable();
            $table->text('alamat_rumah')->nullable();
            $table->text('alamat_kantor')->nullable();
            $table->string('no_telepon', 20)->nullable();
            $table->text('spesialisasi')->nullable();
            $table->enum('tipe_hakim', ['undangan', 'kabupaten']);
            $table->unsignedBigInteger('id_wilayah')->nullable();
            $table->enum('status', ['aktif', 'non_aktif'])->default('aktif');
            $table->timestamps();

            // Foreign keys
            $table->foreign('id_user')->references('id_user')->on('users')->onDelete('cascade');
            $table->foreign('id_wilayah')->references('id_wilayah')->on('wilayah')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('dewan_hakim');
    }
};
