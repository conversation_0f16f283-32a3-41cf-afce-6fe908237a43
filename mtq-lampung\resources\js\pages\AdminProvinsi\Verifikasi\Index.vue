<template>
  <AppLayout>
    <Head title="Verifikasi Pendaftaran - Ad<PERSON>" />

    <div class="space-y-6">
      <!-- Header -->
      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-2xl font-bold text-gray-900">Verifikasi Pendaftaran</h1>
            <p class="text-gray-600 mt-1">Verifikasi NIK dan dokumen peserta MTQ</p>
          </div>
        </div>
      </div>

      <!-- Filters -->
      <div class="bg-white rounded-lg shadow p-6">
        <form @submit.prevent="applyFilters" class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Status Verifikasi</label>
            <select v-model="filters.status_verifikasi"
                    class="w-full rounded-md border-gray-300 shadow-sm focus:border-emerald-500 focus:ring-emerald-500">
              <option value="">Semua Status</option>
              <option value="pending">Menunggu Verifikasi</option>
              <option value="approved">Disetujui</option>
              <option value="rejected">Ditolak</option>
            </select>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Wilayah</label>
            <select v-model="filters.wilayah"
                    class="w-full rounded-md border-gray-300 shadow-sm focus:border-emerald-500 focus:ring-emerald-500">
              <option value="">Semua Wilayah</option>
              <option v-for="w in wilayah" :key="w.id_wilayah" :value="w.id_wilayah">
                {{ w.nama_wilayah }}
              </option>
            </select>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Golongan</label>
            <select v-model="filters.golongan"
                    class="w-full rounded-md border-gray-300 shadow-sm focus:border-emerald-500 focus:ring-emerald-500">
              <option value="">Semua Golongan</option>
              <option v-for="g in golongan" :key="g.id_golongan" :value="g.id_golongan">
                {{ g.nama_golongan }}
              </option>
            </select>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Pencarian</label>
            <input v-model="filters.search"
                   type="text"
                   placeholder="Nama, NIK, atau nomor pendaftaran..."
                   class="w-full rounded-md border-gray-300 shadow-sm focus:border-emerald-500 focus:ring-emerald-500">
          </div>

          <div class="md:col-span-4 flex justify-end space-x-2">
            <button type="submit"
                    class="px-4 py-2 bg-emerald-600 text-white rounded-md hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-emerald-500">
              Terapkan Filter
            </button>
            <button type="button"
                    @click="resetFilters"
                    class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500">
              Reset
            </button>
          </div>
        </form>
      </div>

      <!-- Registrations Table -->
      <div class="bg-white rounded-lg shadow overflow-hidden">
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Peserta</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Wilayah</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Golongan</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status NIK</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status Dokumen</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status Verifikasi</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tanggal</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Aksi</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-for="pendaftaran in pendaftaranData.data" :key="pendaftaran.id_pendaftaran">
                <td class="px-6 py-4 whitespace-nowrap">
                  <div>
                    <div class="text-sm font-medium text-gray-900">{{ pendaftaran.peserta.nama_lengkap }}</div>
                    <div class="text-sm text-gray-500">{{ pendaftaran.peserta.nik }}</div>
                    <div class="text-sm text-gray-500">{{ pendaftaran.nomor_pendaftaran }}</div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {{ pendaftaran.peserta.wilayah.nama_wilayah }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {{ pendaftaran.golongan.nama_golongan }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span v-if="!pendaftaran.verifikasi_pendaftaran"
                        class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
                    Belum Diverifikasi
                  </span>
                  <span v-else-if="pendaftaran.verifikasi_pendaftaran.status_nik === 'sesuai'"
                        class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                    Sesuai
                  </span>
                  <span v-else-if="pendaftaran.verifikasi_pendaftaran.status_nik === 'tidak_sesuai'"
                        class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
                    Tidak Sesuai
                  </span>
                  <span v-else
                        class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
                    Pending
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span v-if="!pendaftaran.verifikasi_pendaftaran"
                        class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
                    Belum Diverifikasi
                  </span>
                  <span v-else-if="pendaftaran.verifikasi_pendaftaran.status_dokumen === 'berkas_sesuai'"
                        class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                    Berkas Sesuai
                  </span>
                  <span v-else-if="pendaftaran.verifikasi_pendaftaran.status_dokumen === 'berkas_tidak_sesuai'"
                        class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
                    Berkas Tidak Sesuai
                  </span>
                  <span v-else
                        class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
                    Pending
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span v-if="!pendaftaran.verifikasi_pendaftaran"
                        class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
                    Menunggu Verifikasi
                  </span>
                  <span v-else-if="pendaftaran.verifikasi_pendaftaran.status_verifikasi === 'approved'"
                        class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                    Disetujui
                  </span>
                  <span v-else-if="pendaftaran.verifikasi_pendaftaran.status_verifikasi === 'rejected'"
                        class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
                    Ditolak
                  </span>
                  <span v-else
                        class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
                    Pending
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {{ new Date(pendaftaran.created_at).toLocaleDateString('id-ID') }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div class="flex space-x-2">
                    <Link :href="route('admin.verifikasi-provinsi.show', pendaftaran.id_pendaftaran)"
                          class="text-emerald-600 hover:text-emerald-900">
                      Detail
                    </Link>
                    <button v-if="!pendaftaran.verifikasi_pendaftaran || pendaftaran.verifikasi_pendaftaran.status_verifikasi === 'pending'"
                            @click="openVerificationModal(pendaftaran)"
                            class="text-blue-600 hover:text-blue-900">
                      Verifikasi
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- Pagination -->
        <div class="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
          <div class="flex items-center justify-between">
            <div class="flex-1 flex justify-between sm:hidden">
              <Link v-if="pendaftaranData.prev_page_url"
                    :href="pendaftaranData.prev_page_url"
                    class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                Previous
              </Link>
              <Link v-if="pendaftaranData.next_page_url"
                    :href="pendaftaranData.next_page_url"
                    class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                Next
              </Link>
            </div>
            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
              <div>
                <p class="text-sm text-gray-700">
                  Showing {{ pendaftaranData.from }} to {{ pendaftaranData.to }} of {{ pendaftaranData.total }} results
                </p>
              </div>
              <div>
                <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                  <Link v-for="link in pendaftaranData.links"
                        :key="link.label"
                        :href="link.url"
                        v-html="link.label"
                        :class="[
                          'relative inline-flex items-center px-4 py-2 border text-sm font-medium',
                          link.active
                            ? 'z-10 bg-emerald-50 border-emerald-500 text-emerald-600'
                            : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                        ]">
                  </Link>
                </nav>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Verification Modal -->
      <div v-if="showVerificationModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
          <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
              <h3 class="text-lg font-medium text-gray-900">Verifikasi Pendaftaran</h3>
              <button @click="closeVerificationModal" class="text-gray-400 hover:text-gray-600">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
              </button>
            </div>

            <div v-if="selectedPendaftaran" class="space-y-6">
              <!-- Participant Info -->
              <div class="bg-gray-50 p-4 rounded-lg">
                <h4 class="font-medium text-gray-900 mb-2">Informasi Peserta</h4>
                <div class="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span class="text-gray-500">Nama:</span>
                    <span class="ml-2 font-medium">{{ selectedPendaftaran.peserta.nama_lengkap }}</span>
                  </div>
                  <div>
                    <span class="text-gray-500">NIK:</span>
                    <span class="ml-2 font-medium">{{ selectedPendaftaran.peserta.nik }}</span>
                  </div>
                  <div>
                    <span class="text-gray-500">Wilayah:</span>
                    <span class="ml-2 font-medium">{{ selectedPendaftaran.peserta.wilayah.nama_wilayah }}</span>
                  </div>
                  <div>
                    <span class="text-gray-500">Golongan:</span>
                    <span class="ml-2 font-medium">{{ selectedPendaftaran.golongan.nama_golongan }}</span>
                  </div>
                </div>
              </div>

              <!-- Verification Form -->
              <form @submit.prevent="submitVerification" class="space-y-4">
                <!-- NIK Verification -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Verifikasi NIK</label>
                  <div class="space-y-2">
                    <label class="flex items-center">
                      <input v-model="verificationForm.status_nik"
                             type="radio"
                             value="sesuai"
                             class="mr-2 text-emerald-600 focus:ring-emerald-500">
                      <span class="text-sm">Sesuai</span>
                    </label>
                    <label class="flex items-center">
                      <input v-model="verificationForm.status_nik"
                             type="radio"
                             value="tidak_sesuai"
                             class="mr-2 text-red-600 focus:ring-red-500">
                      <span class="text-sm">Tidak Sesuai</span>
                    </label>
                  </div>
                  <textarea v-model="verificationForm.catatan_nik"
                            placeholder="Catatan verifikasi NIK (opsional)"
                            rows="2"
                            class="mt-2 w-full rounded-md border-gray-300 shadow-sm focus:border-emerald-500 focus:ring-emerald-500"></textarea>
                </div>

                <!-- Document Verification -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Verifikasi Dokumen</label>
                  <div class="space-y-2">
                    <label class="flex items-center">
                      <input v-model="verificationForm.status_dokumen"
                             type="radio"
                             value="berkas_sesuai"
                             class="mr-2 text-emerald-600 focus:ring-emerald-500">
                      <span class="text-sm">Berkas Sesuai</span>
                    </label>
                    <label class="flex items-center">
                      <input v-model="verificationForm.status_dokumen"
                             type="radio"
                             value="berkas_tidak_sesuai"
                             class="mr-2 text-red-600 focus:ring-red-500">
                      <span class="text-sm">Berkas Tidak Sesuai</span>
                    </label>
                  </div>
                  <textarea v-model="verificationForm.catatan_dokumen"
                            placeholder="Catatan verifikasi dokumen (opsional)"
                            rows="2"
                            class="mt-2 w-full rounded-md border-gray-300 shadow-sm focus:border-emerald-500 focus:ring-emerald-500"></textarea>
                </div>

                <!-- General Notes -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Catatan Verifikasi</label>
                  <textarea v-model="verificationForm.catatan_verifikasi"
                            placeholder="Catatan umum verifikasi (opsional)"
                            rows="3"
                            class="w-full rounded-md border-gray-300 shadow-sm focus:border-emerald-500 focus:ring-emerald-500"></textarea>
                </div>

                <!-- Action Buttons -->
                <div class="flex justify-end space-x-3 pt-4">
                  <button type="button"
                          @click="closeVerificationModal"
                          class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">
                    Batal
                  </button>
                  <button type="submit"
                          :disabled="!verificationForm.status_nik || !verificationForm.status_dokumen || verificationForm.processing"
                          class="px-4 py-2 bg-emerald-600 text-white rounded-md text-sm font-medium hover:bg-emerald-700 disabled:opacity-50 disabled:cursor-not-allowed">
                    {{ verificationForm.processing ? 'Memproses...' : 'Simpan Verifikasi' }}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import { Head, Link, router, useForm } from '@inertiajs/vue3'
import { reactive, ref } from 'vue'
import AppLayout from '@/layouts/AppLayout.vue'

interface Pendaftaran {
  id_pendaftaran: number
  nomor_pendaftaran: string
  created_at: string
  peserta: {
    nama_lengkap: string
    nik: string
    wilayah: {
      nama_wilayah: string
    }
  }
  golongan: {
    nama_golongan: string
  }
  verifikasi_pendaftaran?: {
    status_nik: string
    status_dokumen: string
    status_verifikasi: string
    catatan_nik?: string
    catatan_dokumen?: string
    catatan_verifikasi?: string
  }
}

interface PaginatedData {
  data: Pendaftaran[]
  from: number
  to: number
  total: number
  prev_page_url: string | null
  next_page_url: string | null
  links: Array<{
    url: string | null
    label: string
    active: boolean
  }>
}

interface Wilayah {
  id_wilayah: number
  nama_wilayah: string
}

interface Golongan {
  id_golongan: number
  nama_golongan: string
}

const props = defineProps<{
  pendaftaran: PaginatedData
  filters: Record<string, any>
  wilayah: Wilayah[]
  golongan: Golongan[]
}>()

const pendaftaranData = reactive(props.pendaftaran)
const filters = reactive({ ...props.filters })

// Verification modal state
const showVerificationModal = ref(false)
const selectedPendaftaran = ref<Pendaftaran | null>(null)

// Verification form
const verificationForm = useForm({
  status_nik: '',
  catatan_nik: '',
  status_dokumen: '',
  catatan_dokumen: '',
  catatan_verifikasi: ''
})

function applyFilters() {
  router.get(route('admin.verifikasi-provinsi.index'), filters, {
    preserveState: true,
    preserveScroll: true
  })
}

function resetFilters() {
  Object.keys(filters).forEach(key => {
    filters[key] = ''
  })
  applyFilters()
}

function openVerificationModal(pendaftaran: Pendaftaran) {
  selectedPendaftaran.value = pendaftaran

  // Pre-fill form if verification exists
  if (pendaftaran.verifikasi_pendaftaran) {
    verificationForm.status_nik = pendaftaran.verifikasi_pendaftaran.status_nik
    verificationForm.catatan_nik = pendaftaran.verifikasi_pendaftaran.catatan_nik || ''
    verificationForm.status_dokumen = pendaftaran.verifikasi_pendaftaran.status_dokumen
    verificationForm.catatan_dokumen = pendaftaran.verifikasi_pendaftaran.catatan_dokumen || ''
    verificationForm.catatan_verifikasi = pendaftaran.verifikasi_pendaftaran.catatan_verifikasi || ''
  } else {
    verificationForm.reset()
  }

  showVerificationModal.value = true
}

function closeVerificationModal() {
  showVerificationModal.value = false
  selectedPendaftaran.value = null
  verificationForm.reset()
}

function submitVerification() {
  if (!selectedPendaftaran.value) return

  verificationForm.post(route('admin.verifikasi-provinsi.verify', selectedPendaftaran.value.id_pendaftaran), {
    onSuccess: () => {
      closeVerificationModal()
      // Refresh the page data
      router.reload({ only: ['pendaftaran'] })
    }
  })
}
</script>
