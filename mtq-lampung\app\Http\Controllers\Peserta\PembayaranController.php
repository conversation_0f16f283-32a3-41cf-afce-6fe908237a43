<?php

namespace App\Http\Controllers\Peserta;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Inertia\Inertia;

class PembayaranController extends Controller
{
    public function index()
    {
        return Inertia::render('Peserta/Pembayaran/Index', [
            'message' => 'Pendaftaran MTQ saat ini GRATIS! Tidak ada biaya yang perlu dibayar.'
        ]);
    }

    public function show($id)
    {
        return Inertia::render('Peserta/Pembayaran/Show');
    }

    public function store(Request $request)
    {
        return back()->with('success', 'Pembayaran berhasil disubmit');
    }
}
