import{d as P,c as g,o as u,w as i,a,b as t,u as l,g as L,i as _,e as r,t as n,h as D,W as T}from"./app-B_pmlBSQ.js";import{_ as U}from"./AppLayout.vue_vue_type_script_setup_true_lang-BRf8lUiM.js";import{_ as C}from"./Heading.vue_vue_type_script_setup_true_lang-is4VBSCS.js";import{_ as v}from"./AppLogoIcon.vue_vue_type_script_setup_true_lang-wzz4PWna.js";import{_ as k,a as y}from"./CardContent.vue_vue_type_script_setup_true_lang-ClvLNBXC.js";import{_ as V}from"./CardDescription.vue_vue_type_script_setup_true_lang-DboywBIK.js";import{_ as x,a as w}from"./CardTitle.vue_vue_type_script_setup_true_lang-5HO6oSoB.js";import{_ as f}from"./index-CMGr3-bt.js";import{_ as d}from"./Label.vue_vue_type_script_setup_true_lang-rj_fhE7R.js";import{_ as p}from"./Icon.vue_vue_type_script_setup_true_lang-BuYzjckr.js";import"./useForwardExpose-CO14IhkA.js";import"./RovingFocusGroup-lsWyU4xZ.js";import"./check-DtmHjdxf.js";import"./loader-circle-6d8QrWFr.js";import"./sun-CHIV9RJ8.js";const I={class:"max-w-4xl mx-auto space-y-6"},K={class:"flex justify-between items-start"},B={class:"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center"},E={class:"flex gap-2"},H={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},W={class:"space-y-4"},R={class:"text-sm"},J={class:"text-sm"},M={class:"text-sm"},q={class:"text-sm"},z={class:"space-y-4"},F={class:"text-sm"},G={class:"text-sm"},O={class:"text-sm"},Q={key:0},X={class:"text-sm"},Y={key:1},Z={class:"text-sm"},ss={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},es={class:"space-y-4"},ts={class:"text-sm"},as={class:"text-sm"},is={class:"text-sm"},ls={class:"space-y-4"},rs={class:"text-sm"},ns={class:"text-sm"},ds={class:"text-sm"},us={class:"text-sm"},ms={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},os={class:"space-y-4"},_s={class:"text-sm"},fs={class:"text-sm"},ps={class:"text-sm"},gs={class:"text-sm"},vs={class:"space-y-4"},ks={class:"text-sm"},ys={class:"text-sm"},xs={class:"flex justify-between"},ws={class:"flex gap-2"},Is=P({__name:"Show",props:{user:{}},setup(S){const b=S,j=[{title:"Dashboard",href:"/admin/dashboard"},{title:"Manajemen User",href:"/admin/users"},{title:"Detail User",href:`/admin/users/${b.user.id_user}`}],h={superadmin:"Super Admin",admin:"Admin",admin_daerah:"Admin Daerah",peserta:"Peserta",dewan_hakim:"Dewan Hakim"},N=()=>{T.post(route("admin.users.toggle-status",b.user.id_user),{},{preserveScroll:!0})},c=e=>({superadmin:"destructive",admin:"default",admin_daerah:"secondary",dewan_hakim:"outline",peserta:"secondary"})[e]||"secondary",A=e=>({aktif:"default",non_aktif:"secondary",suspended:"destructive"})[e]||"secondary",$=e=>({aktif:"Aktif",non_aktif:"Non Aktif",suspended:"Suspended"})[e]||e,m=e=>new Date(e).toLocaleDateString("id-ID",{year:"numeric",month:"long",day:"numeric"});return(e,s)=>(u(),g(U,{breadcrumbs:j},{default:i(()=>[a(l(L),{title:`Detail User: ${e.user.nama_lengkap}`},null,8,["title"]),a(C,{title:`Detail User: ${e.user.nama_lengkap}`},null,8,["title"]),t("div",I,[a(l(k),null,{default:i(()=>[a(l(x),null,{default:i(()=>[t("div",K,[t("div",null,[a(l(w),{class:"flex items-center gap-3"},{default:i(()=>[t("div",B,[a(p,{name:"user",class:"w-6 h-6 text-blue-600"})]),r(" "+n(e.user.nama_lengkap),1)]),_:1}),a(l(V),null,{default:i(()=>[r(n(e.user.username)+" • "+n(e.user.email),1)]),_:1})]),t("div",E,[a(l(f),{variant:c(e.user.role)},{default:i(()=>[r(n(h[e.user.role]||e.user.role),1)]),_:1},8,["variant"]),a(l(f),{variant:A(e.user.status)},{default:i(()=>[r(n($(e.user.status)),1)]),_:1},8,["variant"])])])]),_:1}),a(l(y),null,{default:i(()=>{var o;return[t("div",H,[t("div",W,[t("div",null,[a(l(d),{class:"text-sm font-medium text-gray-500"},{default:i(()=>s[2]||(s[2]=[r("Username")])),_:1,__:[2]}),t("p",R,n(e.user.username),1)]),t("div",null,[a(l(d),{class:"text-sm font-medium text-gray-500"},{default:i(()=>s[3]||(s[3]=[r("Email")])),_:1,__:[3]}),t("p",J,n(e.user.email),1)]),t("div",null,[a(l(d),{class:"text-sm font-medium text-gray-500"},{default:i(()=>s[4]||(s[4]=[r("No. Telepon")])),_:1,__:[4]}),t("p",M,n(e.user.no_telepon||"-"),1)]),t("div",null,[a(l(d),{class:"text-sm font-medium text-gray-500"},{default:i(()=>s[5]||(s[5]=[r("Role")])),_:1,__:[5]}),t("p",q,n(h[e.user.role]||e.user.role),1)])]),t("div",z,[t("div",null,[a(l(d),{class:"text-sm font-medium text-gray-500"},{default:i(()=>s[6]||(s[6]=[r("Status")])),_:1,__:[6]}),t("p",F,n($(e.user.status)),1)]),t("div",null,[a(l(d),{class:"text-sm font-medium text-gray-500"},{default:i(()=>s[7]||(s[7]=[r("Wilayah")])),_:1,__:[7]}),t("p",G,n(((o=e.user.wilayah)==null?void 0:o.nama_wilayah)||"-"),1)]),t("div",null,[a(l(d),{class:"text-sm font-medium text-gray-500"},{default:i(()=>s[8]||(s[8]=[r("Dibuat")])),_:1,__:[8]}),t("p",O,n(m(e.user.created_at)),1)]),e.user.created_by?(u(),D("div",Q,[a(l(d),{class:"text-sm font-medium text-gray-500"},{default:i(()=>s[9]||(s[9]=[r("Dibuat oleh")])),_:1,__:[9]}),t("p",X,n(e.user.created_by.nama_lengkap),1)])):_("",!0),e.user.last_login?(u(),D("div",Y,[a(l(d),{class:"text-sm font-medium text-gray-500"},{default:i(()=>s[10]||(s[10]=[r("Login terakhir")])),_:1,__:[10]}),t("p",Z,n(m(e.user.last_login)),1)])):_("",!0)])])]}),_:1})]),_:1}),e.user.role==="peserta"&&e.user.peserta?(u(),g(l(k),{key:0},{default:i(()=>[a(l(x),null,{default:i(()=>[a(l(w),null,{default:i(()=>s[11]||(s[11]=[r("Profil Peserta")])),_:1,__:[11]})]),_:1}),a(l(y),null,{default:i(()=>[t("div",ss,[t("div",es,[t("div",null,[a(l(d),{class:"text-sm font-medium text-gray-500"},{default:i(()=>s[12]||(s[12]=[r("NIK")])),_:1,__:[12]}),t("p",ts,n(e.user.peserta.nik),1)]),t("div",null,[a(l(d),{class:"text-sm font-medium text-gray-500"},{default:i(()=>s[13]||(s[13]=[r("Tempat, Tanggal Lahir")])),_:1,__:[13]}),t("p",as,n(e.user.peserta.tempat_lahir)+", "+n(m(e.user.peserta.tanggal_lahir)),1)]),t("div",null,[a(l(d),{class:"text-sm font-medium text-gray-500"},{default:i(()=>s[14]||(s[14]=[r("Jenis Kelamin")])),_:1,__:[14]}),t("p",is,n(e.user.peserta.jenis_kelamin==="L"?"Laki-laki":"Perempuan"),1)]),t("div",null,[a(l(d),{class:"text-sm font-medium text-gray-500"},{default:i(()=>s[15]||(s[15]=[r("Status Peserta")])),_:1,__:[15]}),a(l(f),{variant:e.user.peserta.status_peserta==="approved"?"default":"secondary"},{default:i(()=>[r(n(e.user.peserta.status_peserta),1)]),_:1},8,["variant"])])]),t("div",ls,[t("div",null,[a(l(d),{class:"text-sm font-medium text-gray-500"},{default:i(()=>s[16]||(s[16]=[r("Alamat")])),_:1,__:[16]}),t("p",rs,n(e.user.peserta.alamat),1)]),t("div",null,[a(l(d),{class:"text-sm font-medium text-gray-500"},{default:i(()=>s[17]||(s[17]=[r("Nama Ayah")])),_:1,__:[17]}),t("p",ns,n(e.user.peserta.nama_ayah||"-"),1)]),t("div",null,[a(l(d),{class:"text-sm font-medium text-gray-500"},{default:i(()=>s[18]||(s[18]=[r("Nama Ibu")])),_:1,__:[18]}),t("p",ds,n(e.user.peserta.nama_ibu||"-"),1)]),t("div",null,[a(l(d),{class:"text-sm font-medium text-gray-500"},{default:i(()=>s[19]||(s[19]=[r("Pekerjaan")])),_:1,__:[19]}),t("p",us,n(e.user.peserta.pekerjaan||"-"),1)])])])]),_:1})]),_:1})):_("",!0),e.user.role==="dewan_hakim"&&e.user.dewa_hakim?(u(),g(l(k),{key:1},{default:i(()=>[a(l(x),null,{default:i(()=>[a(l(w),null,{default:i(()=>s[20]||(s[20]=[r("Profil Dewan Hakim")])),_:1,__:[20]})]),_:1}),a(l(y),null,{default:i(()=>[t("div",ms,[t("div",os,[t("div",null,[a(l(d),{class:"text-sm font-medium text-gray-500"},{default:i(()=>s[21]||(s[21]=[r("NIK")])),_:1,__:[21]}),t("p",_s,n(e.user.dewa_hakim.nik),1)]),t("div",null,[a(l(d),{class:"text-sm font-medium text-gray-500"},{default:i(()=>s[22]||(s[22]=[r("Tempat, Tanggal Lahir")])),_:1,__:[22]}),t("p",fs,n(e.user.dewa_hakim.tempat_lahir)+", "+n(m(e.user.dewa_hakim.tanggal_lahir)),1)]),t("div",null,[a(l(d),{class:"text-sm font-medium text-gray-500"},{default:i(()=>s[23]||(s[23]=[r("Pekerjaan")])),_:1,__:[23]}),t("p",ps,n(e.user.dewa_hakim.pekerjaan),1)]),t("div",null,[a(l(d),{class:"text-sm font-medium text-gray-500"},{default:i(()=>s[24]||(s[24]=[r("Unit Kerja")])),_:1,__:[24]}),t("p",gs,n(e.user.dewa_hakim.unit_kerja),1)])]),t("div",vs,[t("div",null,[a(l(d),{class:"text-sm font-medium text-gray-500"},{default:i(()=>s[25]||(s[25]=[r("Spesialisasi")])),_:1,__:[25]}),t("p",ks,n(e.user.dewa_hakim.spesialisasi),1)]),t("div",null,[a(l(d),{class:"text-sm font-medium text-gray-500"},{default:i(()=>s[26]||(s[26]=[r("Tipe Hakim")])),_:1,__:[26]}),t("p",ys,n(e.user.dewa_hakim.tipe_hakim),1)]),t("div",null,[a(l(d),{class:"text-sm font-medium text-gray-500"},{default:i(()=>s[27]||(s[27]=[r("Status")])),_:1,__:[27]}),a(l(f),{variant:e.user.dewa_hakim.status==="aktif"?"default":"secondary"},{default:i(()=>[r(n(e.user.dewa_hakim.status),1)]),_:1},8,["variant"])])])])]),_:1})]),_:1})):_("",!0),t("div",xs,[a(v,{variant:"outline",onClick:s[0]||(s[0]=o=>e.$inertia.visit(e.route("admin.users.index")))},{default:i(()=>[a(p,{name:"arrow-left",class:"w-4 h-4 mr-2"}),s[28]||(s[28]=r(" Kembali "))]),_:1,__:[28]}),t("div",ws,[a(v,{variant:"outline",onClick:N,disabled:e.user.role==="superadmin"},{default:i(()=>[a(p,{name:e.user.status==="aktif"?"user-x":"user-check",class:"w-4 h-4 mr-2"},null,8,["name"]),r(" "+n(e.user.status==="aktif"?"Non-aktifkan":"Aktifkan"),1)]),_:1},8,["disabled"]),a(v,{onClick:s[1]||(s[1]=o=>e.$inertia.visit(e.route("admin.users.edit",e.user.id_user)))},{default:i(()=>[a(p,{name:"edit",class:"w-4 h-4 mr-2"}),s[29]||(s[29]=r(" Edit User "))]),_:1,__:[29]})])])])]),_:1}))}});export{Is as default};
