import{d as s,j as n,v as d,u as a,o as u,h as m,n as p,Q as f}from"./app-B_pmlBSQ.js";import{a as c}from"./AppLogoIcon.vue_vue_type_script_setup_true_lang-wzz4PWna.js";import{u as b}from"./useForwardExpose-CO14IhkA.js";const w=s({__name:"Input",props:{defaultValue:{},modelValue:{},class:{}},emits:["update:modelValue"],setup(r,{emit:o}){const e=r,t=b(e,"modelValue",o,{passive:!0,defaultValue:e.defaultValue});return(x,i)=>n((u(),m("input",{"onUpdate:modelValue":i[0]||(i[0]=l=>f(t)?t.value=l:null),"data-slot":"input",class:p(a(c)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e.class))},null,2)),[[d,a(t)]])}});export{w as _};
