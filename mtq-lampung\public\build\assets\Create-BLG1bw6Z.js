import{d as M,x as V,c as g,o as l,w as n,a as e,b as s,u as a,g as $,e as m,f as c,h as d,i as o,n as u,t as p,j as A,v as K}from"./app-B_pmlBSQ.js";import{_ as C}from"./AppLayout.vue_vue_type_script_setup_true_lang-BRf8lUiM.js";import{_ as S}from"./Heading.vue_vue_type_script_setup_true_lang-is4VBSCS.js";import{_ as k}from"./AppLogoIcon.vue_vue_type_script_setup_true_lang-wzz4PWna.js";import{_ as U,a as w}from"./CardContent.vue_vue_type_script_setup_true_lang-ClvLNBXC.js";import{_ as B}from"./CardDescription.vue_vue_type_script_setup_true_lang-DboywBIK.js";import{_ as N,a as T}from"./CardTitle.vue_vue_type_script_setup_true_lang-5HO6oSoB.js";import{_ as f}from"./Input.vue_vue_type_script_setup_true_lang-DB1-RsTn.js";import{_ as b}from"./Label.vue_vue_type_script_setup_true_lang-rj_fhE7R.js";import{_ as I,a as D,b as q,c as O,d as x}from"./SelectValue.vue_vue_type_script_setup_true_lang-IzlIronZ.js";import{_ as j}from"./Icon.vue_vue_type_script_setup_true_lang-BuYzjckr.js";import"./useForwardExpose-CO14IhkA.js";import"./RovingFocusGroup-lsWyU4xZ.js";import"./check-DtmHjdxf.js";import"./useFormControl-mZRLifGx.js";import"./loader-circle-6d8QrWFr.js";import"./sun-CHIV9RJ8.js";const P={class:"max-w-2xl mx-auto"},J={class:"space-y-4"},L={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},R={key:0,class:"text-sm text-red-600 mt-1"},z={key:0,class:"text-sm text-red-600 mt-1"},E={key:0,class:"text-sm text-red-600 mt-1"},Q={key:0,class:"text-sm text-red-600 mt-1"},h={key:0,class:"text-sm text-red-600 mt-1"},F={class:"flex justify-end space-x-4 pt-6 border-t"},pa=M({__name:"Create",setup(G){const v=[{title:"Dashboard",href:"/admin/dashboard"},{title:"Manajemen Mimbar",href:"/admin/mimbar"},{title:"Tambah Mimbar",href:"/admin/mimbar/create"}],r=V({kode_mimbar:"",nama_mimbar:"",keterangan:"",kapasitas:"",status:"aktif"}),y=()=>{r.post(route("admin.mimbar.store"),{onSuccess:()=>{}})};return(_,t)=>(l(),g(C,{breadcrumbs:v},{default:n(()=>[e(a($),{title:"Tambah Mimbar"}),e(S,{title:"Tambah Mimbar"}),s("div",P,[e(a(U),null,{default:n(()=>[e(a(N),null,{default:n(()=>[e(a(T),null,{default:n(()=>t[6]||(t[6]=[m("Informasi Mimbar Baru")])),_:1,__:[6]}),e(a(B),null,{default:n(()=>t[7]||(t[7]=[m(" Lengkapi form di bawah untuk menambahkan mimbar/venue baru ")])),_:1,__:[7]})]),_:1}),e(a(w),null,{default:n(()=>[s("form",{onSubmit:c(y,["prevent"]),class:"space-y-6"},[s("div",J,[t[16]||(t[16]=s("h3",{class:"text-lg font-medium"},"Informasi Dasar",-1)),s("div",L,[s("div",null,[e(a(b),{for:"kode_mimbar"},{default:n(()=>t[8]||(t[8]=[m("Kode Mimbar *")])),_:1,__:[8]}),e(a(f),{id:"kode_mimbar",modelValue:a(r).kode_mimbar,"onUpdate:modelValue":t[0]||(t[0]=i=>a(r).kode_mimbar=i),type:"text",required:"",placeholder:"Contoh: A, B, M001",class:u({"border-red-500":a(r).errors.kode_mimbar})},null,8,["modelValue","class"]),a(r).errors.kode_mimbar?(l(),d("p",R,p(a(r).errors.kode_mimbar),1)):o("",!0)]),s("div",null,[e(a(b),{for:"status"},{default:n(()=>t[9]||(t[9]=[m("Status *")])),_:1,__:[9]}),e(a(I),{modelValue:a(r).status,"onUpdate:modelValue":t[1]||(t[1]=i=>a(r).status=i),required:""},{default:n(()=>[e(a(D),{class:u({"border-red-500":a(r).errors.status})},{default:n(()=>[e(a(q),{placeholder:"Pilih Status"})]),_:1},8,["class"]),e(a(O),null,{default:n(()=>[e(a(x),{value:"aktif"},{default:n(()=>t[10]||(t[10]=[m("Aktif")])),_:1,__:[10]}),e(a(x),{value:"non_aktif"},{default:n(()=>t[11]||(t[11]=[m("Non Aktif")])),_:1,__:[11]})]),_:1})]),_:1},8,["modelValue"]),a(r).errors.status?(l(),d("p",z,p(a(r).errors.status),1)):o("",!0)])]),s("div",null,[e(a(b),{for:"nama_mimbar"},{default:n(()=>t[12]||(t[12]=[m("Nama Mimbar *")])),_:1,__:[12]}),e(a(f),{id:"nama_mimbar",modelValue:a(r).nama_mimbar,"onUpdate:modelValue":t[2]||(t[2]=i=>a(r).nama_mimbar=i),type:"text",required:"",placeholder:"Contoh: Mimbar Utama, Aula Serbaguna",class:u({"border-red-500":a(r).errors.nama_mimbar})},null,8,["modelValue","class"]),a(r).errors.nama_mimbar?(l(),d("p",E,p(a(r).errors.nama_mimbar),1)):o("",!0)]),s("div",null,[e(a(b),{for:"kapasitas"},{default:n(()=>t[13]||(t[13]=[m("Kapasitas *")])),_:1,__:[13]}),e(a(f),{id:"kapasitas",modelValue:a(r).kapasitas,"onUpdate:modelValue":t[3]||(t[3]=i=>a(r).kapasitas=i),type:"number",required:"",min:"1",placeholder:"Contoh: 100",class:u({"border-red-500":a(r).errors.kapasitas})},null,8,["modelValue","class"]),a(r).errors.kapasitas?(l(),d("p",Q,p(a(r).errors.kapasitas),1)):o("",!0),t[14]||(t[14]=s("p",{class:"text-sm text-gray-500 mt-1"}," Jumlah maksimum peserta yang dapat menggunakan mimbar ini ",-1))]),s("div",null,[e(a(b),{for:"keterangan"},{default:n(()=>t[15]||(t[15]=[m("Keterangan")])),_:1,__:[15]}),A(s("textarea",{id:"keterangan","onUpdate:modelValue":t[4]||(t[4]=i=>a(r).keterangan=i),rows:"4",class:u(["w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",{"border-red-500":a(r).errors.keterangan}]),placeholder:"Deskripsi mimbar, fasilitas yang tersedia, lokasi, dll..."},null,2),[[K,a(r).keterangan]]),a(r).errors.keterangan?(l(),d("p",h,p(a(r).errors.keterangan),1)):o("",!0)])]),t[19]||(t[19]=s("div",{class:"bg-blue-50 p-4 rounded-lg"},[s("h4",{class:"font-medium text-blue-900 mb-2"},"Informasi Mimbar"),s("div",{class:"text-sm text-blue-800 space-y-1"},[s("p",null,[s("strong",null,"Kode Mimbar:"),m(" Singkatan unik untuk identifikasi mimbar/venue")]),s("p",null,[s("strong",null,"Nama Mimbar:"),m(" Nama lengkap mimbar/venue yang akan ditampilkan")]),s("p",null,[s("strong",null,"Kapasitas:"),m(" Jumlah maksimum peserta yang dapat menggunakan mimbar")]),s("p",null,[s("strong",null,"Keterangan:"),m(" Informasi tambahan tentang fasilitas dan lokasi")]),s("p",null,[s("strong",null,"Status:"),m(" Menentukan apakah mimbar dapat digunakan untuk lomba")])])],-1)),t[20]||(t[20]=s("div",{class:"bg-gray-50 p-4 rounded-lg"},[s("h4",{class:"font-medium text-gray-900 mb-2"},"Contoh Mimbar MTQ"),s("div",{class:"text-sm text-gray-600 space-y-2"},[s("div",{class:"grid grid-cols-2 gap-4"},[s("div",null,[s("p",null,[s("strong",null,"A"),m(" - Mimbar A (Kapasitas: 50)")]),s("p",null,[s("strong",null,"B"),m(" - Mimbar B (Kapasitas: 50)")]),s("p",null,[s("strong",null,"UTAMA"),m(" - Mimbar Utama (Kapasitas: 100)")])]),s("div",null,[s("p",null,[s("strong",null,"AULA"),m(" - Aula Serbaguna (Kapasitas: 200)")]),s("p",null,[s("strong",null,"OUTDOOR"),m(" - Area Outdoor (Kapasitas: 300)")]),s("p",null,[s("strong",null,"VIP"),m(" - Ruang VIP (Kapasitas: 30)")])])])])],-1)),s("div",F,[e(k,{type:"button",variant:"outline",onClick:t[5]||(t[5]=i=>_.$inertia.visit(_.route("admin.mimbar.index")))},{default:n(()=>t[17]||(t[17]=[m(" Batal ")])),_:1,__:[17]}),e(k,{type:"submit",disabled:a(r).processing},{default:n(()=>[a(r).processing?(l(),g(j,{key:0,name:"loader-2",class:"w-4 h-4 mr-2 animate-spin"})):o("",!0),t[18]||(t[18]=m(" Simpan Mimbar "))]),_:1,__:[18]},8,["disabled"])])],32)]),_:1})]),_:1})])]),_:1}))}});export{pa as default};
