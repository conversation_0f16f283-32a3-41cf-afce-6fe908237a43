import{i as We,f as je,d as J,b as Ue,c as re,h as Z,e as me,P as qe,u as Be,g as Pe}from"./RovingFocusGroup-lsWyU4xZ.js";import{l as S,k as F,d as P,s as oe,c as B,o as _,w as y,b as Te,E as D,D as x,u as t,C as ve,i as G,h as Y,F as $e,m as Ye,J as j,H as ee,a as E,p as Ge,G as ie,f as te,V as Xe,O as ke,P as Ee,T as Je,e as he,K as Ze,a0 as Qe,t as et,n as tt}from"./app-B_pmlBSQ.js";import{P as z,a as X}from"./AppLogoIcon.vue_vue_type_script_setup_true_lang-wzz4PWna.js";import{C as Ie,a as ot}from"./Icon.vue_vue_type_script_setup_true_lang-BuYzjckr.js";import{u as _e,a as N,n as at,b as nt,l as ae}from"./useForwardExpose-CO14IhkA.js";import{h as st,i as lt,u as rt,j as it,k as dt,l as Oe,m as ut,n as ct,o as Ce,p as pt,q as ft,r as mt}from"./AppLayout.vue_vue_type_script_setup_true_lang-BRf8lUiM.js";import{C as vt}from"./check-DtmHjdxf.js";import{i as ht,a as Se,u as gt}from"./useFormControl-mZRLifGx.js";function yt(n){const o=We({nonce:S()});return F(()=>{var e;return(n==null?void 0:n.value)||((e=o.nonce)==null?void 0:e.value)})}function xe(n,o=Number.NEGATIVE_INFINITY,e=Number.POSITIVE_INFINITY){return Math.min(e,Math.max(o,n))}const wt=[" ","Enter","ArrowUp","ArrowDown"],bt=[" ","Enter"],V=10;function le(n,o,e){return n===void 0?!1:Array.isArray(n)?n.some(u=>fe(u,o,e)):fe(n,o,e)}function fe(n,o,e){return n===void 0||o===void 0?!1:typeof n=="string"?n===o:typeof e=="function"?e(n,o):typeof e=="string"?(n==null?void 0:n[e])===(o==null?void 0:o[e]):ht(n,o)}function _t(n){return n==null||n===""||Array.isArray(n)&&n.length===0}const Ct=P({__name:"BubbleSelect",props:{autocomplete:{},autofocus:{type:Boolean},disabled:{type:Boolean},form:{},multiple:{type:Boolean},name:{},required:{type:Boolean},size:{},value:{}},setup(n){const o=n,e=S();return oe(()=>o.value,(u,a)=>{const r=window.HTMLSelectElement.prototype,d=Object.getOwnPropertyDescriptor(r,"value").set;if(u!==a&&d&&e.value){const l=new Event("change",{bubbles:!0});d.call(e.value,u),e.value.dispatchEvent(l)}}),(u,a)=>(_(),B(t(je),{"as-child":""},{default:y(()=>[Te("select",D({ref_key:"selectElement",ref:e},o),[x(u.$slots,"default")],16)]),_:3}))}}),St={key:0,value:""},[U,Ae]=re("SelectRoot"),xt=P({inheritAttrs:!1,__name:"SelectRoot",props:{open:{type:Boolean,default:void 0},defaultOpen:{type:Boolean},defaultValue:{},modelValue:{default:void 0},by:{},dir:{},multiple:{type:Boolean},autocomplete:{},disabled:{type:Boolean},name:{},required:{type:Boolean}},emits:["update:modelValue","update:open"],setup(n,{emit:o}){const e=n,u=o,{required:a,disabled:r,multiple:i,dir:d}=ve(e),l=_e(e,"modelValue",u,{defaultValue:e.defaultValue??(i.value?[]:void 0),passive:e.modelValue===void 0,deep:!0}),s=_e(e,"open",u,{defaultValue:e.defaultOpen,passive:e.open===void 0}),f=S(),h=S(),m=S({x:0,y:0}),w=F(()=>{var c;return i.value&&Array.isArray(l.value)?((c=l.value)==null?void 0:c.length)===0:Se(l.value)});J({isProvider:!0});const O=Ue(d),A=gt(f),I=S(new Set),M=F(()=>Array.from(I.value).map(c=>c.value).join(";"));function g(c){if(i.value){const p=Array.isArray(l.value)?[...l.value]:[],b=p.findIndex(v=>fe(v,c,e.by));b===-1?p.push(c):p.splice(b,1),l.value=[...p]}else l.value=c}return Ae({triggerElement:f,onTriggerChange:c=>{f.value=c},valueElement:h,onValueElementChange:c=>{h.value=c},contentId:"",modelValue:l,onValueChange:g,by:e.by,open:s,multiple:i,required:a,onOpenChange:c=>{s.value=c},dir:O,triggerPointerDownPosRef:m,disabled:r,isEmptyModelValue:w,optionsSet:I,onOptionAdd:c=>I.value.add(c),onOptionRemove:c=>I.value.delete(c)}),(c,p)=>(_(),B(t(st),null,{default:y(()=>[x(c.$slots,"default",{modelValue:t(l),open:t(s)}),t(A)?(_(),B(Ct,{key:M.value,"aria-hidden":"true",tabindex:"-1",multiple:t(i),required:t(a),name:c.name,autocomplete:c.autocomplete,disabled:t(r),value:t(l)},{default:y(()=>[t(Se)(t(l))?(_(),Y("option",St)):G("",!0),(_(!0),Y($e,null,Ye(Array.from(I.value),b=>(_(),Y("option",D({key:b.value??"",ref_for:!0},b),null,16))),128))]),_:1},8,["multiple","required","name","autocomplete","disabled","value"])):G("",!0)]),_:3}))}}),[ge,Bt]=re("SelectItemAlignedPosition"),Pt=P({inheritAttrs:!1,__name:"SelectItemAlignedPosition",props:{asChild:{type:Boolean},as:{}},emits:["placed"],setup(n,{emit:o}){const e=n,u=o,{getItems:a}=J(),r=U(),i=q(),d=S(!1),l=S(!0),s=S(),{forwardRef:f,currentElement:h}=N(),{viewport:m,selectedItem:w,selectedItemText:O,focusSelectedItem:A}=i;function I(){if(r.triggerElement.value&&r.valueElement.value&&s.value&&h.value&&(m!=null&&m.value)&&(w!=null&&w.value)&&(O!=null&&O.value)){const c=r.triggerElement.value.getBoundingClientRect(),p=h.value.getBoundingClientRect(),b=r.valueElement.value.getBoundingClientRect(),v=O.value.getBoundingClientRect();if(r.dir.value!=="rtl"){const H=v.left-p.left,L=b.left-H,K=c.left-L,W=c.width+K,ue=Math.max(W,p.width),ce=window.innerWidth-V,pe=xe(L,V,Math.max(V,ce-ue));s.value.style.minWidth=`${W}px`,s.value.style.left=`${pe}px`}else{const H=p.right-v.right,L=window.innerWidth-b.right-H,K=window.innerWidth-c.right-L,W=c.width+K,ue=Math.max(W,p.width),ce=window.innerWidth-V,pe=xe(L,V,Math.max(V,ce-ue));s.value.style.minWidth=`${W}px`,s.value.style.right=`${pe}px`}const C=a().map(H=>H.ref),T=window.innerHeight-V*2,$=m.value.scrollHeight,k=window.getComputedStyle(h.value),R=Number.parseInt(k.borderTopWidth,10),Q=Number.parseInt(k.paddingTop,10),ye=Number.parseInt(k.borderBottomWidth,10),Ve=Number.parseInt(k.paddingBottom,10),we=R+Q+$+Ve+ye,Fe=Math.min(w.value.offsetHeight*5,we),be=window.getComputedStyle(m.value),ze=Number.parseInt(be.paddingTop,10),He=Number.parseInt(be.paddingBottom,10),ne=c.top+c.height/2-V,Le=T-ne,de=w.value.offsetHeight/2,Ne=w.value.offsetTop+de,se=R+Q+Ne,Ke=we-se;if(se<=ne){const H=w.value===C[C.length-1];s.value.style.bottom="0px";const L=h.value.clientHeight-m.value.offsetTop-m.value.offsetHeight,K=Math.max(Le,de+(H?He:0)+L+ye),W=se+K;s.value.style.height=`${W}px`}else{const H=w.value===C[0];s.value.style.top="0px";const K=Math.max(ne,R+m.value.offsetTop+(H?ze:0)+de)+Ke;s.value.style.height=`${K}px`,m.value.scrollTop=se-ne+m.value.offsetTop}s.value.style.margin=`${V}px 0`,s.value.style.minHeight=`${Fe}px`,s.value.style.maxHeight=`${T}px`,u("placed"),requestAnimationFrame(()=>d.value=!0)}}const M=S("");j(async()=>{await ee(),I(),h.value&&(M.value=window.getComputedStyle(h.value).zIndex)});function g(c){c&&l.value===!0&&(I(),A==null||A(),l.value=!1)}return at(r.triggerElement,()=>{I()}),Bt({contentWrapper:s,shouldExpandOnScrollRef:d,onScrollButtonChange:g}),(c,p)=>(_(),Y("div",{ref_key:"contentWrapperElement",ref:s,style:Ge({display:"flex",flexDirection:"column",position:"fixed",zIndex:M.value})},[E(t(z),D({ref:t(f),style:{boxSizing:"border-box",maxHeight:"100%"}},{...c.$attrs,...e}),{default:y(()=>[x(c.$slots,"default")]),_:3},16)],4))}}),Tt=P({__name:"SelectPopperPosition",props:{side:{},sideOffset:{},align:{default:"start"},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{default:V},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},positionStrategy:{},updatePositionStrategy:{},disableUpdateOnLayoutShift:{type:Boolean},prioritizePosition:{type:Boolean},reference:{},asChild:{type:Boolean},as:{}},setup(n){const e=Z(n);return(u,a)=>(_(),B(t(lt),D(t(e),{style:{boxSizing:"border-box","--reka-select-content-transform-origin":"var(--reka-popper-transform-origin)","--reka-select-content-available-width":"var(--reka-popper-available-width)","--reka-select-content-available-height":"var(--reka-popper-available-height)","--reka-select-trigger-width":"var(--reka-popper-anchor-width)","--reka-select-trigger-height":"var(--reka-popper-anchor-height)"}}),{default:y(()=>[x(u.$slots,"default")]),_:3},16))}}),$t={onViewportChange:()=>{},itemTextRefCallback:()=>{},itemRefCallback:()=>{}},[q,De]=re("SelectContent"),kt=P({__name:"SelectContentImpl",props:{position:{default:"item-aligned"},bodyLock:{type:Boolean,default:!0},side:{},sideOffset:{},align:{default:"start"},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},positionStrategy:{},updatePositionStrategy:{},disableUpdateOnLayoutShift:{type:Boolean},prioritizePosition:{type:Boolean},reference:{},asChild:{type:Boolean},as:{}},emits:["closeAutoFocus","escapeKeyDown","pointerDownOutside"],setup(n,{emit:o}){const e=n,u=o,a=U();rt(),it(e.bodyLock);const{CollectionSlot:r,getItems:i}=J(),d=S();dt(d);const{search:l,handleTypeaheadSearch:s}=Oe(),f=S(),h=S(),m=S(),w=S(!1),O=S(!1),A=S(!1);function I(){h.value&&d.value&&Ce([h.value,d.value])}oe(w,()=>{I()});const{onOpenChange:M,triggerPointerDownPosRef:g}=a;ie(v=>{if(!d.value)return;let C={x:0,y:0};const T=k=>{var R,Q;C={x:Math.abs(Math.round(k.pageX)-(((R=g.value)==null?void 0:R.x)??0)),y:Math.abs(Math.round(k.pageY)-(((Q=g.value)==null?void 0:Q.y)??0))}},$=k=>{var R;k.pointerType!=="touch"&&(C.x<=10&&C.y<=10?k.preventDefault():(R=d.value)!=null&&R.contains(k.target)||M(!1),document.removeEventListener("pointermove",T),g.value=null)};g.value!==null&&(document.addEventListener("pointermove",T),document.addEventListener("pointerup",$,{capture:!0,once:!0})),v(()=>{document.removeEventListener("pointermove",T),document.removeEventListener("pointerup",$,{capture:!0})})});function c(v){const C=v.ctrlKey||v.altKey||v.metaKey;if(v.key==="Tab"&&v.preventDefault(),!C&&v.key.length===1&&s(v.key,i()),["ArrowUp","ArrowDown","Home","End"].includes(v.key)){let $=[...i().map(k=>k.ref)];if(["ArrowUp","End"].includes(v.key)&&($=$.slice().reverse()),["ArrowUp","ArrowDown"].includes(v.key)){const k=v.target,R=$.indexOf(k);$=$.slice(R+1)}setTimeout(()=>Ce($)),v.preventDefault()}}const p=F(()=>e.position==="popper"?e:{}),b=Z(p.value);return De({content:d,viewport:f,onViewportChange:v=>{f.value=v},itemRefCallback:(v,C,T)=>{const $=!O.value&&!T,k=le(a.modelValue.value,C,a.by);if(a.multiple.value){if(A.value)return;(k||$)&&(h.value=v,k&&(A.value=!0))}else(k||$)&&(h.value=v);$&&(O.value=!0)},selectedItem:h,selectedItemText:m,onItemLeave:()=>{var v;(v=d.value)==null||v.focus()},itemTextRefCallback:(v,C,T)=>{const $=!O.value&&!T;(le(a.modelValue.value,C,a.by)||$)&&(m.value=v)},focusSelectedItem:I,position:e.position,isPositioned:w,searchRef:l}),(v,C)=>(_(),B(t(r),null,{default:y(()=>[E(t(ut),{"as-child":"",onMountAutoFocus:C[6]||(C[6]=te(()=>{},["prevent"])),onUnmountAutoFocus:C[7]||(C[7]=T=>{var $;u("closeAutoFocus",T),!T.defaultPrevented&&(($=t(a).triggerElement.value)==null||$.focus({preventScroll:!0}),T.preventDefault())})},{default:y(()=>[E(t(ct),{"as-child":"","disable-outside-pointer-events":"",onFocusOutside:C[2]||(C[2]=te(()=>{},["prevent"])),onDismiss:C[3]||(C[3]=T=>t(a).onOpenChange(!1)),onEscapeKeyDown:C[4]||(C[4]=T=>u("escapeKeyDown",T)),onPointerDownOutside:C[5]||(C[5]=T=>u("pointerDownOutside",T))},{default:y(()=>[(_(),B(Xe(v.position==="popper"?Tt:Pt),D({...v.$attrs,...t(b)},{id:t(a).contentId,ref:T=>{d.value=t(nt)(T)},role:"listbox","data-state":t(a).open.value?"open":"closed",dir:t(a).dir.value,style:{display:"flex",flexDirection:"column",outline:"none"},onContextmenu:C[0]||(C[0]=te(()=>{},["prevent"])),onPlaced:C[1]||(C[1]=T=>w.value=!0),onKeydown:c}),{default:y(()=>[x(v.$slots,"default")]),_:3},16,["id","data-state","dir","onKeydown"]))]),_:3})]),_:3})]),_:3}))}}),Et=P({inheritAttrs:!1,__name:"SelectProvider",props:{context:{}},setup(n){return Ae(n.context),De($t),(e,u)=>x(e.$slots,"default")}}),It={key:1},Ot=P({inheritAttrs:!1,__name:"SelectContent",props:{forceMount:{type:Boolean},position:{},bodyLock:{type:Boolean},side:{},sideOffset:{},align:{},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},positionStrategy:{},updatePositionStrategy:{},disableUpdateOnLayoutShift:{type:Boolean},prioritizePosition:{type:Boolean},reference:{},asChild:{type:Boolean},as:{}},emits:["closeAutoFocus","escapeKeyDown","pointerDownOutside"],setup(n,{emit:o}){const e=n,a=me(e,o),r=U(),i=S();j(()=>{i.value=new DocumentFragment});const d=S(),l=F(()=>e.forceMount||r.open.value),s=S(l.value);return oe(l,()=>{setTimeout(()=>s.value=l.value)}),(f,h)=>{var m;return l.value||s.value||(m=d.value)!=null&&m.present?(_(),B(t(qe),{key:0,ref_key:"presenceRef",ref:d,present:l.value},{default:y(()=>[E(kt,ke(Ee({...t(a),...f.$attrs})),{default:y(()=>[x(f.$slots,"default")]),_:3},16)]),_:3},8,["present"])):i.value?(_(),Y("div",It,[(_(),B(Je,{to:i.value},[E(Et,{context:t(r)},{default:y(()=>[x(f.$slots,"default")]),_:3},8,["context"])],8,["to"]))])):G("",!0)}}}),At=P({__name:"SelectIcon",props:{asChild:{type:Boolean},as:{default:"span"}},setup(n){return(o,e)=>(_(),B(t(z),{"aria-hidden":"true",as:o.as,"as-child":o.asChild},{default:y(()=>[x(o.$slots,"default",{},()=>[e[0]||(e[0]=he("▼"))])]),_:3},8,["as","as-child"]))}}),[Me,Dt]=re("SelectItem"),Mt=P({__name:"SelectItem",props:{value:{},disabled:{type:Boolean},textValue:{},asChild:{type:Boolean},as:{}},emits:["select"],setup(n,{emit:o}){const e=n,u=o,{disabled:a}=ve(e),r=U(),i=q(),{forwardRef:d,currentElement:l}=N(),{CollectionItem:s}=J(),f=F(()=>{var p;return le((p=r.modelValue)==null?void 0:p.value,e.value,r.by)}),h=S(!1),m=S(e.textValue??""),w=Be(void 0,"reka-select-item-text"),O="select.select";async function A(p){if(p.defaultPrevented)return;const b={originalEvent:p,value:e.value};pt(O,I,b)}async function I(p){await ee(),u("select",p),!p.defaultPrevented&&(a.value||(r.onValueChange(e.value),r.multiple.value||r.onOpenChange(!1)))}async function M(p){var b,v;await ee(),!p.defaultPrevented&&(a.value?(b=i.onItemLeave)==null||b.call(i):(v=p.currentTarget)==null||v.focus({preventScroll:!0}))}async function g(p){var b;await ee(),!p.defaultPrevented&&p.currentTarget===Pe()&&((b=i.onItemLeave)==null||b.call(i))}async function c(p){var v;await ee(),!(p.defaultPrevented||((v=i.searchRef)==null?void 0:v.value)!==""&&p.key===" ")&&(bt.includes(p.key)&&A(p),p.key===" "&&p.preventDefault())}if(e.value==="")throw new Error("A <SelectItem /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return j(()=>{l.value&&i.itemRefCallback(l.value,e.value,e.disabled)}),Dt({value:e.value,disabled:a,textId:w,isSelected:f,onItemTextChange:p=>{m.value=((m.value||(p==null?void 0:p.textContent))??"").trim()}}),(p,b)=>(_(),B(t(s),{value:{textValue:m.value}},{default:y(()=>[E(t(z),{ref:t(d),role:"option","aria-labelledby":t(w),"data-highlighted":h.value?"":void 0,"aria-selected":f.value,"data-state":f.value?"checked":"unchecked","aria-disabled":t(a)||void 0,"data-disabled":t(a)?"":void 0,tabindex:t(a)?void 0:-1,as:p.as,"as-child":p.asChild,onFocus:b[0]||(b[0]=v=>h.value=!0),onBlur:b[1]||(b[1]=v=>h.value=!1),onPointerup:A,onPointerdown:b[2]||(b[2]=v=>{v.currentTarget.focus({preventScroll:!0})}),onTouchend:b[3]||(b[3]=te(()=>{},["prevent","stop"])),onPointermove:M,onPointerleave:g,onKeydown:c},{default:y(()=>[x(p.$slots,"default")]),_:3},8,["aria-labelledby","data-highlighted","aria-selected","data-state","aria-disabled","data-disabled","tabindex","as","as-child"])]),_:3},8,["value"]))}}),Rt=P({__name:"SelectItemIndicator",props:{asChild:{type:Boolean},as:{default:"span"}},setup(n){const o=n,e=Me();return(u,a)=>t(e).isSelected.value?(_(),B(t(z),D({key:0,"aria-hidden":"true"},o),{default:y(()=>[x(u.$slots,"default")]),_:3},16)):G("",!0)}}),Vt=P({inheritAttrs:!1,__name:"SelectItemText",props:{asChild:{type:Boolean},as:{default:"span"}},setup(n){const o=n,e=U(),u=q(),a=Me(),{forwardRef:r,currentElement:i}=N(),d=F(()=>{var l,s;return{value:a.value,disabled:a.disabled.value,textContent:((l=i.value)==null?void 0:l.textContent)??((s=a.value)==null?void 0:s.toString())??""}});return j(()=>{i.value&&(a.onItemTextChange(i.value),u.itemTextRefCallback(i.value,a.value,a.disabled.value),e.onOptionAdd(d.value))}),Ze(()=>{e.onOptionRemove(d.value)}),(l,s)=>(_(),B(t(z),D({id:t(a).textId,ref:t(r)},{...o,...l.$attrs}),{default:y(()=>[x(l.$slots,"default")]),_:3},16,["id"]))}}),Ft=P({__name:"SelectPortal",props:{to:{},disabled:{type:Boolean},defer:{type:Boolean},forceMount:{type:Boolean}},setup(n){const o=n;return(e,u)=>(_(),B(t(ft),ke(Ee(o)),{default:y(()=>[x(e.$slots,"default")]),_:3},16))}}),Re=P({__name:"SelectScrollButtonImpl",emits:["autoScroll"],setup(n,{emit:o}){const e=o,{getItems:u}=J(),a=q(),r=S(null);function i(){r.value!==null&&(window.clearInterval(r.value),r.value=null)}ie(()=>{const s=u().map(f=>f.ref).find(f=>f===Pe());s==null||s.scrollIntoView({block:"nearest"})});function d(){r.value===null&&(r.value=window.setInterval(()=>{e("autoScroll")},50))}function l(){var s;(s=a.onItemLeave)==null||s.call(a),r.value===null&&(r.value=window.setInterval(()=>{e("autoScroll")},50))}return Qe(()=>i()),(s,f)=>{var h;return _(),B(t(z),D({"aria-hidden":"true",style:{flexShrink:0}},(h=s.$parent)==null?void 0:h.$props,{onPointerdown:d,onPointermove:l,onPointerleave:f[0]||(f[0]=()=>{i()})}),{default:y(()=>[x(s.$slots,"default")]),_:3},16)}}}),zt=P({__name:"SelectScrollDownButton",props:{asChild:{type:Boolean},as:{}},setup(n){const o=q(),e=o.position==="item-aligned"?ge():void 0,{forwardRef:u,currentElement:a}=N(),r=S(!1);return ie(i=>{var d,l;if((d=o.viewport)!=null&&d.value&&((l=o.isPositioned)!=null&&l.value)){let s=function(){const h=f.scrollHeight-f.clientHeight;r.value=Math.ceil(f.scrollTop)<h};const f=o.viewport.value;s(),f.addEventListener("scroll",s),i(()=>f.removeEventListener("scroll",s))}}),oe(a,()=>{a.value&&(e==null||e.onScrollButtonChange(a.value))}),(i,d)=>r.value?(_(),B(Re,{key:0,ref:t(u),onAutoScroll:d[0]||(d[0]=()=>{const{viewport:l,selectedItem:s}=t(o);l!=null&&l.value&&(s!=null&&s.value)&&(l.value.scrollTop=l.value.scrollTop+s.value.offsetHeight)})},{default:y(()=>[x(i.$slots,"default")]),_:3},512)):G("",!0)}}),Ht=P({__name:"SelectScrollUpButton",props:{asChild:{type:Boolean},as:{}},setup(n){const o=q(),e=o.position==="item-aligned"?ge():void 0,{forwardRef:u,currentElement:a}=N(),r=S(!1);return ie(i=>{var d,l;if((d=o.viewport)!=null&&d.value&&((l=o.isPositioned)!=null&&l.value)){let s=function(){r.value=f.scrollTop>0};const f=o.viewport.value;s(),f.addEventListener("scroll",s),i(()=>f.removeEventListener("scroll",s))}}),oe(a,()=>{a.value&&(e==null||e.onScrollButtonChange(a.value))}),(i,d)=>r.value?(_(),B(Re,{key:0,ref:t(u),onAutoScroll:d[0]||(d[0]=()=>{const{viewport:l,selectedItem:s}=t(o);l!=null&&l.value&&(s!=null&&s.value)&&(l.value.scrollTop=l.value.scrollTop-s.value.offsetHeight)})},{default:y(()=>[x(i.$slots,"default")]),_:3},512)):G("",!0)}}),Lt=P({__name:"SelectTrigger",props:{disabled:{type:Boolean},reference:{},asChild:{type:Boolean},as:{default:"button"}},setup(n){const o=n,e=U(),{forwardRef:u,currentElement:a}=N(),r=F(()=>{var m;return((m=e.disabled)==null?void 0:m.value)||o.disabled});e.contentId||(e.contentId=Be(void 0,"reka-select-content")),j(()=>{e.onTriggerChange(a.value)});const{getItems:i}=J(),{search:d,handleTypeaheadSearch:l,resetTypeahead:s}=Oe();function f(){r.value||(e.onOpenChange(!0),s())}function h(m){f(),e.triggerPointerDownPosRef.value={x:Math.round(m.pageX),y:Math.round(m.pageY)}}return(m,w)=>(_(),B(t(mt),{"as-child":"",reference:m.reference},{default:y(()=>{var O,A,I,M;return[E(t(z),{ref:t(u),role:"combobox",type:m.as==="button"?"button":void 0,"aria-controls":t(e).contentId,"aria-expanded":t(e).open.value||!1,"aria-required":(O=t(e).required)==null?void 0:O.value,"aria-autocomplete":"none",disabled:r.value,dir:(A=t(e))==null?void 0:A.dir.value,"data-state":(I=t(e))!=null&&I.open.value?"open":"closed","data-disabled":r.value?"":void 0,"data-placeholder":t(_t)((M=t(e).modelValue)==null?void 0:M.value)?"":void 0,"as-child":m.asChild,as:m.as,onClick:w[0]||(w[0]=g=>{var c;(c=g==null?void 0:g.currentTarget)==null||c.focus()}),onPointerdown:w[1]||(w[1]=g=>{if(g.pointerType==="touch")return g.preventDefault();const c=g.target;c.hasPointerCapture(g.pointerId)&&c.releasePointerCapture(g.pointerId),g.button===0&&g.ctrlKey===!1&&(h(g),g.preventDefault())}),onPointerup:w[2]||(w[2]=te(g=>{g.pointerType==="touch"&&h(g)},["prevent"])),onKeydown:w[3]||(w[3]=g=>{const c=t(d)!=="";!(g.ctrlKey||g.altKey||g.metaKey)&&g.key.length===1&&c&&g.key===" "||(t(l)(g.key,t(i)()),t(wt).includes(g.key)&&(f(),g.preventDefault()))})},{default:y(()=>[x(m.$slots,"default")]),_:3},8,["type","aria-controls","aria-expanded","aria-required","disabled","dir","data-state","data-disabled","data-placeholder","as-child","as"])]}),_:3},8,["reference"]))}}),Nt=P({__name:"SelectValue",props:{placeholder:{default:""},asChild:{type:Boolean},as:{default:"span"}},setup(n){const o=n,{forwardRef:e,currentElement:u}=N(),a=U();j(()=>{a.valueElement=u});const r=F(()=>{var f;let d=[];const l=Array.from(a.optionsSet.value),s=h=>l.find(m=>le(h,m.value,a.by));return Array.isArray(a.modelValue.value)?d=a.modelValue.value.map(h=>{var m;return((m=s(h))==null?void 0:m.textContent)??""}):d=[((f=s(a.modelValue.value))==null?void 0:f.textContent)??""],d.filter(Boolean)}),i=F(()=>r.value.length?r.value.join(", "):o.placeholder);return(d,l)=>(_(),B(t(z),{ref:t(e),as:d.as,"as-child":d.asChild,style:{pointerEvents:"none"},"data-placeholder":r.value.length?void 0:o.placeholder},{default:y(()=>[x(d.$slots,"default",{selectedLabel:r.value,modelValue:t(a).modelValue.value},()=>[he(et(i.value),1)])]),_:3},8,["as","as-child","data-placeholder"]))}}),Kt=P({__name:"SelectViewport",props:{nonce:{},asChild:{type:Boolean},as:{}},setup(n){const o=n,{nonce:e}=ve(o),u=yt(e),a=q(),r=a.position==="item-aligned"?ge():void 0,{forwardRef:i,currentElement:d}=N();j(()=>{a==null||a.onViewportChange(d.value)});const l=S(0);function s(f){const h=f.currentTarget,{shouldExpandOnScrollRef:m,contentWrapper:w}=r??{};if(m!=null&&m.value&&(w!=null&&w.value)){const O=Math.abs(l.value-h.scrollTop);if(O>0){const A=window.innerHeight-V*2,I=Number.parseFloat(w.value.style.minHeight),M=Number.parseFloat(w.value.style.height),g=Math.max(I,M);if(g<A){const c=g+O,p=Math.min(A,c),b=c-p;w.value.style.height=`${p}px`,w.value.style.bottom==="0px"&&(h.scrollTop=b>0?b:0,w.value.style.justifyContent="flex-end")}}}l.value=h.scrollTop}return(f,h)=>(_(),Y($e,null,[E(t(z),D({ref:t(i),"data-reka-select-viewport":"",role:"presentation"},{...f.$attrs,...o},{style:{position:"relative",flex:1,overflow:"hidden auto"},onScroll:s}),{default:y(()=>[x(f.$slots,"default")]),_:3},16),E(t(z),{as:"style",nonce:t(u)},{default:y(()=>h[0]||(h[0]=[he(" /* Hide scrollbars cross-browser and enable momentum scroll for touch devices */ [data-reka-select-viewport] { scrollbar-width:none; -ms-overflow-style: none; -webkit-overflow-scrolling: touch; } [data-reka-select-viewport]::-webkit-scrollbar { display: none; } ")])),_:1},8,["nonce"])],64))}}),oo=P({__name:"Select",props:{open:{type:Boolean},defaultOpen:{type:Boolean},defaultValue:{},modelValue:{},by:{type:[String,Function]},dir:{},multiple:{type:Boolean},autocomplete:{},disabled:{type:Boolean},name:{},required:{type:Boolean}},emits:["update:modelValue","update:open"],setup(n,{emit:o}){const a=me(n,o);return(r,i)=>(_(),B(t(xt),D({"data-slot":"select"},t(a)),{default:y(()=>[x(r.$slots,"default")]),_:3},16))}}),ao=P({inheritAttrs:!1,__name:"SelectContent",props:{forceMount:{type:Boolean},position:{default:"popper"},bodyLock:{type:Boolean},side:{},sideOffset:{},align:{},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},positionStrategy:{},updatePositionStrategy:{},disableUpdateOnLayoutShift:{type:Boolean},prioritizePosition:{type:Boolean},reference:{},asChild:{type:Boolean},as:{},class:{}},emits:["closeAutoFocus","escapeKeyDown","pointerDownOutside"],setup(n,{emit:o}){const e=n,u=o,a=ae(e,"class"),r=me(a,u);return(i,d)=>(_(),B(t(Ft),null,{default:y(()=>[E(t(Ot),D({"data-slot":"select-content"},{...t(r),...i.$attrs},{class:t(X)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--reka-select-content-available-height) min-w-[8rem] overflow-x-hidden overflow-y-auto rounded-md border shadow-md",i.position==="popper"&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e.class)}),{default:y(()=>[E(t(Ut)),E(t(Kt),{class:tt(t(X)("p-1",i.position==="popper"&&"h-[var(--reka-select-trigger-height)] w-full min-w-[var(--reka-select-trigger-width)] scroll-my-1"))},{default:y(()=>[x(i.$slots,"default")]),_:3},8,["class"]),E(t(jt))]),_:3},16,["class"])]),_:3}))}}),Wt={class:"absolute right-2 flex size-3.5 items-center justify-center"},no=P({__name:"SelectItem",props:{value:{},disabled:{type:Boolean},textValue:{},asChild:{type:Boolean},as:{type:[String,Object,Function]},class:{}},setup(n){const o=n,e=ae(o,"class"),u=Z(e);return(a,r)=>(_(),B(t(Mt),D({"data-slot":"select-item"},t(u),{class:t(X)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",o.class)}),{default:y(()=>[Te("span",Wt,[E(t(Rt),null,{default:y(()=>[E(t(vt),{class:"size-4"})]),_:1})]),E(t(Vt),null,{default:y(()=>[x(a.$slots,"default")]),_:3})]),_:3},16,["class"]))}}),jt=P({__name:"SelectScrollDownButton",props:{asChild:{type:Boolean},as:{type:[String,Object,Function]},class:{}},setup(n){const o=n,e=ae(o,"class"),u=Z(e);return(a,r)=>(_(),B(t(zt),D({"data-slot":"select-scroll-down-button"},t(u),{class:t(X)("flex cursor-default items-center justify-center py-1",o.class)}),{default:y(()=>[x(a.$slots,"default",{},()=>[E(t(Ie),{class:"size-4"})])]),_:3},16,["class"]))}}),Ut=P({__name:"SelectScrollUpButton",props:{asChild:{type:Boolean},as:{type:[String,Object,Function]},class:{}},setup(n){const o=n,e=ae(o,"class"),u=Z(e);return(a,r)=>(_(),B(t(Ht),D({"data-slot":"select-scroll-up-button"},t(u),{class:t(X)("flex cursor-default items-center justify-center py-1",o.class)}),{default:y(()=>[x(a.$slots,"default",{},()=>[E(t(ot),{class:"size-4"})])]),_:3},16,["class"]))}}),so=P({__name:"SelectTrigger",props:{disabled:{type:Boolean},reference:{},asChild:{type:Boolean},as:{},class:{},size:{default:"default"}},setup(n){const o=n,e=ae(o,"class","size"),u=Z(e);return(a,r)=>(_(),B(t(Lt),D({"data-slot":"select-trigger","data-size":a.size},t(u),{class:t(X)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",o.class)}),{default:y(()=>[x(a.$slots,"default"),E(t(At),{"as-child":""},{default:y(()=>[E(t(Ie),{class:"size-4 opacity-50"})]),_:1})]),_:3},16,["data-size","class"]))}}),lo=P({__name:"SelectValue",props:{placeholder:{},asChild:{type:Boolean},as:{type:[String,Object,Function]}},setup(n){const o=n;return(e,u)=>(_(),B(t(Nt),D({"data-slot":"select-value"},o),{default:y(()=>[x(e.$slots,"default")]),_:3},16))}});export{oo as _,so as a,lo as b,ao as c,no as d};
