import{d as K,r as M,c as p,o,w as e,b as n,a as t,u as l,e as i,h as v,F as h,m as b,t as d,i as C,W as k}from"./app-B_pmlBSQ.js";import{e as E,f as O,a as W,b as Y,c as A,d as w,g as R,_ as q}from"./AppLayout.vue_vue_type_script_setup_true_lang-BRf8lUiM.js";import{_ as J}from"./Heading.vue_vue_type_script_setup_true_lang-is4VBSCS.js";import{_}from"./AppLogoIcon.vue_vue_type_script_setup_true_lang-wzz4PWna.js";import{_ as D,a as S}from"./CardContent.vue_vue_type_script_setup_true_lang-ClvLNBXC.js";import{_ as y}from"./index-CMGr3-bt.js";import{_ as Q}from"./Input.vue_vue_type_script_setup_true_lang-DB1-RsTn.js";import{_ as $}from"./Label.vue_vue_type_script_setup_true_lang-rj_fhE7R.js";import{_ as V,a as j,b as P,c as L,d as f}from"./SelectValue.vue_vue_type_script_setup_true_lang-IzlIronZ.js";import{_ as m}from"./Icon.vue_vue_type_script_setup_true_lang-BuYzjckr.js";import{_ as X}from"./TextLink.vue_vue_type_script_setup_true_lang-CC2Og0lP.js";import"./useForwardExpose-CO14IhkA.js";import"./RovingFocusGroup-lsWyU4xZ.js";import"./check-DtmHjdxf.js";import"./useFormControl-mZRLifGx.js";import"./loader-circle-6d8QrWFr.js";import"./sun-CHIV9RJ8.js";const Z={class:"space-y-6"},aa={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},ta={class:"flex items-end"},ea={class:"flex justify-between items-center"},sa={class:"flex items-center space-x-4"},na={class:"overflow-x-auto"},ra={class:"min-w-full divide-y divide-gray-200"},la={class:"bg-white divide-y divide-gray-200"},ia={class:"px-6 py-4 whitespace-nowrap"},da={class:"flex items-center"},oa={class:"ml-4"},ua={class:"text-sm font-medium text-gray-900"},fa={class:"text-sm text-gray-500"},pa={class:"px-6 py-4 whitespace-nowrap"},ma={class:"text-sm text-gray-900"},_a={class:"text-sm text-gray-500"},ca={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},ga={class:"px-6 py-4 whitespace-nowrap"},va={class:"px-6 py-4 whitespace-nowrap"},ya={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500"},xa={class:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium space-x-2"},ha={class:"flex items-center justify-between"},ba={class:"flex items-center space-x-2"},ka={class:"text-sm text-gray-700"},wa={class:"flex items-center space-x-2"},Ma=K({__name:"Index",props:{pendaftaran:{},golongan:{}},setup($a){const u=M({search:"",status:"all",golongan:"all"}),c=()=>{const r=Object.fromEntries(Object.entries(u).filter(([a,s])=>s!==""&&s!=="all"));k.get(route("admin-daerah.pendaftaran.index"),r,{preserveState:!0,replace:!0})},N=()=>{u.search="",u.status="all",u.golongan="all",c()},B=r=>r.split(" ").map(a=>a[0]).join("").toUpperCase(),I=r=>({draft:"secondary",submitted:"default",verified:"default",approved:"default",rejected:"destructive"})[r]||"secondary",T=r=>({draft:"Draft",submitted:"Disubmit",verified:"Diverifikasi",approved:"Disetujui",rejected:"Ditolak"})[r]||r,U=r=>({pending:"secondary",approved:"default",rejected:"destructive"})[r]||"secondary",z=r=>({pending:"Pending",approved:"Lunas",rejected:"Ditolak"})[r]||r,F=r=>new Date(r).toLocaleDateString("id-ID",{day:"numeric",month:"short",year:"numeric"}),G=r=>{confirm("Yakin ingin submit pendaftaran ini?")&&k.post(route("admin-daerah.pendaftaran.submit",r))},H=r=>{confirm("Yakin ingin menghapus pendaftaran ini?")&&k.delete(route("admin-daerah.pendaftaran.destroy",r))};return(r,a)=>(o(),p(q,{title:"Kelola Pendaftaran"},{header:e(()=>[t(J,null,{default:e(()=>a[3]||(a[3]=[i("Kelola Pendaftaran")])),_:1,__:[3]})]),default:e(()=>[n("div",Z,[t(l(D),null,{default:e(()=>[t(l(S),{class:"p-6"},{default:e(()=>[n("div",aa,[n("div",null,[t(l($),{for:"search"},{default:e(()=>a[4]||(a[4]=[i("Pencarian")])),_:1,__:[4]}),t(l(Q),{id:"search",modelValue:u.search,"onUpdate:modelValue":a[0]||(a[0]=s=>u.search=s),placeholder:"Nama peserta, NIK...",onInput:c},null,8,["modelValue"])]),n("div",null,[t(l($),{for:"status"},{default:e(()=>a[5]||(a[5]=[i("Status Pendaftaran")])),_:1,__:[5]}),t(l(V),{modelValue:u.status,"onUpdate:modelValue":[a[1]||(a[1]=s=>u.status=s),c]},{default:e(()=>[t(l(j),null,{default:e(()=>[t(l(P),{placeholder:"Semua Status"})]),_:1}),t(l(L),null,{default:e(()=>[t(l(f),{value:"all"},{default:e(()=>a[6]||(a[6]=[i("Semua Status")])),_:1,__:[6]}),t(l(f),{value:"draft"},{default:e(()=>a[7]||(a[7]=[i("Draft")])),_:1,__:[7]}),t(l(f),{value:"submitted"},{default:e(()=>a[8]||(a[8]=[i("Disubmit")])),_:1,__:[8]}),t(l(f),{value:"verified"},{default:e(()=>a[9]||(a[9]=[i("Diverifikasi")])),_:1,__:[9]}),t(l(f),{value:"approved"},{default:e(()=>a[10]||(a[10]=[i("Disetujui")])),_:1,__:[10]}),t(l(f),{value:"rejected"},{default:e(()=>a[11]||(a[11]=[i("Ditolak")])),_:1,__:[11]})]),_:1})]),_:1},8,["modelValue"])]),n("div",null,[t(l($),{for:"golongan"},{default:e(()=>a[12]||(a[12]=[i("Golongan")])),_:1,__:[12]}),t(l(V),{modelValue:u.golongan,"onUpdate:modelValue":[a[2]||(a[2]=s=>u.golongan=s),c]},{default:e(()=>[t(l(j),null,{default:e(()=>[t(l(P),{placeholder:"Semua Golongan"})]),_:1}),t(l(L),null,{default:e(()=>[t(l(f),{value:"all"},{default:e(()=>a[13]||(a[13]=[i("Semua Golongan")])),_:1,__:[13]}),(o(!0),v(h,null,b(r.golongan,s=>(o(),p(l(f),{key:s.id_golongan,value:s.id_golongan.toString()},{default:e(()=>[i(d(s.nama_golongan),1)]),_:2},1032,["value"]))),128))]),_:1})]),_:1},8,["modelValue"])]),n("div",ta,[t(_,{onClick:N,variant:"outline",class:"w-full"},{default:e(()=>a[14]||(a[14]=[i(" Reset Filter ")])),_:1,__:[14]})])])]),_:1})]),_:1}),n("div",ea,[n("div",sa,[t(l(y),{variant:"secondary"},{default:e(()=>[i(" Total: "+d(r.pendaftaran.total)+" pendaftaran ",1)]),_:1})]),t(_,{"as-child":""},{default:e(()=>[t(X,{href:r.route("admin-daerah.pendaftaran.create")},{default:e(()=>[t(m,{name:"plus",class:"w-4 h-4 mr-2"}),a[15]||(a[15]=i(" Daftarkan Peserta "))]),_:1,__:[15]},8,["href"])]),_:1})]),t(l(D),null,{default:e(()=>[t(l(S),{class:"p-0"},{default:e(()=>[n("div",na,[n("table",ra,[a[20]||(a[20]=n("thead",{class:"bg-gray-50"},[n("tr",null,[n("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Peserta "),n("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Golongan "),n("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Nomor Urut "),n("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Status "),n("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Pembayaran "),n("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Tanggal "),n("th",{class:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"}," Aksi ")])],-1)),n("tbody",la,[(o(!0),v(h,null,b(r.pendaftaran.data,s=>{var x;return o(),v("tr",{key:s.id_pendaftaran,class:"hover:bg-gray-50"},[n("td",ia,[n("div",da,[t(E,{class:"h-10 w-10"},{default:e(()=>[t(O,null,{default:e(()=>[i(d(B(s.peserta.nama_lengkap)),1)]),_:2},1024)]),_:2},1024),n("div",oa,[n("div",ua,d(s.peserta.nama_lengkap),1),n("div",fa,d(s.peserta.nik),1)])])]),n("td",pa,[n("div",ma,d((x=s.golongan.cabang_lomba)==null?void 0:x.nama_cabang),1),n("div",_a,d(s.golongan.nama_golongan),1)]),n("td",ca,d(s.nomor_urut),1),n("td",ga,[t(l(y),{variant:I(s.status_pendaftaran)},{default:e(()=>[i(d(T(s.status_pendaftaran)),1)]),_:2},1032,["variant"])]),n("td",va,[s.pembayaran?(o(),p(l(y),{key:0,variant:U(s.pembayaran.status_pembayaran)},{default:e(()=>[i(d(z(s.pembayaran.status_pembayaran)),1)]),_:2},1032,["variant"])):(o(),p(l(y),{key:1,variant:"secondary"},{default:e(()=>a[16]||(a[16]=[i(" Belum Bayar ")])),_:1,__:[16]}))]),n("td",ya,d(F(s.created_at)),1),n("td",xa,[t(_,{variant:"ghost",size:"sm",onClick:g=>r.$inertia.visit(r.route("admin-daerah.pendaftaran.show",s.id_pendaftaran))},{default:e(()=>[t(m,{name:"eye",class:"w-4 h-4"})]),_:2},1032,["onClick"]),t(_,{variant:"ghost",size:"sm",onClick:g=>r.$inertia.visit(r.route("admin-daerah.pendaftaran.edit",s.id_pendaftaran))},{default:e(()=>[t(m,{name:"edit",class:"w-4 h-4"})]),_:2},1032,["onClick"]),t(l(W),null,{default:e(()=>[t(l(Y),{asChild:""},{default:e(()=>[t(_,{variant:"ghost",size:"sm"},{default:e(()=>[t(m,{name:"more-vertical",class:"w-4 h-4"})]),_:1})]),_:1}),t(l(A),{align:"end"},{default:e(()=>[s.status_pendaftaran==="draft"?(o(),p(l(w),{key:0,onClick:g=>G(s.id_pendaftaran)},{default:e(()=>[t(m,{name:"send",class:"w-4 h-4 mr-2"}),a[17]||(a[17]=i(" Submit "))]),_:2,__:[17]},1032,["onClick"])):C("",!0),t(l(w),{onClick:g=>r.$inertia.visit(r.route("admin-daerah.pendaftaran.documents",s.id_pendaftaran))},{default:e(()=>[t(m,{name:"file-text",class:"w-4 h-4 mr-2"}),a[18]||(a[18]=i(" Dokumen "))]),_:2,__:[18]},1032,["onClick"]),t(l(R)),s.status_pendaftaran!=="approved"?(o(),p(l(w),{key:1,onClick:g=>H(s.id_pendaftaran),class:"text-red-600"},{default:e(()=>[t(m,{name:"trash",class:"w-4 h-4 mr-2"}),a[19]||(a[19]=i(" Hapus "))]),_:2,__:[19]},1032,["onClick"])):C("",!0)]),_:2},1024)]),_:2},1024)])])}),128))])])])]),_:1})]),_:1}),n("div",ha,[n("div",ba,[n("span",ka," Menampilkan "+d(r.pendaftaran.from)+" - "+d(r.pendaftaran.to)+" dari "+d(r.pendaftaran.total)+" data ",1)]),n("div",wa,[(o(!0),v(h,null,b(r.pendaftaran.links,s=>(o(),p(_,{key:s.label,variant:s.active?"default":"outline",disabled:!s.url,onClick:x=>s.url&&r.$inertia.visit(s.url),size:"sm",innerHTML:s.label},null,8,["variant","disabled","onClick","innerHTML"]))),128))])])])]),_:1}))}});export{Ma as default};
