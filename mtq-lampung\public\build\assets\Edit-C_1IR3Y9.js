import{d as c,x as U,k as B,c as v,o as u,w as i,a as s,b as e,u as t,g as E,e as m,f as I,h as f,i as p,n as b,t as d,j as N,v as h}from"./app-B_pmlBSQ.js";import{_ as q}from"./AppLayout.vue_vue_type_script_setup_true_lang-BRf8lUiM.js";import{_ as A}from"./Heading.vue_vue_type_script_setup_true_lang-is4VBSCS.js";import{_ as $}from"./AppLogoIcon.vue_vue_type_script_setup_true_lang-wzz4PWna.js";import{_ as j,a as K}from"./CardContent.vue_vue_type_script_setup_true_lang-ClvLNBXC.js";import{_ as J}from"./CardDescription.vue_vue_type_script_setup_true_lang-DboywBIK.js";import{_ as T,a as z}from"./CardTitle.vue_vue_type_script_setup_true_lang-5HO6oSoB.js";import{_ as g}from"./Input.vue_vue_type_script_setup_true_lang-DB1-RsTn.js";import{_}from"./Label.vue_vue_type_script_setup_true_lang-rj_fhE7R.js";import{_ as L,a as F,b as G,c as H,d as V}from"./SelectValue.vue_vue_type_script_setup_true_lang-IzlIronZ.js";import{_ as w}from"./Icon.vue_vue_type_script_setup_true_lang-BuYzjckr.js";import"./useForwardExpose-CO14IhkA.js";import"./RovingFocusGroup-lsWyU4xZ.js";import"./check-DtmHjdxf.js";import"./useFormControl-mZRLifGx.js";import"./loader-circle-6d8QrWFr.js";import"./sun-CHIV9RJ8.js";const O={class:"max-w-2xl mx-auto"},Q={class:"space-y-4"},R={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},W={key:0,class:"text-sm text-red-600 mt-1"},X={key:0,class:"text-sm text-red-600 mt-1"},Y={key:0,class:"text-sm text-red-600 mt-1"},Z={key:0,class:"text-sm text-red-600 mt-1"},aa={key:0,class:"text-sm text-red-600 mt-1"},ta={class:"bg-gray-50 p-4 rounded-lg"},ra={class:"text-sm text-gray-600 space-y-1"},ea={key:0,class:"bg-yellow-50 border border-yellow-200 p-4 rounded-lg"},sa={class:"flex"},ia={class:"text-sm text-yellow-700 mt-1"},ma={class:"flex justify-end space-x-4 pt-6 border-t"},Ma=c({__name:"Edit",props:{mimbar:{}},setup(M){const n=M,D=[{title:"Dashboard",href:"/admin/dashboard"},{title:"Manajemen Mimbar",href:"/admin/mimbar"},{title:"Edit Mimbar",href:`/admin/mimbar/${n.mimbar.id_mimbar}/edit`}],r=U({kode_mimbar:n.mimbar.kode_mimbar,nama_mimbar:n.mimbar.nama_mimbar,keterangan:n.mimbar.keterangan||"",kapasitas:n.mimbar.kapasitas.toString(),status:n.mimbar.status}),S=B(()=>n.mimbar.pendaftaran&&n.mimbar.pendaftaran.length>0),P=()=>{var a;if(n.mimbar.kapasitas===0)return 0;const o=((a=n.mimbar.pendaftaran)==null?void 0:a.length)||0;return Math.round(o/n.mimbar.kapasitas*100)},C=()=>{r.put(route("admin.mimbar.update",n.mimbar.id_mimbar),{onSuccess:()=>{}})},k=o=>new Date(o).toLocaleDateString("id-ID",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"});return(o,a)=>(u(),v(q,{breadcrumbs:D},{default:i(()=>[s(t(E),{title:"Edit Mimbar"}),s(A,{title:`Edit Mimbar: ${o.mimbar.nama_mimbar}`},null,8,["title"]),e("div",O,[s(t(j),null,{default:i(()=>[s(t(T),null,{default:i(()=>[s(t(z),null,{default:i(()=>a[6]||(a[6]=[m("Edit Informasi Mimbar")])),_:1,__:[6]}),s(t(J),null,{default:i(()=>a[7]||(a[7]=[m(" Perbarui informasi mimbar di bawah ini ")])),_:1,__:[7]})]),_:1}),s(t(K),null,{default:i(()=>{var y,x;return[e("form",{onSubmit:I(C,["prevent"]),class:"space-y-6"},[e("div",Q,[a[16]||(a[16]=e("h3",{class:"text-lg font-medium"},"Informasi Dasar",-1)),e("div",R,[e("div",null,[s(t(_),{for:"kode_mimbar"},{default:i(()=>a[8]||(a[8]=[m("Kode Mimbar *")])),_:1,__:[8]}),s(t(g),{id:"kode_mimbar",modelValue:t(r).kode_mimbar,"onUpdate:modelValue":a[0]||(a[0]=l=>t(r).kode_mimbar=l),type:"text",required:"",placeholder:"Contoh: A, B, M001",class:b({"border-red-500":t(r).errors.kode_mimbar})},null,8,["modelValue","class"]),t(r).errors.kode_mimbar?(u(),f("p",W,d(t(r).errors.kode_mimbar),1)):p("",!0)]),e("div",null,[s(t(_),{for:"status"},{default:i(()=>a[9]||(a[9]=[m("Status *")])),_:1,__:[9]}),s(t(L),{modelValue:t(r).status,"onUpdate:modelValue":a[1]||(a[1]=l=>t(r).status=l),required:""},{default:i(()=>[s(t(F),{class:b({"border-red-500":t(r).errors.status})},{default:i(()=>[s(t(G),{placeholder:"Pilih Status"})]),_:1},8,["class"]),s(t(H),null,{default:i(()=>[s(t(V),{value:"aktif"},{default:i(()=>a[10]||(a[10]=[m("Aktif")])),_:1,__:[10]}),s(t(V),{value:"non_aktif"},{default:i(()=>a[11]||(a[11]=[m("Non Aktif")])),_:1,__:[11]})]),_:1})]),_:1},8,["modelValue"]),t(r).errors.status?(u(),f("p",X,d(t(r).errors.status),1)):p("",!0)])]),e("div",null,[s(t(_),{for:"nama_mimbar"},{default:i(()=>a[12]||(a[12]=[m("Nama Mimbar *")])),_:1,__:[12]}),s(t(g),{id:"nama_mimbar",modelValue:t(r).nama_mimbar,"onUpdate:modelValue":a[2]||(a[2]=l=>t(r).nama_mimbar=l),type:"text",required:"",placeholder:"Contoh: Mimbar Utama, Aula Serbaguna",class:b({"border-red-500":t(r).errors.nama_mimbar})},null,8,["modelValue","class"]),t(r).errors.nama_mimbar?(u(),f("p",Y,d(t(r).errors.nama_mimbar),1)):p("",!0)]),e("div",null,[s(t(_),{for:"kapasitas"},{default:i(()=>a[13]||(a[13]=[m("Kapasitas *")])),_:1,__:[13]}),s(t(g),{id:"kapasitas",modelValue:t(r).kapasitas,"onUpdate:modelValue":a[3]||(a[3]=l=>t(r).kapasitas=l),type:"number",required:"",min:"1",placeholder:"Contoh: 100",class:b({"border-red-500":t(r).errors.kapasitas})},null,8,["modelValue","class"]),t(r).errors.kapasitas?(u(),f("p",Z,d(t(r).errors.kapasitas),1)):p("",!0),a[14]||(a[14]=e("p",{class:"text-sm text-gray-500 mt-1"}," Jumlah maksimum peserta yang dapat menggunakan mimbar ini ",-1))]),e("div",null,[s(t(_),{for:"keterangan"},{default:i(()=>a[15]||(a[15]=[m("Keterangan")])),_:1,__:[15]}),N(e("textarea",{id:"keterangan","onUpdate:modelValue":a[4]||(a[4]=l=>t(r).keterangan=l),rows:"4",class:b(["w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",{"border-red-500":t(r).errors.keterangan}]),placeholder:"Deskripsi mimbar, fasilitas yang tersedia, lokasi, dll..."},null,2),[[h,t(r).keterangan]]),t(r).errors.keterangan?(u(),f("p",aa,d(t(r).errors.keterangan),1)):p("",!0)])]),e("div",ta,[a[21]||(a[21]=e("h4",{class:"font-medium text-gray-900 mb-2"},"Informasi Saat Ini",-1)),e("div",ra,[e("p",null,[a[17]||(a[17]=e("strong",null,"Dibuat:",-1)),m(" "+d(k(o.mimbar.created_at)),1)]),e("p",null,[a[18]||(a[18]=e("strong",null,"Diperbarui:",-1)),m(" "+d(k(o.mimbar.updated_at)),1)]),e("p",null,[a[19]||(a[19]=e("strong",null,"Jumlah Pendaftaran:",-1)),m(" "+d(((y=o.mimbar.pendaftaran)==null?void 0:y.length)||0)+" pendaftaran",1)]),e("p",null,[a[20]||(a[20]=e("strong",null,"Penggunaan:",-1)),m(" "+d(P())+"% dari kapasitas",1)])])]),S.value?(u(),f("div",ea,[e("div",sa,[s(w,{name:"alert-triangle",class:"w-5 h-5 text-yellow-600 mr-2 mt-0.5"}),e("div",null,[a[22]||(a[22]=e("h4",{class:"font-medium text-yellow-800"},"Perhatian",-1)),e("p",ia," Mimbar ini memiliki "+d(((x=o.mimbar.pendaftaran)==null?void 0:x.length)||0)+" pendaftaran. Perubahan kapasitas atau status dapat mempengaruhi peserta yang sudah terdaftar. ",1)])])])):p("",!0),e("div",ma,[s($,{type:"button",variant:"outline",onClick:a[5]||(a[5]=l=>o.$inertia.visit(o.route("admin.mimbar.index")))},{default:i(()=>a[23]||(a[23]=[m(" Batal ")])),_:1,__:[23]}),s($,{type:"submit",disabled:t(r).processing},{default:i(()=>[t(r).processing?(u(),v(w,{key:0,name:"loader-2",class:"w-4 h-4 mr-2 animate-spin"})):p("",!0),a[24]||(a[24]=m(" Simpan Perubahan "))]),_:1,__:[24]},8,["disabled"])])],32)]}),_:1})]),_:1})])]),_:1}))}});export{Ma as default};
