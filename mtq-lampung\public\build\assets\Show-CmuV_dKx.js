import{d as K,c as S,o as c,w as e,a as s,b as t,u as o,g as A,e as d,t as i,h as v,F as T,m as V}from"./app-B_pmlBSQ.js";import{_ as U}from"./AppLayout.vue_vue_type_script_setup_true_lang-BRf8lUiM.js";import{_ as E}from"./Heading.vue_vue_type_script_setup_true_lang-is4VBSCS.js";import{_ as x}from"./AppLogoIcon.vue_vue_type_script_setup_true_lang-wzz4PWna.js";import{_,a as f}from"./CardContent.vue_vue_type_script_setup_true_lang-ClvLNBXC.js";import{_ as D}from"./CardDescription.vue_vue_type_script_setup_true_lang-DboywBIK.js";import{_ as $,a as j}from"./CardTitle.vue_vue_type_script_setup_true_lang-5HO6oSoB.js";import{_ as y}from"./index-CMGr3-bt.js";import{_ as m}from"./Label.vue_vue_type_script_setup_true_lang-rj_fhE7R.js";import{_ as r}from"./Icon.vue_vue_type_script_setup_true_lang-BuYzjckr.js";import"./useForwardExpose-CO14IhkA.js";import"./RovingFocusGroup-lsWyU4xZ.js";import"./check-DtmHjdxf.js";import"./loader-circle-6d8QrWFr.js";import"./sun-CHIV9RJ8.js";const R={class:"max-w-4xl mx-auto space-y-6"},Y={class:"flex justify-between items-start"},z={class:"w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center"},J={class:"flex gap-2"},W={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},q={class:"space-y-4"},H={class:"text-sm"},O={class:"text-sm"},Q={class:"text-sm"},X={class:"text-sm"},Z={class:"space-y-4"},tt={class:"text-sm"},at={class:"text-sm"},nt={class:"text-sm font-medium text-green-600"},st={class:"text-sm"},et={class:"grid grid-cols-1 md:grid-cols-4 gap-6"},ot={class:"flex items-center"},lt={class:"flex-shrink-0"},it={class:"ml-4"},dt={class:"text-2xl font-semibold text-gray-900"},rt={class:"flex items-center"},gt={class:"flex-shrink-0"},mt={class:"ml-4"},ut={class:"text-2xl font-semibold text-gray-900"},_t={class:"flex items-center"},ft={class:"flex-shrink-0"},pt={class:"ml-4"},ct={class:"text-2xl font-semibold text-gray-900"},vt={class:"flex items-center"},xt={class:"flex-shrink-0"},yt={class:"ml-4"},ht={class:"text-2xl font-semibold text-gray-900"},bt={class:"flex justify-between items-center"},kt={key:0,class:"text-center py-8 text-gray-500"},wt={key:1,class:"space-y-4"},Dt={class:"flex justify-between items-start"},$t={class:"flex-1"},jt={class:"flex items-center gap-3 mb-2"},Ct={class:"font-medium"},Lt={class:"text-sm text-gray-500 mb-2"},Pt={class:"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm"},Nt={class:"font-medium"},Bt={class:"font-medium"},Gt={class:"font-medium"},Mt={class:"font-medium"},Ft={class:"flex gap-2 ml-4"},It={class:"flex justify-between"},Kt={class:"flex gap-2"},Zt=K({__name:"Show",props:{golongan:{}},setup(C){const u=C,L=[{title:"Dashboard",href:"/admin/dashboard"},{title:"Manajemen Golongan",href:"/admin/golongan"},{title:"Detail Golongan",href:`/admin/golongan/${u.golongan.id_golongan}`}],h=n=>({aktif:"default",non_aktif:"secondary",approved:"default",pending:"secondary",draft:"outline"})[n]||"secondary",P=n=>({aktif:"Aktif",non_aktif:"Non Aktif",approved:"Disetujui",pending:"Menunggu",draft:"Draft"})[n]||n,N=()=>u.golongan.pendaftaran?u.golongan.pendaftaran.filter(n=>n.status_pendaftaran==="approved").length:0,B=()=>u.golongan.pendaftaran?u.golongan.pendaftaran.filter(n=>n.status_pendaftaran==="pending"||n.status_pendaftaran==="draft").length:0,G=()=>{var l;const a=(((l=u.golongan.pendaftaran)==null?void 0:l.length)||0)/u.golongan.kuota_max*100;return Math.round(a)},M=n=>{const a=new Date,l=new Date(n);let g=a.getFullYear()-l.getFullYear();const p=a.getMonth()-l.getMonth();return(p<0||p===0&&a.getDate()<l.getDate())&&g--,g},F=n=>new Date(n).toLocaleDateString("id-ID",{year:"numeric",month:"short",day:"numeric"}),I=n=>new Intl.NumberFormat("id-ID").format(n);return(n,a)=>(c(),S(U,{breadcrumbs:L},{default:e(()=>[s(o(A),{title:`Detail Golongan: ${n.golongan.nama_golongan}`},null,8,["title"]),s(E,{title:`Detail Golongan: ${n.golongan.nama_golongan}`},null,8,["title"]),t("div",R,[s(o(_),null,{default:e(()=>[s(o($),null,{default:e(()=>[t("div",Y,[t("div",null,[s(o(j),{class:"flex items-center gap-3"},{default:e(()=>[t("div",z,[s(r,{name:"award",class:"w-6 h-6 text-purple-600"})]),d(" "+i(n.golongan.nama_golongan),1)]),_:1}),s(o(D),null,{default:e(()=>{var l;return[d(i(n.golongan.kode_golongan)+" • "+i((l=n.golongan.cabang_lomba)==null?void 0:l.nama_cabang),1)]}),_:1})]),t("div",J,[s(o(y),{variant:n.golongan.jenis_kelamin==="L"?"default":"secondary"},{default:e(()=>[d(i(n.golongan.jenis_kelamin==="L"?"Laki-laki":"Perempuan"),1)]),_:1},8,["variant"]),s(o(y),{variant:h(n.golongan.status)},{default:e(()=>[d(i(P(n.golongan.status)),1)]),_:1},8,["variant"])])])]),_:1}),s(o(f),null,{default:e(()=>{var l,g;return[t("div",W,[t("div",q,[t("div",null,[s(o(m),{class:"text-sm font-medium text-gray-500"},{default:e(()=>a[2]||(a[2]=[d("Kode Golongan")])),_:1,__:[2]}),t("p",H,i(n.golongan.kode_golongan),1)]),t("div",null,[s(o(m),{class:"text-sm font-medium text-gray-500"},{default:e(()=>a[3]||(a[3]=[d("Nama Golongan")])),_:1,__:[3]}),t("p",O,i(n.golongan.nama_golongan),1)]),t("div",null,[s(o(m),{class:"text-sm font-medium text-gray-500"},{default:e(()=>a[4]||(a[4]=[d("Cabang Lomba")])),_:1,__:[4]}),t("p",Q,i((l=n.golongan.cabang_lomba)==null?void 0:l.nama_cabang)+" ("+i((g=n.golongan.cabang_lomba)==null?void 0:g.kode_cabang)+")",1)]),t("div",null,[s(o(m),{class:"text-sm font-medium text-gray-500"},{default:e(()=>a[5]||(a[5]=[d("Jenis Kelamin")])),_:1,__:[5]}),t("p",X,i(n.golongan.jenis_kelamin==="L"?"Laki-laki":"Perempuan"),1)])]),t("div",Z,[t("div",null,[s(o(m),{class:"text-sm font-medium text-gray-500"},{default:e(()=>a[6]||(a[6]=[d("Batas Usia")])),_:1,__:[6]}),t("p",tt,i(n.golongan.batas_umur_min)+" - "+i(n.golongan.batas_umur_max)+" tahun",1)]),t("div",null,[s(o(m),{class:"text-sm font-medium text-gray-500"},{default:e(()=>a[7]||(a[7]=[d("Kuota Maksimum")])),_:1,__:[7]}),t("p",at,i(n.golongan.kuota_max)+" peserta",1)]),t("div",null,[s(o(m),{class:"text-sm font-medium text-gray-500"},{default:e(()=>a[8]||(a[8]=[d("Biaya Pendaftaran")])),_:1,__:[8]}),t("p",nt,"Rp "+i(I(n.golongan.biaya_pendaftaran)),1)]),t("div",null,[s(o(m),{class:"text-sm font-medium text-gray-500"},{default:e(()=>a[9]||(a[9]=[d("Range Nomor Urut")])),_:1,__:[9]}),t("p",st,i(n.golongan.nomor_urut_awal)+" - "+i(n.golongan.nomor_urut_akhir),1)])])])]}),_:1})]),_:1}),t("div",et,[s(o(_),null,{default:e(()=>[s(o(f),{class:"p-6"},{default:e(()=>{var l;return[t("div",ot,[t("div",lt,[s(r,{name:"users",class:"h-8 w-8 text-blue-600"})]),t("div",it,[a[10]||(a[10]=t("p",{class:"text-sm font-medium text-gray-500"},"Total Pendaftaran",-1)),t("p",dt,i(((l=n.golongan.pendaftaran)==null?void 0:l.length)||0),1)])])]}),_:1})]),_:1}),s(o(_),null,{default:e(()=>[s(o(f),{class:"p-6"},{default:e(()=>[t("div",rt,[t("div",gt,[s(r,{name:"check-circle",class:"h-8 w-8 text-green-600"})]),t("div",mt,[a[11]||(a[11]=t("p",{class:"text-sm font-medium text-gray-500"},"Disetujui",-1)),t("p",ut,i(N()),1)])])]),_:1})]),_:1}),s(o(_),null,{default:e(()=>[s(o(f),{class:"p-6"},{default:e(()=>[t("div",_t,[t("div",ft,[s(r,{name:"clock",class:"h-8 w-8 text-yellow-600"})]),t("div",pt,[a[12]||(a[12]=t("p",{class:"text-sm font-medium text-gray-500"},"Menunggu",-1)),t("p",ct,i(B()),1)])])]),_:1})]),_:1}),s(o(_),null,{default:e(()=>[s(o(f),{class:"p-6"},{default:e(()=>[t("div",vt,[t("div",xt,[s(r,{name:"percent",class:"h-8 w-8 text-purple-600"})]),t("div",yt,[a[13]||(a[13]=t("p",{class:"text-sm font-medium text-gray-500"},"Persentase Terisi",-1)),t("p",ht,i(G())+"% ",1)])])]),_:1})]),_:1})]),s(o(_),null,{default:e(()=>[s(o($),null,{default:e(()=>[t("div",bt,[t("div",null,[s(o(j),null,{default:e(()=>a[14]||(a[14]=[d("Daftar Pendaftaran")])),_:1,__:[14]}),s(o(D),null,{default:e(()=>[d(" Peserta yang mendaftar untuk golongan "+i(n.golongan.nama_golongan),1)]),_:1})])])]),_:1}),s(o(f),null,{default:e(()=>[!n.golongan.pendaftaran||n.golongan.pendaftaran.length===0?(c(),v("div",kt,[s(r,{name:"users",class:"w-12 h-12 mx-auto mb-4 text-gray-300"}),a[15]||(a[15]=t("p",null,"Belum ada pendaftaran untuk golongan ini",-1))])):(c(),v("div",wt,[(c(!0),v(T,null,V(n.golongan.pendaftaran,l=>{var g,p,b,k,w;return c(),v("div",{key:l.id_pendaftaran,class:"border rounded-lg p-4 hover:bg-gray-50"},[t("div",Dt,[t("div",$t,[t("div",jt,[t("h4",Ct,i((g=l.peserta)==null?void 0:g.nama_lengkap),1),s(o(y),{variant:h(l.status_pendaftaran)},{default:e(()=>[d(i(l.status_pendaftaran),1)]),_:2},1032,["variant"])]),t("p",Lt,i(l.nomor_pendaftaran),1),t("div",Pt,[t("div",null,[a[16]||(a[16]=t("span",{class:"text-gray-500"},"NIK:",-1)),t("p",Nt,i((p=l.peserta)==null?void 0:p.nik),1)]),t("div",null,[a[17]||(a[17]=t("span",{class:"text-gray-500"},"Umur:",-1)),t("p",Bt,i(M((b=l.peserta)==null?void 0:b.tanggal_lahir))+" tahun",1)]),t("div",null,[a[18]||(a[18]=t("span",{class:"text-gray-500"},"Wilayah:",-1)),t("p",Gt,i((w=(k=l.peserta)==null?void 0:k.wilayah)==null?void 0:w.nama_wilayah),1)]),t("div",null,[a[19]||(a[19]=t("span",{class:"text-gray-500"},"Tgl Daftar:",-1)),t("p",Mt,i(F(l.created_at)),1)])])]),t("div",Ft,[s(x,{variant:"ghost",size:"sm",onClick:St=>n.$inertia.visit(n.route("admin.pendaftaran.show",l.id_pendaftaran))},{default:e(()=>[s(r,{name:"eye",class:"w-4 h-4"})]),_:2},1032,["onClick"])])])])}),128))]))]),_:1})]),_:1}),t("div",It,[s(x,{variant:"outline",onClick:a[0]||(a[0]=l=>n.$inertia.visit(n.route("admin.golongan.index")))},{default:e(()=>[s(r,{name:"arrow-left",class:"w-4 h-4 mr-2"}),a[20]||(a[20]=d(" Kembali "))]),_:1,__:[20]}),t("div",Kt,[s(x,{onClick:a[1]||(a[1]=l=>n.$inertia.visit(n.route("admin.golongan.edit",n.golongan.id_golongan)))},{default:e(()=>[s(r,{name:"edit",class:"w-4 h-4 mr-2"}),a[21]||(a[21]=d(" Edit Golongan "))]),_:1,__:[21]})])])])]),_:1}))}});export{Zt as default};
