import{d as c,x as g,c as d,o as m,w as e,a,b as t,u as i,g as x,e as n,f as b,h as $,F as h,m as v,t as p,i as y}from"./app-B_pmlBSQ.js";import{_ as w}from"./AppLayout.vue_vue_type_script_setup_true_lang-BRf8lUiM.js";import{_ as k}from"./Heading.vue_vue_type_script_setup_true_lang-is4VBSCS.js";import{_}from"./AppLogoIcon.vue_vue_type_script_setup_true_lang-wzz4PWna.js";import{_ as P}from"./Label.vue_vue_type_script_setup_true_lang-rj_fhE7R.js";import{_ as V,a as B,b as S,c as C,d as N}from"./SelectValue.vue_vue_type_script_setup_true_lang-IzlIronZ.js";import{_ as L,a as T}from"./CardContent.vue_vue_type_script_setup_true_lang-ClvLNBXC.js";import{_ as j,a as D}from"./CardTitle.vue_vue_type_script_setup_true_lang-5HO6oSoB.js";import{_ as F}from"./CardDescription.vue_vue_type_script_setup_true_lang-DboywBIK.js";import{_ as M}from"./InputError.vue_vue_type_script_setup_true_lang-A-vdzbq9.js";import{_ as f}from"./Icon.vue_vue_type_script_setup_true_lang-BuYzjckr.js";import{_ as q}from"./TextLink.vue_vue_type_script_setup_true_lang-CC2Og0lP.js";import"./useForwardExpose-CO14IhkA.js";import"./RovingFocusGroup-lsWyU4xZ.js";import"./check-DtmHjdxf.js";import"./useFormControl-mZRLifGx.js";import"./loader-circle-6d8QrWFr.js";import"./sun-CHIV9RJ8.js";const E={class:"space-y-6"},I={class:"flex items-center space-x-3"},K={class:"max-w-5xl mx-auto"},U={class:"flex items-center space-x-2"},z={class:"w-6 h-6 bg-blue-500/10 rounded-full flex items-center justify-center"},A={class:"space-y-1"},G={class:"flex justify-end space-x-4 pt-6 border-t"},da=c({__name:"Create",props:{peserta:{},golongan:{},mimbar:{}},setup(H){const r=g({id_peserta:"",id_golongan:"",id_mimbar:""});function u(){r.post(route("admin.pendaftaran.store"),{onSuccess:()=>{}})}return(l,s)=>(m(),d(w,null,{default:e(()=>[a(i(x),{title:"Tambah Pendaftaran"}),t("div",E,[t("div",I,[a(_,{"as-child":"",class:"text-muted-foreground bg-gray-100 cursor-pointer hover:text-foreground"},{default:e(()=>[a(q,{href:l.route("admin.pendaftaran.index")},{default:e(()=>[a(f,{name:"arrowLeft",class:"w-4 h-4 mr-2"}),s[1]||(s[1]=n(" Kembali "))]),_:1,__:[1]},8,["href"])]),_:1}),t("div",null,[a(k,{title:"Tambah Pendaftaran",description:"Lengkapi informasi pendaftaran di bawah ini"})])]),t("div",K,[t("form",{onSubmit:b(u,["prevent"]),class:"space-y-4"},[a(L,null,{default:e(()=>[a(j,{class:"pb-3"},{default:e(()=>[t("div",U,[t("div",z,[a(f,{name:"user",class:"w-3 h-3 text-blue-500"})]),t("div",null,[a(D,{class:"text-base"},{default:e(()=>s[2]||(s[2]=[n("Informasi Peserta")])),_:1,__:[2]}),a(F,{class:"text-xs"},{default:e(()=>s[3]||(s[3]=[n(" Data peserta yang mendaftar ")])),_:1,__:[3]})])])]),_:1}),a(T,{class:"space-y-3"},{default:e(()=>[t("div",A,[a(P,{for:"id_peserta",class:"text-xs font-medium"},{default:e(()=>s[4]||(s[4]=[n(" Peserta "),t("span",{class:"text-red-500"},"*",-1)])),_:1,__:[4]}),a(V,{modelValue:i(r).id_peserta,"onUpdate:modelValue":s[0]||(s[0]=o=>i(r).id_peserta=o),required:""},{default:e(()=>[a(B,{class:"w-full"},{default:e(()=>[a(S,{placeholder:"Pilih Peserta"})]),_:1}),a(C,null,{default:e(()=>[(m(!0),$(h,null,v(l.peserta,o=>(m(),d(N,{key:o.id_peserta,value:o.id_peserta.toString()},{default:e(()=>[n(p(o.nama_lengkap),1)]),_:2},1032,["value"]))),128))]),_:1}),a(M,{message:i(r).errors.id_peserta},null,8,["message"])]),_:1},8,["modelValue"])])]),_:1})]),_:1}),t("div",G,[a(_,{as:"link",href:l.route("admin.pendaftaran.index"),variant:"outline"},{default:e(()=>s[5]||(s[5]=[n(" Batal ")])),_:1,__:[5]},8,["href"]),a(_,{type:"submit",variant:"primary",disabled:i(r).processing},{default:e(()=>[i(r).processing?(m(),d(f,{key:0,name:"loader-2",class:"w-4 h-4 mr-2 animate-spin"})):y("",!0),n(" "+p(i(r).processing?"Menyimpan...":"Simpan Pendaftaran"),1)]),_:1},8,["disabled"])])],32)])])]),_:1}))}});export{da as default};
