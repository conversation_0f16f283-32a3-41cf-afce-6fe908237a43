import{d as q,l as N,x as A,k as w,c as b,o as d,w as s,a as l,b as i,u as e,g as G,i as p,h as f,t as _,n as g,e as n,f as H,F as O,m as R}from"./app-B_pmlBSQ.js";import{_ as X}from"./AppLayout.vue_vue_type_script_setup_true_lang-BRf8lUiM.js";import{_ as Y}from"./Heading.vue_vue_type_script_setup_true_lang-is4VBSCS.js";import{_ as v}from"./AppLogoIcon.vue_vue_type_script_setup_true_lang-wzz4PWna.js";import{_ as D,a as E}from"./CardContent.vue_vue_type_script_setup_true_lang-ClvLNBXC.js";import{_ as Z}from"./CardDescription.vue_vue_type_script_setup_true_lang-DboywBIK.js";import{_ as ee,a as ae}from"./CardTitle.vue_vue_type_script_setup_true_lang-5HO6oSoB.js";import{_ as te}from"./index-CMGr3-bt.js";import{_ as V,a as $}from"./index-Cae_Ab9-.js";import{_ as y}from"./Input.vue_vue_type_script_setup_true_lang-DB1-RsTn.js";import{_ as c}from"./Label.vue_vue_type_script_setup_true_lang-rj_fhE7R.js";import{_ as le}from"./Textarea.vue_vue_type_script_setup_true_lang-jqOjZ7RT.js";import{_ as I,a as L,b as T,c as B,d as j}from"./SelectValue.vue_vue_type_script_setup_true_lang-IzlIronZ.js";import{_ as k}from"./Icon.vue_vue_type_script_setup_true_lang-BuYzjckr.js";import"./useForwardExpose-CO14IhkA.js";import"./RovingFocusGroup-lsWyU4xZ.js";import"./check-DtmHjdxf.js";import"./useFormControl-mZRLifGx.js";import"./loader-circle-6d8QrWFr.js";import"./sun-CHIV9RJ8.js";const se={class:"space-y-6"},re={class:"flex items-center space-x-6"},ie={class:"relative"},ne={class:"h-20 w-20 rounded-full bg-blue-500 flex items-center justify-center"},oe={class:"text-2xl font-medium text-white"},de={class:"flex-1"},ue={class:"text-2xl font-bold text-gray-900"},me={class:"text-gray-600"},_e={class:"mt-2 flex items-center space-x-4"},pe={class:"text-sm text-gray-500"},fe={class:"flex space-x-2"},ge={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},ce={key:0,class:"text-sm text-red-600 mt-1"},ke={key:0,class:"text-sm text-red-600 mt-1"},be={key:0,class:"text-sm text-red-600 mt-1"},ye={key:0,class:"text-sm text-red-600 mt-1"},ve={key:0,class:"text-sm text-red-600 mt-1"},xe={key:0,class:"text-sm text-red-600 mt-1"},he={key:0,class:"text-sm text-red-600 mt-1"},we={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},Ve={key:0,class:"text-sm text-red-600 mt-1"},$e={key:0,class:"text-sm text-red-600 mt-1"},je={key:0,class:"flex justify-end space-x-4"},Ge=q({__name:"Show",props:{peserta:{},wilayah:{}},setup(F){const o=F,u=N(!1),P=N(),t=A({nik:o.peserta.nik,nama_lengkap:o.peserta.nama_lengkap,tempat_lahir:o.peserta.tempat_lahir,tanggal_lahir:o.peserta.tanggal_lahir,jenis_kelamin:o.peserta.jenis_kelamin,alamat:o.peserta.alamat,id_wilayah:o.peserta.wilayah.id_wilayah.toString(),no_telepon:o.peserta.no_telepon||"",email:o.peserta.email||"",nama_ayah:o.peserta.nama_ayah||"",nama_ibu:o.peserta.nama_ibu||"",pekerjaan:o.peserta.pekerjaan||"",instansi_asal:o.peserta.instansi_asal||"",nama_rekening:o.peserta.nama_rekening||"",no_rekening:o.peserta.no_rekening||""}),x=A({photo:null}),h=w(()=>{switch(o.peserta.status_peserta){case"draft":return{text:"Draft",color:"bg-gray-100 text-gray-800",icon:"edit"};case"submitted":return{text:"Disubmit",color:"bg-blue-100 text-blue-800",icon:"clock"};case"verified":return{text:"Terverifikasi",color:"bg-indigo-100 text-indigo-800",icon:"check"};case"approved":return{text:"Disetujui",color:"bg-green-100 text-green-800",icon:"check-circle"};case"rejected":return{text:"Ditolak",color:"bg-red-100 text-red-800",icon:"x-circle"};default:return{text:o.peserta.status_peserta,color:"bg-gray-100 text-gray-800",icon:"help-circle"}}}),S=w(()=>["draft","submitted"].includes(o.peserta.status_peserta)),K=w(()=>o.peserta.status_peserta==="draft");function C(){u.value=!u.value,u.value||t.reset()}function M(){t.put(route("peserta.profile.update"),{onSuccess:()=>{u.value=!1}})}function z(){t.post(route("peserta.profile.submit"))}function J(){var m;(m=P.value)==null||m.click()}function Q(m){var U;const r=(U=m.target.files)==null?void 0:U[0];r&&(x.photo=r,x.post(route("peserta.profile.upload-photo"),{onSuccess:()=>{x.reset()}}))}function W(m){return m.split(" ").map(a=>a.charAt(0)).join("").toUpperCase().slice(0,2)}return(m,a)=>(d(),b(X,null,{header:s(()=>[l(Y,null,{default:s(()=>a[9]||(a[9]=[n("Profil Saya")])),_:1,__:[9]})]),default:s(()=>[l(e(G),{title:"Profil Saya"}),i("div",se,[l(e(D),null,{default:s(()=>[l(e(E),{class:"p-6"},{default:s(()=>[i("div",re,[i("div",ie,[i("div",ne,[i("span",oe,_(W(m.peserta.nama_lengkap)),1)]),S.value?(d(),f("button",{key:0,onClick:J,class:"absolute -bottom-1 -right-1 h-8 w-8 rounded-full bg-white border-2 border-gray-300 flex items-center justify-center hover:bg-gray-50"},[l(k,{name:"camera",class:"h-4 w-4 text-gray-600"})])):p("",!0),i("input",{ref_key:"photoInput",ref:P,type:"file",accept:"image/*",class:"hidden",onChange:Q},null,544)]),i("div",de,[i("h2",ue,_(m.peserta.nama_lengkap),1),i("p",me,_(m.peserta.wilayah.nama_wilayah),1),i("div",_e,[l(e(te),{class:g(h.value.color)},{default:s(()=>[l(k,{name:h.value.icon,class:"w-3 h-3 mr-1"},null,8,["name"]),n(" "+_(h.value.text),1)]),_:1},8,["class"]),i("span",pe,"NIK: "+_(m.peserta.nik),1)])]),i("div",fe,[S.value&&!u.value?(d(),b(v,{key:0,onClick:C,variant:"outline"},{default:s(()=>[l(k,{name:"edit",class:"w-4 h-4 mr-2"}),a[10]||(a[10]=n(" Edit Profil "))]),_:1,__:[10]})):p("",!0),K.value&&!u.value?(d(),b(v,{key:1,onClick:z,disabled:e(t).processing},{default:s(()=>[l(k,{name:"send",class:"w-4 h-4 mr-2"}),a[11]||(a[11]=n(" Submit untuk Verifikasi "))]),_:1,__:[11]},8,["disabled"])):p("",!0)])])]),_:1})]),_:1}),m.peserta.status_peserta==="draft"?(d(),b(e(V),{key:0,class:"border-yellow-200 bg-yellow-50"},{default:s(()=>[l(k,{name:"alert-triangle",class:"h-4 w-4"}),l(e($),null,{default:s(()=>a[12]||(a[12]=[n(" Profil Anda masih dalam status draft. Silakan lengkapi data dan submit untuk verifikasi. ")])),_:1,__:[12]})]),_:1})):m.peserta.status_peserta==="submitted"?(d(),b(e(V),{key:1,class:"border-blue-200 bg-blue-50"},{default:s(()=>[l(k,{name:"clock",class:"h-4 w-4"}),l(e($),null,{default:s(()=>a[13]||(a[13]=[n(" Profil Anda telah disubmit dan sedang menunggu verifikasi admin. ")])),_:1,__:[13]})]),_:1})):m.peserta.status_peserta==="rejected"?(d(),b(e(V),{key:2,class:"border-red-200 bg-red-50"},{default:s(()=>[l(k,{name:"x-circle",class:"h-4 w-4"}),l(e($),null,{default:s(()=>a[14]||(a[14]=[n(" Profil Anda ditolak. Silakan perbaiki data dan submit ulang. ")])),_:1,__:[14]})]),_:1})):p("",!0),l(e(D),null,{default:s(()=>[l(e(ee),null,{default:s(()=>[l(e(ae),null,{default:s(()=>a[15]||(a[15]=[n("Data Pribadi")])),_:1,__:[15]}),l(e(Z),null,{default:s(()=>a[16]||(a[16]=[n(" Informasi pribadi Anda untuk pendaftaran MTQ ")])),_:1,__:[16]})]),_:1}),l(e(E),null,{default:s(()=>[i("form",{onSubmit:H(M,["prevent"]),class:"space-y-6"},[i("div",ge,[i("div",null,[l(e(c),{for:"nik"},{default:s(()=>a[17]||(a[17]=[n("NIK *")])),_:1,__:[17]}),l(e(y),{id:"nik",modelValue:e(t).nik,"onUpdate:modelValue":a[0]||(a[0]=r=>e(t).nik=r),disabled:!u.value,maxlength:"16",class:g({"border-red-500":e(t).errors.nik})},null,8,["modelValue","disabled","class"]),e(t).errors.nik?(d(),f("p",ce,_(e(t).errors.nik),1)):p("",!0)]),i("div",null,[l(e(c),{for:"nama_lengkap"},{default:s(()=>a[18]||(a[18]=[n("Nama Lengkap *")])),_:1,__:[18]}),l(e(y),{id:"nama_lengkap",modelValue:e(t).nama_lengkap,"onUpdate:modelValue":a[1]||(a[1]=r=>e(t).nama_lengkap=r),disabled:!u.value,class:g({"border-red-500":e(t).errors.nama_lengkap})},null,8,["modelValue","disabled","class"]),e(t).errors.nama_lengkap?(d(),f("p",ke,_(e(t).errors.nama_lengkap),1)):p("",!0)]),i("div",null,[l(e(c),{for:"tempat_lahir"},{default:s(()=>a[19]||(a[19]=[n("Tempat Lahir *")])),_:1,__:[19]}),l(e(y),{id:"tempat_lahir",modelValue:e(t).tempat_lahir,"onUpdate:modelValue":a[2]||(a[2]=r=>e(t).tempat_lahir=r),disabled:!u.value,class:g({"border-red-500":e(t).errors.tempat_lahir})},null,8,["modelValue","disabled","class"]),e(t).errors.tempat_lahir?(d(),f("p",be,_(e(t).errors.tempat_lahir),1)):p("",!0)]),i("div",null,[l(e(c),{for:"tanggal_lahir"},{default:s(()=>a[20]||(a[20]=[n("Tanggal Lahir *")])),_:1,__:[20]}),l(e(y),{id:"tanggal_lahir",modelValue:e(t).tanggal_lahir,"onUpdate:modelValue":a[3]||(a[3]=r=>e(t).tanggal_lahir=r),type:"date",disabled:!u.value,class:g({"border-red-500":e(t).errors.tanggal_lahir})},null,8,["modelValue","disabled","class"]),e(t).errors.tanggal_lahir?(d(),f("p",ye,_(e(t).errors.tanggal_lahir),1)):p("",!0)]),i("div",null,[l(e(c),{for:"jenis_kelamin"},{default:s(()=>a[21]||(a[21]=[n("Jenis Kelamin *")])),_:1,__:[21]}),l(e(I),{modelValue:e(t).jenis_kelamin,"onUpdate:modelValue":a[4]||(a[4]=r=>e(t).jenis_kelamin=r),disabled:!u.value},{default:s(()=>[l(e(L),{class:g({"border-red-500":e(t).errors.jenis_kelamin})},{default:s(()=>[l(e(T),{placeholder:"Pilih jenis kelamin"})]),_:1},8,["class"]),l(e(B),null,{default:s(()=>[l(e(j),{value:"L"},{default:s(()=>a[22]||(a[22]=[n("Laki-laki")])),_:1,__:[22]}),l(e(j),{value:"P"},{default:s(()=>a[23]||(a[23]=[n("Perempuan")])),_:1,__:[23]})]),_:1})]),_:1},8,["modelValue","disabled"]),e(t).errors.jenis_kelamin?(d(),f("p",ve,_(e(t).errors.jenis_kelamin),1)):p("",!0)]),i("div",null,[l(e(c),{for:"id_wilayah"},{default:s(()=>a[24]||(a[24]=[n("Wilayah *")])),_:1,__:[24]}),l(e(I),{modelValue:e(t).id_wilayah,"onUpdate:modelValue":a[5]||(a[5]=r=>e(t).id_wilayah=r),disabled:!u.value},{default:s(()=>[l(e(L),{class:g({"border-red-500":e(t).errors.id_wilayah})},{default:s(()=>[l(e(T),{placeholder:"Pilih wilayah"})]),_:1},8,["class"]),l(e(B),null,{default:s(()=>[(d(!0),f(O,null,R(m.wilayah,r=>(d(),b(e(j),{key:r.id_wilayah,value:r.id_wilayah.toString()},{default:s(()=>[n(_(r.nama_wilayah),1)]),_:2},1032,["value"]))),128))]),_:1})]),_:1},8,["modelValue","disabled"]),e(t).errors.id_wilayah?(d(),f("p",xe,_(e(t).errors.id_wilayah),1)):p("",!0)])]),i("div",null,[l(e(c),{for:"alamat"},{default:s(()=>a[25]||(a[25]=[n("Alamat *")])),_:1,__:[25]}),l(e(le),{id:"alamat",modelValue:e(t).alamat,"onUpdate:modelValue":a[6]||(a[6]=r=>e(t).alamat=r),disabled:!u.value,rows:"3",class:g({"border-red-500":e(t).errors.alamat})},null,8,["modelValue","disabled","class"]),e(t).errors.alamat?(d(),f("p",he,_(e(t).errors.alamat),1)):p("",!0)]),i("div",we,[i("div",null,[l(e(c),{for:"no_telepon"},{default:s(()=>a[26]||(a[26]=[n("No. Telepon")])),_:1,__:[26]}),l(e(y),{id:"no_telepon",modelValue:e(t).no_telepon,"onUpdate:modelValue":a[7]||(a[7]=r=>e(t).no_telepon=r),disabled:!u.value,class:g({"border-red-500":e(t).errors.no_telepon})},null,8,["modelValue","disabled","class"]),e(t).errors.no_telepon?(d(),f("p",Ve,_(e(t).errors.no_telepon),1)):p("",!0)]),i("div",null,[l(e(c),{for:"email"},{default:s(()=>a[27]||(a[27]=[n("Email")])),_:1,__:[27]}),l(e(y),{id:"email",modelValue:e(t).email,"onUpdate:modelValue":a[8]||(a[8]=r=>e(t).email=r),type:"email",disabled:!u.value,class:g({"border-red-500":e(t).errors.email})},null,8,["modelValue","disabled","class"]),e(t).errors.email?(d(),f("p",$e,_(e(t).errors.email),1)):p("",!0)])]),u.value?(d(),f("div",je,[l(v,{type:"button",variant:"outline",onClick:C},{default:s(()=>a[28]||(a[28]=[n(" Batal ")])),_:1,__:[28]}),l(v,{type:"submit",disabled:e(t).processing},{default:s(()=>[l(k,{name:"save",class:"w-4 h-4 mr-2"}),a[29]||(a[29]=n(" Simpan Perubahan "))]),_:1,__:[29]},8,["disabled"])])):p("",!0)],32)]),_:1})]),_:1})])]),_:1}))}});export{Ge as default};
