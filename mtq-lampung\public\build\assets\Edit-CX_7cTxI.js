import{d as D,x as N,c as x,o as w,w as s,a as t,b as l,u as a,g as q,f as S,e as o,j as T,q as P,t as y,i as A}from"./app-B_pmlBSQ.js";import{_ as j}from"./AppLayout.vue_vue_type_script_setup_true_lang-BRf8lUiM.js";import{_ as B}from"./Heading.vue_vue_type_script_setup_true_lang-is4VBSCS.js";import{_}from"./AppLogoIcon.vue_vue_type_script_setup_true_lang-wzz4PWna.js";import{_ as p}from"./Input.vue_vue_type_script_setup_true_lang-DB1-RsTn.js";import{_ as i}from"./Label.vue_vue_type_script_setup_true_lang-rj_fhE7R.js";import{_ as $}from"./Textarea.vue_vue_type_script_setup_true_lang-jqOjZ7RT.js";import{_ as f,a as g}from"./CardContent.vue_vue_type_script_setup_true_lang-ClvLNBXC.js";import{_ as b}from"./CardDescription.vue_vue_type_script_setup_true_lang-DboywBIK.js";import{_ as k,a as v}from"./CardTitle.vue_vue_type_script_setup_true_lang-5HO6oSoB.js";import{_ as m}from"./InputError.vue_vue_type_script_setup_true_lang-A-vdzbq9.js";import{_ as V}from"./Icon.vue_vue_type_script_setup_true_lang-BuYzjckr.js";import"./useForwardExpose-CO14IhkA.js";import"./RovingFocusGroup-lsWyU4xZ.js";import"./check-DtmHjdxf.js";import"./loader-circle-6d8QrWFr.js";import"./sun-CHIV9RJ8.js";const C={class:"flex items-center space-x-4"},E={class:"space-y-6"},K={class:"grid gap-2"},L={class:"grid gap-2"},M={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},I={class:"grid gap-2"},O={class:"grid gap-2"},z={class:"grid gap-2"},F={class:"grid gap-2"},G={class:"grid gap-2"},H={class:"grid gap-2"},J={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},Q={class:"text-sm font-mono bg-gray-50 p-2 rounded border"},R={class:"mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg"},W={class:"flex items-start space-x-3"},X={class:"flex justify-end space-x-4 pt-6 border-t"},fe=D({__name:"Edit",props:{peserta:{}},setup(U){const d=U,r=N({nama_lengkap:d.peserta.nama_lengkap,email:d.peserta.user.email,tempat_lahir:d.peserta.tempat_lahir,tanggal_lahir:d.peserta.tanggal_lahir,alamat:d.peserta.alamat,no_telepon:d.peserta.no_telepon||"",status_peserta:d.peserta.status_peserta,keterangan:d.peserta.keterangan||""});function h(){r.put(route("admin-daerah.peserta.update",d.peserta.id_peserta))}return(u,e)=>(w(),x(j,null,{header:s(()=>[l("div",C,[t(_,{as:"link",href:u.route("admin-daerah.peserta.show",u.peserta.id_peserta),variant:"ghost",size:"sm"},{default:s(()=>[t(V,{name:"arrow-left",class:"w-4 h-4 mr-2"}),e[8]||(e[8]=o(" Kembali "))]),_:1,__:[8]},8,["href"]),t(B,null,{default:s(()=>e[9]||(e[9]=[o("Edit Peserta")])),_:1,__:[9]})])]),default:s(()=>[t(a(q),{title:"Edit Peserta"}),l("div",E,[l("form",{onSubmit:S(h,["prevent"]),class:"space-y-6"},[t(a(f),null,{default:s(()=>[t(a(k),null,{default:s(()=>[t(a(v),null,{default:s(()=>e[10]||(e[10]=[o("Data Pribadi")])),_:1,__:[10]}),t(a(b),null,{default:s(()=>e[11]||(e[11]=[o("Perbarui informasi pribadi peserta")])),_:1,__:[11]})]),_:1}),t(a(g),{class:"space-y-4"},{default:s(()=>[l("div",K,[t(a(i),{for:"nama_lengkap"},{default:s(()=>e[12]||(e[12]=[o("Nama Lengkap")])),_:1,__:[12]}),t(a(p),{id:"nama_lengkap",modelValue:a(r).nama_lengkap,"onUpdate:modelValue":e[0]||(e[0]=n=>a(r).nama_lengkap=n),placeholder:"Nama lengkap peserta",required:""},null,8,["modelValue"]),t(m,{message:a(r).errors.nama_lengkap},null,8,["message"])]),l("div",L,[t(a(i),{for:"email"},{default:s(()=>e[13]||(e[13]=[o("Email")])),_:1,__:[13]}),t(a(p),{id:"email",type:"email",modelValue:a(r).email,"onUpdate:modelValue":e[1]||(e[1]=n=>a(r).email=n),placeholder:"<EMAIL>",required:""},null,8,["modelValue"]),t(m,{message:a(r).errors.email},null,8,["message"])]),l("div",M,[l("div",I,[t(a(i),{for:"tempat_lahir"},{default:s(()=>e[14]||(e[14]=[o("Tempat Lahir")])),_:1,__:[14]}),t(a(p),{id:"tempat_lahir",modelValue:a(r).tempat_lahir,"onUpdate:modelValue":e[2]||(e[2]=n=>a(r).tempat_lahir=n),placeholder:"Tempat lahir",required:""},null,8,["modelValue"]),t(m,{message:a(r).errors.tempat_lahir},null,8,["message"])]),l("div",O,[t(a(i),{for:"tanggal_lahir"},{default:s(()=>e[15]||(e[15]=[o("Tanggal Lahir")])),_:1,__:[15]}),t(a(p),{id:"tanggal_lahir",type:"date",modelValue:a(r).tanggal_lahir,"onUpdate:modelValue":e[3]||(e[3]=n=>a(r).tanggal_lahir=n),required:""},null,8,["modelValue"]),t(m,{message:a(r).errors.tanggal_lahir},null,8,["message"])])]),l("div",z,[t(a(i),{for:"alamat"},{default:s(()=>e[16]||(e[16]=[o("Alamat")])),_:1,__:[16]}),t(a($),{id:"alamat",modelValue:a(r).alamat,"onUpdate:modelValue":e[4]||(e[4]=n=>a(r).alamat=n),placeholder:"Alamat lengkap",rows:"3",required:""},null,8,["modelValue"]),t(m,{message:a(r).errors.alamat},null,8,["message"])]),l("div",F,[t(a(i),{for:"no_telepon"},{default:s(()=>e[17]||(e[17]=[o("No. Telepon (Opsional)")])),_:1,__:[17]}),t(a(p),{id:"no_telepon",type:"tel",modelValue:a(r).no_telepon,"onUpdate:modelValue":e[5]||(e[5]=n=>a(r).no_telepon=n),placeholder:"Nomor telepon"},null,8,["modelValue"]),t(m,{message:a(r).errors.no_telepon},null,8,["message"])])]),_:1})]),_:1}),t(a(f),null,{default:s(()=>[t(a(k),null,{default:s(()=>[t(a(v),null,{default:s(()=>e[18]||(e[18]=[o("Status dan Catatan")])),_:1,__:[18]}),t(a(b),null,{default:s(()=>e[19]||(e[19]=[o("Kelola status peserta dan tambahkan catatan")])),_:1,__:[19]})]),_:1}),t(a(g),{class:"space-y-4"},{default:s(()=>[l("div",G,[t(a(i),{for:"status_peserta"},{default:s(()=>e[20]||(e[20]=[o("Status Peserta")])),_:1,__:[20]}),T(l("select",{id:"status_peserta","onUpdate:modelValue":e[6]||(e[6]=n=>a(r).status_peserta=n),required:"",class:"flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50"},e[21]||(e[21]=[l("option",{value:"draft"},"Draft",-1),l("option",{value:"submitted"},"Disubmit",-1),l("option",{value:"verified"},"Terverifikasi",-1),l("option",{value:"approved"},"Disetujui",-1),l("option",{value:"rejected"},"Ditolak",-1)]),512),[[P,a(r).status_peserta]]),t(m,{message:a(r).errors.status_peserta},null,8,["message"])]),l("div",H,[t(a(i),{for:"keterangan"},{default:s(()=>e[22]||(e[22]=[o("Keterangan (Opsional)")])),_:1,__:[22]}),t(a($),{id:"keterangan",modelValue:a(r).keterangan,"onUpdate:modelValue":e[7]||(e[7]=n=>a(r).keterangan=n),placeholder:"Tambahkan catatan atau keterangan khusus...",rows:"4"},null,8,["modelValue"]),t(m,{message:a(r).errors.keterangan},null,8,["message"])])]),_:1})]),_:1}),t(a(f),null,{default:s(()=>[t(a(k),null,{default:s(()=>[t(a(v),null,{default:s(()=>e[23]||(e[23]=[o("Informasi Akun")])),_:1,__:[23]}),t(a(b),null,{default:s(()=>e[24]||(e[24]=[o("Data akun peserta (hanya untuk referensi)")])),_:1,__:[24]})]),_:1}),t(a(g),null,{default:s(()=>[l("div",J,[l("div",null,[t(a(i),null,{default:s(()=>e[25]||(e[25]=[o("Username")])),_:1,__:[25]}),l("p",Q,y(u.peserta.user.username),1)]),l("div",null,[t(a(i),null,{default:s(()=>e[26]||(e[26]=[o("Status Akun")])),_:1,__:[26]}),e[27]||(e[27]=l("p",{class:"text-sm bg-gray-50 p-2 rounded border"},"Aktif",-1))])]),l("div",R,[l("div",W,[t(V,{name:"info",class:"h-5 w-5 text-blue-600 mt-0.5"}),e[28]||(e[28]=l("div",null,[l("h4",{class:"text-sm font-medium text-blue-900"},"Informasi"),l("p",{class:"text-sm text-blue-700 mt-1"}," Username tidak dapat diubah. Untuk mengubah password, peserta harus melakukan reset password melalui halaman login. ")],-1))])])]),_:1})]),_:1}),l("div",X,[t(_,{as:"link",href:u.route("admin-daerah.peserta.show",u.peserta.id_peserta),variant:"outline"},{default:s(()=>e[29]||(e[29]=[o(" Batal ")])),_:1,__:[29]},8,["href"]),t(_,{type:"submit",disabled:a(r).processing},{default:s(()=>[a(r).processing?(w(),x(V,{key:0,name:"loader-2",class:"w-4 h-4 mr-2 animate-spin"})):A("",!0),o(" "+y(a(r).processing?"Menyimpan...":"Simpan Perubahan"),1)]),_:1},8,["disabled"])])],32)])]),_:1}))}});export{fe as default};
