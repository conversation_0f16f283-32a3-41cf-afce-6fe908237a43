import{a as o}from"./AppLogoIcon.vue_vue_type_script_setup_true_lang-wzz4PWna.js";import{d as r,h as t,o as n,D as c,n as l,u as d}from"./app-B_pmlBSQ.js";const f=r({__name:"Card",props:{class:{}},setup(s){const a=s;return(e,p)=>(n(),t("div",{"data-slot":"card",class:l(d(o)("bg-card text-card-foreground flex flex-col gap-3 rounded-xl border shadow-sm pb-3",a.class))},[c(e.$slots,"default")],2))}}),_=r({__name:"CardContent",props:{class:{}},setup(s){const a=s;return(e,p)=>(n(),t("div",{"data-slot":"card-content",class:l(d(o)("px-3",a.class))},[c(e.$slots,"default")],2))}});export{f as _,_ as a};
