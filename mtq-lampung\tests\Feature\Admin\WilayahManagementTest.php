<?php

namespace Tests\Feature\Admin;

use App\Models\User;
use App\Models\Wilayah;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class WilayahManagementTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a superadmin user for testing
        $this->superadmin = User::factory()->create([
            'role' => 'superadmin',
            'status' => 'aktif'
        ]);
    }

    /** @test */
    public function superadmin_can_view_wilayah_index()
    {
        $this->actingAs($this->superadmin);
        
        $response = $this->get(route('admin.wilayah.index'));
        
        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => $page->component('Admin/Wilayah/Index'));
    }

    /** @test */
    public function superadmin_can_create_provinsi()
    {
        $this->actingAs($this->superadmin);
        
        $wilayahData = [
            'kode_wilayah' => 'LP',
            'nama_wilayah' => 'Lampung',
            'level_wilayah' => 'provinsi',
            'status' => 'aktif'
        ];
        
        $response = $this->post(route('admin.wilayah.store'), $wilayahData);
        
        $response->assertRedirect(route('admin.wilayah.index'));
        $this->assertDatabaseHas('wilayah', [
            'kode_wilayah' => 'LP',
            'nama_wilayah' => 'Lampung',
            'level_wilayah' => 'provinsi'
        ]);
    }

    /** @test */
    public function superadmin_can_create_kabupaten_with_parent()
    {
        $this->actingAs($this->superadmin);
        
        $provinsi = Wilayah::factory()->create([
            'level_wilayah' => 'provinsi',
            'status' => 'aktif'
        ]);
        
        $wilayahData = [
            'kode_wilayah' => 'BDL',
            'nama_wilayah' => 'Bandar Lampung',
            'level_wilayah' => 'kota',
            'parent_id' => $provinsi->id_wilayah,
            'status' => 'aktif'
        ];
        
        $response = $this->post(route('admin.wilayah.store'), $wilayahData);
        
        $response->assertRedirect(route('admin.wilayah.index'));
        $this->assertDatabaseHas('wilayah', [
            'kode_wilayah' => 'BDL',
            'nama_wilayah' => 'Bandar Lampung',
            'level_wilayah' => 'kota',
            'parent_id' => $provinsi->id_wilayah
        ]);
    }

    /** @test */
    public function superadmin_can_update_wilayah()
    {
        $this->actingAs($this->superadmin);
        
        $wilayah = Wilayah::factory()->create([
            'level_wilayah' => 'provinsi'
        ]);
        
        $updateData = [
            'kode_wilayah' => 'LP_UPDATED',
            'nama_wilayah' => 'Lampung Updated',
            'level_wilayah' => 'provinsi',
            'status' => 'aktif'
        ];
        
        $response = $this->put(route('admin.wilayah.update', $wilayah), $updateData);
        
        $response->assertRedirect(route('admin.wilayah.index'));
        $this->assertDatabaseHas('wilayah', [
            'id_wilayah' => $wilayah->id_wilayah,
            'kode_wilayah' => 'LP_UPDATED',
            'nama_wilayah' => 'Lampung Updated'
        ]);
    }

    /** @test */
    public function superadmin_can_view_wilayah_children()
    {
        $this->actingAs($this->superadmin);
        
        $provinsi = Wilayah::factory()->create(['level_wilayah' => 'provinsi']);
        $kabupaten = Wilayah::factory()->create([
            'level_wilayah' => 'kabupaten',
            'parent_id' => $provinsi->id_wilayah
        ]);
        
        $response = $this->get(route('admin.wilayah.children', $provinsi));
        
        $response->assertStatus(200);
        $response->assertJson([
            [
                'id_wilayah' => $kabupaten->id_wilayah,
                'nama_wilayah' => $kabupaten->nama_wilayah
            ]
        ]);
    }

    /** @test */
    public function wilayah_creation_validates_required_fields()
    {
        $this->actingAs($this->superadmin);
        
        $response = $this->post(route('admin.wilayah.store'), []);
        
        $response->assertSessionHasErrors([
            'kode_wilayah',
            'nama_wilayah',
            'level_wilayah',
            'status'
        ]);
    }

    /** @test */
    public function wilayah_creation_validates_unique_kode()
    {
        $this->actingAs($this->superadmin);
        
        $existingWilayah = Wilayah::factory()->create();
        
        $response = $this->post(route('admin.wilayah.store'), [
            'kode_wilayah' => $existingWilayah->kode_wilayah,
            'nama_wilayah' => 'Test Wilayah',
            'level_wilayah' => 'provinsi',
            'status' => 'aktif'
        ]);
        
        $response->assertSessionHasErrors(['kode_wilayah']);
    }

    /** @test */
    public function wilayah_hierarchy_validation_works()
    {
        $this->actingAs($this->superadmin);
        
        $kabupaten = Wilayah::factory()->create(['level_wilayah' => 'kabupaten']);
        
        // Try to create provinsi with kabupaten as parent (invalid)
        $response = $this->post(route('admin.wilayah.store'), [
            'kode_wilayah' => 'TEST',
            'nama_wilayah' => 'Test Provinsi',
            'level_wilayah' => 'provinsi',
            'parent_id' => $kabupaten->id_wilayah,
            'status' => 'aktif'
        ]);
        
        // Should not allow provinsi to have parent
        $this->assertDatabaseMissing('wilayah', [
            'kode_wilayah' => 'TEST',
            'parent_id' => $kabupaten->id_wilayah
        ]);
    }

    /** @test */
    public function superadmin_cannot_delete_wilayah_with_children()
    {
        $this->actingAs($this->superadmin);
        
        $provinsi = Wilayah::factory()->create(['level_wilayah' => 'provinsi']);
        $kabupaten = Wilayah::factory()->create([
            'level_wilayah' => 'kabupaten',
            'parent_id' => $provinsi->id_wilayah
        ]);
        
        $response = $this->delete(route('admin.wilayah.destroy', $provinsi));
        
        $response->assertRedirect();
        $response->assertSessionHas('error');
        $this->assertDatabaseHas('wilayah', ['id_wilayah' => $provinsi->id_wilayah]);
    }

    /** @test */
    public function wilayah_search_functionality_works()
    {
        $this->actingAs($this->superadmin);
        
        Wilayah::factory()->create(['nama_wilayah' => 'Lampung Selatan']);
        Wilayah::factory()->create(['nama_wilayah' => 'Lampung Utara']);
        Wilayah::factory()->create(['nama_wilayah' => 'Bandar Lampung']);
        
        $response = $this->get(route('admin.wilayah.index', ['search' => 'Selatan']));
        
        $response->assertStatus(200);
    }

    /** @test */
    public function wilayah_filtering_by_level_works()
    {
        $this->actingAs($this->superadmin);
        
        Wilayah::factory()->create(['level_wilayah' => 'provinsi']);
        Wilayah::factory()->create(['level_wilayah' => 'kabupaten']);
        Wilayah::factory()->create(['level_wilayah' => 'kota']);
        
        $response = $this->get(route('admin.wilayah.index', ['level' => 'provinsi']));
        
        $response->assertStatus(200);
    }

    /** @test */
    public function wilayah_show_displays_correct_data()
    {
        $this->actingAs($this->superadmin);
        
        $provinsi = Wilayah::factory()->create(['level_wilayah' => 'provinsi']);
        $kabupaten = Wilayah::factory()->create([
            'level_wilayah' => 'kabupaten',
            'parent_id' => $provinsi->id_wilayah
        ]);
        
        $response = $this->get(route('admin.wilayah.show', $provinsi));
        
        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => $page->component('Admin/Wilayah/Show'));
    }
}
