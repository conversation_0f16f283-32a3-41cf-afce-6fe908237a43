<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Storage;

class SuratMandat extends Model
{
    protected $table = 'surat_mandat';
    protected $primaryKey = 'id_surat_mandat';

    protected $fillable = [
        'id_wilayah',
        'nomor_surat',
        'tanggal_surat',
        'path_file',
        'keterangan',
        'status'
    ];

    protected $casts = [
        'tanggal_surat' => 'date',
        'status' => 'string'
    ];

    // Relationships
    public function wilayah(): BelongsTo
    {
        return $this->belongsTo(Wilayah::class, 'id_wilayah', 'id_wilayah');
    }

    // Scopes
    public function scopeAktif($query)
    {
        return $query->where('status', 'aktif');
    }

    public function scopeByWilayah($query, $idWilayah)
    {
        return $query->where('id_wilayah', $idWilayah);
    }

    public function scopeByTahun($query, $tahun)
    {
        return $query->whereYear('tanggal_surat', $tahun);
    }

    // Helper methods
    public function isAktif(): bool
    {
        return $this->status === 'aktif';
    }

    public function getFileUrl(): string
    {
        return Storage::url($this->path_file);
    }

    public function getFileExists(): bool
    {
        return Storage::exists($this->path_file);
    }

    public function getFileSize(): int
    {
        return $this->getFileExists() ? Storage::size($this->path_file) : 0;
    }

    public function getFormattedFileSizeAttribute(): string
    {
        $size = $this->getFileSize();
        
        if ($size >= 1048576) {
            return number_format($size / 1048576, 2) . ' MB';
        } elseif ($size >= 1024) {
            return number_format($size / 1024, 2) . ' KB';
        } else {
            return $size . ' bytes';
        }
    }

    public function getFormattedTanggalAttribute(): string
    {
        return $this->tanggal_surat ? $this->tanggal_surat->format('d/m/Y') : '';
    }

    public function deleteFile(): bool
    {
        if ($this->getFileExists()) {
            return Storage::delete($this->path_file);
        }
        return true;
    }
}
