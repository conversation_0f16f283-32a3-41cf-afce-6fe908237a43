<template>
  <AppLayout>
    <Head title="Detail Verifikasi Pendaftaran" />
    
    <div class="space-y-6">
      <!-- Header -->
      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-2xl font-bold text-gray-900">Detail Verifikasi Pendaftaran</h1>
            <p class="text-gray-600 mt-1">{{ pendaftaran.nomor_pendaftaran }}</p>
          </div>
          <div class="flex space-x-3">
            <Link :href="route('admin.verifikasi-provinsi.index')" 
                  class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">
              Kembali
            </Link>
            <button v-if="!pendaftaran.verifikasi_pendaftaran || pendaftaran.verifikasi_pendaftaran.status_verifikasi === 'pending'"
                    @click="showVerificationModal = true"
                    class="px-4 py-2 bg-emerald-600 text-white rounded-md text-sm font-medium hover:bg-emerald-700">
              Verifikasi Sekarang
            </button>
          </div>
        </div>
      </div>

      <!-- Participant Information -->
      <div class="bg-white rounded-lg shadow p-6">
        <h2 class="text-lg font-semibold text-gray-900 mb-4">Informasi Peserta</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-500">Nama Lengkap</label>
              <p class="mt-1 text-sm text-gray-900">{{ pendaftaran.peserta.nama_lengkap }}</p>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-500">NIK</label>
              <p class="mt-1 text-sm text-gray-900">{{ pendaftaran.peserta.nik }}</p>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-500">Tempat, Tanggal Lahir</label>
              <p class="mt-1 text-sm text-gray-900">
                {{ pendaftaran.peserta.tempat_lahir }}, {{ formatDate(pendaftaran.peserta.tanggal_lahir) }}
              </p>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-500">Jenis Kelamin</label>
              <p class="mt-1 text-sm text-gray-900">{{ pendaftaran.peserta.jenis_kelamin === 'L' ? 'Laki-laki' : 'Perempuan' }}</p>
            </div>
          </div>
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-500">Wilayah</label>
              <p class="mt-1 text-sm text-gray-900">{{ pendaftaran.peserta.wilayah.nama_wilayah }}</p>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-500">No. Telepon</label>
              <p class="mt-1 text-sm text-gray-900">{{ pendaftaran.peserta.no_telepon || '-' }}</p>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-500">Email</label>
              <p class="mt-1 text-sm text-gray-900">{{ pendaftaran.peserta.email || '-' }}</p>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-500">Alamat</label>
              <p class="mt-1 text-sm text-gray-900">{{ pendaftaran.peserta.alamat }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Registration Information -->
      <div class="bg-white rounded-lg shadow p-6">
        <h2 class="text-lg font-semibold text-gray-900 mb-4">Informasi Pendaftaran</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <label class="block text-sm font-medium text-gray-500">Nomor Pendaftaran</label>
            <p class="mt-1 text-sm text-gray-900 font-mono">{{ pendaftaran.nomor_pendaftaran }}</p>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-500">Nomor Peserta</label>
            <p class="mt-1 text-sm text-gray-900 font-mono">{{ pendaftaran.nomor_peserta }}</p>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-500">Golongan</label>
            <p class="mt-1 text-sm text-gray-900">{{ pendaftaran.golongan.nama_golongan }}</p>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-500">Cabang Lomba</label>
            <p class="mt-1 text-sm text-gray-900">{{ pendaftaran.golongan.cabang_lomba.nama_cabang }}</p>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-500">Tanggal Daftar</label>
            <p class="mt-1 text-sm text-gray-900">{{ formatDate(pendaftaran.tanggal_daftar) }}</p>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-500">Status Pendaftaran</label>
            <span :class="getStatusBadgeClass(pendaftaran.status_pendaftaran)" 
                  class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
              {{ getStatusText(pendaftaran.status_pendaftaran) }}
            </span>
          </div>
        </div>
      </div>

      <!-- Documents -->
      <div class="bg-white rounded-lg shadow p-6">
        <h2 class="text-lg font-semibold text-gray-900 mb-4">Dokumen Peserta</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div v-for="dokumen in pendaftaran.dokumen_peserta" :key="dokumen.id_dokumen" 
               class="border border-gray-200 rounded-lg p-4">
            <div class="flex items-center justify-between mb-2">
              <h3 class="text-sm font-medium text-gray-900">{{ getDocumentTypeName(dokumen.jenis_dokumen) }}</h3>
              <span :class="getDocumentStatusClass(dokumen.status_verifikasi)" 
                    class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                {{ getDocumentStatusText(dokumen.status_verifikasi) }}
              </span>
            </div>
            <p class="text-xs text-gray-500 mb-3">{{ dokumen.nama_file }}</p>
            <div class="flex space-x-2">
              <button @click="previewDocument(dokumen)" 
                      class="flex-1 px-3 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700">
                Preview
              </button>
              <a :href="route('admin.verifikasi-provinsi.dokumen.download', dokumen.id_dokumen)" 
                 target="_blank"
                 class="flex-1 px-3 py-1 text-xs bg-gray-600 text-white rounded hover:bg-gray-700 text-center">
                Download
              </a>
            </div>
          </div>
        </div>
      </div>

      <!-- Verification Status -->
      <div v-if="pendaftaran.verifikasi_pendaftaran" class="bg-white rounded-lg shadow p-6">
        <h2 class="text-lg font-semibold text-gray-900 mb-4">Status Verifikasi</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-500">Status NIK</label>
              <div class="mt-1 flex items-center space-x-2">
                <span :class="getNikStatusClass(pendaftaran.verifikasi_pendaftaran.status_nik)" 
                      class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                  {{ getNikStatusText(pendaftaran.verifikasi_pendaftaran.status_nik) }}
                </span>
              </div>
              <p v-if="pendaftaran.verifikasi_pendaftaran.catatan_nik" 
                 class="mt-2 text-sm text-gray-600">
                {{ pendaftaran.verifikasi_pendaftaran.catatan_nik }}
              </p>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-500">Status Dokumen</label>
              <div class="mt-1 flex items-center space-x-2">
                <span :class="getDocumentVerificationStatusClass(pendaftaran.verifikasi_pendaftaran.status_dokumen)" 
                      class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                  {{ getDocumentVerificationStatusText(pendaftaran.verifikasi_pendaftaran.status_dokumen) }}
                </span>
              </div>
              <p v-if="pendaftaran.verifikasi_pendaftaran.catatan_dokumen" 
                 class="mt-2 text-sm text-gray-600">
                {{ pendaftaran.verifikasi_pendaftaran.catatan_dokumen }}
              </p>
            </div>
          </div>
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-500">Status Verifikasi</label>
              <div class="mt-1 flex items-center space-x-2">
                <span :class="getVerificationStatusClass(pendaftaran.verifikasi_pendaftaran.status_verifikasi)" 
                      class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                  {{ getVerificationStatusText(pendaftaran.verifikasi_pendaftaran.status_verifikasi) }}
                </span>
              </div>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-500">Diverifikasi Oleh</label>
              <p class="mt-1 text-sm text-gray-900">{{ pendaftaran.verifikasi_pendaftaran.verified_by?.nama_lengkap || '-' }}</p>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-500">Tanggal Verifikasi</label>
              <p class="mt-1 text-sm text-gray-900">
                {{ pendaftaran.verifikasi_pendaftaran.verified_at ? formatDate(pendaftaran.verifikasi_pendaftaran.verified_at) : '-' }}
              </p>
            </div>
            <div v-if="pendaftaran.verifikasi_pendaftaran.catatan_verifikasi">
              <label class="block text-sm font-medium text-gray-500">Catatan Verifikasi</label>
              <p class="mt-1 text-sm text-gray-600">{{ pendaftaran.verifikasi_pendaftaran.catatan_verifikasi }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Document Preview Modal -->
      <div v-if="showPreviewModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-4/5 lg:w-3/4 shadow-lg rounded-md bg-white">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-medium text-gray-900">Preview Dokumen</h3>
            <button @click="closePreviewModal" class="text-gray-400 hover:text-gray-600">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
          </div>
          <div v-if="selectedDocument" class="mb-4">
            <p class="text-sm text-gray-600">{{ getDocumentTypeName(selectedDocument.jenis_dokumen) }}</p>
            <p class="text-xs text-gray-500">{{ selectedDocument.nama_file }}</p>
          </div>
          <div class="h-96 md:h-[600px] border border-gray-200 rounded">
            <iframe v-if="previewUrl" 
                    :src="previewUrl" 
                    class="w-full h-full rounded"
                    frameborder="0">
            </iframe>
            <div v-else class="flex items-center justify-center h-full">
              <p class="text-gray-500">Memuat preview...</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Verification Modal (same as in Index.vue) -->
      <div v-if="showVerificationModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
          <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
              <h3 class="text-lg font-medium text-gray-900">Verifikasi Pendaftaran</h3>
              <button @click="closeVerificationModal" class="text-gray-400 hover:text-gray-600">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
              </button>
            </div>

            <div class="space-y-6">
              <!-- Participant Info -->
              <div class="bg-gray-50 p-4 rounded-lg">
                <h4 class="font-medium text-gray-900 mb-2">Informasi Peserta</h4>
                <div class="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span class="text-gray-500">Nama:</span>
                    <span class="ml-2 font-medium">{{ pendaftaran.peserta.nama_lengkap }}</span>
                  </div>
                  <div>
                    <span class="text-gray-500">NIK:</span>
                    <span class="ml-2 font-medium">{{ pendaftaran.peserta.nik }}</span>
                  </div>
                </div>
              </div>

              <!-- Verification Form -->
              <form @submit.prevent="submitVerification" class="space-y-4">
                <!-- NIK Verification -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Verifikasi NIK</label>
                  <div class="space-y-2">
                    <label class="flex items-center">
                      <input v-model="verificationForm.status_nik" 
                             type="radio" 
                             value="sesuai" 
                             class="mr-2 text-emerald-600 focus:ring-emerald-500">
                      <span class="text-sm">Sesuai</span>
                    </label>
                    <label class="flex items-center">
                      <input v-model="verificationForm.status_nik" 
                             type="radio" 
                             value="tidak_sesuai" 
                             class="mr-2 text-red-600 focus:ring-red-500">
                      <span class="text-sm">Tidak Sesuai</span>
                    </label>
                  </div>
                  <textarea v-model="verificationForm.catatan_nik" 
                            placeholder="Catatan verifikasi NIK (opsional)"
                            rows="2"
                            class="mt-2 w-full rounded-md border-gray-300 shadow-sm focus:border-emerald-500 focus:ring-emerald-500"></textarea>
                </div>

                <!-- Document Verification -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Verifikasi Dokumen</label>
                  <div class="space-y-2">
                    <label class="flex items-center">
                      <input v-model="verificationForm.status_dokumen" 
                             type="radio" 
                             value="berkas_sesuai" 
                             class="mr-2 text-emerald-600 focus:ring-emerald-500">
                      <span class="text-sm">Berkas Sesuai</span>
                    </label>
                    <label class="flex items-center">
                      <input v-model="verificationForm.status_dokumen" 
                             type="radio" 
                             value="berkas_tidak_sesuai" 
                             class="mr-2 text-red-600 focus:ring-red-500">
                      <span class="text-sm">Berkas Tidak Sesuai</span>
                    </label>
                  </div>
                  <textarea v-model="verificationForm.catatan_dokumen" 
                            placeholder="Catatan verifikasi dokumen (opsional)"
                            rows="2"
                            class="mt-2 w-full rounded-md border-gray-300 shadow-sm focus:border-emerald-500 focus:ring-emerald-500"></textarea>
                </div>

                <!-- General Notes -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Catatan Verifikasi</label>
                  <textarea v-model="verificationForm.catatan_verifikasi" 
                            placeholder="Catatan umum verifikasi (opsional)"
                            rows="3"
                            class="w-full rounded-md border-gray-300 shadow-sm focus:border-emerald-500 focus:ring-emerald-500"></textarea>
                </div>

                <!-- Action Buttons -->
                <div class="flex justify-end space-x-3 pt-4">
                  <button type="button" 
                          @click="closeVerificationModal"
                          class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">
                    Batal
                  </button>
                  <button type="submit" 
                          :disabled="!verificationForm.status_nik || !verificationForm.status_dokumen || verificationForm.processing"
                          class="px-4 py-2 bg-emerald-600 text-white rounded-md text-sm font-medium hover:bg-emerald-700 disabled:opacity-50 disabled:cursor-not-allowed">
                    {{ verificationForm.processing ? 'Memproses...' : 'Simpan Verifikasi' }}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import { Head, Link, useForm } from '@inertiajs/vue3'
import { ref } from 'vue'
import AppLayout from '@/layouts/AppLayout.vue'

interface Pendaftaran {
  id_pendaftaran: number
  nomor_pendaftaran: string
  nomor_peserta: string
  status_pendaftaran: string
  tanggal_daftar: string
  peserta: {
    nama_lengkap: string
    nik: string
    tempat_lahir: string
    tanggal_lahir: string
    jenis_kelamin: string
    alamat: string
    no_telepon?: string
    email?: string
    wilayah: {
      nama_wilayah: string
    }
  }
  golongan: {
    nama_golongan: string
    cabang_lomba: {
      nama_cabang: string
    }
  }
  dokumen_peserta: Array<{
    id_dokumen: number
    jenis_dokumen: string
    nama_file: string
    status_verifikasi: string
  }>
  verifikasi_pendaftaran?: {
    status_nik: string
    status_dokumen: string
    status_verifikasi: string
    catatan_nik?: string
    catatan_dokumen?: string
    catatan_verifikasi?: string
    verified_at?: string
    verified_by?: {
      nama_lengkap: string
    }
  }
}

const props = defineProps<{
  pendaftaran: Pendaftaran
}>()

// Modal states
const showVerificationModal = ref(false)
const showPreviewModal = ref(false)
const selectedDocument = ref<any>(null)
const previewUrl = ref('')

// Verification form
const verificationForm = useForm({
  status_nik: props.pendaftaran.verifikasi_pendaftaran?.status_nik || '',
  catatan_nik: props.pendaftaran.verifikasi_pendaftaran?.catatan_nik || '',
  status_dokumen: props.pendaftaran.verifikasi_pendaftaran?.status_dokumen || '',
  catatan_dokumen: props.pendaftaran.verifikasi_pendaftaran?.catatan_dokumen || '',
  catatan_verifikasi: props.pendaftaran.verifikasi_pendaftaran?.catatan_verifikasi || ''
})

// Helper functions
function formatDate(dateString: string) {
  return new Date(dateString).toLocaleDateString('id-ID', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

function getStatusBadgeClass(status: string) {
  const classes = {
    'draft': 'bg-gray-100 text-gray-800',
    'submitted': 'bg-blue-100 text-blue-800',
    'verified': 'bg-yellow-100 text-yellow-800',
    'approved': 'bg-green-100 text-green-800',
    'rejected': 'bg-red-100 text-red-800'
  }
  return classes[status] || 'bg-gray-100 text-gray-800'
}

function getStatusText(status: string) {
  const texts = {
    'draft': 'Draft',
    'submitted': 'Disubmit',
    'verified': 'Terverifikasi',
    'approved': 'Disetujui',
    'rejected': 'Ditolak'
  }
  return texts[status] || status
}

function getDocumentTypeName(type: string) {
  const names = {
    'foto': 'Foto Peserta',
    'ktp': 'KTP',
    'kartu_keluarga': 'Kartu Keluarga',
    'surat_rekomendasi': 'Surat Rekomendasi',
    'ijazah': 'Ijazah',
    'sertifikat': 'Sertifikat'
  }
  return names[type] || type
}

function getDocumentStatusClass(status: string) {
  const classes = {
    'pending': 'bg-yellow-100 text-yellow-800',
    'approved': 'bg-green-100 text-green-800',
    'rejected': 'bg-red-100 text-red-800'
  }
  return classes[status] || 'bg-gray-100 text-gray-800'
}

function getDocumentStatusText(status: string) {
  const texts = {
    'pending': 'Pending',
    'approved': 'Disetujui',
    'rejected': 'Ditolak'
  }
  return texts[status] || status
}

function getNikStatusClass(status: string) {
  const classes = {
    'pending': 'bg-yellow-100 text-yellow-800',
    'sesuai': 'bg-green-100 text-green-800',
    'tidak_sesuai': 'bg-red-100 text-red-800'
  }
  return classes[status] || 'bg-gray-100 text-gray-800'
}

function getNikStatusText(status: string) {
  const texts = {
    'pending': 'Pending',
    'sesuai': 'Sesuai',
    'tidak_sesuai': 'Tidak Sesuai'
  }
  return texts[status] || status
}

function getDocumentVerificationStatusClass(status: string) {
  const classes = {
    'pending': 'bg-yellow-100 text-yellow-800',
    'berkas_sesuai': 'bg-green-100 text-green-800',
    'berkas_tidak_sesuai': 'bg-red-100 text-red-800'
  }
  return classes[status] || 'bg-gray-100 text-gray-800'
}

function getDocumentVerificationStatusText(status: string) {
  const texts = {
    'pending': 'Pending',
    'berkas_sesuai': 'Berkas Sesuai',
    'berkas_tidak_sesuai': 'Berkas Tidak Sesuai'
  }
  return texts[status] || status
}

function getVerificationStatusClass(status: string) {
  const classes = {
    'pending': 'bg-yellow-100 text-yellow-800',
    'approved': 'bg-green-100 text-green-800',
    'rejected': 'bg-red-100 text-red-800'
  }
  return classes[status] || 'bg-gray-100 text-gray-800'
}

function getVerificationStatusText(status: string) {
  const texts = {
    'pending': 'Pending',
    'approved': 'Disetujui',
    'rejected': 'Ditolak'
  }
  return texts[status] || status
}

function previewDocument(dokumen: any) {
  selectedDocument.value = dokumen
  previewUrl.value = route('admin.verifikasi-provinsi.dokumen.preview', dokumen.id_dokumen)
  showPreviewModal.value = true
}

function closePreviewModal() {
  showPreviewModal.value = false
  selectedDocument.value = null
  previewUrl.value = ''
}

function closeVerificationModal() {
  showVerificationModal.value = false
  verificationForm.reset()
}

function submitVerification() {
  verificationForm.post(route('admin.verifikasi-provinsi.verify', props.pendaftaran.id_pendaftaran), {
    onSuccess: () => {
      closeVerificationModal()
    }
  })
}
</script>
