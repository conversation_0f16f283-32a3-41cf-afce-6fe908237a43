<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class DewaHakimPengalaman extends Model
{
    protected $table = 'dewan_hakim_pengalaman';
    protected $primaryKey = 'id_pengalaman';

    public $timestamps = false;

    protected $fillable = [
        'id_dewan_hakim',
        'nama_kegiatan',
        'penyelenggara',
        'tahun',
        'tingkat'
    ];

    protected $casts = [
        'tahun' => 'integer',
        'tingkat' => 'string'
    ];

    // Relationships
    public function dewaHakim(): BelongsTo
    {
        return $this->belongsTo(DewaHakim::class, 'id_dewan_hakim', 'id_dewan_hakim');
    }

    // Scopes
    public function scopeByTingkat($query, $tingkat)
    {
        return $query->where('tingkat', $tingkat);
    }

    public function scopeByTahun($query, $tahun)
    {
        return $query->where('tahun', $tahun);
    }

    // Helper methods
    public function isKabupaten(): bool
    {
        return $this->tingkat === 'kabupaten';
    }

    public function isProvinsi(): bool
    {
        return $this->tingkat === 'provinsi';
    }

    public function isNasional(): bool
    {
        return $this->tingkat === 'nasional';
    }

    public function isInternasional(): bool
    {
        return $this->tingkat === 'internasional';
    }

    public function getTingkatLabelAttribute(): string
    {
        return match($this->tingkat) {
            'kabupaten' => 'Kabupaten/Kota',
            'provinsi' => 'Provinsi',
            'nasional' => 'Nasional',
            'internasional' => 'Internasional',
            default => ucfirst($this->tingkat)
        };
    }
}
