<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('jenis_dokumen', function (Blueprint $table) {
            $table->id('id_jenis_dokumen');
            $table->string('kode_dokumen', 20)->unique();
            $table->string('nama_dokumen', 100);
            $table->text('deskripsi')->nullable();
            $table->enum('kategori', ['identitas', 'pendidikan', 'rekomendasi', 'lainnya'])->default('lainnya');
            $table->boolean('wajib')->default(true);
            $table->string('format_file')->nullable(); // pdf,jpg,png
            $table->integer('ukuran_max_kb')->default(2048); // 2MB default
            $table->enum('status', ['aktif', 'non_aktif'])->default('aktif');
            $table->integer('urutan')->default(0);
            $table->timestamps();
            
            $table->index(['status']);
            $table->index(['kategori']);
            $table->index(['wajib']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('jenis_dokumen');
    }
};
