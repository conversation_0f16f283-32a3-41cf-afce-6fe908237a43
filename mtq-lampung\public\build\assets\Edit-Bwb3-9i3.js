import{d as h,x as B,k as G,c as x,o as i,w as n,a as e,b as r,u as a,g as N,e as s,f as I,h as m,i as _,n as g,t as d,F as E,m as A}from"./app-B_pmlBSQ.js";import{_ as K}from"./AppLayout.vue_vue_type_script_setup_true_lang-BRf8lUiM.js";import{_ as M}from"./Heading.vue_vue_type_script_setup_true_lang-is4VBSCS.js";import{_ as U}from"./AppLogoIcon.vue_vue_type_script_setup_true_lang-wzz4PWna.js";import{_ as T,a as J}from"./CardContent.vue_vue_type_script_setup_true_lang-ClvLNBXC.js";import{_ as c}from"./CardDescription.vue_vue_type_script_setup_true_lang-DboywBIK.js";import{_ as z,a as F}from"./CardTitle.vue_vue_type_script_setup_true_lang-5HO6oSoB.js";import{_ as b}from"./Input.vue_vue_type_script_setup_true_lang-DB1-RsTn.js";import{_ as f}from"./Label.vue_vue_type_script_setup_true_lang-rj_fhE7R.js";import{_ as y,a as V,b as v,c as w,d as k}from"./SelectValue.vue_vue_type_script_setup_true_lang-IzlIronZ.js";import{_ as C}from"./Icon.vue_vue_type_script_setup_true_lang-BuYzjckr.js";import"./useForwardExpose-CO14IhkA.js";import"./RovingFocusGroup-lsWyU4xZ.js";import"./check-DtmHjdxf.js";import"./useFormControl-mZRLifGx.js";import"./loader-circle-6d8QrWFr.js";import"./sun-CHIV9RJ8.js";const H={class:"max-w-2xl mx-auto"},O={class:"space-y-4"},Q={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},R={key:0,class:"text-sm text-red-600 mt-1"},W={key:0,class:"text-sm text-red-600 mt-1"},X={key:0,class:"text-sm text-red-600 mt-1"},Y={key:0,class:"text-sm text-red-600 mt-1"},Z={class:"space-y-4"},aa={key:0,class:"text-sm text-red-600 mt-1"},oa={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},ta={key:0,class:"text-sm text-red-600 mt-1"},ea={key:0,class:"text-sm text-red-600 mt-1"},ra={class:"space-y-4"},na={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},la={key:0,class:"text-sm text-red-600 mt-1"},sa={key:0,class:"text-sm text-red-600 mt-1"},da={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},ia={key:0,class:"text-sm text-red-600 mt-1"},ua={key:0,class:"text-sm text-red-600 mt-1"},ma={class:"bg-gray-50 p-4 rounded-lg"},_a={class:"text-sm text-gray-600 space-y-1"},ga={key:0,class:"bg-yellow-50 border border-yellow-200 p-4 rounded-lg"},fa={class:"flex"},pa={class:"text-sm text-yellow-700 mt-1"},ba={class:"flex justify-end space-x-4 pt-6 border-t"},Ga=h({__name:"Edit",props:{golongan:{},cabangLomba:{}},setup(D){const u=D,j=[{title:"Dashboard",href:"/admin/dashboard"},{title:"Manajemen Golongan",href:"/admin/golongan"},{title:"Edit Golongan",href:`/admin/golongan/${u.golongan.id_golongan}/edit`}],t=B({kode_golongan:u.golongan.kode_golongan,nama_golongan:u.golongan.nama_golongan,id_cabang:u.golongan.id_cabang.toString(),jenis_kelamin:u.golongan.jenis_kelamin,batas_umur_min:u.golongan.batas_umur_min.toString(),batas_umur_max:u.golongan.batas_umur_max.toString(),kuota_max:u.golongan.kuota_max.toString(),biaya_pendaftaran:u.golongan.biaya_pendaftaran.toString(),nomor_urut_awal:u.golongan.nomor_urut_awal.toString(),nomor_urut_akhir:u.golongan.nomor_urut_akhir.toString(),status:u.golongan.status}),L=G(()=>u.golongan.pendaftaran&&u.golongan.pendaftaran.length>0),q=()=>{t.put(route("admin.golongan.update",u.golongan.id_golongan),{onSuccess:()=>{}})},$=p=>new Date(p).toLocaleDateString("id-ID",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"});return(p,o)=>(i(),x(K,{breadcrumbs:j},{default:n(()=>[e(a(N),{title:"Edit Golongan"}),e(M,{title:`Edit Golongan: ${p.golongan.nama_golongan}`},null,8,["title"]),r("div",H,[e(a(T),null,{default:n(()=>[e(a(z),null,{default:n(()=>[e(a(F),null,{default:n(()=>o[12]||(o[12]=[s("Edit Informasi Golongan")])),_:1,__:[12]}),e(a(c),null,{default:n(()=>o[13]||(o[13]=[s(" Perbarui informasi golongan di bawah ini ")])),_:1,__:[13]})]),_:1}),e(a(J),null,{default:n(()=>{var P,S;return[r("form",{onSubmit:I(q,["prevent"]),class:"space-y-6"},[r("div",O,[o[20]||(o[20]=r("h3",{class:"text-lg font-medium"},"Informasi Dasar",-1)),r("div",Q,[r("div",null,[e(a(f),{for:"kode_golongan"},{default:n(()=>o[14]||(o[14]=[s("Kode Golongan *")])),_:1,__:[14]}),e(a(b),{id:"kode_golongan",modelValue:a(t).kode_golongan,"onUpdate:modelValue":o[0]||(o[0]=l=>a(t).kode_golongan=l),type:"text",required:"",placeholder:"Contoh: TIL-1Pa, TAH-5Pi",class:g({"border-red-500":a(t).errors.kode_golongan})},null,8,["modelValue","class"]),a(t).errors.kode_golongan?(i(),m("p",R,d(a(t).errors.kode_golongan),1)):_("",!0)]),r("div",null,[e(a(f),{for:"status"},{default:n(()=>o[15]||(o[15]=[s("Status *")])),_:1,__:[15]}),e(a(y),{modelValue:a(t).status,"onUpdate:modelValue":o[1]||(o[1]=l=>a(t).status=l),required:""},{default:n(()=>[e(a(V),{class:g({"border-red-500":a(t).errors.status})},{default:n(()=>[e(a(v),{placeholder:"Pilih Status"})]),_:1},8,["class"]),e(a(w),null,{default:n(()=>[e(a(k),{value:"aktif"},{default:n(()=>o[16]||(o[16]=[s("Aktif")])),_:1,__:[16]}),e(a(k),{value:"non_aktif"},{default:n(()=>o[17]||(o[17]=[s("Non Aktif")])),_:1,__:[17]})]),_:1})]),_:1},8,["modelValue"]),a(t).errors.status?(i(),m("p",W,d(a(t).errors.status),1)):_("",!0)])]),r("div",null,[e(a(f),{for:"nama_golongan"},{default:n(()=>o[18]||(o[18]=[s("Nama Golongan *")])),_:1,__:[18]}),e(a(b),{id:"nama_golongan",modelValue:a(t).nama_golongan,"onUpdate:modelValue":o[2]||(o[2]=l=>a(t).nama_golongan=l),type:"text",required:"",placeholder:"Contoh: Tilawah 1 Putra, Tahfidz 5 Juz Putri",class:g({"border-red-500":a(t).errors.nama_golongan})},null,8,["modelValue","class"]),a(t).errors.nama_golongan?(i(),m("p",X,d(a(t).errors.nama_golongan),1)):_("",!0)]),r("div",null,[e(a(f),{for:"id_cabang"},{default:n(()=>o[19]||(o[19]=[s("Cabang Lomba *")])),_:1,__:[19]}),e(a(y),{modelValue:a(t).id_cabang,"onUpdate:modelValue":o[3]||(o[3]=l=>a(t).id_cabang=l),required:""},{default:n(()=>[e(a(V),{class:g({"border-red-500":a(t).errors.id_cabang})},{default:n(()=>[e(a(v),{placeholder:"Pilih Cabang Lomba"})]),_:1},8,["class"]),e(a(w),null,{default:n(()=>[(i(!0),m(E,null,A(p.cabangLomba,l=>(i(),x(a(k),{key:l.id_cabang,value:l.id_cabang.toString()},{default:n(()=>[s(d(l.nama_cabang)+" ("+d(l.kode_cabang)+") ",1)]),_:2},1032,["value"]))),128))]),_:1})]),_:1},8,["modelValue"]),a(t).errors.id_cabang?(i(),m("p",Y,d(a(t).errors.id_cabang),1)):_("",!0)])]),r("div",Z,[o[26]||(o[26]=r("h3",{class:"text-lg font-medium"},"Kriteria Peserta",-1)),r("div",null,[e(a(f),{for:"jenis_kelamin"},{default:n(()=>o[21]||(o[21]=[s("Jenis Kelamin *")])),_:1,__:[21]}),e(a(y),{modelValue:a(t).jenis_kelamin,"onUpdate:modelValue":o[4]||(o[4]=l=>a(t).jenis_kelamin=l),required:""},{default:n(()=>[e(a(V),{class:g({"border-red-500":a(t).errors.jenis_kelamin})},{default:n(()=>[e(a(v),{placeholder:"Pilih Jenis Kelamin"})]),_:1},8,["class"]),e(a(w),null,{default:n(()=>[e(a(k),{value:"L"},{default:n(()=>o[22]||(o[22]=[s("Laki-laki")])),_:1,__:[22]}),e(a(k),{value:"P"},{default:n(()=>o[23]||(o[23]=[s("Perempuan")])),_:1,__:[23]})]),_:1})]),_:1},8,["modelValue"]),a(t).errors.jenis_kelamin?(i(),m("p",aa,d(a(t).errors.jenis_kelamin),1)):_("",!0)]),r("div",oa,[r("div",null,[e(a(f),{for:"batas_umur_min"},{default:n(()=>o[24]||(o[24]=[s("Batas Umur Minimum *")])),_:1,__:[24]}),e(a(b),{id:"batas_umur_min",modelValue:a(t).batas_umur_min,"onUpdate:modelValue":o[5]||(o[5]=l=>a(t).batas_umur_min=l),type:"number",required:"",min:"1",max:"100",placeholder:"Contoh: 7",class:g({"border-red-500":a(t).errors.batas_umur_min})},null,8,["modelValue","class"]),a(t).errors.batas_umur_min?(i(),m("p",ta,d(a(t).errors.batas_umur_min),1)):_("",!0)]),r("div",null,[e(a(f),{for:"batas_umur_max"},{default:n(()=>o[25]||(o[25]=[s("Batas Umur Maksimum *")])),_:1,__:[25]}),e(a(b),{id:"batas_umur_max",modelValue:a(t).batas_umur_max,"onUpdate:modelValue":o[6]||(o[6]=l=>a(t).batas_umur_max=l),type:"number",required:"",min:"1",max:"100",placeholder:"Contoh: 12",class:g({"border-red-500":a(t).errors.batas_umur_max})},null,8,["modelValue","class"]),a(t).errors.batas_umur_max?(i(),m("p",ea,d(a(t).errors.batas_umur_max),1)):_("",!0)])])]),r("div",ra,[o[31]||(o[31]=r("h3",{class:"text-lg font-medium"},"Pengaturan Lomba",-1)),r("div",na,[r("div",null,[e(a(f),{for:"kuota_max"},{default:n(()=>o[27]||(o[27]=[s("Kuota Maksimum *")])),_:1,__:[27]}),e(a(b),{id:"kuota_max",modelValue:a(t).kuota_max,"onUpdate:modelValue":o[7]||(o[7]=l=>a(t).kuota_max=l),type:"number",required:"",min:"1",placeholder:"Contoh: 50",class:g({"border-red-500":a(t).errors.kuota_max})},null,8,["modelValue","class"]),a(t).errors.kuota_max?(i(),m("p",la,d(a(t).errors.kuota_max),1)):_("",!0)]),r("div",null,[e(a(f),{for:"biaya_pendaftaran"},{default:n(()=>o[28]||(o[28]=[s("Biaya Pendaftaran *")])),_:1,__:[28]}),e(a(b),{id:"biaya_pendaftaran",modelValue:a(t).biaya_pendaftaran,"onUpdate:modelValue":o[8]||(o[8]=l=>a(t).biaya_pendaftaran=l),type:"number",required:"",min:"0",step:"1000",placeholder:"Contoh: 150000",class:g({"border-red-500":a(t).errors.biaya_pendaftaran})},null,8,["modelValue","class"]),a(t).errors.biaya_pendaftaran?(i(),m("p",sa,d(a(t).errors.biaya_pendaftaran),1)):_("",!0)])]),r("div",da,[r("div",null,[e(a(f),{for:"nomor_urut_awal"},{default:n(()=>o[29]||(o[29]=[s("Nomor Urut Awal")])),_:1,__:[29]}),e(a(b),{id:"nomor_urut_awal",modelValue:a(t).nomor_urut_awal,"onUpdate:modelValue":o[9]||(o[9]=l=>a(t).nomor_urut_awal=l),type:"number",min:"1",placeholder:"Default: 1",class:g({"border-red-500":a(t).errors.nomor_urut_awal})},null,8,["modelValue","class"]),a(t).errors.nomor_urut_awal?(i(),m("p",ia,d(a(t).errors.nomor_urut_awal),1)):_("",!0)]),r("div",null,[e(a(f),{for:"nomor_urut_akhir"},{default:n(()=>o[30]||(o[30]=[s("Nomor Urut Akhir")])),_:1,__:[30]}),e(a(b),{id:"nomor_urut_akhir",modelValue:a(t).nomor_urut_akhir,"onUpdate:modelValue":o[10]||(o[10]=l=>a(t).nomor_urut_akhir=l),type:"number",min:"1",placeholder:"Default: 999",class:g({"border-red-500":a(t).errors.nomor_urut_akhir})},null,8,["modelValue","class"]),a(t).errors.nomor_urut_akhir?(i(),m("p",ua,d(a(t).errors.nomor_urut_akhir),1)):_("",!0)])])]),r("div",ma,[o[35]||(o[35]=r("h4",{class:"font-medium text-gray-900 mb-2"},"Informasi Saat Ini",-1)),r("div",_a,[r("p",null,[o[32]||(o[32]=r("strong",null,"Dibuat:",-1)),s(" "+d($(p.golongan.created_at)),1)]),r("p",null,[o[33]||(o[33]=r("strong",null,"Diperbarui:",-1)),s(" "+d($(p.golongan.updated_at)),1)]),r("p",null,[o[34]||(o[34]=r("strong",null,"Jumlah Pendaftaran:",-1)),s(" "+d(((P=p.golongan.pendaftaran)==null?void 0:P.length)||0)+" pendaftaran",1)])])]),L.value?(i(),m("div",ga,[r("div",fa,[e(C,{name:"alert-triangle",class:"w-5 h-5 text-yellow-600 mr-2 mt-0.5"}),r("div",null,[o[36]||(o[36]=r("h4",{class:"font-medium text-yellow-800"},"Perhatian",-1)),r("p",pa," Golongan ini memiliki "+d(((S=p.golongan.pendaftaran)==null?void 0:S.length)||0)+" pendaftaran. Perubahan kriteria dapat mempengaruhi peserta yang sudah terdaftar. ",1)])])])):_("",!0),r("div",ba,[e(U,{type:"button",variant:"outline",onClick:o[11]||(o[11]=l=>p.$inertia.visit(p.route("admin.golongan.index")))},{default:n(()=>o[37]||(o[37]=[s(" Batal ")])),_:1,__:[37]}),e(U,{type:"submit",disabled:a(t).processing},{default:n(()=>[a(t).processing?(i(),x(C,{key:0,name:"loader-2",class:"w-4 h-4 mr-2 animate-spin"})):_("",!0),o[38]||(o[38]=s(" Simpan Perubahan "))]),_:1,__:[38]},8,["disabled"])])],32)]}),_:1})]),_:1})])]),_:1}))}});export{Ga as default};
