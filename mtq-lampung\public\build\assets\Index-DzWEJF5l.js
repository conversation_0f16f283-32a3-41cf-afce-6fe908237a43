import{_ as N}from"./AppLayout.vue_vue_type_script_setup_true_lang-BRf8lUiM.js";import{d as T,c as g,o as d,w as s,b as t,a as e,e as i,u as l,g as I,h as p,F as z,m as A,t as o,n as x,i as c,p as F}from"./app-B_pmlBSQ.js";import{_ as V}from"./Heading.vue_vue_type_script_setup_true_lang-is4VBSCS.js";import{_ as m}from"./AppLogoIcon.vue_vue_type_script_setup_true_lang-wzz4PWna.js";import{_ as v,a as h}from"./CardContent.vue_vue_type_script_setup_true_lang-ClvLNBXC.js";import{_ as k}from"./CardDescription.vue_vue_type_script_setup_true_lang-DboywBIK.js";import{_ as w,a as D}from"./CardTitle.vue_vue_type_script_setup_true_lang-5HO6oSoB.js";import{_ as S}from"./index-CMGr3-bt.js";import{_ as u}from"./Icon.vue_vue_type_script_setup_true_lang-BuYzjckr.js";import{_ as f}from"./TextLink.vue_vue_type_script_setup_true_lang-CC2Og0lP.js";import"./useForwardExpose-CO14IhkA.js";import"./RovingFocusGroup-lsWyU4xZ.js";import"./check-DtmHjdxf.js";import"./loader-circle-6d8QrWFr.js";import"./sun-CHIV9RJ8.js";const M={class:"flex items-center justify-between"},E={class:"space-y-6"},K={key:0,class:"text-center py-12"},q={key:1,class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},G={class:"flex items-center justify-between"},J={class:"space-y-4"},R={class:"grid grid-cols-2 gap-4 text-sm"},H={class:"font-mono"},O={class:"font-mono"},Q={class:"font-medium text-green-600"},U={key:0,class:"p-3 bg-gray-50 rounded-lg"},W={class:"flex items-center justify-between"},X={key:0,class:"text-xs text-gray-600 mt-1"},Y={class:"p-3 bg-gray-50 rounded-lg"},Z={class:"flex items-center justify-between mb-2"},aa={class:"text-xs text-gray-600"},ta={class:"w-full bg-gray-200 rounded-full h-2"},ea={class:"flex space-x-2 pt-2"},sa={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},na={class:"text-center"},ra={class:"text-center"},la={class:"text-center"},wa=T({__name:"Index",props:{pendaftaran:{}},setup(oa){function $(n){return{draft:"bg-gray-100 text-gray-800",submitted:"bg-blue-100 text-blue-800",payment_pending:"bg-yellow-100 text-yellow-800",paid:"bg-green-100 text-green-800",verified:"bg-indigo-100 text-indigo-800",approved:"bg-green-100 text-green-800",rejected:"bg-red-100 text-red-800"}[n]||"bg-gray-100 text-gray-800"}function P(n){return{draft:"Draft",submitted:"Disubmit",payment_pending:"Menunggu Pembayaran",paid:"Sudah Dibayar",verified:"Terverifikasi",approved:"Disetujui",rejected:"Ditolak"}[n]||n}function j(n){return{pending:"bg-yellow-100 text-yellow-800",paid:"bg-green-100 text-green-800",failed:"bg-red-100 text-red-800",expired:"bg-gray-100 text-gray-800"}[n]||"bg-gray-100 text-gray-800"}function B(n){return{pending:"Menunggu",paid:"Lunas",failed:"Gagal",expired:"Kedaluwarsa"}[n]||n}function L(n){return new Intl.NumberFormat("id-ID",{style:"currency",currency:"IDR",minimumFractionDigits:0}).format(n)}function y(n){return new Date(n).toLocaleDateString("id-ID",{year:"numeric",month:"long",day:"numeric"})}function _(n){const a=["foto","ktp","kartu_keluarga"];return{completed:a.filter(C=>n.some(b=>b.jenis_dokumen===C&&b.status_verifikasi==="approved")).length,total:a.length}}return(n,a)=>(d(),g(N,null,{default:s(()=>[t("div",M,[e(V,{title:"Pendaftaran Lomba Saya"}),e(m,{"as-child":""},{default:s(()=>[e(f,{href:n.route("peserta.pendaftaran.create")},{default:s(()=>[e(u,{name:"plus",class:"w-4 h-4 mr-2"}),a[0]||(a[0]=i(" Daftar Lomba Baru "))]),_:1,__:[0]},8,["href"])]),_:1})]),e(l(I),{title:"Pendaftaran Lomba Saya"}),t("div",E,[n.pendaftaran.length===0?(d(),p("div",K,[e(u,{name:"clipboard-list",class:"h-12 w-12 text-gray-400 mx-auto mb-4"}),a[2]||(a[2]=t("h3",{class:"text-lg font-medium text-gray-900 mb-2"},"Belum Ada Pendaftaran",-1)),a[3]||(a[3]=t("p",{class:"text-gray-600 mb-4"},"Anda belum mendaftar untuk lomba apapun. Mulai daftar sekarang!",-1)),e(m,{"as-child":""},{default:s(()=>[e(f,{href:n.route("peserta.pendaftaran.create"),class:"text-white"},{default:s(()=>[e(u,{name:"plus",class:"w-8 h-6 mr-2"}),a[1]||(a[1]=i(" Daftar Lomba Baru "))]),_:1,__:[1]},8,["href"])]),_:1})])):(d(),p("div",q,[(d(!0),p(z,null,A(n.pendaftaran,r=>(d(),g(l(v),{key:r.id_pendaftaran,class:"hover:shadow-lg transition-shadow"},{default:s(()=>[e(l(w),null,{default:s(()=>[t("div",G,[e(l(D),{class:"text-lg"},{default:s(()=>[i(o(r.golongan.nama_golongan),1)]),_:2},1024),e(l(S),{class:x($(r.status_pendaftaran))},{default:s(()=>[i(o(P(r.status_pendaftaran)),1)]),_:2},1032,["class"])]),e(l(k),null,{default:s(()=>[i(o(r.golongan.cabang_lomba.nama_cabang),1)]),_:2},1024)]),_:2},1024),e(l(h),null,{default:s(()=>[t("div",J,[t("div",R,[t("div",null,[a[4]||(a[4]=t("p",{class:"font-medium text-gray-500"},"No. Pendaftaran",-1)),t("p",H,o(r.nomor_pendaftaran),1)]),t("div",null,[a[5]||(a[5]=t("p",{class:"font-medium text-gray-500"},"No. Peserta",-1)),t("p",O,o(r.nomor_peserta),1)]),t("div",null,[a[6]||(a[6]=t("p",{class:"font-medium text-gray-500"},"Tanggal Daftar",-1)),t("p",null,o(y(r.tanggal_daftar)),1)]),t("div",null,[a[7]||(a[7]=t("p",{class:"font-medium text-gray-500"},"Biaya",-1)),t("p",Q,o(L(r.golongan.biaya_pendaftaran)),1)])]),r.pembayaran?(d(),p("div",U,[t("div",W,[a[8]||(a[8]=t("span",{class:"text-sm font-medium"},"Status Pembayaran",-1)),e(l(S),{class:x(j(r.pembayaran.status_pembayaran))},{default:s(()=>[i(o(B(r.pembayaran.status_pembayaran)),1)]),_:2},1032,["class"])]),r.pembayaran.tanggal_bayar?(d(),p("div",X," Dibayar: "+o(y(r.pembayaran.tanggal_bayar)),1)):c("",!0)])):c("",!0),t("div",Y,[t("div",Z,[a[9]||(a[9]=t("span",{class:"text-sm font-medium"},"Dokumen",-1)),t("span",aa,o(_(r.dokumen_peserta).completed)+"/"+o(_(r.dokumen_peserta).total),1)]),t("div",ta,[t("div",{class:"bg-blue-600 h-2 rounded-full transition-all",style:F({width:`${_(r.dokumen_peserta).completed/_(r.dokumen_peserta).total*100}%`})},null,4)])]),t("div",ea,[e(m,{"as-child":"",size:"sm",class:"flex-1"},{default:s(()=>[e(f,{href:n.route("peserta.pendaftaran.show",r.id_pendaftaran)},{default:s(()=>[e(u,{name:"eye",class:"w-4 h-4 mr-2"}),a[10]||(a[10]=i(" Detail "))]),_:2,__:[10]},1032,["href"])]),_:2},1024),r.status_pendaftaran==="draft"||r.status_pendaftaran==="payment_pending"?(d(),g(m,{key:0,"as-child":"",size:"sm",variant:"outline",class:"flex-1"},{default:s(()=>[e(f,{href:n.route("peserta.pendaftaran.edit",r.id_pendaftaran)},{default:s(()=>[e(u,{name:"edit",class:"w-4 h-4 mr-2"}),a[11]||(a[11]=i(" Edit "))]),_:2,__:[11]},1032,["href"])]),_:2},1024)):c("",!0)])])]),_:2},1024)]),_:2},1024))),128))])),e(l(v),null,{default:s(()=>[e(l(w),null,{default:s(()=>[e(l(D),null,{default:s(()=>a[12]||(a[12]=[i("Aksi Cepat")])),_:1,__:[12]}),e(l(k),null,{default:s(()=>a[13]||(a[13]=[i("Tindakan yang mungkin Anda perlukan")])),_:1,__:[13]})]),_:1}),e(l(h),null,{default:s(()=>[t("div",sa,[e(m,{"as-child":"",variant:"outline",class:"h-auto p-4"},{default:s(()=>[e(f,{href:n.route("competition.index")},{default:s(()=>[t("div",na,[e(u,{name:"search",class:"h-6 w-6 mx-auto mb-2"}),a[14]||(a[14]=t("div",{class:"font-medium"},"Jelajahi Lomba",-1)),a[15]||(a[15]=t("div",{class:"text-xs text-gray-500"},"Lihat semua cabang lomba",-1))])]),_:1},8,["href"])]),_:1}),e(m,{"as-child":"",variant:"outline",class:"h-auto p-4"},{default:s(()=>[e(f,{href:n.route("peserta.dashboard")},{default:s(()=>[t("div",ra,[e(u,{name:"user",class:"h-6 w-6 mx-auto mb-2"}),a[16]||(a[16]=t("div",{class:"font-medium"},"Profil Saya",-1)),a[17]||(a[17]=t("div",{class:"text-xs text-gray-500"},"Kelola data pribadi",-1))])]),_:1},8,["href"])]),_:1}),e(m,{"as-child":"",class:"h-auto p-4"},{default:s(()=>[e(f,{href:n.route("peserta.pendaftaran.create")},{default:s(()=>[t("div",la,[e(u,{name:"plus",class:"h-6 w-6 mx-auto mb-2"}),a[18]||(a[18]=t("div",{class:"font-medium"},"Daftar Baru",-1)),a[19]||(a[19]=t("div",{class:"text-xs"},"Daftar lomba lainnya",-1))])]),_:1},8,["href"])]),_:1})])]),_:1})]),_:1})])]),_:1}))}});export{wa as default};
