import{d as D,r as N,W as p,c as j,o as f,w as i,a as t,b as a,u as r,g as z,e as l,t as o,h as x,F as I,m as B,p as U}from"./app-B_pmlBSQ.js";import{a as F,b as L,c as P,d as E,_ as K}from"./AppLayout.vue_vue_type_script_setup_true_lang-BRf8lUiM.js";import{_ as T}from"./Heading.vue_vue_type_script_setup_true_lang-is4VBSCS.js";import{_ as u}from"./AppLogoIcon.vue_vue_type_script_setup_true_lang-wzz4PWna.js";import{_ as h,a as b}from"./CardContent.vue_vue_type_script_setup_true_lang-ClvLNBXC.js";import{_ as W}from"./index-CMGr3-bt.js";import{_ as H}from"./Input.vue_vue_type_script_setup_true_lang-DB1-RsTn.js";import{_ as y}from"./Label.vue_vue_type_script_setup_true_lang-rj_fhE7R.js";import{_ as q,a as G,b as J,c as O,d as g}from"./SelectValue.vue_vue_type_script_setup_true_lang-IzlIronZ.js";import{_ as d}from"./Icon.vue_vue_type_script_setup_true_lang-BuYzjckr.js";import{l as Q,_ as R}from"./lodash-Cvgo55ys.js";import"./useForwardExpose-CO14IhkA.js";import"./RovingFocusGroup-lsWyU4xZ.js";import"./check-DtmHjdxf.js";import"./useFormControl-mZRLifGx.js";import"./loader-circle-6d8QrWFr.js";import"./sun-CHIV9RJ8.js";import"./TextLink.vue_vue_type_script_setup_true_lang-CC2Og0lP.js";const X={class:"space-y-6"},Y={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},Z={class:"flex items-end"},tt={class:"flex justify-between items-center"},at={class:"text-sm text-gray-600"},st={class:"overflow-x-auto"},et={class:"w-full"},it={class:"bg-white divide-y divide-gray-200"},rt={class:"px-6 py-4 whitespace-nowrap"},nt={class:"text-sm font-medium text-gray-900"},ot={class:"text-sm text-gray-500"},lt={class:"px-6 py-4"},mt={class:"text-sm text-gray-900 max-w-xs truncate"},dt={class:"px-6 py-4 whitespace-nowrap"},ut={class:"text-sm text-gray-900"},ct={class:"px-6 py-4 whitespace-nowrap"},_t={class:"flex items-center"},pt={class:"text-sm text-gray-900"},ft={class:"ml-2 w-16 bg-gray-200 rounded-full h-2"},gt={class:"px-6 py-4 whitespace-nowrap"},xt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500"},ht={class:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium space-x-2"},Lt=D({__name:"Index",props:{mimbar:{},filters:{}},setup(k){const v=[{title:"Dashboard",href:"/admin/dashboard"},{title:"Manajemen Mimbar",href:"/admin/mimbar"}],m=N({...k.filters}),c=Q.debounce(()=>{p.get(route("admin.mimbar.index"),m,{preserveState:!0,replace:!0})},300),w=()=>{m.search="",m.status="",c()},$=s=>{p.post(route("admin.mimbar.toggle-status",s.id_mimbar),{},{preserveScroll:!0})},S=s=>{confirm(`Apakah Anda yakin ingin menghapus mimbar ${s.nama_mimbar}?`)&&p.delete(route("admin.mimbar.destroy",s.id_mimbar))},C=s=>s.kapasitas===0?0:Math.round((s.pendaftaran_count||0)/s.kapasitas*100),M=s=>({aktif:"default",non_aktif:"secondary"})[s]||"secondary",V=s=>({aktif:"Aktif",non_aktif:"Non Aktif"})[s]||s,A=s=>new Date(s).toLocaleDateString("id-ID",{year:"numeric",month:"short",day:"numeric"});return(s,e)=>(f(),j(K,{breadcrumbs:v},{default:i(()=>[t(r(z),{title:"Manajemen Mimbar"}),t(T,{title:"Manajemen Mimbar"}),a("div",X,[t(r(h),null,{default:i(()=>[t(r(b),{class:"p-6"},{default:i(()=>[a("div",Y,[a("div",null,[t(r(y),{for:"search"},{default:i(()=>e[3]||(e[3]=[l("Pencarian")])),_:1,__:[3]}),t(r(H),{id:"search",modelValue:m.search,"onUpdate:modelValue":e[0]||(e[0]=n=>m.search=n),placeholder:"Nama mimbar, kode...",onInput:r(c)},null,8,["modelValue","onInput"])]),a("div",null,[t(r(y),{for:"status"},{default:i(()=>e[4]||(e[4]=[l("Status")])),_:1,__:[4]}),t(r(q),{modelValue:m.status,"onUpdate:modelValue":[e[1]||(e[1]=n=>m.status=n),r(c)]},{default:i(()=>[t(r(G),null,{default:i(()=>[t(r(J),{placeholder:"Semua Status"})]),_:1}),t(r(O),null,{default:i(()=>[t(r(g),{value:"all"},{default:i(()=>e[5]||(e[5]=[l("Semua Status")])),_:1,__:[5]}),t(r(g),{value:"aktif"},{default:i(()=>e[6]||(e[6]=[l("Aktif")])),_:1,__:[6]}),t(r(g),{value:"non_aktif"},{default:i(()=>e[7]||(e[7]=[l("Non Aktif")])),_:1,__:[7]})]),_:1})]),_:1},8,["modelValue","onUpdate:modelValue"])]),a("div",Z,[t(u,{onClick:w,variant:"outline",class:"w-full"},{default:i(()=>[t(d,{name:"x",class:"w-4 h-4 mr-2"}),e[8]||(e[8]=l(" Clear "))]),_:1,__:[8]})])])]),_:1})]),_:1}),a("div",tt,[a("div",at," Menampilkan "+o(s.mimbar.from)+" - "+o(s.mimbar.to)+" dari "+o(s.mimbar.total)+" mimbar ",1),t(u,{onClick:e[2]||(e[2]=n=>s.$inertia.visit(s.route("admin.mimbar.create")))},{default:i(()=>[t(d,{name:"plus",class:"w-4 h-4 mr-2"}),e[9]||(e[9]=l(" Tambah Mimbar "))]),_:1,__:[9]})]),t(r(h),null,{default:i(()=>[t(r(b),{class:"p-0"},{default:i(()=>[a("div",st,[a("table",et,[e[11]||(e[11]=a("thead",{class:"bg-gray-50 border-b"},[a("tr",null,[a("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Mimbar "),a("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Keterangan "),a("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Kapasitas "),a("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Penggunaan "),a("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Status "),a("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Dibuat "),a("th",{class:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"}," Aksi ")])],-1)),a("tbody",it,[(f(!0),x(I,null,B(s.mimbar.data,n=>(f(),x("tr",{key:n.id_mimbar,class:"hover:bg-gray-50"},[a("td",rt,[a("div",null,[a("div",nt,o(n.nama_mimbar),1),a("div",ot,o(n.kode_mimbar),1)])]),a("td",lt,[a("div",mt,o(n.keterangan||"-"),1)]),a("td",dt,[a("div",ut,o(n.kapasitas)+" orang",1)]),a("td",ct,[a("div",_t,[a("div",pt,o(n.pendaftaran_count||0)+" / "+o(n.kapasitas),1),a("div",ft,[a("div",{class:"bg-blue-600 h-2 rounded-full",style:U({width:`${C(n)}%`})},null,4)])])]),a("td",gt,[t(r(W),{variant:M(n.status)},{default:i(()=>[l(o(V(n.status)),1)]),_:2},1032,["variant"])]),a("td",xt,o(A(n.created_at)),1),a("td",ht,[t(u,{variant:"ghost",size:"sm",onClick:_=>s.$inertia.visit(s.route("admin.mimbar.show",n.id_mimbar))},{default:i(()=>[t(d,{name:"eye",class:"w-4 h-4"})]),_:2},1032,["onClick"]),t(u,{variant:"ghost",size:"sm",onClick:_=>s.$inertia.visit(s.route("admin.mimbar.edit",n.id_mimbar))},{default:i(()=>[t(d,{name:"edit",class:"w-4 h-4"})]),_:2},1032,["onClick"]),t(u,{variant:"ghost",size:"sm",onClick:_=>$(n)},{default:i(()=>[t(d,{name:n.status==="aktif"?"pause":"play",class:"w-4 h-4"},null,8,["name"])]),_:2},1032,["onClick"]),t(r(F),null,{default:i(()=>[t(r(L),{"as-child":""},{default:i(()=>[t(u,{variant:"ghost",size:"sm"},{default:i(()=>[t(d,{name:"more-vertical",class:"w-4 h-4"})]),_:1})]),_:1}),t(r(P),{align:"end"},{default:i(()=>[t(r(E),{onClick:_=>S(n),class:"text-red-600"},{default:i(()=>[t(d,{name:"trash",class:"w-4 h-4 mr-2"}),e[10]||(e[10]=l(" Hapus "))]),_:2,__:[10]},1032,["onClick"])]),_:2},1024)]),_:2},1024)])]))),128))])])])]),_:1})]),_:1}),t(R,{links:s.mimbar.links},null,8,["links"])])]),_:1}))}});export{Lt as default};
