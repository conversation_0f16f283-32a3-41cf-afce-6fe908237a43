<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue'
import { Head } from '@inertiajs/vue3'
import Heading from '@/components/Heading.vue'
import Button from '@/components/ui/button/Button.vue'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import Icon from '@/components/Icon.vue'
import TextLink from '@/components/TextLink.vue'

interface CabangLomba {
    id_cabang: number
    nama_cabang: string
    deskripsi: string
}

interface Golongan {
    id_golongan: number
    nama_golongan: string
    jenis_kelamin: string
    batas_umur_min: number
    batas_umur_max: number
    kuota_max: number
    biaya_pendaftaran: number
    status: string
    cabang_lomba: CabangLomba
}

interface Peserta {
    id_peserta: number
    nama_lengkap: string
    jenis_kelamin: string
    tanggal_lahir: string
    status_peserta: string
}

const props = defineProps<{
    golongan: Golongan
    canRegister: boolean
    peserta: Peserta | null
}>()

function formatCurrency(amount: number): string {
    return new Intl.NumberFormat('id-ID', {
        style: 'currency',
        currency: 'IDR',
        minimumFractionDigits: 0
    }).format(amount)
}

function calculateAge(birthDate: string): number {
    const today = new Date()
    const birth = new Date(birthDate)
    let age = today.getFullYear() - birth.getFullYear()
    const monthDiff = today.getMonth() - birth.getMonth()

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
        age--
    }

    return age
}

function isEligible(): boolean {
    if (!props.peserta) return false

    const age = calculateAge(props.peserta.tanggal_lahir)
    const genderMatch = props.peserta.jenis_kelamin === props.golongan.jenis_kelamin
    const ageMatch = age >= props.golongan.batas_umur_min && age <= props.golongan.batas_umur_max

    return genderMatch && ageMatch
}

function getEligibilityMessage(): string {
    if (!props.peserta) return ''

    const age = calculateAge(props.peserta.tanggal_lahir)
    const genderMatch = props.peserta.jenis_kelamin === props.golongan.jenis_kelamin
    const ageMatch = age >= props.golongan.batas_umur_min && age <= props.golongan.batas_umur_max

    if (!genderMatch) {
        return `Golongan ini khusus untuk ${props.golongan.jenis_kelamin === 'L' ? 'putra' : 'putri'}`
    }

    if (!ageMatch) {
        return `Usia Anda (${age} tahun) tidak sesuai dengan persyaratan golongan (${props.golongan.batas_umur_min}-${props.golongan.batas_umur_max} tahun)`
    }

    return ''
}
</script>

<template>
    <AppLayout>
        <template #header>
            <div class="flex items-center space-x-4">
                <Button
                    as="link"
                    :href="route('competition.index')"
                    variant="ghost"
                    size="sm"
                >
                    <Icon name="arrow-left" class="w-4 h-4 mr-2" />
                    Kembali
                </Button>
                <Heading>{{ golongan.nama_golongan }}</Heading>
            </div>
        </template>

        <Head :title="golongan.nama_golongan" />

        <div class="space-y-6">
            <!-- Competition Details -->
            <Card>
                <CardHeader>
                    <CardTitle class="flex items-center justify-between">
                        {{ golongan.nama_golongan }}
                        <Badge :class="golongan.jenis_kelamin === 'L' ? 'bg-blue-100 text-blue-800' : 'bg-pink-100 text-pink-800'">
                            {{ golongan.jenis_kelamin === 'L' ? 'Putra' : 'Putri' }}
                        </Badge>
                    </CardTitle>
                    <CardDescription>
                        Cabang Lomba: {{ golongan.cabang_lomba.nama_cabang }}
                    </CardDescription>
                </CardHeader>
                <CardContent>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="space-y-4">
                            <div class="flex items-center space-x-3">
                                <Icon name="calendar" class="h-5 w-5 text-gray-500" />
                                <div>
                                    <p class="font-medium">Batas Usia</p>
                                    <p class="text-sm text-gray-600">{{ golongan.batas_umur_min }} - {{ golongan.batas_umur_max }} tahun</p>
                                </div>
                            </div>

                            <div class="flex items-center space-x-3">
                                <Icon name="users" class="h-5 w-5 text-gray-500" />
                                <div>
                                    <p class="font-medium">Kuota Peserta</p>
                                    <p class="text-sm text-gray-600">Maksimal {{ golongan.kuota_max }} peserta</p>
                                </div>
                            </div>
                        </div>

                        <div class="space-y-4">
                            <div class="flex items-center space-x-3">
                                <Icon name="credit-card" class="h-5 w-5 text-gray-500" />
                                <div>
                                    <p class="font-medium">Biaya Pendaftaran</p>
                                    <p class="text-lg font-bold text-green-600">{{ formatCurrency(golongan.biaya_pendaftaran) }}</p>
                                </div>
                            </div>

                            <div class="flex items-center space-x-3">
                                <Icon name="check-circle" class="h-5 w-5 text-gray-500" />
                                <div>
                                    <p class="font-medium">Status</p>
                                    <Badge variant="secondary">{{ golongan.status === 'aktif' ? 'Pendaftaran Dibuka' : 'Pendaftaran Ditutup' }}</Badge>
                                </div>
                            </div>
                        </div>
                    </div>
                </CardContent>
            </Card>

            <!-- Registration Section -->
            <Card>
                <CardHeader>
                    <CardTitle>Pendaftaran</CardTitle>
                    <CardDescription>
                        Daftar untuk mengikuti lomba {{ golongan.nama_golongan }}
                    </CardDescription>
                </CardHeader>
                <CardContent>
                    <!-- Not Logged In -->
                    <div v-if="!$page.props.auth.user" class="text-center py-8">
                        <Icon name="user-x" class="h-12 w-12 text-gray-400 mx-auto mb-4" />
                        <h3 class="text-lg font-medium text-gray-900 mb-2">Silakan Masuk Terlebih Dahulu</h3>
                        <p class="text-gray-600 mb-4">Anda perlu masuk ke akun untuk mendaftar lomba.</p>
                        <div class="space-x-4">
                            <Button as-child>
                                <TextLink :href="route('login')">
                                    Masuk
                                </TextLink>
                            </Button>
                            <Button as-child>
                                <TextLink :href="route('register')" variant="outline">
                                    Daftar Akun Baru
                                </TextLink>
                            </Button>
                        </div>
                    </div>

                    <!-- Not Peserta Role -->
                    <div v-else-if="$page.props.auth.user.role !== 'peserta'" class="text-center py-8">
                        <Icon name="shield-x" class="h-12 w-12 text-gray-400 mx-auto mb-4" />
                        <h3 class="text-lg font-medium text-gray-900 mb-2">Akses Terbatas</h3>
                        <p class="text-gray-600">Hanya peserta yang dapat mendaftar lomba.</p>
                    </div>

                    <!-- Peserta Not Approved -->
                    <div v-else-if="!canRegister" class="text-center py-8">
                        <Icon name="clock" class="h-12 w-12 text-gray-400 mx-auto mb-4" />
                        <h3 class="text-lg font-medium text-gray-900 mb-2">Profil Belum Disetujui</h3>
                        <p class="text-gray-600 mb-4">Profil peserta Anda masih dalam proses verifikasi. Silakan lengkapi data dan tunggu persetujuan admin.</p>
                        <Button as-child>
                            <TextLink :href="route('peserta.dashboard')" variant="outline">
                                Lihat Status Profil
                            </TextLink>
                        </Button>
                    </div>

                    <!-- Can Register but Not Eligible -->
                    <div v-else-if="!isEligible()" class="space-y-4">
                        <Alert>
                            <Icon name="alert-triangle" class="h-4 w-4" />
                            <AlertDescription>
                                {{ getEligibilityMessage() }}
                            </AlertDescription>
                        </Alert>
                        <div class="text-center">
                            <Button as-child>
                                <TextLink :href="route('competition.index')" variant="outline">
                                    Lihat Golongan Lain
                                </TextLink>
                            </Button>
                        </div>
                    </div>

                    <!-- Can Register and Eligible -->
                    <div v-else class="space-y-4">
                        <Alert>
                            <Icon name="check-circle" class="h-4 w-4" />
                            <AlertDescription>
                                Anda memenuhi syarat untuk mendaftar di golongan ini.
                            </AlertDescription>
                        </Alert>

                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h4 class="font-medium mb-2">Data Peserta:</h4>
                            <div class="text-sm space-y-1">
                                <p><span class="font-medium">Nama:</span> {{ peserta?.nama_lengkap }}</p>
                                <p><span class="font-medium">Jenis Kelamin:</span> {{ peserta?.jenis_kelamin === 'L' ? 'Laki-laki' : 'Perempuan' }}</p>
                                <p><span class="font-medium">Usia:</span> {{ calculateAge(peserta?.tanggal_lahir || '') }} tahun</p>
                            </div>
                        </div>

                        <div class="text-center">
                            <Button
                                as-child
                                size="lg"
                                class="w-full md:w-auto"
                            >
                                <TextLink :href="route('peserta.pendaftaran.create', { golongan: golongan.id_golongan })">
                                <Icon name="plus" class="w-4 h-4 mr-2" />
                                Daftar Sekarang
                                </TextLink>
                            </Button>
                        </div>
                    </div>
                </CardContent>
            </Card>

            <!-- Competition Description -->
            <Card>
                <CardHeader>
                    <CardTitle>Tentang {{ golongan.cabang_lomba.nama_cabang }}</CardTitle>
                </CardHeader>
                <CardContent>
                    <p class="text-gray-700">{{ golongan.cabang_lomba.deskripsi }}</p>
                </CardContent>
            </Card>
        </div>
    </AppLayout>
</template>
