import{_ as S}from"./AppLayout.vue_vue_type_script_setup_true_lang-BRf8lUiM.js";import{d as L,k,c as g,o as u,w as s,a,b as t,u as l,g as C,i as w,t as i,n as h,e as r,h as c,F as T,m as A}from"./app-B_pmlBSQ.js";import{_ as B}from"./Heading.vue_vue_type_script_setup_true_lang-is4VBSCS.js";import{_ as p}from"./AppLogoIcon.vue_vue_type_script_setup_true_lang-wzz4PWna.js";import{_ as f,a as m}from"./CardContent.vue_vue_type_script_setup_true_lang-ClvLNBXC.js";import{_ as N}from"./CardDescription.vue_vue_type_script_setup_true_lang-DboywBIK.js";import{_ as V,a as K}from"./CardTitle.vue_vue_type_script_setup_true_lang-5HO6oSoB.js";import{_ as y}from"./index-CMGr3-bt.js";import{_ as M,a as z}from"./index-Cae_Ab9-.js";import{_ as o}from"./Icon.vue_vue_type_script_setup_true_lang-BuYzjckr.js";import{_}from"./TextLink.vue_vue_type_script_setup_true_lang-CC2Og0lP.js";import"./useForwardExpose-CO14IhkA.js";import"./RovingFocusGroup-lsWyU4xZ.js";import"./check-DtmHjdxf.js";import"./loader-circle-6d8QrWFr.js";import"./sun-CHIV9RJ8.js";const F={class:"space-y-6"},J={class:"flex items-center space-x-4"},E={class:"flex-shrink-0 h-12 w-12"},I={class:"h-12 w-12 rounded-full bg-blue-500 flex items-center justify-center"},Q={class:"text-lg font-medium text-white"},R={class:"flex-1"},U={class:"text-xl font-semibold text-gray-900"},q={class:"text-right"},G={class:"flex items-center justify-between"},H={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},O={class:"flex items-center space-x-2"},W={class:"text-2xl font-bold"},X={class:"flex items-center space-x-2"},Y={class:"text-2xl font-bold"},Z={class:"flex items-center space-x-2"},tt={class:"text-2xl font-bold"},et={class:"flex items-center space-x-2"},at={class:"text-2xl font-bold"},st={class:"grid grid-cols-1 md:grid-cols-3 gap-6"},lt={class:"flex items-center space-x-4"},nt={class:"flex-shrink-0"},rt={class:"mt-4"},it={class:"flex items-center space-x-4"},ot={class:"flex-shrink-0"},dt={class:"mt-4"},ut={class:"flex items-center space-x-4"},ft={class:"flex-shrink-0"},mt={class:"mt-4"},pt={class:"flex items-center justify-between"},ct={key:0,class:"text-center py-8"},_t={key:1,class:"space-y-4"},gt={class:"flex-1"},xt={class:"font-medium"},bt={class:"text-sm text-gray-600"},ht={class:"text-xs text-gray-500 mt-1"},yt={class:"text-right"},vt={key:0,class:"mt-1"},zt=L({__name:"Dashboard",props:{peserta:{},pendaftaran:{},stats:{}},setup(P){const x=P,b=k(()=>{switch(x.peserta.status_peserta){case"draft":return{text:"Draft",color:"bg-gray-100 text-gray-800",icon:"edit"};case"submitted":return{text:"Disubmit",color:"bg-blue-100 text-blue-800",icon:"clock"};case"verified":return{text:"Terverifikasi",color:"bg-indigo-100 text-indigo-800",icon:"check"};case"approved":return{text:"Disetujui",color:"bg-green-100 text-green-800",icon:"check-circle"};case"rejected":return{text:"Ditolak",color:"bg-red-100 text-red-800",icon:"x-circle"};default:return{text:x.peserta.status_peserta,color:"bg-gray-100 text-gray-800",icon:"help-circle"}}}),v=k(()=>x.peserta.status_peserta==="approved");function D(n){return n.split(" ").map(e=>e.charAt(0)).join("").toUpperCase().slice(0,2)}function j(n){return{draft:"bg-gray-100 text-gray-800",submitted:"bg-blue-100 text-blue-800",payment_pending:"bg-yellow-100 text-yellow-800",paid:"bg-green-100 text-green-800",verified:"bg-indigo-100 text-indigo-800",approved:"bg-green-100 text-green-800",rejected:"bg-red-100 text-red-800"}[n]||"bg-gray-100 text-gray-800"}function $(n){return{draft:"Draft",submitted:"Disubmit",payment_pending:"Menunggu Pembayaran",paid:"Sudah Dibayar",verified:"Terverifikasi",approved:"Disetujui",rejected:"Ditolak"}[n]||n}return(n,e)=>(u(),g(S,null,{header:s(()=>[a(B,null,{default:s(()=>e[0]||(e[0]=[r("Dashboard Peserta")])),_:1,__:[0]})]),default:s(()=>[a(l(C),{title:"Dashboard Peserta"}),t("div",F,[a(l(f),null,{default:s(()=>[a(l(m),{class:"p-6"},{default:s(()=>[t("div",J,[t("div",E,[t("div",I,[t("span",Q,i(D(n.peserta.nama_lengkap)),1)])]),t("div",R,[t("h2",U," Selamat datang, "+i(n.peserta.nama_lengkap)+"! ",1),e[1]||(e[1]=t("p",{class:"text-gray-600"}," Kelola profil dan pendaftaran lomba MTQ Anda di sini. ",-1))]),t("div",q,[a(l(y),{class:h(b.value.color)},{default:s(()=>[a(o,{name:b.value.icon,class:"w-3 h-3 mr-1"},null,8,["name"]),r(" "+i(b.value.text),1)]),_:1},8,["class"]),e[2]||(e[2]=t("p",{class:"text-xs text-gray-500 mt-1"},"Status Profil",-1))])])]),_:1})]),_:1}),v.value?w("",!0):(u(),g(l(M),{key:0,class:"border-yellow-200 bg-yellow-50"},{default:s(()=>[a(o,{name:"alert-triangle",class:"h-4 w-4"}),a(l(z),null,{default:s(()=>[t("div",G,[e[4]||(e[4]=t("span",null," Profil Anda belum disetujui. Silakan lengkapi data dan tunggu persetujuan admin untuk dapat mendaftar lomba. ",-1)),a(p,{"as-child":""},{default:s(()=>[a(_,{href:n.route("peserta.profile.show")},{default:s(()=>e[3]||(e[3]=[r(" Lengkapi Profil ")])),_:1,__:[3]},8,["href"])]),_:1})])]),_:1})]),_:1})),t("div",H,[a(l(f),null,{default:s(()=>[a(l(m),{class:"p-6"},{default:s(()=>[t("div",O,[a(o,{name:"clipboard-list",class:"h-8 w-8 text-blue-600"}),t("div",null,[t("p",W,i(n.stats.total_pendaftaran),1),e[5]||(e[5]=t("p",{class:"text-sm text-gray-600"},"Total Pendaftaran",-1))])])]),_:1})]),_:1}),a(l(f),null,{default:s(()=>[a(l(m),{class:"p-6"},{default:s(()=>[t("div",X,[a(o,{name:"check-circle",class:"h-8 w-8 text-green-600"}),t("div",null,[t("p",Y,i(n.stats.pendaftaran_approved),1),e[6]||(e[6]=t("p",{class:"text-sm text-gray-600"},"Disetujui",-1))])])]),_:1})]),_:1}),a(l(f),null,{default:s(()=>[a(l(m),{class:"p-6"},{default:s(()=>[t("div",Z,[a(o,{name:"clock",class:"h-8 w-8 text-yellow-600"}),t("div",null,[t("p",tt,i(n.stats.pendaftaran_pending),1),e[7]||(e[7]=t("p",{class:"text-sm text-gray-600"},"Menunggu",-1))])])]),_:1})]),_:1}),a(l(f),null,{default:s(()=>[a(l(m),{class:"p-6"},{default:s(()=>[t("div",et,[a(o,{name:"file-text",class:"h-8 w-8 text-orange-600"}),t("div",null,[t("p",at,i(n.stats.dokumen_pending),1),e[8]||(e[8]=t("p",{class:"text-sm text-gray-600"},"Dokumen Pending",-1))])])]),_:1})]),_:1})]),t("div",st,[a(l(f),{class:"hover:shadow-lg transition-shadow"},{default:s(()=>[a(l(m),{class:"p-6"},{default:s(()=>[t("div",lt,[t("div",nt,[a(o,{name:"user",class:"h-8 w-8 text-blue-600"})]),e[9]||(e[9]=t("div",{class:"flex-1"},[t("h3",{class:"text-lg font-medium text-gray-900"},"Profil Saya"),t("p",{class:"text-sm text-gray-500"},"Kelola data pribadi")],-1))]),t("div",rt,[a(p,{"as-child":""},{default:s(()=>[a(_,{href:n.route("peserta.profile.show")},{default:s(()=>e[10]||(e[10]=[r(" Kelola Profil ")])),_:1,__:[10]},8,["href"])]),_:1})])]),_:1})]),_:1}),a(l(f),{class:"hover:shadow-lg transition-shadow"},{default:s(()=>[a(l(m),{class:"p-6"},{default:s(()=>[t("div",it,[t("div",ot,[a(o,{name:"clipboard-list",class:"h-8 w-8 text-green-600"})]),e[11]||(e[11]=t("div",{class:"flex-1"},[t("h3",{class:"text-lg font-medium text-gray-900"},"Pendaftaran Saya"),t("p",{class:"text-sm text-gray-500"},"Lihat status pendaftaran")],-1))]),t("div",dt,[a(p,{"as-child":""},{default:s(()=>[a(_,{href:n.route("peserta.pendaftaran.index")},{default:s(()=>e[12]||(e[12]=[r(" Lihat Pendaftaran ")])),_:1,__:[12]},8,["href"])]),_:1})])]),_:1})]),_:1}),a(l(f),{class:"hover:shadow-lg transition-shadow"},{default:s(()=>[a(l(m),{class:"p-6"},{default:s(()=>[t("div",ut,[t("div",ft,[a(o,{name:"search",class:"h-8 w-8 text-purple-600"})]),e[13]||(e[13]=t("div",{class:"flex-1"},[t("h3",{class:"text-lg font-medium text-gray-900"},"Jelajahi Lomba"),t("p",{class:"text-sm text-gray-500"},"Cari cabang lomba")],-1))]),t("div",mt,[a(p,{"as-child":""},{default:s(()=>[a(_,{href:n.route("competition.index")},{default:s(()=>e[14]||(e[14]=[r(" Jelajahi Lomba ")])),_:1,__:[14]},8,["href"])]),_:1})])]),_:1})]),_:1})]),a(l(f),null,{default:s(()=>[a(l(V),null,{default:s(()=>[t("div",pt,[a(l(K),null,{default:s(()=>e[15]||(e[15]=[r("Pendaftaran Terbaru")])),_:1,__:[15]}),a(p,{as:"link",href:n.route("peserta.pendaftaran.index"),variant:"outline",size:"sm"},{default:s(()=>e[16]||(e[16]=[r(" Lihat Semua ")])),_:1,__:[16]},8,["href"])]),a(l(N),null,{default:s(()=>e[17]||(e[17]=[r("Pendaftaran lomba terbaru Anda")])),_:1,__:[17]})]),_:1}),a(l(m),null,{default:s(()=>[n.pendaftaran.length===0?(u(),c("div",ct,[a(o,{name:"clipboard-list",class:"h-12 w-12 text-gray-400 mx-auto mb-4"}),e[20]||(e[20]=t("h3",{class:"text-lg font-medium text-gray-900 mb-2"},"Belum Ada Pendaftaran",-1)),e[21]||(e[21]=t("p",{class:"text-gray-600 mb-4"},"Anda belum mendaftar untuk lomba apapun.",-1)),v.value?(u(),g(p,{key:0,as:"link",href:n.route("peserta.pendaftaran.create")},{default:s(()=>e[18]||(e[18]=[r(" Daftar Lomba Pertama ")])),_:1,__:[18]},8,["href"])):(u(),g(p,{key:1,"as-child":""},{default:s(()=>[a(_,{href:n.route("peserta.profile.show")},{default:s(()=>e[19]||(e[19]=[r(" Lengkapi Profil Dulu ")])),_:1,__:[19]},8,["href"])]),_:1}))])):(u(),c("div",_t,[(u(!0),c(T,null,A(n.pendaftaran,d=>(u(),c("div",{key:d.id_pendaftaran,class:"flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 transition-colors"},[t("div",gt,[t("h4",xt,i(d.golongan.nama_golongan),1),t("p",bt,i(d.golongan.cabang_lomba.nama_cabang),1),t("p",ht,i(d.nomor_pendaftaran),1)]),t("div",yt,[a(l(y),{class:h(j(d.status_pendaftaran))},{default:s(()=>[r(i($(d.status_pendaftaran)),1)]),_:2},1032,["class"]),d.pembayaran?(u(),c("div",vt,[a(l(y),{class:h([d.pembayaran.status_pembayaran==="paid"?"bg-green-100 text-green-800":"bg-yellow-100 text-yellow-800","text-xs"]),variant:"outline"},{default:s(()=>[r(i(d.pembayaran.status_pembayaran==="paid"?"Lunas":"Pending"),1)]),_:2},1032,["class"])])):w("",!0)])]))),128))]))]),_:1})]),_:1})])]),_:1}))}});export{zt as default};
