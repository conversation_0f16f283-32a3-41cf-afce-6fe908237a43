# Number Generation Implementation (SQLite Compatible)

## Overview
Implementasi sistem generate nomor untuk MTQ yang kompatibel dengan SQLite (tanpa stored procedure).

## 1. Identifier Types

### nomor_urut (Sequential Number)
- **Purpose**: Nomor urut peserta dalam kategori lomba
- **Range**: <PERSON>rdasarkan `nomor_urut_awal` dan `nomor_urut_akhir` di tabel `golongan`
- **Calculation**: `nomor_urut_awal + existing_count`
- **Validation**: Memastikan tidak melebihi `nomor_urut_akhir`

### nomor_pendaftaran (Registration Number)
- **Format**: `YYYY-KODE_WILAYAH-KODE_GOLONGAN-XXXX`
- **Example**: `2025-LP-THA-0001`
- **Components**:
  - `YYYY`: Tahun pendaftaran
  - `KODE_WILAYAH`: Kode wilayah dari tabel `wilayah`
  - `KODE_GOLONGAN`: Kode golongan dari tabel `golongan`
  - `XXXX`: Sequential number (4 digit, zero-padded)

### nomor_peserta (Participant Number)
- **Format**: `KODE_GOLONGAN-YYYY-XXX`
- **Example**: `THA-2025-001`
- **Components**:
  - `KODE_GOLONGAN`: Kode golongan dari tabel `golongan`
  - `YYYY`: Tahun pendaftaran
  - `XXX`: Sequential number (3 digit, zero-padded)

## 2. Implementation Functions

### generateNomorPendaftaran()
```php
private function generateNomorPendaftaran(int $tahun, int $idWilayah, int $idGolongan): string
{
    // Get wilayah and golongan codes
    $wilayah = \App\Models\Wilayah::find($idWilayah);
    $golongan = \App\Models\Golongan::find($idGolongan);
    
    if (!$wilayah || !$golongan) {
        return $this->fallbackNomorPendaftaran($tahun);
    }

    // Get next sequential number for this combination
    $lastNumber = Pendaftaran::join('peserta', 'pendaftaran.id_peserta', '=', 'peserta.id_peserta')
        ->where('peserta.id_wilayah', $idWilayah)
        ->where('pendaftaran.id_golongan', $idGolongan)
        ->where('pendaftaran.tahun_pendaftaran', $tahun)
        ->count();

    $nextNumber = $lastNumber + 1;
    
    return sprintf('%d-%s-%s-%04d', 
        $tahun, 
        $wilayah->kode_wilayah, 
        $golongan->kode_golongan, 
        $nextNumber
    );
}
```

### generateNomorPeserta()
```php
private function generateNomorPeserta(Golongan $golongan, int $tahun): string
{
    // Get next sequential number for this golongan and year
    $lastNumber = Pendaftaran::where('id_golongan', $golongan->id_golongan)
        ->where('tahun_pendaftaran', $tahun)
        ->count();

    $nextNumber = $lastNumber + 1;
    
    return sprintf('%s-%d-%03d', 
        $golongan->kode_golongan, 
        $tahun, 
        $nextNumber
    );
}
```

### generateNomorUrut()
```php
private function generateNomorUrut(Golongan $golongan, int $tahun): int
{
    // Get the range from golongan
    $startRange = $golongan->nomor_urut_awal ?? 1;
    $endRange = $golongan->nomor_urut_akhir ?? 999;
    
    // Count existing registrations for this golongan and year
    $existingCount = Pendaftaran::where('id_golongan', $golongan->id_golongan)
        ->where('tahun_pendaftaran', $tahun)
        ->count();
    
    // Calculate next nomor_urut within the range
    $nextNomorUrut = $startRange + $existingCount;
    
    // Ensure we don't exceed the range
    if ($nextNomorUrut > $endRange) {
        throw new \Exception("Nomor urut untuk golongan {$golongan->nama_golongan} sudah mencapai batas maksimum ({$endRange})");
    }
    
    return $nextNomorUrut;
}
```

## 3. Database Schema Updates

### Migration: add_nomor_urut_to_pendaftaran_table
```sql
ALTER TABLE pendaftaran ADD COLUMN nomor_urut INTEGER;
CREATE INDEX idx_pendaftaran_nomor_urut ON pendaftaran(nomor_urut);
```

### Updated Pendaftaran Model
```php
protected $fillable = [
    // ... existing fields
    'nomor_urut',
    // ... other fields
];
```

## 4. Usage in Controller

### AdminDaerah\PendaftaranController::store()
```php
DB::transaction(function () use ($validated, $golongan, $peserta, $adminWilayah) {
    // Generate registration numbers
    $tahun = date('Y');
    $nomorPendaftaran = $this->generateNomorPendaftaran($tahun, $adminWilayah, $golongan->id_golongan);
    $nomorPeserta = $this->generateNomorPeserta($golongan, $tahun);
    $nomorUrut = $this->generateNomorUrut($golongan, $tahun);

    // Create pendaftaran with all numbers
    $pendaftaran = Pendaftaran::create([
        'id_peserta' => $validated['id_peserta'],
        'id_golongan' => $validated['id_golongan'],
        'nomor_pendaftaran' => $nomorPendaftaran,
        'nomor_peserta' => $nomorPeserta,
        'nomor_urut' => $nomorUrut,
        'tahun_pendaftaran' => $tahun,
        'status_pendaftaran' => 'verified',
        // ... other fields
    ]);
});
```

## 5. Error Handling

### Range Validation
- Checks if nomor_urut exceeds the allocated range
- Throws exception with clear error message
- Prevents registration if range is full

### Fallback Methods
- `fallbackNomorPendaftaran()`: Simple REG{YEAR}XXXXXX format
- Used when wilayah or golongan data is missing

### Transaction Safety
- All number generation happens within database transaction
- Ensures consistency and prevents duplicate numbers
- Rollback on any error

## 6. Benefits of This Implementation

### SQLite Compatibility
- No stored procedures required
- Works with any SQL database
- Portable across environments

### Atomic Operations
- Database transactions ensure consistency
- No race conditions in number generation
- Safe for concurrent registrations

### Flexible Range Management
- Range allocation per golongan
- Easy to adjust ranges in database
- Clear error messages when ranges are full

### Audit Trail
- All numbers are stored in database
- Easy to track and debug
- Clear relationship between numbers

## 7. Testing Scenarios

### Normal Flow
1. Register participant in golongan with range 1-100
2. First registration gets nomor_urut = 1
3. Second registration gets nomor_urut = 2
4. Numbers are properly formatted

### Edge Cases
1. Range exhaustion (nomor_urut > nomor_urut_akhir)
2. Missing wilayah/golongan data
3. Concurrent registrations
4. Database transaction failures

### Validation
1. Unique nomor_pendaftaran across system
2. Unique nomor_peserta per golongan per year
3. Sequential nomor_urut within range
4. Proper format for all numbers

## 8. Migration Steps

1. Run migration to add nomor_urut field
2. Update Pendaftaran model fillable array
3. Update controller to use new generation methods
4. Test with sample data
5. Verify number uniqueness and format

This implementation provides a robust, SQLite-compatible solution for generating all three types of numbers in the MTQ system.
