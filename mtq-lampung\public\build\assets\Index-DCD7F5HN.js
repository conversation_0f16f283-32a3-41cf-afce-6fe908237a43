import{d as B,r as U,l as F,W as y,c as M,o as u,w as n,a,b as s,u as o,g as T,e as r,t as i,h as _,F as k,m as w}from"./app-B_pmlBSQ.js";import{a as z,b as E,c as P,d as $,_ as W}from"./AppLayout.vue_vue_type_script_setup_true_lang-BRf8lUiM.js";import{_ as H}from"./Heading.vue_vue_type_script_setup_true_lang-is4VBSCS.js";import{_ as f}from"./AppLogoIcon.vue_vue_type_script_setup_true_lang-wzz4PWna.js";import{_ as C,a as L}from"./CardContent.vue_vue_type_script_setup_true_lang-ClvLNBXC.js";import{_ as b}from"./index-CMGr3-bt.js";import{_ as J}from"./Input.vue_vue_type_script_setup_true_lang-DB1-RsTn.js";import{_ as D}from"./Label.vue_vue_type_script_setup_true_lang-rj_fhE7R.js";import{_ as K,a as R,b as q,c as O,d as v}from"./SelectValue.vue_vue_type_script_setup_true_lang-IzlIronZ.js";import{_ as Q,a as X,b as Y,c as Z,d as aa}from"./DialogTitle.vue_vue_type_script_setup_true_lang-MyozO0uv.js";import{_ as ta}from"./DialogFooter.vue_vue_type_script_setup_true_lang-D2zTaokr.js";import{_ as m}from"./Icon.vue_vue_type_script_setup_true_lang-BuYzjckr.js";import{l as ea,_ as sa}from"./lodash-Cvgo55ys.js";import"./useForwardExpose-CO14IhkA.js";import"./RovingFocusGroup-lsWyU4xZ.js";import"./check-DtmHjdxf.js";import"./useFormControl-mZRLifGx.js";import"./loader-circle-6d8QrWFr.js";import"./sun-CHIV9RJ8.js";import"./TextLink.vue_vue_type_script_setup_true_lang-CC2Og0lP.js";const na={class:"space-y-6"},oa={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},la={class:"flex items-end"},ia={class:"flex justify-between items-center"},ra={class:"text-sm text-gray-600"},da={class:"overflow-x-auto"},ma={class:"w-full"},ua={class:"bg-white divide-y divide-gray-200"},ca={class:"px-6 py-4 whitespace-nowrap"},_a={class:"text-sm font-medium text-gray-900"},fa={class:"text-sm text-gray-500"},ga={class:"px-6 py-4"},pa={class:"text-sm text-gray-900 max-w-xs truncate"},ba={class:"px-6 py-4 whitespace-nowrap"},va={class:"flex items-center"},xa={class:"px-6 py-4 whitespace-nowrap"},ha={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500"},ya={class:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium space-x-2"},ka={key:0,class:"flex justify-center py-8"},wa={key:1,class:"text-center py-8 text-gray-500"},$a={key:2,class:"space-y-3 max-h-96 overflow-y-auto"},Ca={class:"flex-1"},La={class:"font-medium"},Da={class:"text-sm text-gray-500"},Sa={class:"text-sm text-gray-500 mt-1"},Va={class:"flex flex-col items-end gap-2"},ja={class:"text-sm text-gray-500"},Aa={class:"text-sm font-medium text-green-600"},Za=B({__name:"Index",props:{cabangLomba:{},filters:{}},setup(S){const V=[{title:"Dashboard",href:"/admin/dashboard"},{title:"Manajemen Cabang Lomba",href:"/admin/cabang-lomba"}],c=U({...S.filters}),d=F({show:!1,cabang:null,golongan:[],loading:!1}),p=ea.debounce(()=>{y.get(route("admin.cabang-lomba.index"),c,{preserveState:!0,replace:!0})},300),j=()=>{c.search="",c.status="",p()},A=async l=>{d.value.cabang=l,d.value.show=!0,d.value.loading=!0,d.value.golongan=[];try{const e=await(await fetch(route("admin.cabang-lomba.golongan",l.id_cabang))).json();d.value.golongan=e}catch(t){console.error("Error fetching golongan:",t)}finally{d.value.loading=!1}},I=l=>{confirm(`Apakah Anda yakin ingin menghapus cabang lomba ${l.nama_cabang}?`)&&y.delete(route("admin.cabang-lomba.destroy",l.id_cabang))},x=l=>({aktif:"default",non_aktif:"secondary"})[l]||"secondary",h=l=>({aktif:"Aktif",non_aktif:"Non Aktif"})[l]||l,N=l=>new Date(l).toLocaleDateString("id-ID",{year:"numeric",month:"short",day:"numeric"}),G=l=>new Intl.NumberFormat("id-ID").format(l);return(l,t)=>(u(),M(W,{breadcrumbs:V},{default:n(()=>[a(o(T),{title:"Manajemen Cabang Lomba"}),a(H,{title:"Manajemen Cabang Lomba"}),s("div",na,[a(o(C),null,{default:n(()=>[a(o(L),{class:"p-6"},{default:n(()=>[s("div",oa,[s("div",null,[a(o(D),{for:"search"},{default:n(()=>t[6]||(t[6]=[r("Pencarian")])),_:1,__:[6]}),a(o(J),{id:"search",modelValue:c.search,"onUpdate:modelValue":t[0]||(t[0]=e=>c.search=e),placeholder:"Nama cabang, kode...",onInput:o(p)},null,8,["modelValue","onInput"])]),s("div",null,[a(o(D),{for:"status"},{default:n(()=>t[7]||(t[7]=[r("Status")])),_:1,__:[7]}),a(o(K),{modelValue:c.status,"onUpdate:modelValue":[t[1]||(t[1]=e=>c.status=e),o(p)]},{default:n(()=>[a(o(R),null,{default:n(()=>[a(o(q),{placeholder:"Semua Status"})]),_:1}),a(o(O),null,{default:n(()=>[a(o(v),{value:"all"},{default:n(()=>t[8]||(t[8]=[r("Semua Status")])),_:1,__:[8]}),a(o(v),{value:"aktif"},{default:n(()=>t[9]||(t[9]=[r("Aktif")])),_:1,__:[9]}),a(o(v),{value:"non_aktif"},{default:n(()=>t[10]||(t[10]=[r("Non Aktif")])),_:1,__:[10]})]),_:1})]),_:1},8,["modelValue","onUpdate:modelValue"])]),s("div",la,[a(f,{onClick:j,variant:"outline",class:"w-full"},{default:n(()=>[a(m,{name:"x",class:"w-4 h-4 mr-2"}),t[11]||(t[11]=r(" Clear "))]),_:1,__:[11]})])])]),_:1})]),_:1}),s("div",ia,[s("div",ra," Menampilkan "+i(l.cabangLomba.from)+" - "+i(l.cabangLomba.to)+" dari "+i(l.cabangLomba.total)+" cabang lomba ",1),a(f,{onClick:t[2]||(t[2]=e=>l.$inertia.visit(l.route("admin.cabang-lomba.create")))},{default:n(()=>[a(m,{name:"plus",class:"w-4 h-4 mr-2"}),t[12]||(t[12]=r(" Tambah Cabang Lomba "))]),_:1,__:[12]})]),a(o(C),null,{default:n(()=>[a(o(L),{class:"p-0"},{default:n(()=>[s("div",da,[s("table",ma,[t[15]||(t[15]=s("thead",{class:"bg-gray-50 border-b"},[s("tr",null,[s("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Cabang Lomba "),s("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Deskripsi "),s("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Jumlah Golongan "),s("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Status "),s("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Dibuat "),s("th",{class:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"}," Aksi ")])],-1)),s("tbody",ua,[(u(!0),_(k,null,w(l.cabangLomba.data,e=>(u(),_("tr",{key:e.id_cabang,class:"hover:bg-gray-50"},[s("td",ca,[s("div",null,[s("div",_a,i(e.nama_cabang),1),s("div",fa,i(e.kode_cabang),1)])]),s("td",ga,[s("div",pa,i(e.deskripsi||"-"),1)]),s("td",ba,[s("div",va,[a(o(b),{variant:"outline"},{default:n(()=>[r(i(e.golongan_count||0)+" Golongan ",1)]),_:2},1024)])]),s("td",xa,[a(o(b),{variant:x(e.status)},{default:n(()=>[r(i(h(e.status)),1)]),_:2},1032,["variant"])]),s("td",ha,i(N(e.created_at)),1),s("td",ya,[a(f,{variant:"ghost",size:"sm",onClick:g=>l.$inertia.visit(l.route("admin.cabang-lomba.show",e.id_cabang))},{default:n(()=>[a(m,{name:"eye",class:"w-4 h-4"})]),_:2},1032,["onClick"]),a(f,{variant:"ghost",size:"sm",onClick:g=>l.$inertia.visit(l.route("admin.cabang-lomba.edit",e.id_cabang))},{default:n(()=>[a(m,{name:"edit",class:"w-4 h-4"})]),_:2},1032,["onClick"]),a(o(z),null,{default:n(()=>[a(o(E),{"as-child":""},{default:n(()=>[a(f,{variant:"ghost",size:"sm"},{default:n(()=>[a(m,{name:"more-vertical",class:"w-4 h-4"})]),_:1})]),_:1}),a(o(P),{align:"end"},{default:n(()=>[a(o($),{onClick:g=>A(e)},{default:n(()=>[a(m,{name:"list",class:"w-4 h-4 mr-2"}),t[13]||(t[13]=r(" Lihat Golongan "))]),_:2,__:[13]},1032,["onClick"]),a(o($),{onClick:g=>I(e),class:"text-red-600"},{default:n(()=>[a(m,{name:"trash",class:"w-4 h-4 mr-2"}),t[14]||(t[14]=r(" Hapus "))]),_:2,__:[14]},1032,["onClick"])]),_:2},1024)]),_:2},1024)])]))),128))])])])]),_:1})]),_:1}),a(sa,{links:l.cabangLomba.links},null,8,["links"])]),a(o(Q),{open:d.value.show,"onUpdate:open":t[5]||(t[5]=e=>d.value.show=e)},{default:n(()=>[a(o(X),{class:"max-w-3xl"},{default:n(()=>[a(o(Y),null,{default:n(()=>[a(o(Z),null,{default:n(()=>{var e;return[r("Golongan: "+i((e=d.value.cabang)==null?void 0:e.nama_cabang),1)]}),_:1}),a(o(aa),null,{default:n(()=>{var e;return[r(" Daftar golongan yang tersedia untuk cabang lomba "+i((e=d.value.cabang)==null?void 0:e.nama_cabang),1)]}),_:1})]),_:1}),d.value.loading?(u(),_("div",ka,[a(m,{name:"loader-2",class:"w-6 h-6 animate-spin"})])):d.value.golongan.length===0?(u(),_("div",wa," Belum ada golongan untuk cabang lomba ini ")):(u(),_("div",$a,[(u(!0),_(k,null,w(d.value.golongan,e=>(u(),_("div",{key:e.id_golongan,class:"flex justify-between items-center p-4 border rounded-lg"},[s("div",Ca,[s("div",La,i(e.nama_golongan),1),s("div",Da,i(e.kode_golongan),1),s("div",Sa,i(e.jenis_kelamin==="L"?"Laki-laki":"Perempuan")+" • Usia "+i(e.batas_umur_min)+"-"+i(e.batas_umur_max)+" tahun ",1)]),s("div",Va,[a(o(b),{variant:x(e.status)},{default:n(()=>[r(i(h(e.status)),1)]),_:2},1032,["variant"]),s("div",ja," Kuota: "+i(e.kuota_max),1),s("div",Aa," Rp "+i(G(e.biaya_pendaftaran)),1)])]))),128))])),a(o(ta),null,{default:n(()=>[a(f,{onClick:t[3]||(t[3]=e=>d.value.show=!1)},{default:n(()=>t[16]||(t[16]=[r("Tutup")])),_:1,__:[16]}),a(f,{onClick:t[4]||(t[4]=e=>{var g;return l.$inertia.visit(l.route("admin.golongan.create",{cabang:(g=d.value.cabang)==null?void 0:g.id_cabang}))})},{default:n(()=>[a(m,{name:"plus",class:"w-4 h-4 mr-2"}),t[17]||(t[17]=r(" Tambah Golongan "))]),_:1,__:[17]})]),_:1})]),_:1})]),_:1},8,["open"])]),_:1}))}});export{Za as default};
