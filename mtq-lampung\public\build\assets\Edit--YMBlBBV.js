import{d as I,x as P,s as L,c as k,o as n,w as t,a as r,b as l,u as e,g as R,e as i,f as A,h as u,i as m,n as f,t as _,F as D,m as E}from"./app-B_pmlBSQ.js";import{_ as T}from"./AppLayout.vue_vue_type_script_setup_true_lang-BRf8lUiM.js";import{_ as j}from"./Heading.vue_vue_type_script_setup_true_lang-is4VBSCS.js";import{_ as N}from"./AppLogoIcon.vue_vue_type_script_setup_true_lang-wzz4PWna.js";import{_ as F,a as M}from"./CardContent.vue_vue_type_script_setup_true_lang-ClvLNBXC.js";import{_ as W}from"./CardDescription.vue_vue_type_script_setup_true_lang-DboywBIK.js";import{_ as z,a as G}from"./CardTitle.vue_vue_type_script_setup_true_lang-5HO6oSoB.js";import{_ as b}from"./Input.vue_vue_type_script_setup_true_lang-DB1-RsTn.js";import{_ as y}from"./Label.vue_vue_type_script_setup_true_lang-rj_fhE7R.js";import{_ as V,a as v,b as x,c as w,d as g}from"./SelectValue.vue_vue_type_script_setup_true_lang-IzlIronZ.js";import{_ as H}from"./Icon.vue_vue_type_script_setup_true_lang-BuYzjckr.js";import"./useForwardExpose-CO14IhkA.js";import"./RovingFocusGroup-lsWyU4xZ.js";import"./check-DtmHjdxf.js";import"./useFormControl-mZRLifGx.js";import"./loader-circle-6d8QrWFr.js";import"./sun-CHIV9RJ8.js";const J={class:"max-w-2xl mx-auto"},K={class:"space-y-4"},O={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},Q={key:0,class:"text-sm text-red-600 mt-1"},X={key:0,class:"text-sm text-red-600 mt-1"},Y={key:0,class:"text-sm text-red-600 mt-1"},Z={key:0,class:"text-sm text-red-600 mt-1"},c={class:"space-y-4"},ee={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},se={key:0,class:"text-sm text-red-600 mt-1"},ae={key:1,class:"text-sm text-gray-500 mt-1"},re={key:0,class:"text-sm text-red-600 mt-1"},te={key:1,class:"text-sm text-gray-500 mt-1"},le={key:0},oe={key:0,class:"text-sm text-red-600 mt-1"},ie={class:"bg-gray-50 p-4 rounded-lg"},de={class:"text-sm text-gray-600 space-y-1"},ne={key:0},ue={key:1},me={class:"flex justify-end space-x-4 pt-6 border-t"},Ne=I({__name:"Edit",props:{user:{},wilayah:{},roles:{}},setup(q){var U;const p=q,B=[{title:"Dashboard",href:"/admin/dashboard"},{title:"Manajemen User",href:"/admin/users"},{title:"Edit User",href:`/admin/users/${p.user.id_user}/edit`}],a=P({username:p.user.username,email:p.user.email,role:p.user.role,nama_lengkap:p.user.nama_lengkap,no_telepon:p.user.no_telepon||"",id_wilayah:((U=p.user.id_wilayah)==null?void 0:U.toString())||"",status:p.user.status});L(()=>a.role,d=>{d==="admin"&&(a.id_wilayah="")});const C=()=>{a.put(route("admin.users.update",p.user.id_user),{onSuccess:()=>{}})},$=d=>new Date(d).toLocaleDateString("id-ID",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"});return(d,s)=>(n(),k(T,{breadcrumbs:B},{default:t(()=>[r(e(R),{title:"Edit User"}),r(j,{title:`Edit User: ${d.user.nama_lengkap}`},null,8,["title"]),l("div",J,[r(e(F),null,{default:t(()=>[r(e(z),null,{default:t(()=>[r(e(G),null,{default:t(()=>s[8]||(s[8]=[i("Edit Informasi User")])),_:1,__:[8]}),r(e(W),null,{default:t(()=>s[9]||(s[9]=[i(" Perbarui informasi user di bawah ini ")])),_:1,__:[9]})]),_:1}),r(e(M),null,{default:t(()=>{var h;return[l("form",{onSubmit:A(C,["prevent"]),class:"space-y-6"},[l("div",K,[s[14]||(s[14]=l("h3",{class:"text-lg font-medium"},"Informasi Dasar",-1)),l("div",O,[l("div",null,[r(e(y),{for:"username"},{default:t(()=>s[10]||(s[10]=[i("Username *")])),_:1,__:[10]}),r(e(b),{id:"username",modelValue:e(a).username,"onUpdate:modelValue":s[0]||(s[0]=o=>e(a).username=o),type:"text",required:"",class:f({"border-red-500":e(a).errors.username})},null,8,["modelValue","class"]),e(a).errors.username?(n(),u("p",Q,_(e(a).errors.username),1)):m("",!0)]),l("div",null,[r(e(y),{for:"email"},{default:t(()=>s[11]||(s[11]=[i("Email *")])),_:1,__:[11]}),r(e(b),{id:"email",modelValue:e(a).email,"onUpdate:modelValue":s[1]||(s[1]=o=>e(a).email=o),type:"email",required:"",class:f({"border-red-500":e(a).errors.email})},null,8,["modelValue","class"]),e(a).errors.email?(n(),u("p",X,_(e(a).errors.email),1)):m("",!0)])]),l("div",null,[r(e(y),{for:"nama_lengkap"},{default:t(()=>s[12]||(s[12]=[i("Nama Lengkap *")])),_:1,__:[12]}),r(e(b),{id:"nama_lengkap",modelValue:e(a).nama_lengkap,"onUpdate:modelValue":s[2]||(s[2]=o=>e(a).nama_lengkap=o),type:"text",required:"",class:f({"border-red-500":e(a).errors.nama_lengkap})},null,8,["modelValue","class"]),e(a).errors.nama_lengkap?(n(),u("p",Y,_(e(a).errors.nama_lengkap),1)):m("",!0)]),l("div",null,[r(e(y),{for:"no_telepon"},{default:t(()=>s[13]||(s[13]=[i("No. Telepon")])),_:1,__:[13]}),r(e(b),{id:"no_telepon",modelValue:e(a).no_telepon,"onUpdate:modelValue":s[3]||(s[3]=o=>e(a).no_telepon=o),type:"tel",class:f({"border-red-500":e(a).errors.no_telepon})},null,8,["modelValue","class"]),e(a).errors.no_telepon?(n(),u("p",Z,_(e(a).errors.no_telepon),1)):m("",!0)])]),l("div",c,[s[21]||(s[21]=l("h3",{class:"text-lg font-medium"},"Role dan Akses",-1)),l("div",ee,[l("div",null,[r(e(y),{for:"role"},{default:t(()=>s[15]||(s[15]=[i("Role *")])),_:1,__:[15]}),r(e(V),{modelValue:e(a).role,"onUpdate:modelValue":s[4]||(s[4]=o=>e(a).role=o),required:"",disabled:d.user.role==="superadmin"},{default:t(()=>[r(e(v),{class:f({"border-red-500":e(a).errors.role})},{default:t(()=>[r(e(x),{placeholder:"Pilih Role"})]),_:1},8,["class"]),r(e(w),null,{default:t(()=>[(n(!0),u(D,null,E(d.roles,(o,S)=>(n(),k(e(g),{key:S,value:S},{default:t(()=>[i(_(o),1)]),_:2},1032,["value"]))),128))]),_:1})]),_:1},8,["modelValue","disabled"]),e(a).errors.role?(n(),u("p",se,_(e(a).errors.role),1)):m("",!0),d.user.role==="superadmin"?(n(),u("p",ae," Role superadmin tidak dapat diubah ")):m("",!0)]),l("div",null,[r(e(y),{for:"status"},{default:t(()=>s[16]||(s[16]=[i("Status *")])),_:1,__:[16]}),r(e(V),{modelValue:e(a).status,"onUpdate:modelValue":s[5]||(s[5]=o=>e(a).status=o),required:"",disabled:d.user.role==="superadmin"},{default:t(()=>[r(e(v),{class:f({"border-red-500":e(a).errors.status})},{default:t(()=>[r(e(x),{placeholder:"Pilih Status"})]),_:1},8,["class"]),r(e(w),null,{default:t(()=>[r(e(g),{value:"aktif"},{default:t(()=>s[17]||(s[17]=[i("Aktif")])),_:1,__:[17]}),r(e(g),{value:"non_aktif"},{default:t(()=>s[18]||(s[18]=[i("Non Aktif")])),_:1,__:[18]}),r(e(g),{value:"suspended"},{default:t(()=>s[19]||(s[19]=[i("Suspended")])),_:1,__:[19]})]),_:1})]),_:1},8,["modelValue","disabled"]),e(a).errors.status?(n(),u("p",re,_(e(a).errors.status),1)):m("",!0),d.user.role==="superadmin"?(n(),u("p",te," Status superadmin tidak dapat diubah ")):m("",!0)])]),e(a).role==="admin_daerah"||e(a).role==="dewan_hakim"?(n(),u("div",le,[r(e(y),{for:"id_wilayah"},{default:t(()=>s[20]||(s[20]=[i("Wilayah *")])),_:1,__:[20]}),r(e(V),{modelValue:e(a).id_wilayah,"onUpdate:modelValue":s[6]||(s[6]=o=>e(a).id_wilayah=o),required:""},{default:t(()=>[r(e(v),{class:f({"border-red-500":e(a).errors.id_wilayah})},{default:t(()=>[r(e(x),{placeholder:"Pilih Wilayah"})]),_:1},8,["class"]),r(e(w),null,{default:t(()=>[(n(!0),u(D,null,E(d.wilayah,o=>(n(),k(e(g),{key:o.id_wilayah,value:o.id_wilayah.toString()},{default:t(()=>[i(_(o.nama_wilayah),1)]),_:2},1032,["value"]))),128))]),_:1})]),_:1},8,["modelValue"]),e(a).errors.id_wilayah?(n(),u("p",oe,_(e(a).errors.id_wilayah),1)):m("",!0)])):m("",!0)]),l("div",ie,[s[25]||(s[25]=l("h4",{class:"font-medium text-gray-900 mb-2"},"Informasi Tambahan",-1)),l("div",de,[l("p",null,[s[22]||(s[22]=l("strong",null,"Dibuat:",-1)),i(" "+_($(d.user.created_at)),1)]),d.user.created_by?(n(),u("p",ne,[s[23]||(s[23]=l("strong",null,"Dibuat oleh:",-1)),i(" "+_((h=d.user.created_by)==null?void 0:h.nama_lengkap),1)])):m("",!0),d.user.last_login?(n(),u("p",ue,[s[24]||(s[24]=l("strong",null,"Login terakhir:",-1)),i(" "+_($(d.user.last_login)),1)])):m("",!0)])]),l("div",me,[r(N,{type:"button",variant:"outline",onClick:s[7]||(s[7]=o=>d.$inertia.visit(d.route("admin.users.index")))},{default:t(()=>s[26]||(s[26]=[i(" Batal ")])),_:1,__:[26]}),r(N,{type:"submit",disabled:e(a).processing},{default:t(()=>[e(a).processing?(n(),k(H,{key:0,name:"loader-2",class:"w-4 h-4 mr-2 animate-spin"})):m("",!0),s[27]||(s[27]=i(" Simpan Perubahan "))]),_:1,__:[27]},8,["disabled"])])],32)]}),_:1})]),_:1})])]),_:1}))}});export{Ne as default};
