import{d as l,x as f,c as i,o as d,w as t,a as r,b as a,u as s,g as c,f as _,e as m,i as u}from"./app-B_pmlBSQ.js";import{_ as w}from"./InputError.vue_vue_type_script_setup_true_lang-A-vdzbq9.js";import{_ as g}from"./AppLogoIcon.vue_vue_type_script_setup_true_lang-wzz4PWna.js";import{_ as C}from"./Input.vue_vue_type_script_setup_true_lang-DB1-RsTn.js";import{_ as b}from"./Label.vue_vue_type_script_setup_true_lang-rj_fhE7R.js";import{_ as V}from"./AuthLayout.vue_vue_type_script_setup_true_lang-DMSGHxRq.js";import{L as x}from"./loader-circle-6d8QrWFr.js";import"./useForwardExpose-CO14IhkA.js";const h={class:"space-y-6"},y={class:"grid gap-2"},$={class:"flex items-center"},S=l({__name:"ConfirmPassword",setup(k){const o=f({password:""}),n=()=>{o.post(route("password.confirm"),{onFinish:()=>{o.reset()}})};return(v,e)=>(d(),i(V,{title:"Confirm your password",description:"This is a secure area of the application. Please confirm your password before continuing."},{default:t(()=>[r(s(c),{title:"Confirm password"}),a("form",{onSubmit:_(n,["prevent"])},[a("div",h,[a("div",y,[r(s(b),{htmlFor:"password"},{default:t(()=>e[1]||(e[1]=[m("Password")])),_:1,__:[1]}),r(s(C),{id:"password",type:"password",class:"mt-1 block w-full",modelValue:s(o).password,"onUpdate:modelValue":e[0]||(e[0]=p=>s(o).password=p),required:"",autocomplete:"current-password",autofocus:""},null,8,["modelValue"]),r(w,{message:s(o).errors.password},null,8,["message"])]),a("div",$,[r(s(g),{class:"w-full",disabled:s(o).processing},{default:t(()=>[s(o).processing?(d(),i(s(x),{key:0,class:"h-4 w-4 animate-spin"})):u("",!0),e[2]||(e[2]=m(" Confirm Password "))]),_:1,__:[2]},8,["disabled"])])])],32)]),_:1}))}});export{S as default};
