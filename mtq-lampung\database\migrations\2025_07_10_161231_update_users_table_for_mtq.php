<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Rename id to id_user
            $table->renameColumn('id', 'id_user');

            // Add new columns for MTQ system
            $table->string('username', 50)->unique()->after('id_user');
            $table->enum('role', ['superadmin', 'admin', 'admin_daerah', 'peserta', 'dewan_hakim'])->after('email_verified_at');
            $table->unsignedBigInteger('id_wilayah')->nullable()->after('role');
            $table->string('nama_lengkap', 100)->after('id_wilayah');
            $table->string('no_telepon', 20)->nullable()->after('nama_lengkap');
            $table->enum('status', ['aktif', 'non_aktif', 'suspended'])->default('aktif')->after('no_telepon');
            $table->timestamp('last_login')->nullable()->after('status');
            $table->unsignedBigInteger('created_by')->nullable()->after('last_login');

            // Rename name to match schema
            $table->renameColumn('name', 'name_temp');
        });

        // Add foreign key constraints
        Schema::table('users', function (Blueprint $table) {
            $table->foreign('id_wilayah')->references('id_wilayah')->on('wilayah')->onDelete('set null');
            $table->foreign('created_by')->references('id_user')->on('users')->onDelete('set null');

            // Add indexes
            $table->index(['role', 'status']);
            $table->index('id_wilayah');
        });

        // Drop the temporary name column
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn('name_temp');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Drop foreign keys first
            $table->dropForeign(['id_wilayah']);
            $table->dropForeign(['created_by']);

            // Drop indexes
            $table->dropIndex(['role', 'status']);
            $table->dropIndex(['id_wilayah']);

            // Add back name column
            $table->string('name')->after('id_user');

            // Drop new columns
            $table->dropColumn([
                'username', 'role', 'id_wilayah', 'nama_lengkap',
                'no_telepon', 'status', 'last_login', 'created_by'
            ]);

            // Rename id_user back to id
            $table->renameColumn('id_user', 'id');
        });
    }
};
