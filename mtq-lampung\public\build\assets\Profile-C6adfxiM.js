import{d as w,c as k,o as m,u as e,E as S,w as a,D as h,l as D,x,h as v,a as s,b as r,e as i,Y as P,g as C,f as E,i as $,Z as B,aj as U,j as N,L as j}from"./app-B_pmlBSQ.js";import{a as V,_ as F}from"./Layout.vue_vue_type_script_setup_true_lang-DmO1Dm59.js";import{_ as y}from"./InputError.vue_vue_type_script_setup_true_lang-A-vdzbq9.js";import{_ as f}from"./AppLogoIcon.vue_vue_type_script_setup_true_lang-wzz4PWna.js";import{a as I,b as q,c as A,d as M,_ as O}from"./DialogTitle.vue_vue_type_script_setup_true_lang-MyozO0uv.js";import{Q as T,_ as Y}from"./AppLayout.vue_vue_type_script_setup_true_lang-BRf8lUiM.js";import{_ as L}from"./DialogFooter.vue_vue_type_script_setup_true_lang-D2zTaokr.js";import{_ as Q}from"./DialogTrigger.vue_vue_type_script_setup_true_lang-oTNwh9lu.js";import{_ as g}from"./Input.vue_vue_type_script_setup_true_lang-DB1-RsTn.js";import{_ as b}from"./Label.vue_vue_type_script_setup_true_lang-rj_fhE7R.js";import"./Heading.vue_vue_type_script_setup_true_lang-is4VBSCS.js";import"./useForwardExpose-CO14IhkA.js";import"./RovingFocusGroup-lsWyU4xZ.js";const W=w({__name:"DialogClose",props:{asChild:{type:Boolean},as:{type:[String,Object,Function]}},setup(p){const d=p;return(n,u)=>(m(),k(e(T),S({"data-slot":"dialog-close"},d),{default:a(()=>[h(n.$slots,"default")]),_:3},16))}}),Z={class:"space-y-6"},z={class:"space-y-4 rounded-lg border border-red-100 bg-red-50 p-4 dark:border-red-200/10 dark:bg-red-700/10"},G={class:"grid gap-2"},H=w({__name:"DeleteUser",setup(p){const d=D(null),n=x({password:""}),u=c=>{c.preventDefault(),n.delete(route("profile.destroy"),{preserveScroll:!0,onSuccess:()=>l(),onError:()=>{var t;return(t=d.value)==null?void 0:t.focus()},onFinish:()=>n.reset()})},l=()=>{n.clearErrors(),n.reset()};return(c,t)=>(m(),v("div",Z,[s(V,{title:"Delete account",description:"Delete your account and all of its resources"}),r("div",z,[t[7]||(t[7]=r("div",{class:"relative space-y-0.5 text-red-600 dark:text-red-100"},[r("p",{class:"font-medium"},"Warning"),r("p",{class:"text-sm"},"Please proceed with caution, this cannot be undone.")],-1)),s(e(O),null,{default:a(()=>[s(e(Q),{"as-child":""},{default:a(()=>[s(e(f),{variant:"destructive"},{default:a(()=>t[1]||(t[1]=[i("Delete account")])),_:1,__:[1]})]),_:1}),s(e(I),null,{default:a(()=>[r("form",{class:"space-y-6",onSubmit:u},[s(e(q),{class:"space-y-3"},{default:a(()=>[s(e(A),null,{default:a(()=>t[2]||(t[2]=[i("Are you sure you want to delete your account?")])),_:1,__:[2]}),s(e(M),null,{default:a(()=>t[3]||(t[3]=[i(" Once your account is deleted, all of its resources and data will also be permanently deleted. Please enter your password to confirm you would like to permanently delete your account. ")])),_:1,__:[3]})]),_:1}),r("div",G,[s(e(b),{for:"password",class:"sr-only"},{default:a(()=>t[4]||(t[4]=[i("Password")])),_:1,__:[4]}),s(e(g),{id:"password",type:"password",name:"password",ref_key:"passwordInput",ref:d,modelValue:e(n).password,"onUpdate:modelValue":t[0]||(t[0]=o=>e(n).password=o),placeholder:"Password"},null,8,["modelValue"]),s(y,{message:e(n).errors.password},null,8,["message"])]),s(e(L),{class:"gap-2"},{default:a(()=>[s(e(W),{"as-child":""},{default:a(()=>[s(e(f),{variant:"secondary",onClick:l},{default:a(()=>t[5]||(t[5]=[i(" Cancel ")])),_:1,__:[5]})]),_:1}),s(e(f),{type:"submit",variant:"destructive",disabled:e(n).processing},{default:a(()=>t[6]||(t[6]=[i(" Delete account ")])),_:1,__:[6]},8,["disabled"])]),_:1})],32)]),_:1})]),_:1})])]))}}),J={class:"flex flex-col space-y-6"},K={class:"grid gap-2"},R={class:"grid gap-2"},X={key:0},ee={class:"-mt-4 text-sm text-muted-foreground"},se={key:0,class:"mt-2 text-sm font-medium text-green-600"},te={class:"flex items-center gap-4"},ae={class:"text-sm text-neutral-600"},ye=w({__name:"Profile",props:{mustVerifyEmail:{type:Boolean},status:{}},setup(p){const d=[{title:"Profile settings",href:"/settings/profile"}],u=P().props.auth.user,l=x({name:u.name,email:u.email}),c=()=>{l.patch(route("profile.update"),{preserveScroll:!0})};return(t,o)=>(m(),k(Y,{breadcrumbs:d},{default:a(()=>[s(e(C),{title:"Profile settings"}),s(F,null,{default:a(()=>[r("div",J,[s(V,{title:"Profile information",description:"Update your name and email address"}),r("form",{onSubmit:E(c,["prevent"]),class:"space-y-6"},[r("div",K,[s(e(b),{for:"name"},{default:a(()=>o[2]||(o[2]=[i("Name")])),_:1,__:[2]}),s(e(g),{id:"name",class:"mt-1 block w-full",modelValue:e(l).name,"onUpdate:modelValue":o[0]||(o[0]=_=>e(l).name=_),required:"",autocomplete:"name",placeholder:"Full name"},null,8,["modelValue"]),s(y,{class:"mt-2",message:e(l).errors.name},null,8,["message"])]),r("div",R,[s(e(b),{for:"email"},{default:a(()=>o[3]||(o[3]=[i("Email address")])),_:1,__:[3]}),s(e(g),{id:"email",type:"email",class:"mt-1 block w-full",modelValue:e(l).email,"onUpdate:modelValue":o[1]||(o[1]=_=>e(l).email=_),required:"",autocomplete:"username",placeholder:"Email address"},null,8,["modelValue"]),s(y,{class:"mt-2",message:e(l).errors.email},null,8,["message"])]),t.mustVerifyEmail&&!e(u).email_verified_at?(m(),v("div",X,[r("p",ee,[o[5]||(o[5]=i(" Your email address is unverified. ")),s(e(B),{href:t.route("verification.send"),method:"post",as:"button",class:"text-foreground underline decoration-neutral-300 underline-offset-4 transition-colors duration-300 ease-out hover:decoration-current! dark:decoration-neutral-500"},{default:a(()=>o[4]||(o[4]=[i(" Click here to resend the verification email. ")])),_:1,__:[4]},8,["href"])]),t.status==="verification-link-sent"?(m(),v("div",se," A new verification link has been sent to your email address. ")):$("",!0)])):$("",!0),r("div",te,[s(e(f),{disabled:e(l).processing},{default:a(()=>o[6]||(o[6]=[i("Save")])),_:1,__:[6]},8,["disabled"]),s(U,{"enter-active-class":"transition ease-in-out","enter-from-class":"opacity-0","leave-active-class":"transition ease-in-out","leave-to-class":"opacity-0"},{default:a(()=>[N(r("p",ae,"Saved.",512),[[j,e(l).recentlySuccessful]])]),_:1})])],32)]),s(H)]),_:1})]),_:1}))}});export{ye as default};
