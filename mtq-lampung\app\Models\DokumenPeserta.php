<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;

class DokumenPeserta extends Model
{
    protected $table = 'dokumen_peserta';
    protected $primaryKey = 'id_dokumen';

    protected $fillable = [
        'id_pendaftaran',
        'jenis_dokumen',
        'nama_file',
        'path_file',
        'ukuran_file',
        'mime_type',
        'status_verifikasi',
        'catatan_verifikasi',
        'verified_by',
        'verified_at',
        'uploaded_by',
        'keterangan'
    ];

    protected $casts = [
        'jenis_dokumen' => 'string',
        'status_verifikasi' => 'string',
        'ukuran_file' => 'integer',
        'verified_at' => 'datetime'
    ];

    // Relationships
    public function pendaftaran(): BelongsTo
    {
        return $this->belongsTo(Pendaftaran::class, 'id_pendaftaran', 'id_pendaftaran');
    }

    public function verifiedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'verified_by', 'id_user');
    }

    public function uploadedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'uploaded_by', 'id_user');
    }

    public function verifikasiDokumen(): HasOne
    {
        return $this->hasOne(VerifikasiDokumen::class, 'id_dokumen', 'id_dokumen');
    }

    // Scopes
    public function scopeByJenis($query, $jenis)
    {
        return $query->where('jenis_dokumen', $jenis);
    }

    public function scopeByStatus($query, $status)
    {
        return $query->where('status_verifikasi', $status);
    }
}
