<template>
  <AppLayout :breadcrumbs="breadcrumbItems">
    <Head :title="`Detail Golongan: ${golongan.nama_golongan}`" />
    <Heading :title="`Detail Golongan: ${golongan.nama_golongan}`" />

    <div class="max-w-4xl mx-auto space-y-6">
      <!-- Golongan Information -->
      <Card>
        <CardHeader>
          <div class="flex justify-between items-start">
            <div>
              <CardTitle class="flex items-center gap-3">
                <div class="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center">
                  <Icon name="award" class="w-6 h-6 text-purple-600" />
                </div>
                {{ golongan.nama_golongan }}
              </CardTitle>
              <CardDescription>
                {{ golongan.kode_golongan }} • {{ golongan.cabang_lomba?.nama_cabang }}
              </CardDescription>
            </div>
            <div class="flex gap-2">
              <Badge :variant="golongan.jenis_kelamin === 'L' ? 'default' : 'secondary'">
                {{ golongan.jenis_kelamin === 'L' ? 'Laki-laki' : 'Perempuan' }}
              </Badge>
              <Badge :variant="getStatusVariant(golongan.status)">
                {{ getStatusLabel(golongan.status) }}
              </Badge>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="space-y-4">
              <div>
                <Label class="text-sm font-medium text-gray-500">Kode Golongan</Label>
                <p class="text-sm">{{ golongan.kode_golongan }}</p>
              </div>
              <div>
                <Label class="text-sm font-medium text-gray-500">Nama Golongan</Label>
                <p class="text-sm">{{ golongan.nama_golongan }}</p>
              </div>
              <div>
                <Label class="text-sm font-medium text-gray-500">Cabang Lomba</Label>
                <p class="text-sm">{{ golongan.cabang_lomba?.nama_cabang }} ({{ golongan.cabang_lomba?.kode_cabang }})</p>
              </div>
              <div>
                <Label class="text-sm font-medium text-gray-500">Jenis Kelamin</Label>
                <p class="text-sm">{{ golongan.jenis_kelamin === 'L' ? 'Laki-laki' : 'Perempuan' }}</p>
              </div>
            </div>
            <div class="space-y-4">
              <div>
                <Label class="text-sm font-medium text-gray-500">Batas Usia</Label>
                <p class="text-sm">{{ golongan.batas_umur_min }} - {{ golongan.batas_umur_max }} tahun</p>
              </div>
              <div>
                <Label class="text-sm font-medium text-gray-500">Kuota Maksimum</Label>
                <p class="text-sm">{{ golongan.kuota_max }} peserta</p>
              </div>
              <div>
                <Label class="text-sm font-medium text-gray-500">Biaya Pendaftaran</Label>
                <p class="text-sm font-medium text-green-600">Rp {{ formatCurrency(golongan.biaya_pendaftaran) }}</p>
              </div>
              <div>
                <Label class="text-sm font-medium text-gray-500">Range Nomor Urut</Label>
                <p class="text-sm">{{ golongan.nomor_urut_awal }} - {{ golongan.nomor_urut_akhir }}</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- Statistics -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent class="p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <Icon name="users" class="h-8 w-8 text-blue-600" />
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">Total Pendaftaran</p>
                <p class="text-2xl font-semibold text-gray-900">{{ golongan.pendaftaran?.length || 0 }}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent class="p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <Icon name="check-circle" class="h-8 w-8 text-green-600" />
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">Disetujui</p>
                <p class="text-2xl font-semibold text-gray-900">
                  {{ getApprovedCount() }}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent class="p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <Icon name="clock" class="h-8 w-8 text-yellow-600" />
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">Menunggu</p>
                <p class="text-2xl font-semibold text-gray-900">
                  {{ getPendingCount() }}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent class="p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <Icon name="percent" class="h-8 w-8 text-purple-600" />
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">Persentase Terisi</p>
                <p class="text-2xl font-semibold text-gray-900">
                  {{ getPercentageFilled() }}%
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <!-- Pendaftaran List -->
      <Card>
        <CardHeader>
          <div class="flex justify-between items-center">
            <div>
              <CardTitle>Daftar Pendaftaran</CardTitle>
              <CardDescription>
                Peserta yang mendaftar untuk golongan {{ golongan.nama_golongan }}
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div v-if="!golongan.pendaftaran || golongan.pendaftaran.length === 0" class="text-center py-8 text-gray-500">
            <Icon name="users" class="w-12 h-12 mx-auto mb-4 text-gray-300" />
            <p>Belum ada pendaftaran untuk golongan ini</p>
          </div>
          <div v-else class="space-y-4">
            <div
              v-for="pendaftaran in golongan.pendaftaran"
              :key="pendaftaran.id_pendaftaran"
              class="border rounded-lg p-4 hover:bg-gray-50"
            >
              <div class="flex justify-between items-start">
                <div class="flex-1">
                  <div class="flex items-center gap-3 mb-2">
                    <h4 class="font-medium">{{ pendaftaran.peserta?.nama_lengkap }}</h4>
                    <Badge :variant="getStatusVariant(pendaftaran.status_pendaftaran)">
                      {{ pendaftaran.status_pendaftaran }}
                    </Badge>
                  </div>
                  <p class="text-sm text-gray-500 mb-2">{{ pendaftaran.nomor_pendaftaran }}</p>
                  <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <span class="text-gray-500">NIK:</span>
                      <p class="font-medium">{{ pendaftaran.peserta?.nik }}</p>
                    </div>
                    <div>
                      <span class="text-gray-500">Umur:</span>
                      <p class="font-medium">{{ calculateAge(pendaftaran.peserta?.tanggal_lahir) }} tahun</p>
                    </div>
                    <div>
                      <span class="text-gray-500">Wilayah:</span>
                      <p class="font-medium">{{ pendaftaran.peserta?.wilayah?.nama_wilayah }}</p>
                    </div>
                    <div>
                      <span class="text-gray-500">Tgl Daftar:</span>
                      <p class="font-medium">{{ formatDate(pendaftaran.created_at) }}</p>
                    </div>
                  </div>
                </div>
                <div class="flex gap-2 ml-4">
                  <Button
                    variant="ghost"
                    size="sm"
                    @click="$inertia.visit(route('admin.pendaftaran.show', pendaftaran.id_pendaftaran))"
                  >
                    <Icon name="eye" class="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- Actions -->
      <div class="flex justify-between">
        <Button
          variant="outline"
          @click="$inertia.visit(route('admin.golongan.index'))"
        >
          <Icon name="arrow-left" class="w-4 h-4 mr-2" />
          Kembali
        </Button>
        <div class="flex gap-2">
          <Button @click="$inertia.visit(route('admin.golongan.edit', golongan.id_golongan))">
            <Icon name="edit" class="w-4 h-4 mr-2" />
            Edit Golongan
          </Button>
        </div>
      </div>
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import { Head } from '@inertiajs/vue3'
import AppLayout from '@/layouts/AppLayout.vue'
import Heading from '@/components/Heading.vue'
import Button from '@/components/ui/button/Button.vue'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Label } from '@/components/ui/label'
import Icon from '@/components/Icon.vue'
import { type BreadcrumbItem } from '@/types'

interface CabangLomba {
  id_cabang: number
  kode_cabang: string
  nama_cabang: string
}

interface Peserta {
  nama_lengkap: string
  nik: string
  tanggal_lahir: string
  wilayah?: {
    nama_wilayah: string
  }
}

interface Pendaftaran {
  id_pendaftaran: number
  nomor_pendaftaran: string
  status_pendaftaran: string
  created_at: string
  peserta?: Peserta
}

interface Golongan {
  id_golongan: number
  kode_golongan: string
  nama_golongan: string
  jenis_kelamin: string
  batas_umur_min: number
  batas_umur_max: number
  kuota_max: number
  biaya_pendaftaran: number
  nomor_urut_awal: number
  nomor_urut_akhir: number
  status: string
  cabang_lomba?: CabangLomba
  pendaftaran?: Pendaftaran[]
}

const props = defineProps<{
  golongan: Golongan
}>()

const breadcrumbItems: BreadcrumbItem[] = [
  { title: 'Dashboard', href: '/admin/dashboard' },
  { title: 'Manajemen Golongan', href: '/admin/golongan' },
  { title: 'Detail Golongan', href: `/admin/golongan/${props.golongan.id_golongan}` }
]

const getStatusVariant = (status: string) => {
  const variants: Record<string, string> = {
    aktif: 'default',
    non_aktif: 'secondary',
    approved: 'default',
    pending: 'secondary',
    draft: 'outline'
  }
  return variants[status] || 'secondary'
}

const getStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    aktif: 'Aktif',
    non_aktif: 'Non Aktif',
    approved: 'Disetujui',
    pending: 'Menunggu',
    draft: 'Draft'
  }
  return labels[status] || status
}

const getApprovedCount = () => {
  if (!props.golongan.pendaftaran) return 0
  return props.golongan.pendaftaran.filter(p => p.status_pendaftaran === 'approved').length
}

const getPendingCount = () => {
  if (!props.golongan.pendaftaran) return 0
  return props.golongan.pendaftaran.filter(p => p.status_pendaftaran === 'pending' || p.status_pendaftaran === 'draft').length
}

const getPercentageFilled = () => {
  const total = props.golongan.pendaftaran?.length || 0
  const percentage = (total / props.golongan.kuota_max) * 100
  return Math.round(percentage)
}

const calculateAge = (birthDate: string) => {
  const today = new Date()
  const birth = new Date(birthDate)
  let age = today.getFullYear() - birth.getFullYear()
  const monthDiff = today.getMonth() - birth.getMonth()
  
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
    age--
  }
  
  return age
}

const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString('id-ID', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('id-ID').format(amount)
}
</script>
