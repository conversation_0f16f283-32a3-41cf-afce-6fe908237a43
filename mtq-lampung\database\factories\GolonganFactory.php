<?php

namespace Database\Factories;

use App\Models\CabangLomba;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Golongan>
 */
class GolonganFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $jenisKelamin = fake()->randomElement(['L', 'P']);
        $kategoriUmur = fake()->randomElement([
            ['min' => 10, 'max' => 12, 'kategori' => 'Anak-anak'],
            ['min' => 13, 'max' => 16, 'kategori' => 'Remaja'],
            ['min' => 17, 'max' => 25, 'kategori' => 'Dewasa'],
            ['min' => 26, 'max' => 40, 'kategori' => 'Dewasa Tua'],
        ]);

        $cabang = CabangLomba::inRandomOrder()->first();
        $kodeGolongan = $cabang ? $cabang->kode_cabang . '-' . substr($kategoriUmur['kategori'], 0, 2) . ($jenisKelamin === 'L' ? 'Pa' : 'Pi') : 'GOL-' . fake()->randomNumber(2);
        $namaGolongan = $cabang ? $cabang->nama_cabang . ' ' . ($jenisKelamin === 'L' ? 'Putra' : 'Putri') . ' ' . $kategoriUmur['kategori'] : fake()->words(3, true);

        return [
            'kode_golongan' => $kodeGolongan,
            'nama_golongan' => $namaGolongan,
            'id_cabang' => $cabang?->id_cabang ?? 1,
            'jenis_kelamin' => $jenisKelamin,
            'batas_umur_min' => $kategoriUmur['min'],
            'batas_umur_max' => $kategoriUmur['max'],
            'kuota_max' => fake()->numberBetween(20, 100),
            'biaya_pendaftaran' => fake()->randomElement([50000, 75000, 100000, 150000, 200000]),
            'nomor_urut_awal' => 1,
            'nomor_urut_akhir' => 999,
            'status' => 'aktif',
        ];
    }

    /**
     * Create a male golongan.
     */
    public function male(): static
    {
        return $this->state(fn (array $attributes) => [
            'jenis_kelamin' => 'L',
            'nama_golongan' => str_replace(['Putri', 'Pi'], ['Putra', 'Pa'], $attributes['nama_golongan'] ?? ''),
            'kode_golongan' => str_replace('Pi', 'Pa', $attributes['kode_golongan'] ?? ''),
        ]);
    }

    /**
     * Create a female golongan.
     */
    public function female(): static
    {
        return $this->state(fn (array $attributes) => [
            'jenis_kelamin' => 'P',
            'nama_golongan' => str_replace(['Putra', 'Pa'], ['Putri', 'Pi'], $attributes['nama_golongan'] ?? ''),
            'kode_golongan' => str_replace('Pa', 'Pi', $attributes['kode_golongan'] ?? ''),
        ]);
    }

    /**
     * Create an active golongan.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'aktif',
        ]);
    }

    /**
     * Create a children category golongan.
     */
    public function children(): static
    {
        return $this->state(fn (array $attributes) => [
            'batas_umur_min' => 10,
            'batas_umur_max' => 12,
            'biaya_pendaftaran' => 50000,
        ]);
    }

    /**
     * Create a teen category golongan.
     */
    public function teen(): static
    {
        return $this->state(fn (array $attributes) => [
            'batas_umur_min' => 13,
            'batas_umur_max' => 16,
            'biaya_pendaftaran' => 75000,
        ]);
    }

    /**
     * Create an adult category golongan.
     */
    public function adult(): static
    {
        return $this->state(fn (array $attributes) => [
            'batas_umur_min' => 17,
            'batas_umur_max' => 25,
            'biaya_pendaftaran' => 100000,
        ]);
    }
}
