import{P as K,r as ne,S as B}from"./AppLogoIcon.vue_vue_type_script_setup_true_lang-wzz4PWna.js";import{a7 as se,a8 as oe,U as re,a9 as ie,aa as le,ab as ue,ac as ce,F as de,ad as fe,ae as me,af as pe,ag as ve,T as be,ah as he,ai as ye,aj as ge,ak as Ee,al as Se,am as we,an as Ce,ao as Ae,ap as Te,aq as N,ar as Ie,_ as Re,as as Me,k as C,at as Pe,c as _,i as Oe,h as Fe,b as xe,au as Ne,av as _e,aw as De,ax as $e,ay as ke,az as Ue,e as Ve,a as G,a6 as je,aA as Be,d as I,aB as Le,aC as He,aD as ze,aE as Ke,aF as Ge,aG as qe,aH as We,aI as Ye,aJ as Qe,aK as Je,a5 as Xe,a2 as x,z as Ze,aL as et,aM as tt,P as at,$ as O,aN as nt,a3 as st,aO as ot,aP as rt,aQ as it,aR as lt,aS as ut,aT as ct,aU as dt,a4 as D,aV as ft,aW as mt,aX as pt,aY as vt,Q as bt,aZ as ht,a_ as yt,a$ as gt,b0 as q,N as Et,b1 as St,E as wt,H as W,n as Ct,O as At,p as Tt,b2 as It,b3 as Rt,a0 as Mt,b4 as Pt,b5 as Ot,b6 as Ft,J as xt,b7 as Nt,b8 as _t,A as Dt,b9 as $t,K as Y,ba as kt,bb as Ut,o as $,bc as Vt,bd as k,be as jt,bf as Bt,bg as Lt,r as Ht,a1 as zt,l as v,bh as Kt,bi as Gt,m as qt,D as U,bj as Wt,bk as Yt,V as Qt,bl as Jt,bm as Xt,bn as Zt,bo as ea,bp as ta,bq as aa,B as na,y as sa,br as oa,bs as ra,bt as ia,t as la,bu as Q,X as ua,bv as ca,bw as J,C as V,I as da,bx as fa,by as ma,u as T,bz as pa,bA as va,bB as ba,bC as ha,bD as X,bE as ya,bF as ga,bG as Ea,S as Sa,bH as wa,bI as Ca,bJ as Aa,bK as Ta,bL as Ia,q as Ra,v as Ma,L as Pa,bM as Oa,bN as Fa,s as M,G as Z,M as xa,bO as Na,bP as _a,w as F,bQ as Da,j as $a,R as ka,bR as Ua,f as Va,bS as ja}from"./app-B_pmlBSQ.js";import{p as Ba,i as La,b as ee,u as Ha}from"./useForwardExpose-CO14IhkA.js";/**
* vue v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/const za=()=>{},Ka=Object.freeze(Object.defineProperty({__proto__:null,BaseTransition:se,BaseTransitionPropsValidators:oe,Comment:re,DeprecationTypes:ie,EffectScope:le,ErrorCodes:ue,ErrorTypeStrings:ce,Fragment:de,KeepAlive:fe,ReactiveEffect:me,Static:pe,Suspense:ve,Teleport:be,Text:he,TrackOpTypes:ye,Transition:ge,TransitionGroup:Ee,TriggerOpTypes:Se,VueElement:we,assertNumber:Ce,callWithAsyncErrorHandling:Ae,callWithErrorHandling:Te,camelize:N,capitalize:Ie,cloneVNode:Re,compatUtils:Me,compile:za,computed:C,createApp:Pe,createBlock:_,createCommentVNode:Oe,createElementBlock:Fe,createElementVNode:xe,createHydrationRenderer:Ne,createPropsRestProxy:_e,createRenderer:De,createSSRApp:$e,createSlots:ke,createStaticVNode:Ue,createTextVNode:Ve,createVNode:G,customRef:je,defineAsyncComponent:Be,defineComponent:I,defineCustomElement:Le,defineEmits:He,defineExpose:ze,defineModel:Ke,defineOptions:Ge,defineProps:qe,defineSSRCustomElement:We,defineSlots:Ye,devtools:Qe,effect:Je,effectScope:Xe,getCurrentInstance:x,getCurrentScope:Ze,getCurrentWatcher:et,getTransitionRawChildren:tt,guardReactiveProps:at,h:O,handleError:nt,hasInjectionContext:st,hydrate:ot,hydrateOnIdle:rt,hydrateOnInteraction:it,hydrateOnMediaQuery:lt,hydrateOnVisible:ut,initCustomFormatter:ct,initDirectivesForSSR:dt,inject:D,isMemoSame:ft,isProxy:mt,isReactive:pt,isReadonly:vt,isRef:bt,isRuntimeOnly:ht,isShallow:yt,isVNode:gt,markRaw:q,mergeDefaults:Et,mergeModels:St,mergeProps:wt,nextTick:W,normalizeClass:Ct,normalizeProps:At,normalizeStyle:Tt,onActivated:It,onBeforeMount:Rt,onBeforeUnmount:Mt,onBeforeUpdate:Pt,onDeactivated:Ot,onErrorCaptured:Ft,onMounted:xt,onRenderTracked:Nt,onRenderTriggered:_t,onScopeDispose:Dt,onServerPrefetch:$t,onUnmounted:Y,onUpdated:kt,onWatcherCleanup:Ut,openBlock:$,popScopeId:Vt,provide:k,proxyRefs:jt,pushScopeId:Bt,queuePostFlushCb:Lt,reactive:Ht,readonly:zt,ref:v,registerRuntimeCompiler:Kt,render:Gt,renderList:qt,renderSlot:U,resolveComponent:Wt,resolveDirective:Yt,resolveDynamicComponent:Qt,resolveFilter:Jt,resolveTransitionHooks:Xt,setBlockTracking:Zt,setDevtoolsHook:ea,setTransitionHooks:ta,shallowReactive:aa,shallowReadonly:na,shallowRef:sa,ssrContextKey:oa,ssrUtils:ra,stop:ia,toDisplayString:la,toHandlerKey:Q,toHandlers:ua,toRaw:ca,toRef:J,toRefs:V,toValue:da,transformVNodeArgs:fa,triggerRef:ma,unref:T,useAttrs:pa,useCssModule:va,useCssVars:ba,useHost:ha,useId:X,useModel:ya,useSSRContext:ga,useShadowRoot:Ea,useSlots:Sa,useTemplateRef:wa,useTransitionState:Ca,vModelCheckbox:Aa,vModelDynamic:Ta,vModelRadio:Ia,vModelSelect:Ra,vModelText:Ma,vShow:Pa,version:Oa,warn:Fa,watch:M,watchEffect:Z,watchPostEffect:xa,watchSyncEffect:Na,withAsyncContext:_a,withCtx:F,withDefaults:Da,withDirectives:$a,withKeys:ka,withMemo:Ua,withModifiers:Va,withScopeId:ja},Symbol.toStringTag,{value:"Module"})),un=I({__name:"VisuallyHidden",props:{feature:{default:"focusable"},asChild:{type:Boolean},as:{default:"span"}},setup(t){return(e,n)=>($(),_(T(K),{as:e.as,"as-child":e.asChild,"aria-hidden":e.feature==="focusable"?"true":void 0,"data-hidden":e.feature==="fully-hidden"?"":void 0,tabindex:e.feature==="fully-hidden"?"-1":void 0,style:{position:"absolute",border:0,width:"1px",height:"1px",padding:0,margin:"-1px",overflow:"hidden",clip:"rect(0, 0, 0, 0)",clipPath:"inset(50%)",whiteSpace:"nowrap",wordWrap:"normal"}},{default:F(()=>[U(e.$slots,"default")]),_:3},8,["as","as-child","aria-hidden","data-hidden","tabindex"]))}});function te(t,e){const n=typeof t=="string"&&!e?`${t}Context`:e,a=Symbol(n);return[i=>{const l=D(a,i);if(l||l===null)return l;throw new Error(`Injection \`${a.toString()}\` not found. Component must be used within ${Array.isArray(t)?`one of the following components: ${t.join(", ")}`:`\`${t}\``}`)},i=>(k(a,i),i)]}const[ae,cn]=te("ConfigProvider");function Ga(t){const e=ae({dir:v("ltr")});return C(()=>{var n;return(t==null?void 0:t.value)||((n=e.dir)==null?void 0:n.value)||"ltr"})}let qa=0;function dn(t,e="reka"){var a;const n=ae({useId:void 0});return Object.hasOwn(Ka,"useId")?`${e}-${(a=X)==null?void 0:a()}`:n.useId?`${e}-${n.useId()}`:`${e}-${++qa}`}function Wa(t,e){const n=v(t);function a(d){return e[n.value][d]??n.value}return{state:n,dispatch:d=>{n.value=a(d)}}}function Ya(t,e){var g;const n=v({}),a=v("none"),r=v(t),d=t.value?"mounted":"unmounted";let i;const l=((g=e.value)==null?void 0:g.ownerDocument.defaultView)??Ba,{state:m,dispatch:f}=Wa(d,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}}),c=o=>{var u;if(La){const b=new CustomEvent(o,{bubbles:!1,cancelable:!1});(u=e.value)==null||u.dispatchEvent(b)}};M(t,async(o,u)=>{var w;const b=u!==o;if(await W(),b){const R=a.value,E=P(e.value);o?(f("MOUNT"),c("enter"),E==="none"&&c("after-enter")):E==="none"||E==="undefined"||((w=n.value)==null?void 0:w.display)==="none"?(f("UNMOUNT"),c("leave"),c("after-leave")):u&&R!==E?(f("ANIMATION_OUT"),c("leave")):(f("UNMOUNT"),c("after-leave"))}},{immediate:!0});const s=o=>{const u=P(e.value),b=u.includes(o.animationName),w=m.value==="mounted"?"enter":"leave";if(o.target===e.value&&b&&(c(`after-${w}`),f("ANIMATION_END"),!r.value)){const R=e.value.style.animationFillMode;e.value.style.animationFillMode="forwards",i=l==null?void 0:l.setTimeout(()=>{var E;((E=e.value)==null?void 0:E.style.animationFillMode)==="forwards"&&(e.value.style.animationFillMode=R)})}o.target===e.value&&u==="none"&&f("ANIMATION_END")},p=o=>{o.target===e.value&&(a.value=P(e.value))},h=M(e,(o,u)=>{o?(n.value=getComputedStyle(o),o.addEventListener("animationstart",p),o.addEventListener("animationcancel",s),o.addEventListener("animationend",s)):(f("ANIMATION_END"),i!==void 0&&(l==null||l.clearTimeout(i)),u==null||u.removeEventListener("animationstart",p),u==null||u.removeEventListener("animationcancel",s),u==null||u.removeEventListener("animationend",s))},{immediate:!0}),y=M(m,()=>{const o=P(e.value);a.value=m.value==="mounted"?o:"none"});return Y(()=>{h(),y()}),{isPresent:C(()=>["mounted","unmountSuspended"].includes(m.value))}}function P(t){return t&&getComputedStyle(t).animationName||"none"}const fn=I({name:"Presence",props:{present:{type:Boolean,required:!0},forceMount:{type:Boolean}},slots:{},setup(t,{slots:e,expose:n}){var f;const{present:a,forceMount:r}=V(t),d=v(),{isPresent:i}=Ya(a,d);n({present:i});let l=e.default({present:i.value});l=ne(l||[]);const m=x();if(l&&(l==null?void 0:l.length)>1){const c=(f=m==null?void 0:m.parent)!=null&&f.type.name?`<${m.parent.type.name} />`:"component";throw new Error([`Detected an invalid children for \`${c}\` for  \`Presence\` component.`,"","Note: Presence works similarly to `v-if` directly, but it waits for animation/transition to finished before unmounting. So it expect only one direct child of valid VNode type.","You can apply a few solutions:",["Provide a single child element so that `presence` directive attach correctly.","Ensure the first child is an actual element instead of a raw text node or comment node."].map(s=>`  - ${s}`).join(`
`)].join(`
`))}return()=>r.value||a.value||i.value?O(e.default({present:i.value})[0],{ref:c=>{const s=ee(c);return typeof(s==null?void 0:s.hasAttribute)>"u"||(s!=null&&s.hasAttribute("data-reka-popper-content-wrapper")?d.value=s.firstElementChild:d.value=s),s}}):null}});function Qa(t){const e=x(),n=e==null?void 0:e.type.emits,a={};return n!=null&&n.length||console.warn(`No emitted event found. Please check component: ${e==null?void 0:e.type.__name}`),n==null||n.forEach(r=>{a[Q(N(r))]=(...d)=>t(r,...d)}),a}function L(){let t=document.activeElement;if(t==null)return null;for(;t!=null&&t.shadowRoot!=null&&t.shadowRoot.activeElement!=null;)t=t.shadowRoot.activeElement;return t}function Ja(t){const e=x(),n=Object.keys((e==null?void 0:e.type.props)??{}).reduce((r,d)=>{const i=(e==null?void 0:e.type.props[d]).default;return i!==void 0&&(r[d]=i),r},{}),a=J(t);return C(()=>{const r={},d=(e==null?void 0:e.vnode.props)??{};return Object.keys(d).forEach(i=>{r[N(i)]=d[i]}),Object.keys({...n,...r}).reduce((i,l)=>(a.value[l]!==void 0&&(i[l]=a.value[l]),i),{})})}function mn(t,e){const n=Ja(t),a=e?Qa(e):{};return C(()=>({...n.value,...a}))}function H(){const t=v(),e=C(()=>{var n,a;return["#text","#comment"].includes((n=t.value)==null?void 0:n.$el.nodeName)?(a=t.value)==null?void 0:a.$el.nextElementSibling:ee(t)});return{primitiveElement:t,currentElement:e}}const z="data-reka-collection-item";function Xa(t={}){const{key:e="",isProvider:n=!1}=t,a=`${e}CollectionProvider`;let r;if(n){const c=v(new Map);r={collectionRef:v(),itemMap:c},k(a,r)}else r=D(a);const d=(c=!1)=>{const s=r.collectionRef.value;if(!s)return[];const p=Array.from(s.querySelectorAll(`[${z}]`)),y=Array.from(r.itemMap.value.values()).sort((S,g)=>p.indexOf(S.ref)-p.indexOf(g.ref));return c?y:y.filter(S=>S.ref.dataset.disabled!=="")},i=I({name:"CollectionSlot",setup(c,{slots:s}){const{primitiveElement:p,currentElement:h}=H();return M(h,()=>{r.collectionRef.value=h.value}),()=>O(B,{ref:p},s)}}),l=I({name:"CollectionItem",inheritAttrs:!1,props:{value:{validator:()=>!0}},setup(c,{slots:s,attrs:p}){const{primitiveElement:h,currentElement:y}=H();return Z(S=>{if(y.value){const g=q(y.value);r.itemMap.value.set(g,{ref:y.value,value:c.value}),S(()=>r.itemMap.value.delete(g))}}),()=>O(B,{...p,[z]:"",ref:h},s)}}),m=C(()=>Array.from(r.itemMap.value.values())),f=C(()=>r.itemMap.value.size);return{getItems:d,reactiveItems:m,itemMapSize:f,CollectionSlot:i,CollectionItem:l}}const Za="rovingFocusGroup.onEntryFocus",en={bubbles:!1,cancelable:!0},tn={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function an(t,e){return e!=="rtl"?t:t==="ArrowLeft"?"ArrowRight":t==="ArrowRight"?"ArrowLeft":t}function pn(t,e,n){const a=an(t.key,n);if(!(e==="vertical"&&["ArrowLeft","ArrowRight"].includes(a))&&!(e==="horizontal"&&["ArrowUp","ArrowDown"].includes(a)))return tn[a]}function nn(t,e=!1){const n=L();for(const a of t)if(a===n||(a.focus({preventScroll:e}),L()!==n))return}function vn(t,e){return t.map((n,a)=>t[(e+a)%t.length])}const[bn,sn]=te("RovingFocusGroup"),hn=I({__name:"RovingFocusGroup",props:{orientation:{default:void 0},dir:{},loop:{type:Boolean,default:!1},currentTabStopId:{},defaultCurrentTabStopId:{},preventScrollOnEntryFocus:{type:Boolean,default:!1},asChild:{type:Boolean},as:{}},emits:["entryFocus","update:currentTabStopId"],setup(t,{expose:e,emit:n}){const a=t,r=n,{loop:d,orientation:i,dir:l}=V(a),m=Ga(l),f=Ha(a,"currentTabStopId",r,{defaultValue:a.defaultCurrentTabStopId,passive:a.currentTabStopId===void 0}),c=v(!1),s=v(!1),p=v(0),{getItems:h,CollectionSlot:y}=Xa({isProvider:!0});function S(o){const u=!s.value;if(o.currentTarget&&o.target===o.currentTarget&&u&&!c.value){const b=new CustomEvent(Za,en);if(o.currentTarget.dispatchEvent(b),r("entryFocus",b),!b.defaultPrevented){const w=h().map(A=>A.ref).filter(A=>A.dataset.disabled!==""),R=w.find(A=>A.getAttribute("data-active")===""),E=w.find(A=>A.id===f.value),j=[R,E,...w].filter(Boolean);nn(j,a.preventScrollOnEntryFocus)}}s.value=!1}function g(){setTimeout(()=>{s.value=!1},1)}return e({getItems:h}),sn({loop:d,dir:m,orientation:i,currentTabStopId:f,onItemFocus:o=>{f.value=o},onItemShiftTab:()=>{c.value=!0},onFocusableItemAdd:()=>{p.value++},onFocusableItemRemove:()=>{p.value--}}),(o,u)=>($(),_(T(y),null,{default:F(()=>[G(T(K),{tabindex:c.value||p.value===0?-1:0,"data-orientation":T(i),as:o.as,"as-child":o.asChild,dir:T(m),style:{outline:"none"},onMousedown:u[0]||(u[0]=b=>s.value=!0),onMouseup:g,onFocus:S,onBlur:u[1]||(u[1]=b=>c.value=!1)},{default:F(()=>[U(o.$slots,"default")]),_:3},8,["tabindex","data-orientation","as","as-child","dir"])]),_:3}))}});export{fn as P,hn as _,Qa as a,Ga as b,te as c,Xa as d,mn as e,un as f,L as g,Ja as h,ae as i,H as j,bn as k,pn as l,nn as m,dn as u,vn as w};
