import{_ as V}from"./AppLayout.vue_vue_type_script_setup_true_lang-BRf8lUiM.js";import{d as z,c as p,o as i,w as r,a as n,b as t,u as s,g as E,i as d,e as o,t as l,n as c,h as f,F as L,m as q}from"./app-B_pmlBSQ.js";import{_ as M}from"./Heading.vue_vue_type_script_setup_true_lang-is4VBSCS.js";import{_ as g}from"./AppLogoIcon.vue_vue_type_script_setup_true_lang-wzz4PWna.js";import{_,a as y}from"./CardContent.vue_vue_type_script_setup_true_lang-ClvLNBXC.js";import{_ as $}from"./CardDescription.vue_vue_type_script_setup_true_lang-DboywBIK.js";import{_ as b,a as v}from"./CardTitle.vue_vue_type_script_setup_true_lang-5HO6oSoB.js";import{_ as w}from"./index-CMGr3-bt.js";import{_ as D,a as h}from"./index-Cae_Ab9-.js";import{_ as m}from"./Icon.vue_vue_type_script_setup_true_lang-BuYzjckr.js";import"./useForwardExpose-CO14IhkA.js";import"./RovingFocusGroup-lsWyU4xZ.js";import"./check-DtmHjdxf.js";import"./loader-circle-6d8QrWFr.js";import"./sun-CHIV9RJ8.js";const R={class:"flex items-center space-x-4"},A={class:"space-y-6"},G={class:"flex items-center justify-between"},J={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"},U={class:"font-mono text-lg"},H={class:"font-mono text-lg"},O={class:"text-lg font-bold text-green-600"},Q={class:"space-y-4"},W={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},X={class:"space-y-4"},Y={class:"font-mono"},Z={class:"text-lg font-bold"},aa={class:"space-y-4"},ea={key:0},ta={key:1},na={class:"text-sm"},ra={key:0,class:"text-center py-8"},sa={key:1,class:"space-y-4"},la={class:"flex items-center space-x-3"},ia={class:"font-medium"},oa={class:"text-sm text-gray-600"},da={key:0,class:"text-xs text-gray-500 mt-1"},ua={class:"text-right"},fa={key:0,class:"text-xs text-gray-500 mt-1"},ma={class:"space-y-4"},pa={key:0},ga={class:"text-sm"},_a={key:1},ya={class:"text-sm"},ka={key:2},ba={class:"text-sm"},va={class:"flex flex-wrap gap-4"},Fa=z({__name:"Show",props:{pendaftaran:{}},setup(C){const k=C;function T(e){return{draft:"bg-gray-100 text-gray-800",submitted:"bg-blue-100 text-blue-800",payment_pending:"bg-yellow-100 text-yellow-800",paid:"bg-green-100 text-green-800",verified:"bg-indigo-100 text-indigo-800",approved:"bg-green-100 text-green-800",rejected:"bg-red-100 text-red-800"}[e]||"bg-gray-100 text-gray-800"}function B(e){return{draft:"Draft",submitted:"Disubmit",payment_pending:"Menunggu Pembayaran",paid:"Sudah Dibayar",verified:"Terverifikasi",approved:"Disetujui",rejected:"Ditolak"}[e]||e}function I(e){return{pending:"bg-yellow-100 text-yellow-800",paid:"bg-green-100 text-green-800",failed:"bg-red-100 text-red-800",expired:"bg-gray-100 text-gray-800"}[e]||"bg-gray-100 text-gray-800"}function N(e){return{pending:"bg-yellow-100 text-yellow-800",approved:"bg-green-100 text-green-800",rejected:"bg-red-100 text-red-800"}[e]||"bg-gray-100 text-gray-800"}function S(e){return new Intl.NumberFormat("id-ID",{style:"currency",currency:"IDR",minimumFractionDigits:0}).format(e)}function x(e){return new Date(e).toLocaleDateString("id-ID",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"})}function K(e){return{foto:"Foto",ktp:"KTP",kartu_keluarga:"Kartu Keluarga",surat_rekomendasi:"Surat Rekomendasi",ijazah:"Ijazah",sertifikat:"Sertifikat",lainnya:"Lainnya"}[e]||e}function P(){return["draft","payment_pending"].includes(k.pendaftaran.status_pendaftaran)}function j(){return!k.pendaftaran.pembayaran||k.pendaftaran.pembayaran.status_pembayaran==="pending"}function F(){return["foto","ktp","kartu_keluarga"].some(a=>!k.pendaftaran.dokumen_peserta.some(u=>u.jenis_dokumen===a&&u.status_verifikasi==="approved"))}return(e,a)=>(i(),p(V,null,{header:r(()=>[t("div",R,[n(g,{as:"link",href:e.route("peserta.pendaftaran.index"),variant:"ghost",size:"sm"},{default:r(()=>[n(m,{name:"arrow-left",class:"w-4 h-4 mr-2"}),a[0]||(a[0]=o(" Kembali "))]),_:1,__:[0]},8,["href"]),n(M,null,{default:r(()=>a[1]||(a[1]=[o("Detail Pendaftaran")])),_:1,__:[1]})])]),default:r(()=>[n(s(E),{title:"Detail Pendaftaran"}),t("div",A,[n(s(_),null,{default:r(()=>[n(s(b),null,{default:r(()=>[t("div",G,[n(s(v),null,{default:r(()=>[o(l(e.pendaftaran.golongan.nama_golongan),1)]),_:1}),n(s(w),{class:c(T(e.pendaftaran.status_pendaftaran))},{default:r(()=>[o(l(B(e.pendaftaran.status_pendaftaran)),1)]),_:1},8,["class"])]),n(s($),null,{default:r(()=>[o(l(e.pendaftaran.golongan.cabang_lomba.nama_cabang),1)]),_:1})]),_:1}),n(s(y),null,{default:r(()=>[t("div",J,[t("div",null,[a[2]||(a[2]=t("p",{class:"text-sm font-medium text-gray-500"},"No. Pendaftaran",-1)),t("p",U,l(e.pendaftaran.nomor_pendaftaran),1)]),t("div",null,[a[3]||(a[3]=t("p",{class:"text-sm font-medium text-gray-500"},"No. Peserta",-1)),t("p",H,l(e.pendaftaran.nomor_peserta),1)]),t("div",null,[a[4]||(a[4]=t("p",{class:"text-sm font-medium text-gray-500"},"Tanggal Daftar",-1)),t("p",null,l(x(e.pendaftaran.tanggal_daftar)),1)]),t("div",null,[a[5]||(a[5]=t("p",{class:"text-sm font-medium text-gray-500"},"Biaya Pendaftaran",-1)),t("p",O,l(S(e.pendaftaran.golongan.biaya_pendaftaran)),1)])])]),_:1})]),_:1}),t("div",Q,[j()?(i(),p(s(D),{key:0,class:"border-yellow-200 bg-yellow-50"},{default:r(()=>[n(m,{name:"credit-card",class:"h-4 w-4"}),n(s(h),null,{default:r(()=>a[6]||(a[6]=[o(" Pembayaran belum dilakukan. Silakan lakukan pembayaran untuk melanjutkan proses pendaftaran. ")])),_:1,__:[6]})]),_:1})):d("",!0),F()?(i(),p(s(D),{key:1,class:"border-blue-200 bg-blue-50"},{default:r(()=>[n(m,{name:"file-text",class:"h-4 w-4"}),n(s(h),null,{default:r(()=>a[7]||(a[7]=[o(" Dokumen belum lengkap. Silakan upload dokumen yang diperlukan. ")])),_:1,__:[7]})]),_:1})):d("",!0),e.pendaftaran.status_pendaftaran==="rejected"?(i(),p(s(D),{key:2,class:"border-red-200 bg-red-50"},{default:r(()=>[n(m,{name:"x-circle",class:"h-4 w-4"}),n(s(h),null,{default:r(()=>[o(" Pendaftaran ditolak. "+l(e.pendaftaran.catatan_approval||"Silakan hubungi admin untuk informasi lebih lanjut."),1)]),_:1})]),_:1})):d("",!0)]),e.pendaftaran.pembayaran?(i(),p(s(_),{key:0},{default:r(()=>[n(s(b),null,{default:r(()=>[n(s(v),null,{default:r(()=>a[8]||(a[8]=[o("Informasi Pembayaran")])),_:1,__:[8]})]),_:1}),n(s(y),null,{default:r(()=>[t("div",W,[t("div",X,[t("div",null,[a[9]||(a[9]=t("p",{class:"text-sm font-medium text-gray-500"},"No. Transaksi",-1)),t("p",Y,l(e.pendaftaran.pembayaran.nomor_transaksi),1)]),t("div",null,[a[10]||(a[10]=t("p",{class:"text-sm font-medium text-gray-500"},"Jumlah",-1)),t("p",Z,l(S(e.pendaftaran.pembayaran.jumlah_bayar)),1)]),t("div",null,[a[11]||(a[11]=t("p",{class:"text-sm font-medium text-gray-500"},"Metode Pembayaran",-1)),t("p",null,l(e.pendaftaran.pembayaran.metode_pembayaran),1)])]),t("div",aa,[t("div",null,[a[12]||(a[12]=t("p",{class:"text-sm font-medium text-gray-500"},"Status",-1)),n(s(w),{class:c(I(e.pendaftaran.pembayaran.status_pembayaran))},{default:r(()=>[o(l(e.pendaftaran.pembayaran.status_pembayaran),1)]),_:1},8,["class"])]),e.pendaftaran.pembayaran.tanggal_bayar?(i(),f("div",ea,[a[13]||(a[13]=t("p",{class:"text-sm font-medium text-gray-500"},"Tanggal Bayar",-1)),t("p",null,l(x(e.pendaftaran.pembayaran.tanggal_bayar)),1)])):d("",!0),e.pendaftaran.pembayaran.catatan?(i(),f("div",ta,[a[14]||(a[14]=t("p",{class:"text-sm font-medium text-gray-500"},"Catatan",-1)),t("p",na,l(e.pendaftaran.pembayaran.catatan),1)])):d("",!0)])])]),_:1})]),_:1})):d("",!0),n(s(_),null,{default:r(()=>[n(s(b),null,{default:r(()=>[n(s(v),null,{default:r(()=>a[15]||(a[15]=[o("Dokumen")])),_:1,__:[15]}),n(s($),null,{default:r(()=>a[16]||(a[16]=[o("Status dokumen yang telah diupload")])),_:1,__:[16]})]),_:1}),n(s(y),null,{default:r(()=>[e.pendaftaran.dokumen_peserta.length===0?(i(),f("div",ra,[n(m,{name:"file-x",class:"h-12 w-12 text-gray-400 mx-auto mb-4"}),a[18]||(a[18]=t("h3",{class:"text-lg font-medium text-gray-900 mb-2"},"Belum Ada Dokumen",-1)),a[19]||(a[19]=t("p",{class:"text-gray-600 mb-4"},"Silakan upload dokumen yang diperlukan.",-1)),P()?(i(),p(g,{key:0,as:"link",href:e.route("peserta.pendaftaran.edit",e.pendaftaran.id_pendaftaran)},{default:r(()=>a[17]||(a[17]=[o(" Upload Dokumen ")])),_:1,__:[17]},8,["href"])):d("",!0)])):(i(),f("div",sa,[(i(!0),f(L,null,q(e.pendaftaran.dokumen_peserta,u=>(i(),f("div",{key:u.id_dokumen,class:"flex items-center justify-between p-4 border rounded-lg"},[t("div",la,[n(m,{name:"file-text",class:"h-5 w-5 text-gray-500"}),t("div",null,[t("p",ia,l(K(u.jenis_dokumen)),1),t("p",oa,l(u.nama_file),1),u.catatan_verifikasi?(i(),f("p",da,l(u.catatan_verifikasi),1)):d("",!0)])]),t("div",ua,[n(s(w),{class:c(N(u.status_verifikasi))},{default:r(()=>[o(l(u.status_verifikasi),1)]),_:2},1032,["class"]),u.verified_at?(i(),f("p",fa,l(x(u.verified_at)),1)):d("",!0)])]))),128))]))]),_:1})]),_:1}),e.pendaftaran.keterangan||e.pendaftaran.catatan_verifikasi||e.pendaftaran.catatan_approval?(i(),p(s(_),{key:1},{default:r(()=>[n(s(b),null,{default:r(()=>[n(s(v),null,{default:r(()=>a[20]||(a[20]=[o("Informasi Tambahan")])),_:1,__:[20]})]),_:1}),n(s(y),null,{default:r(()=>[t("div",ma,[e.pendaftaran.keterangan?(i(),f("div",pa,[a[21]||(a[21]=t("p",{class:"text-sm font-medium text-gray-500"},"Keterangan Pendaftaran",-1)),t("p",ga,l(e.pendaftaran.keterangan),1)])):d("",!0),e.pendaftaran.catatan_verifikasi?(i(),f("div",_a,[a[22]||(a[22]=t("p",{class:"text-sm font-medium text-gray-500"},"Catatan Verifikasi",-1)),t("p",ya,l(e.pendaftaran.catatan_verifikasi),1)])):d("",!0),e.pendaftaran.catatan_approval?(i(),f("div",ka,[a[23]||(a[23]=t("p",{class:"text-sm font-medium text-gray-500"},"Catatan Persetujuan",-1)),t("p",ba,l(e.pendaftaran.catatan_approval),1)])):d("",!0)])]),_:1})]),_:1})):d("",!0),n(s(_),null,{default:r(()=>[n(s(y),{class:"p-6"},{default:r(()=>[t("div",va,[P()?(i(),p(g,{key:0,as:"link",href:e.route("peserta.pendaftaran.edit",e.pendaftaran.id_pendaftaran)},{default:r(()=>[n(m,{name:"edit",class:"w-4 h-4 mr-2"}),a[24]||(a[24]=o(" Edit Pendaftaran "))]),_:1,__:[24]},8,["href"])):d("",!0),n(g,{as:"link",href:e.route("peserta.dokumen.index",e.pendaftaran.id_pendaftaran),variant:"outline"},{default:r(()=>[n(m,{name:"file-text",class:"w-4 h-4 mr-2"}),a[25]||(a[25]=o(" Kelola Dokumen "))]),_:1,__:[25]},8,["href"]),j()?(i(),p(g,{key:1,as:"link",href:e.route("peserta.pembayaran.create",e.pendaftaran.id_pendaftaran),variant:"outline"},{default:r(()=>[n(m,{name:"credit-card",class:"w-4 h-4 mr-2"}),a[26]||(a[26]=o(" Bayar Sekarang "))]),_:1,__:[26]},8,["href"])):d("",!0),n(g,{as:"link",href:e.route("competition.golongan",e.pendaftaran.golongan.id_golongan),variant:"outline"},{default:r(()=>[n(m,{name:"info",class:"w-4 h-4 mr-2"}),a[27]||(a[27]=o(" Info Golongan "))]),_:1,__:[27]},8,["href"])])]),_:1})]),_:1})])]),_:1}))}});export{Fa as default};
