<?php

namespace App\Http\Controllers\AdminDaerah;

use App\Http\Controllers\Controller;
use App\Models\Pendaftaran;
use App\Models\Peserta;
use App\Models\Golongan;
use App\Models\DokumenPeserta;
use App\Models\Pembayaran;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;
use Inertia\Response;

class PendaftaranController extends Controller
{
    /**
     * Display a listing of pendaftaran in admin daerah's wilayah
     */
    public function index(): Response
    {
        $adminWilayah = Auth::user()->id_wilayah;

        $pendaftaran = Pendaftaran::with([
            'peserta.user',
            'peserta.wilayah',
            'golongan.cabangLomba',
            'pembayaran',
            'dokumenPeserta'
        ])
        ->whereHas('peserta', function($q) use ($adminWilayah) {
            $q->where('id_wilayah', $adminWilayah);
        })
        ->orderBy('created_at', 'desc')
        ->paginate(20);

        // Get available golongan for filtering
        $golongan = Golongan::with('cabangLomba')
            ->where('status', 'aktif')
            ->orderBy('nama_golongan')
            ->get();

        return Inertia::render('AdminDaerah/Pendaftaran/Index', [
            'pendaftaran' => $pendaftaran,
            'golongan' => $golongan
        ]);
    }

    /**
     * Show the form for creating a new pendaftaran
     */
    public function create(Request $request): Response
    {
        $adminWilayah = Auth::user()->id_wilayah;

        // Get peserta from admin's wilayah
        $peserta = Peserta::with('user')
            ->where('id_wilayah', $adminWilayah)
            ->where('status_peserta', 'approved')
            ->orderBy('nama_lengkap')
            ->get();

        // Get available golongan
        $golongan = Golongan::with('cabangLomba')
            ->where('status', 'aktif')
            ->orderBy('nama_golongan')
            ->get();

        // If peserta ID is provided, get the specific peserta
        $selectedPeserta = null;
        if ($request->has('peserta')) {
            $selectedPeserta = Peserta::with('user')
                ->where('id_wilayah', $adminWilayah)
                ->findOrFail($request->peserta);
        }

        return Inertia::render('AdminDaerah/Pendaftaran/Create', [
            'peserta' => $peserta,
            'golongan' => $golongan,
            'selectedPeserta' => $selectedPeserta
        ]);
    }

    /**
     * Store a newly created pendaftaran
     */
    public function store(Request $request)
    {
        $adminWilayah = Auth::user()->id_wilayah;

        $validated = $request->validate([
            'id_peserta' => 'required|exists:peserta,id_peserta',
            'id_golongan' => 'required|exists:golongan,id_golongan',
            'keterangan' => 'nullable|string|max:500',
        ]);

        // Verify peserta belongs to admin's wilayah
        $peserta = Peserta::where('id_peserta', $validated['id_peserta'])
            ->where('id_wilayah', $adminWilayah)
            ->firstOrFail();

        // Check if peserta already registered for this golongan
        $existingPendaftaran = Pendaftaran::where('id_peserta', $validated['id_peserta'])
            ->where('id_golongan', $validated['id_golongan'])
            ->where('tahun_pendaftaran', date('Y'))
            ->first();

        if ($existingPendaftaran) {
            return back()->withErrors(['id_golongan' => 'Peserta sudah terdaftar pada golongan ini.']);
        }

        $golongan = Golongan::findOrFail($validated['id_golongan']);

        DB::transaction(function () use ($validated, $golongan, $peserta, $adminWilayah) {
            // Generate registration numbers
            $tahun = date('Y');
            $nomorPendaftaran = $this->generateNomorPendaftaran($tahun, $adminWilayah, $golongan->id_golongan);
            $nomorPeserta = $this->generateNomorPeserta($golongan, $tahun);
            $nomorUrut = $this->generateNomorUrut($golongan, $tahun);

            // Create pendaftaran
            $pendaftaran = Pendaftaran::create([
                'id_peserta' => $validated['id_peserta'],
                'id_golongan' => $validated['id_golongan'],
                'nomor_pendaftaran' => $nomorPendaftaran,
                'nomor_peserta' => $nomorPeserta,
                'nomor_urut' => $nomorUrut,
                'tahun_pendaftaran' => $tahun,
                'status_pendaftaran' => 'verified', // Admin daerah registration is automatically verified
                'tanggal_daftar' => now(),
                'registered_by' => Auth::id(),
                'verified_by' => Auth::id(),
                'verified_at' => now(),
                'keterangan' => $validated['keterangan'],
            ]);

            // Also update peserta status to verified if registered by admin daerah
            $peserta->update([
                'status_peserta' => 'verified'
            ]);

            // Create pembayaran record
            Pembayaran::create([
                'id_pendaftaran' => $pendaftaran->id_pendaftaran,
                'nomor_transaksi' => $this->generateNomorTransaksi(),
                'jumlah_bayar' => $golongan->biaya_pendaftaran,
                'metode_pembayaran' => 'transfer_manual',
                'status_pembayaran' => 'pending',
                'tanggal_bayar' => now(),
            ]);
        });

        return redirect()->route('admin-daerah.pendaftaran.index')
            ->with('success', 'Pendaftaran berhasil dibuat.');
    }

    /**
     * Display the specified pendaftaran
     */
    public function show(string $id): Response
    {
        $adminWilayah = Auth::user()->id_wilayah;

        $pendaftaran = Pendaftaran::with([
            'peserta.user',
            'peserta.wilayah',
            'golongan.cabangLomba',
            'pembayaran',
            'dokumenPeserta'
        ])
        ->whereHas('peserta', function($q) use ($adminWilayah) {
            $q->where('id_wilayah', $adminWilayah);
        })
        ->findOrFail($id);

        return Inertia::render('AdminDaerah/Pendaftaran/Show', [
            'pendaftaran' => $pendaftaran
        ]);
    }

    /**
     * Show the form for editing the specified pendaftaran
     */
    public function edit(string $id): Response
    {
        $adminWilayah = Auth::user()->id_wilayah;

        $pendaftaran = Pendaftaran::with([
            'peserta.user',
            'golongan.cabangLomba'
        ])
        ->whereHas('peserta', function($q) use ($adminWilayah) {
            $q->where('id_wilayah', $adminWilayah);
        })
        ->findOrFail($id);

        // Get available golongan
        $golongan = Golongan::with('cabangLomba')
            ->where('status', 'aktif')
            ->orderBy('nama_golongan')
            ->get();

        return Inertia::render('AdminDaerah/Pendaftaran/Edit', [
            'pendaftaran' => $pendaftaran,
            'golongan' => $golongan
        ]);
    }

    /**
     * Update the specified pendaftaran
     */
    public function update(Request $request, string $id)
    {
        $adminWilayah = Auth::user()->id_wilayah;

        $pendaftaran = Pendaftaran::whereHas('peserta', function($q) use ($adminWilayah) {
            $q->where('id_wilayah', $adminWilayah);
        })->findOrFail($id);

        $validated = $request->validate([
            'id_golongan' => 'required|exists:golongan,id_golongan',
            'keterangan' => 'nullable|string|max:500',
        ]);

        // Check if golongan changed and no conflict
        $golonganChanged = $pendaftaran->id_golongan != $validated['id_golongan'];

        if ($golonganChanged) {
            $existingPendaftaran = Pendaftaran::where('id_peserta', $pendaftaran->id_peserta)
                ->where('id_golongan', $validated['id_golongan'])
                ->where('id_pendaftaran', '!=', $id)
                ->where('tahun_pendaftaran', date('Y'))
                ->first();

            if ($existingPendaftaran) {
                return back()->withErrors(['id_golongan' => 'Peserta sudah terdaftar pada golongan ini.']);
            }
        }

        $golongan = Golongan::findOrFail($validated['id_golongan']);

        $pendaftaran->update([
            'id_golongan' => $validated['id_golongan'],
            'keterangan' => $validated['keterangan'],
        ]);

        // Update pembayaran amount if golongan changed
        if ($golonganChanged && $pendaftaran->pembayaran) {
            $pendaftaran->pembayaran->update([
                'jumlah_bayar' => $golongan->biaya_pendaftaran
            ]);
        }

        return redirect()->route('admin-daerah.pendaftaran.index')
            ->with('success', 'Pendaftaran berhasil diperbarui.');
    }

    /**
     * Remove the specified pendaftaran
     */
    public function destroy(string $id)
    {
        $adminWilayah = Auth::user()->id_wilayah;

        $pendaftaran = Pendaftaran::whereHas('peserta', function($q) use ($adminWilayah) {
            $q->where('id_wilayah', $adminWilayah);
        })->findOrFail($id);

        // Only allow deletion if not yet approved
        if ($pendaftaran->status_pendaftaran === 'approved') {
            return back()->withErrors(['error' => 'Pendaftaran yang sudah disetujui tidak dapat dihapus.']);
        }

        $pendaftaran->delete();

        return redirect()->route('admin-daerah.pendaftaran.index')
            ->with('success', 'Pendaftaran berhasil dihapus.');
    }

    /**
     * Submit pendaftaran for approval
     */
    public function submit(string $id)
    {
        $adminWilayah = Auth::user()->id_wilayah;

        $pendaftaran = Pendaftaran::whereHas('peserta', function($q) use ($adminWilayah) {
            $q->where('id_wilayah', $adminWilayah);
        })->findOrFail($id);

        if ($pendaftaran->status_pendaftaran !== 'draft') {
            return back()->withErrors(['error' => 'Pendaftaran sudah disubmit atau disetujui.']);
        }

        $pendaftaran->update([
            'status_pendaftaran' => 'verified',
            'tanggal_daftar' => now(),
            'verified_by' => Auth::id(),
            'verified_at' => now(),
        ]);

        // Also update peserta status to verified
        $pendaftaran->peserta->update([
            'status_peserta' => 'verified'
        ]);

        return back()->with('success', 'Pendaftaran berhasil diverifikasi dan siap untuk persetujuan provinsi.');
    }

    /**
     * Get documents for pendaftaran
     */
    public function documents(string $id): Response
    {
        $adminWilayah = Auth::user()->id_wilayah;

        $pendaftaran = Pendaftaran::with([
            'peserta.user',
            'dokumenPeserta'
        ])
        ->whereHas('peserta', function($q) use ($adminWilayah) {
            $q->where('id_wilayah', $adminWilayah);
        })
        ->findOrFail($id);

        return Inertia::render('AdminDaerah/Pendaftaran/Documents', [
            'pendaftaran' => $pendaftaran
        ]);
    }

    /**
     * Generate unique registration number
     * Format: YYYY-KODE_WILAYAH-KODE_GOLONGAN-XXXX
     */
    private function generateNomorPendaftaran(int $tahun, int $idWilayah, int $idGolongan): string
    {
        // Get wilayah and golongan codes
        $wilayah = \App\Models\Wilayah::find($idWilayah);
        $golongan = \App\Models\Golongan::find($idGolongan);

        if (!$wilayah || !$golongan) {
            return $this->fallbackNomorPendaftaran($tahun);
        }

        // Get next sequential number for this combination
        $lastNumber = Pendaftaran::join('peserta', 'pendaftaran.id_peserta', '=', 'peserta.id_peserta')
            ->where('peserta.id_wilayah', $idWilayah)
            ->where('pendaftaran.id_golongan', $idGolongan)
            ->where('pendaftaran.tahun_pendaftaran', $tahun)
            ->count();

        $nextNumber = $lastNumber + 1;

        return sprintf('%d-%s-%s-%04d',
            $tahun,
            $wilayah->kode_wilayah,
            $golongan->kode_golongan,
            $nextNumber
        );
    }

    /**
     * Generate unique participant number
     * Format: KODE_GOLONGAN-YYYY-XXX
     */
    private function generateNomorPeserta(Golongan $golongan, int $tahun): string
    {
        // Get next sequential number for this golongan and year
        $lastNumber = Pendaftaran::where('id_golongan', $golongan->id_golongan)
            ->where('tahun_pendaftaran', $tahun)
            ->count();

        $nextNumber = $lastNumber + 1;

        return sprintf('%s-%d-%03d',
            $golongan->kode_golongan,
            $tahun,
            $nextNumber
        );
    }

    /**
     * Generate nomor urut based on golongan range
     * Uses the range defined in golongan table (nomor_urut_awal to nomor_urut_akhir)
     */
    private function generateNomorUrut(Golongan $golongan, int $tahun): int
    {
        // Get the range from golongan
        $startRange = $golongan->nomor_urut_awal ?? 1;
        $endRange = $golongan->nomor_urut_akhir ?? 999;

        // Count existing registrations for this golongan and year
        $existingCount = Pendaftaran::where('id_golongan', $golongan->id_golongan)
            ->where('tahun_pendaftaran', $tahun)
            ->count();

        // Calculate next nomor_urut within the range
        $nextNomorUrut = $startRange + $existingCount;

        // Ensure we don't exceed the range
        if ($nextNomorUrut > $endRange) {
            throw new \Exception("Nomor urut untuk golongan {$golongan->nama_golongan} sudah mencapai batas maksimum ({$endRange})");
        }

        return $nextNomorUrut;
    }

    /**
     * Fallback method for generating nomor_pendaftaran
     */
    private function fallbackNomorPendaftaran(int $tahun): string
    {
        $prefix = "REG{$tahun}";
        $lastNumber = Pendaftaran::where('tahun_pendaftaran', $tahun)
            ->where('nomor_pendaftaran', 'like', "{$prefix}%")
            ->count();

        return $prefix . str_pad($lastNumber + 1, 6, '0', STR_PAD_LEFT);
    }

    /**
     * Generate unique transaction number
     */
    private function generateNomorTransaksi(): string
    {
        $prefix = 'TRX';
        $year = date('Y');
        $month = date('m');

        $lastNumber = Pembayaran::whereYear('created_at', $year)
            ->whereMonth('created_at', $month)
            ->count();

        return $prefix . $year . $month . str_pad($lastNumber + 1, 4, '0', STR_PAD_LEFT);
    }
}
