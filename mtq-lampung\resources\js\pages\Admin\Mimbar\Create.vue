<template>
  <AppLayout :breadcrumbs="breadcrumbItems">
    <Head title="Tambah Mimbar" />
    <Heading title="Tambah Mimbar" />

    <div class="max-w-2xl mx-auto">
      <Card>
        <CardHeader>
          <CardTitle>Informasi Mimbar Baru</CardTitle>
          <CardDescription>
            Lengkapi form di bawah untuk menambahkan mimbar/venue baru
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form @submit.prevent="submit" class="space-y-6">
            <!-- Basic Information -->
            <div class="space-y-4">
              <h3 class="text-lg font-medium">Informasi Dasar</h3>
              
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label for="kode_mimbar">Kode Mimbar *</Label>
                  <Input
                    id="kode_mimbar"
                    v-model="form.kode_mimbar"
                    type="text"
                    required
                    placeholder="Contoh: A, B, M001"
                    :class="{ 'border-red-500': form.errors.kode_mimbar }"
                  />
                  <p v-if="form.errors.kode_mimbar" class="text-sm text-red-600 mt-1">
                    {{ form.errors.kode_mimbar }}
                  </p>
                </div>

                <div>
                  <Label for="status">Status *</Label>
                  <Select v-model="form.status" required>
                    <SelectTrigger :class="{ 'border-red-500': form.errors.status }">
                      <SelectValue placeholder="Pilih Status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="aktif">Aktif</SelectItem>
                      <SelectItem value="non_aktif">Non Aktif</SelectItem>
                    </SelectContent>
                  </Select>
                  <p v-if="form.errors.status" class="text-sm text-red-600 mt-1">
                    {{ form.errors.status }}
                  </p>
                </div>
              </div>

              <div>
                <Label for="nama_mimbar">Nama Mimbar *</Label>
                <Input
                  id="nama_mimbar"
                  v-model="form.nama_mimbar"
                  type="text"
                  required
                  placeholder="Contoh: Mimbar Utama, Aula Serbaguna"
                  :class="{ 'border-red-500': form.errors.nama_mimbar }"
                />
                <p v-if="form.errors.nama_mimbar" class="text-sm text-red-600 mt-1">
                  {{ form.errors.nama_mimbar }}
                </p>
              </div>

              <div>
                <Label for="kapasitas">Kapasitas *</Label>
                <Input
                  id="kapasitas"
                  v-model="form.kapasitas"
                  type="number"
                  required
                  min="1"
                  placeholder="Contoh: 100"
                  :class="{ 'border-red-500': form.errors.kapasitas }"
                />
                <p v-if="form.errors.kapasitas" class="text-sm text-red-600 mt-1">
                  {{ form.errors.kapasitas }}
                </p>
                <p class="text-sm text-gray-500 mt-1">
                  Jumlah maksimum peserta yang dapat menggunakan mimbar ini
                </p>
              </div>

              <div>
                <Label for="keterangan">Keterangan</Label>
                <textarea
                  id="keterangan"
                  v-model="form.keterangan"
                  rows="4"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Deskripsi mimbar, fasilitas yang tersedia, lokasi, dll..."
                  :class="{ 'border-red-500': form.errors.keterangan }"
                ></textarea>
                <p v-if="form.errors.keterangan" class="text-sm text-red-600 mt-1">
                  {{ form.errors.keterangan }}
                </p>
              </div>
            </div>

            <!-- Information -->
            <div class="bg-blue-50 p-4 rounded-lg">
              <h4 class="font-medium text-blue-900 mb-2">Informasi Mimbar</h4>
              <div class="text-sm text-blue-800 space-y-1">
                <p><strong>Kode Mimbar:</strong> Singkatan unik untuk identifikasi mimbar/venue</p>
                <p><strong>Nama Mimbar:</strong> Nama lengkap mimbar/venue yang akan ditampilkan</p>
                <p><strong>Kapasitas:</strong> Jumlah maksimum peserta yang dapat menggunakan mimbar</p>
                <p><strong>Keterangan:</strong> Informasi tambahan tentang fasilitas dan lokasi</p>
                <p><strong>Status:</strong> Menentukan apakah mimbar dapat digunakan untuk lomba</p>
              </div>
            </div>

            <!-- Examples -->
            <div class="bg-gray-50 p-4 rounded-lg">
              <h4 class="font-medium text-gray-900 mb-2">Contoh Mimbar MTQ</h4>
              <div class="text-sm text-gray-600 space-y-2">
                <div class="grid grid-cols-2 gap-4">
                  <div>
                    <p><strong>A</strong> - Mimbar A (Kapasitas: 50)</p>
                    <p><strong>B</strong> - Mimbar B (Kapasitas: 50)</p>
                    <p><strong>UTAMA</strong> - Mimbar Utama (Kapasitas: 100)</p>
                  </div>
                  <div>
                    <p><strong>AULA</strong> - Aula Serbaguna (Kapasitas: 200)</p>
                    <p><strong>OUTDOOR</strong> - Area Outdoor (Kapasitas: 300)</p>
                    <p><strong>VIP</strong> - Ruang VIP (Kapasitas: 30)</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- Actions -->
            <div class="flex justify-end space-x-4 pt-6 border-t">
              <Button
                type="button"
                variant="outline"
                @click="$inertia.visit(route('admin.mimbar.index'))"
              >
                Batal
              </Button>
              <Button type="submit" :disabled="form.processing">
                <Icon v-if="form.processing" name="loader-2" class="w-4 h-4 mr-2 animate-spin" />
                Simpan Mimbar
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import { Head, useForm } from '@inertiajs/vue3'
import AppLayout from '@/layouts/AppLayout.vue'
import Heading from '@/components/Heading.vue'
import Button from '@/components/ui/button/Button.vue'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import Icon from '@/components/Icon.vue'
import { type BreadcrumbItem } from '@/types'

const breadcrumbItems: BreadcrumbItem[] = [
  { title: 'Dashboard', href: '/admin/dashboard' },
  { title: 'Manajemen Mimbar', href: '/admin/mimbar' },
  { title: 'Tambah Mimbar', href: '/admin/mimbar/create' }
]

const form = useForm({
  kode_mimbar: '',
  nama_mimbar: '',
  keterangan: '',
  kapasitas: '',
  status: 'aktif'
})

const submit = () => {
  form.post(route('admin.mimbar.store'), {
    onSuccess: () => {
      // Redirect handled by controller
    }
  })
}
</script>
