import{d as J,x as q,k as v,s as R,c as y,o as u,w as r,b as e,a as o,u as s,e as p,f as Y,h as g,i as b,F as S,m as C,t as n,n as I}from"./app-B_pmlBSQ.js";import{_ as z}from"./AppLayout.vue_vue_type_script_setup_true_lang-BRf8lUiM.js";import{_ as E}from"./Heading.vue_vue_type_script_setup_true_lang-is4VBSCS.js";import{_ as N}from"./AppLogoIcon.vue_vue_type_script_setup_true_lang-wzz4PWna.js";import{_ as O,a as H}from"./CardContent.vue_vue_type_script_setup_true_lang-ClvLNBXC.js";import{_ as Q}from"./CardDescription.vue_vue_type_script_setup_true_lang-DboywBIK.js";import{_ as W,a as X}from"./CardTitle.vue_vue_type_script_setup_true_lang-5HO6oSoB.js";import{_ as Z}from"./Input.vue_vue_type_script_setup_true_lang-DB1-RsTn.js";import{_ as $}from"./Label.vue_vue_type_script_setup_true_lang-rj_fhE7R.js";import{_ as B,a as U,b as j,c as F,d as G}from"./SelectValue.vue_vue_type_script_setup_true_lang-IzlIronZ.js";import{_ as h}from"./Icon.vue_vue_type_script_setup_true_lang-BuYzjckr.js";import"./useForwardExpose-CO14IhkA.js";import"./RovingFocusGroup-lsWyU4xZ.js";import"./check-DtmHjdxf.js";import"./useFormControl-mZRLifGx.js";import"./loader-circle-6d8QrWFr.js";import"./sun-CHIV9RJ8.js";const aa={class:"max-w-4xl mx-auto space-y-6"},ea={class:"space-y-2"},ta={class:"flex flex-col"},sa={class:"font-medium"},na={class:"text-sm text-gray-500"},la={key:0,class:"text-sm text-red-600"},oa={key:0,class:"p-4 bg-blue-50 rounded-lg"},ra={class:"grid grid-cols-2 gap-4 text-sm"},ia={class:"ml-2 font-medium"},ua={class:"ml-2 font-medium"},ma={class:"ml-2 font-medium"},da={class:"ml-2 font-medium"},_a={class:"space-y-2"},pa={class:"flex flex-col"},ga={class:"font-medium"},fa={class:"text-sm text-gray-500"},ca={class:"text-sm text-green-600"},va={key:0,class:"text-sm text-red-600"},ba={key:1,class:"p-4 bg-green-50 rounded-lg"},ka={class:"grid grid-cols-2 gap-4 text-sm"},xa={class:"ml-2 font-medium"},ya={class:"ml-2 font-medium"},$a={class:"ml-2 font-medium"},ha={class:"ml-2 font-medium"},Pa={class:"ml-2 font-medium text-lg"},wa={class:"space-y-2"},Da={key:0,class:"text-sm text-red-600"},Va={class:"flex"},La={class:"font-medium"},Sa={class:"text-sm"},Ca={class:"flex justify-between pt-6"},Oa=J({__name:"Create",props:{peserta:{},golongan:{},selectedPeserta:{},errors:{}},setup(K){var D,V;const k=K,i=q({id_peserta:((V=(D=k.selectedPeserta)==null?void 0:D.id_peserta)==null?void 0:V.toString())||"",id_golongan:"",nomor_urut:""}),x=v(()=>i.processing),d=v(()=>i.id_peserta?k.peserta.find(t=>t.id_peserta.toString()===i.id_peserta):null),m=v(()=>i.id_golongan?k.golongan.find(t=>t.id_golongan.toString()===i.id_golongan):null),M=v(()=>d.value?k.golongan.filter(t=>t.jenis_kelamin===d.value.jenis_kelamin):k.golongan),c=v(()=>{if(!d.value||!m.value)return null;const t=new Date(d.value.tanggal_lahir),a=new Date,f=a.getFullYear()-t.getFullYear(),l=a.getMonth()-t.getMonth();(l<0||l===0&&a.getDate()<t.getDate())&&f--;const _=m.value.batas_umur_min,L=m.value.batas_umur_max;return f<_||f>L?{type:"error",title:"Umur Tidak Sesuai",message:`Peserta berumur ${f} tahun, sedangkan golongan ini untuk umur ${_}-${L} tahun.`}:null}),P=v(()=>{var t;return i.id_peserta&&i.id_golongan&&!((t=c.value)!=null&&t.type)==="error"});R(()=>i.id_peserta,()=>{i.id_golongan=""});const T=t=>new Date(t).toLocaleDateString("id-ID",{day:"numeric",month:"long",year:"numeric"}),w=t=>new Intl.NumberFormat("id-ID").format(t),A=()=>{i.post(route("admin-daerah.pendaftaran.store"))};return(t,a)=>(u(),y(z,{title:"Daftarkan Peserta ke Lomba"},{header:r(()=>[o(E,null,{default:r(()=>a[4]||(a[4]=[p("Daftarkan Peserta ke Lomba")])),_:1,__:[4]})]),default:r(()=>[e("div",aa,[o(s(O),null,{default:r(()=>[o(s(W),null,{default:r(()=>[o(s(X),null,{default:r(()=>a[5]||(a[5]=[p("Form Pendaftaran Lomba")])),_:1,__:[5]}),o(s(Q),null,{default:r(()=>a[6]||(a[6]=[p(" Daftarkan peserta dari wilayah Anda ke golongan lomba yang tersedia ")])),_:1,__:[6]})]),_:1}),o(s(H),{class:"space-y-6"},{default:r(()=>{var f;return[e("form",{onSubmit:Y(A,["prevent"])},[e("div",ea,[o(s($),{for:"id_peserta"},{default:r(()=>a[7]||(a[7]=[p("Pilih Peserta *")])),_:1,__:[7]}),o(s(B),{modelValue:s(i).id_peserta,"onUpdate:modelValue":a[0]||(a[0]=l=>s(i).id_peserta=l),required:""},{default:r(()=>[o(s(U),null,{default:r(()=>[o(s(j),{placeholder:"Pilih peserta yang akan didaftarkan"})]),_:1}),o(s(F),null,{default:r(()=>[(u(!0),g(S,null,C(t.peserta,l=>(u(),y(s(G),{key:l.id_peserta,value:l.id_peserta.toString()},{default:r(()=>{var _;return[e("div",ta,[e("span",sa,n(l.nama_lengkap),1),e("span",na,n(l.nik)+" - "+n((_=l.user)==null?void 0:_.email),1)])]}),_:2},1032,["value"]))),128))]),_:1})]),_:1},8,["modelValue"]),t.errors.id_peserta?(u(),g("p",la,n(t.errors.id_peserta),1)):b("",!0)]),d.value?(u(),g("div",oa,[a[12]||(a[12]=e("h4",{class:"font-medium text-blue-900 mb-2"},"Informasi Peserta Terpilih",-1)),e("div",ra,[e("div",null,[a[8]||(a[8]=e("span",{class:"text-blue-700"},"Nama:",-1)),e("span",ia,n(d.value.nama_lengkap),1)]),e("div",null,[a[9]||(a[9]=e("span",{class:"text-blue-700"},"NIK:",-1)),e("span",ua,n(d.value.nik),1)]),e("div",null,[a[10]||(a[10]=e("span",{class:"text-blue-700"},"Jenis Kelamin:",-1)),e("span",ma,n(d.value.jenis_kelamin==="L"?"Laki-laki":"Perempuan"),1)]),e("div",null,[a[11]||(a[11]=e("span",{class:"text-blue-700"},"Tanggal Lahir:",-1)),e("span",da,n(T(d.value.tanggal_lahir)),1)])])])):b("",!0),e("div",_a,[o(s($),{for:"id_golongan"},{default:r(()=>a[13]||(a[13]=[p("Pilih Golongan *")])),_:1,__:[13]}),o(s(B),{modelValue:s(i).id_golongan,"onUpdate:modelValue":a[1]||(a[1]=l=>s(i).id_golongan=l),required:""},{default:r(()=>[o(s(U),null,{default:r(()=>[o(s(j),{placeholder:"Pilih golongan lomba"})]),_:1}),o(s(F),null,{default:r(()=>[(u(!0),g(S,null,C(M.value,l=>(u(),y(s(G),{key:l.id_golongan,value:l.id_golongan.toString()},{default:r(()=>{var _;return[e("div",pa,[e("span",ga,n(l.nama_golongan),1),e("span",fa,n((_=l.cabang_lomba)==null?void 0:_.nama_cabang)+" - "+n(l.jenis_kelamin==="L"?"Putra":"Putri")+" - Umur "+n(l.batas_umur_min)+"-"+n(l.batas_umur_max)+" tahun ",1),e("span",ca," Biaya: Rp "+n(w(l.biaya_pendaftaran)),1)])]}),_:2},1032,["value"]))),128))]),_:1})]),_:1},8,["modelValue"]),t.errors.id_golongan?(u(),g("p",va,n(t.errors.id_golongan),1)):b("",!0)]),m.value?(u(),g("div",ba,[a[19]||(a[19]=e("h4",{class:"font-medium text-green-900 mb-2"},"Informasi Golongan Terpilih",-1)),e("div",ka,[e("div",null,[a[14]||(a[14]=e("span",{class:"text-green-700"},"Cabang Lomba:",-1)),e("span",xa,n((f=m.value.cabang_lomba)==null?void 0:f.nama_cabang),1)]),e("div",null,[a[15]||(a[15]=e("span",{class:"text-green-700"},"Golongan:",-1)),e("span",ya,n(m.value.nama_golongan),1)]),e("div",null,[a[16]||(a[16]=e("span",{class:"text-green-700"},"Jenis Kelamin:",-1)),e("span",$a,n(m.value.jenis_kelamin==="L"?"Putra":"Putri"),1)]),e("div",null,[a[17]||(a[17]=e("span",{class:"text-green-700"},"Batas Umur:",-1)),e("span",ha,n(m.value.batas_umur_min)+"-"+n(m.value.batas_umur_max)+" tahun",1)]),e("div",null,[a[18]||(a[18]=e("span",{class:"text-green-700"},"Biaya Pendaftaran:",-1)),e("span",Pa,"Rp "+n(w(m.value.biaya_pendaftaran)),1)])])])):b("",!0),e("div",wa,[o(s($),{for:"nomor_urut"},{default:r(()=>a[20]||(a[20]=[p("Nomor Urut (Opsional)")])),_:1,__:[20]}),o(s(Z),{id:"nomor_urut",modelValue:s(i).nomor_urut,"onUpdate:modelValue":a[2]||(a[2]=l=>s(i).nomor_urut=l),type:"number",min:"1",placeholder:"Kosongkan untuk auto-generate"},null,8,["modelValue"]),a[21]||(a[21]=e("p",{class:"text-sm text-gray-500"}," Jika dikosongkan, nomor urut akan dibuat otomatis ",-1)),t.errors.nomor_urut?(u(),g("p",Da,n(t.errors.nomor_urut),1)):b("",!0)]),c.value?(u(),g("div",{key:2,class:I(["p-4 rounded-lg",c.value.type==="error"?"bg-red-50 text-red-700":"bg-yellow-50 text-yellow-700"])},[e("div",Va,[o(h,{name:c.value.type==="error"?"alert-circle":"alert-triangle",class:"w-5 h-5 mr-2 mt-0.5"},null,8,["name"]),e("div",null,[e("p",La,n(c.value.title),1),e("p",Sa,n(c.value.message),1)])])],2)):b("",!0),e("div",Ca,[o(N,{type:"button",variant:"outline",onClick:a[3]||(a[3]=l=>t.$inertia.visit(t.route("admin-daerah.pendaftaran.index")))},{default:r(()=>[o(h,{name:"arrow-left",class:"w-4 h-4 mr-2"}),a[22]||(a[22]=p(" Kembali "))]),_:1,__:[22]}),o(N,{type:"submit",disabled:x.value||!P.value,class:I({"opacity-50 cursor-not-allowed":x.value||!P.value})},{default:r(()=>[o(h,{name:"save",class:"w-4 h-4 mr-2"}),p(" "+n(x.value?"Menyimpan...":"Daftarkan Peserta"),1)]),_:1},8,["disabled","class"])])],32)]}),_:1})]),_:1})])]),_:1}))}});export{Oa as default};
