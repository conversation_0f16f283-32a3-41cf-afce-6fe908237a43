import{d as N,x as U,k as q,s as j,c as h,o as n,w as i,a as t,b as s,u as a,g as A,e as d,f as M,h as m,i as y,n as p,t as _,F as W,m as c}from"./app-B_pmlBSQ.js";import{_ as F}from"./AppLayout.vue_vue_type_script_setup_true_lang-BRf8lUiM.js";import{_ as K}from"./Heading.vue_vue_type_script_setup_true_lang-is4VBSCS.js";import{_ as D}from"./AppLogoIcon.vue_vue_type_script_setup_true_lang-wzz4PWna.js";import{_ as T,a as z}from"./CardContent.vue_vue_type_script_setup_true_lang-ClvLNBXC.js";import{_ as R}from"./CardDescription.vue_vue_type_script_setup_true_lang-DboywBIK.js";import{_ as G,a as H}from"./CardTitle.vue_vue_type_script_setup_true_lang-5HO6oSoB.js";import{_ as S}from"./Input.vue_vue_type_script_setup_true_lang-DB1-RsTn.js";import{_ as f}from"./Label.vue_vue_type_script_setup_true_lang-rj_fhE7R.js";import{_ as v,a as k,b as g,c as b,d as w}from"./SelectValue.vue_vue_type_script_setup_true_lang-IzlIronZ.js";import{_ as I}from"./Icon.vue_vue_type_script_setup_true_lang-BuYzjckr.js";import"./useForwardExpose-CO14IhkA.js";import"./RovingFocusGroup-lsWyU4xZ.js";import"./check-DtmHjdxf.js";import"./useFormControl-mZRLifGx.js";import"./loader-circle-6d8QrWFr.js";import"./sun-CHIV9RJ8.js";const J={class:"max-w-2xl mx-auto"},O={class:"space-y-4"},Q={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},X={key:0,class:"text-sm text-red-600 mt-1"},Y={key:0,class:"text-sm text-red-600 mt-1"},Z={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},aa={key:0,class:"text-sm text-red-600 mt-1"},ea={key:0,class:"text-sm text-red-600 mt-1"},la={key:0},ta={key:0,class:"text-sm text-red-600 mt-1"},ia={class:"bg-gray-50 p-4 rounded-lg"},sa={class:"text-sm text-gray-600 space-y-1"},ra={key:0},oa={key:0,class:"bg-yellow-50 border border-yellow-200 p-4 rounded-lg"},da={class:"flex"},na={class:"flex justify-end space-x-4 pt-6 border-t"},Da=N({__name:"Edit",props:{wilayah:{},parentWilayah:{},levels:{}},setup(P){var $;const u=P,C=[{title:"Dashboard",href:"/admin/dashboard"},{title:"Manajemen Wilayah",href:"/admin/wilayah"},{title:"Edit Wilayah",href:`/admin/wilayah/${u.wilayah.id_wilayah}/edit`}],l=U({kode_wilayah:u.wilayah.kode_wilayah,nama_wilayah:u.wilayah.nama_wilayah,level_wilayah:u.wilayah.level_wilayah,parent_id:(($=u.wilayah.parent_id)==null?void 0:$.toString())||"",status:u.wilayah.status}),L=q(()=>u.wilayah.children&&u.wilayah.children.length>0);j(()=>l.level_wilayah,r=>{r==="provinsi"&&(l.parent_id="")});const B=()=>l.level_wilayah==="provinsi"?[]:l.level_wilayah==="kabupaten"||l.level_wilayah==="kota"?u.parentWilayah.filter(r=>r.level_wilayah==="provinsi"):u.parentWilayah.filter(r=>r.level_wilayah==="kabupaten"||r.level_wilayah==="kota"),E=()=>{l.put(route("admin.wilayah.update",u.wilayah.id_wilayah),{onSuccess:()=>{}})},x=r=>new Date(r).toLocaleDateString("id-ID",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"});return(r,e)=>(n(),h(F,{breadcrumbs:C},{default:i(()=>[t(a(A),{title:"Edit Wilayah"}),t(K,{title:`Edit Wilayah: ${r.wilayah.nama_wilayah}`},null,8,["title"]),s("div",J,[t(a(T),null,{default:i(()=>[t(a(G),null,{default:i(()=>[t(a(H),null,{default:i(()=>e[6]||(e[6]=[d("Edit Informasi Wilayah")])),_:1,__:[6]}),t(a(R),null,{default:i(()=>e[7]||(e[7]=[d(" Perbarui informasi wilayah di bawah ini ")])),_:1,__:[7]})]),_:1}),t(a(z),null,{default:i(()=>[s("form",{onSubmit:M(E,["prevent"]),class:"space-y-6"},[s("div",O,[e[16]||(e[16]=s("h3",{class:"text-lg font-medium"},"Informasi Dasar",-1)),s("div",Q,[s("div",null,[t(a(f),{for:"kode_wilayah"},{default:i(()=>e[8]||(e[8]=[d("Kode Wilayah *")])),_:1,__:[8]}),t(a(S),{id:"kode_wilayah",modelValue:a(l).kode_wilayah,"onUpdate:modelValue":e[0]||(e[0]=o=>a(l).kode_wilayah=o),type:"text",required:"",placeholder:"Contoh: LP, BDL, MTR",class:p({"border-red-500":a(l).errors.kode_wilayah})},null,8,["modelValue","class"]),a(l).errors.kode_wilayah?(n(),m("p",X,_(a(l).errors.kode_wilayah),1)):y("",!0)]),s("div",null,[t(a(f),{for:"nama_wilayah"},{default:i(()=>e[9]||(e[9]=[d("Nama Wilayah *")])),_:1,__:[9]}),t(a(S),{id:"nama_wilayah",modelValue:a(l).nama_wilayah,"onUpdate:modelValue":e[1]||(e[1]=o=>a(l).nama_wilayah=o),type:"text",required:"",placeholder:"Contoh: Lampung, Bandar Lampung",class:p({"border-red-500":a(l).errors.nama_wilayah})},null,8,["modelValue","class"]),a(l).errors.nama_wilayah?(n(),m("p",Y,_(a(l).errors.nama_wilayah),1)):y("",!0)])]),s("div",Z,[s("div",null,[t(a(f),{for:"level_wilayah"},{default:i(()=>e[10]||(e[10]=[d("Level Wilayah *")])),_:1,__:[10]}),t(a(v),{modelValue:a(l).level_wilayah,"onUpdate:modelValue":e[2]||(e[2]=o=>a(l).level_wilayah=o),required:""},{default:i(()=>[t(a(k),{class:p({"border-red-500":a(l).errors.level_wilayah})},{default:i(()=>[t(a(g),{placeholder:"Pilih Level"})]),_:1},8,["class"]),t(a(b),null,{default:i(()=>[(n(!0),m(W,null,c(r.levels,(o,V)=>(n(),h(a(w),{key:V,value:V},{default:i(()=>[d(_(o),1)]),_:2},1032,["value"]))),128))]),_:1})]),_:1},8,["modelValue"]),a(l).errors.level_wilayah?(n(),m("p",aa,_(a(l).errors.level_wilayah),1)):y("",!0)]),s("div",null,[t(a(f),{for:"status"},{default:i(()=>e[11]||(e[11]=[d("Status *")])),_:1,__:[11]}),t(a(v),{modelValue:a(l).status,"onUpdate:modelValue":e[3]||(e[3]=o=>a(l).status=o),required:""},{default:i(()=>[t(a(k),{class:p({"border-red-500":a(l).errors.status})},{default:i(()=>[t(a(g),{placeholder:"Pilih Status"})]),_:1},8,["class"]),t(a(b),null,{default:i(()=>[t(a(w),{value:"aktif"},{default:i(()=>e[12]||(e[12]=[d("Aktif")])),_:1,__:[12]}),t(a(w),{value:"non_aktif"},{default:i(()=>e[13]||(e[13]=[d("Non Aktif")])),_:1,__:[13]})]),_:1})]),_:1},8,["modelValue"]),a(l).errors.status?(n(),m("p",ea,_(a(l).errors.status),1)):y("",!0)])]),a(l).level_wilayah!=="provinsi"?(n(),m("div",la,[t(a(f),{for:"parent_id"},{default:i(()=>e[14]||(e[14]=[d("Wilayah Induk")])),_:1,__:[14]}),t(a(v),{modelValue:a(l).parent_id,"onUpdate:modelValue":e[4]||(e[4]=o=>a(l).parent_id=o)},{default:i(()=>[t(a(k),{class:p({"border-red-500":a(l).errors.parent_id})},{default:i(()=>[t(a(g),{placeholder:"Pilih Wilayah Induk"})]),_:1},8,["class"]),t(a(b),null,{default:i(()=>[(n(!0),m(W,null,c(B(),o=>(n(),h(a(w),{key:o.id_wilayah,value:o.id_wilayah.toString()},{default:i(()=>[d(_(o.nama_wilayah),1)]),_:2},1032,["value"]))),128))]),_:1})]),_:1},8,["modelValue"]),a(l).errors.parent_id?(n(),m("p",ta,_(a(l).errors.parent_id),1)):y("",!0),e[15]||(e[15]=s("p",{class:"text-sm text-gray-500 mt-1"}," Kosongkan jika wilayah ini tidak memiliki induk ",-1))])):y("",!0)]),s("div",ia,[e[20]||(e[20]=s("h4",{class:"font-medium text-gray-900 mb-2"},"Informasi Saat Ini",-1)),s("div",sa,[s("p",null,[e[17]||(e[17]=s("strong",null,"Dibuat:",-1)),d(" "+_(x(r.wilayah.created_at)),1)]),s("p",null,[e[18]||(e[18]=s("strong",null,"Diperbarui:",-1)),d(" "+_(x(r.wilayah.updated_at)),1)]),r.wilayah.parent?(n(),m("p",ra,[e[19]||(e[19]=s("strong",null,"Wilayah Induk:",-1)),d(" "+_(r.wilayah.parent.nama_wilayah),1)])):y("",!0)])]),L.value?(n(),m("div",oa,[s("div",da,[t(I,{name:"alert-triangle",class:"w-5 h-5 text-yellow-600 mr-2 mt-0.5"}),e[21]||(e[21]=s("div",null,[s("h4",{class:"font-medium text-yellow-800"},"Perhatian"),s("p",{class:"text-sm text-yellow-700 mt-1"}," Wilayah ini memiliki anak wilayah. Perubahan level atau parent dapat mempengaruhi hierarki wilayah. ")],-1))])])):y("",!0),s("div",na,[t(D,{type:"button",variant:"outline",onClick:e[5]||(e[5]=o=>r.$inertia.visit(r.route("admin.wilayah.index")))},{default:i(()=>e[22]||(e[22]=[d(" Batal ")])),_:1,__:[22]}),t(D,{type:"submit",disabled:a(l).processing},{default:i(()=>[a(l).processing?(n(),h(I,{key:0,name:"loader-2",class:"w-4 h-4 mr-2 animate-spin"})):y("",!0),e[23]||(e[23]=d(" Simpan Perubahan "))]),_:1,__:[23]},8,["disabled"])])],32)]),_:1})]),_:1})])]),_:1}))}});export{Da as default};
