# MTQ (<PERSON><PERSON><PERSON><PERSON><PERSON>) System Architecture

## Overview

The MTQ system is a comprehensive web application built with <PERSON>vel and Vue.js (Inertia.js) for managing Quran recitation competitions. The system supports multiple user roles with specific permissions and workflows.

## System Architecture

### Technology Stack
- **Backend**: <PERSON><PERSON> 11
- **Frontend**: Vue.js 3 with Inertia.js
- **Database**: MySQL
- **Authentication**: Laravel Sanctum
- **UI Framework**: Tailwind CSS with custom components

### Core Principles
- **Role-Based Access Control (RBAC)**: Five distinct user roles with specific permissions
- **Multi-tenant Regional Management**: Support for provincial and regional administration
- **Workflow-based Processes**: Structured approval workflows for registrations
- **Document Management**: Comprehensive file upload and verification system
- **Scoring System**: Flexible scoring with multiple criteria and judges

## User Roles & Permissions

### 1. SuperAdmin
- **Access**: Full system access
- **Responsibilities**: System configuration, user management, global oversight
- **Routes**: `/admin/*` (all admin routes)

### 2. Admin
- **Access**: Administrative functions except system configuration
- **Responsibilities**: User management, competition setup, participant oversight
- **Routes**: `/admin/*` (most admin routes)

### 3. Admin Daerah (Regional Administrator)
- **Access**: Regional participant management
- **Responsibilities**: Direct participant registration, regional oversight
- **Routes**: `/admin-daerah/*`
- **Key Feature**: Can directly register participants without requiring self-registration

### 4. Peserta (Participant)
- **Access**: Personal registration and document management
- **Responsibilities**: Self-registration, document upload, payment
- **Routes**: `/peserta/*`

### 5. Dewan Hakim (Judge)
- **Access**: Scoring and evaluation functions
- **Responsibilities**: Participant scoring, profile management
- **Routes**: `/dewan-hakim/*`

## Database Schema

### Core Entities

#### Users & Authentication
- `users`: Multi-role user management
- `wilayah`: Regional hierarchy (Province → Kabupaten/Kota)

#### Competition Structure
- `cabang_lomba`: Competition categories (e.g., Tilawah, Tahfidz)
- `golongan`: Competition groups (age/gender-based)
- `mimbar`: Competition venues
- `pelaksanaan`: Event management

#### Participants & Registration
- `peserta`: Participant profiles
- `pendaftaran`: Competition registrations
- `dokumen_peserta`: Document management
- `pembayaran`: Payment processing

#### Judging System
- `dewan_hakim`: Judge profiles
- `dewan_hakim_pendidikan`: Judge education history
- `dewan_hakim_pengalaman`: Judge experience
- `dewan_hakim_prestasi`: Judge achievements
- `jenis_nilai`: Scoring criteria
- `nilai_peserta`: Participant scores

#### Administrative
- `surat_mandat`: Regional mandate letters

## Routing Architecture

### Web Routes (`routes/web.php`)

#### Public Routes
```php
Route::get('/', WelcomeController::class)->name('home');
Route::prefix('competition')->group(function () {
    Route::get('/', [CompetitionController::class, 'index']);
    Route::get('/cabang/{id}', [CompetitionController::class, 'show']);
    Route::get('/golongan/{id}', [CompetitionController::class, 'golongan']);
});
```

#### Admin Routes
```php
Route::middleware(['auth', 'role:superadmin,admin'])->prefix('admin')->group(function () {
    // User Management
    Route::resource('users', UserController::class);
    Route::post('users/{user}/toggle-status', [UserController::class, 'toggleStatus']);
    
    // Regional Management
    Route::resource('wilayah', WilayahController::class);
    
    // Competition Management
    Route::resource('cabang-lomba', CabangLombaController::class);
    Route::resource('golongan', GolonganController::class);
    Route::resource('mimbar', MimbarController::class);
    
    // Judge Management
    Route::resource('dewan-hakim', DewaHakimController::class);
    
    // Event Management
    Route::resource('pelaksanaan', PelaksanaanController::class);
    
    // Participant & Registration Management
    Route::resource('peserta', PesertaController::class);
    Route::resource('pendaftaran', PendaftaranController::class);
    
    // Payment Management
    Route::resource('pembayaran', PembayaranController::class);
    
    // Reports
    Route::prefix('laporan')->group(function () {
        Route::get('/peserta', [LaporanController::class, 'peserta']);
        Route::get('/pendaftaran', [LaporanController::class, 'pendaftaran']);
        Route::get('/export/peserta', [LaporanController::class, 'exportPeserta']);
    });
});
```

#### Regional Admin Routes
```php
Route::middleware(['auth', 'role:admin_daerah'])->prefix('admin-daerah')->group(function () {
    // Direct Participant Registration (Key Feature)
    Route::resource('peserta', AdminDaerahPesertaController::class);
    Route::post('peserta/register-direct', [AdminDaerahPesertaController::class, 'registerDirect']);
    
    // Registration Management
    Route::resource('pendaftaran', AdminDaerahPendaftaranController::class);
    
    // Regional Reports
    Route::prefix('laporan')->group(function () {
        Route::get('/peserta', [AdminDaerahLaporanController::class, 'peserta']);
        Route::get('/export/peserta', [AdminDaerahLaporanController::class, 'exportPeserta']);
    });
});
```

#### Participant Routes
```php
Route::middleware(['auth', 'role:peserta'])->prefix('peserta')->group(function () {
    // Profile Management
    Route::get('/profile', [PesertaProfileController::class, 'show']);
    Route::put('/profile', [PesertaProfileController::class, 'update']);
    
    // Registration Management
    Route::resource('pendaftaran', PesertaPendaftaranController::class);
    Route::post('pendaftaran/{pendaftaran}/submit', [PesertaPendaftaranController::class, 'submit']);
    
    // Document Management
    Route::prefix('pendaftaran/{pendaftaran}/dokumen')->group(function () {
        Route::get('/', [DokumenController::class, 'index']);
        Route::post('/', [DokumenController::class, 'store']);
        Route::post('/{dokumen}/replace', [DokumenController::class, 'replace']);
    });
    
    // Payment Management
    Route::prefix('pembayaran')->group(function () {
        Route::get('/{pendaftaran}', [PesertaPembayaranController::class, 'show']);
        Route::post('/{pendaftaran}/pay', [PesertaPembayaranController::class, 'pay']);
        Route::post('/{pembayaran}/upload-proof', [PesertaPembayaranController::class, 'uploadProof']);
    });
});
```

#### Judge Routes
```php
Route::middleware(['auth', 'role:dewan_hakim'])->prefix('dewan-hakim')->group(function () {
    // Profile Management
    Route::get('/profile', [DewaHakimProfileController::class, 'show']);
    Route::put('/profile', [DewaHakimProfileController::class, 'update']);
    Route::post('/profile/education', [DewaHakimProfileController::class, 'addEducation']);
    Route::post('/profile/experience', [DewaHakimProfileController::class, 'addExperience']);
    Route::post('/profile/achievement', [DewaHakimProfileController::class, 'addAchievement']);
    
    // Scoring System
    Route::prefix('penilaian')->group(function () {
        Route::get('/', [PenilaianController::class, 'index']);
        Route::get('/peserta/{pendaftaran}', [PenilaianController::class, 'show']);
        Route::post('/peserta/{pendaftaran}/score', [PenilaianController::class, 'score']);
        Route::put('/peserta/{pendaftaran}/update-score', [PenilaianController::class, 'updateScore']);
    });
});
```

### API Routes (`routes/api.php`)

The API provides RESTful endpoints for mobile applications and external integrations:

#### Authentication
```php
Route::prefix('v1')->group(function () {
    Route::post('/auth/login', [AuthController::class, 'login']);
    Route::post('/auth/register', [AuthController::class, 'register']);
    Route::post('/auth/forgot-password', [AuthController::class, 'forgotPassword']);
});
```

#### Protected Endpoints
```php
Route::prefix('v1')->middleware(['auth:sanctum'])->group(function () {
    // Role-specific API endpoints for each user type
    Route::middleware(['role:peserta'])->prefix('participant')->group(function () {
        Route::get('/dashboard', [ParticipantController::class, 'dashboard']);
        Route::apiResource('registrations', RegistrationController::class);
        Route::apiResource('payments', PaymentController::class);
    });
    
    Route::middleware(['role:dewan_hakim'])->prefix('judge')->group(function () {
        Route::get('/assignments', [ScoringController::class, 'assignments']);
        Route::post('/participants/{registration}/score', [ScoringController::class, 'score']);
    });
});
```

## Key Features

### 1. Regional Admin Direct Registration
- Admin Daerah can register participants directly without requiring user accounts
- Streamlines the registration process for regional competitions
- Maintains audit trail of who registered each participant

### 2. Workflow-based Registration Process
1. **Draft**: Initial registration creation
2. **Submitted**: Participant submits for review
3. **Payment Pending**: Awaiting payment
4. **Paid**: Payment completed
5. **Verified**: Documents verified by admin
6. **Approved**: Final approval for competition
7. **Rejected**: Registration rejected with reason

### 3. Comprehensive Document Management
- Multiple document types (KTP, Kartu Keluarga, Photo, etc.)
- Document verification workflow
- File replacement capability
- Download and audit trail

### 4. Flexible Scoring System
- Multiple scoring criteria (Jenis Nilai)
- Weighted scoring calculations
- Multiple judges per participant
- Score history and audit trail

### 5. Multi-level Regional Support
- Province → Kabupaten/Kota hierarchy
- Regional-specific user management
- Regional reporting and analytics

## Security Features

### Authentication & Authorization
- Laravel Sanctum for API authentication
- Role-based middleware protection
- Route-level permission checking
- Session-based web authentication

### Data Protection
- Input validation on all forms
- CSRF protection
- SQL injection prevention
- File upload security

### Audit Trail
- User action logging
- Document change tracking
- Score modification history
- Registration status changes

## Development Guidelines

### Controller Organization
```
app/Http/Controllers/
├── Admin/              # Admin-specific controllers
├── AdminDaerah/        # Regional admin controllers
├── Peserta/           # Participant controllers
├── DewaHakim/         # Judge controllers
├── Api/               # API controllers
└── Auth/              # Authentication controllers
```

### Model Relationships
- Proper foreign key constraints
- Eloquent relationships for data integrity
- Scopes for common queries
- Accessors for formatted data

### Frontend Structure
- Vue.js components with Inertia.js
- Role-based component rendering
- Reusable UI components
- Responsive design with Tailwind CSS

## Deployment Considerations

### Environment Requirements
- PHP 8.1+
- MySQL 8.0+
- Node.js 18+
- Redis (for caching and queues)

### Performance Optimization
- Database indexing on foreign keys
- Query optimization with eager loading
- File storage optimization
- Caching strategies for static data

### Scalability
- Horizontal scaling support
- Database connection pooling
- CDN integration for file storage
- Queue system for background processing

## Future Enhancements

### Planned Features
1. **Real-time Scoring**: Live score updates during competitions
2. **Mobile Application**: Native mobile app for participants and judges
3. **Advanced Analytics**: Comprehensive reporting and analytics dashboard
4. **Integration APIs**: Third-party system integrations
5. **Notification System**: Email/SMS notifications for status changes
6. **Multi-language Support**: Indonesian and regional language support

### Technical Improvements
1. **Microservices Architecture**: Service separation for better scalability
2. **Event Sourcing**: Complete audit trail with event sourcing
3. **Advanced Caching**: Redis-based caching strategies
4. **API Rate Limiting**: Enhanced API security and performance
5. **Automated Testing**: Comprehensive test coverage
6. **CI/CD Pipeline**: Automated deployment and testing

## Implementation Status

### ✅ Completed Components

1. **Database Schema**: Complete schema with all necessary tables and relationships
2. **Core Models**: All essential models implemented with relationships
3. **Routing Architecture**: Comprehensive routing structure for all user roles
4. **Authentication System**: Laravel Breeze with role-based middleware
5. **Basic Controllers**: Foundation controllers with placeholder methods
6. **API Structure**: RESTful API endpoints for mobile integration

### 🚧 In Progress / Next Steps

1. **Controller Implementation**: Complete implementation of all controller methods
2. **Frontend Components**: Vue.js components for all user interfaces
3. **Middleware Enhancement**: Advanced permission checking and authorization policies
4. **File Upload System**: Document management and storage implementation
5. **Payment Integration**: Payment gateway integration (Midtrans, Xendit)

### 📋 Implementation Priority

#### Phase 1: Core Functionality (Weeks 1-2)
1. Complete Admin User Management controllers
2. Implement Regional Management (Wilayah) functionality
3. Build Competition Setup (Cabang Lomba, Golongan) interfaces
4. Create basic participant registration workflow

#### Phase 2: Registration System (Weeks 3-4)
1. Implement participant self-registration
2. Build admin daerah direct registration feature
3. Create document upload and verification system
4. Implement registration approval workflow

#### Phase 3: Payment & Verification (Weeks 5-6)
1. Integrate payment gateways
2. Build payment verification system
3. Implement document verification workflow
4. Create notification system for status changes

#### Phase 4: Judging System (Weeks 7-8)
1. Implement judge profile management
2. Build scoring interface and calculations
3. Create judge assignment system
4. Implement score verification and approval

#### Phase 5: Reporting & Analytics (Weeks 9-10)
1. Build comprehensive reporting system
2. Implement data export functionality
3. Create analytics dashboard
4. Add performance monitoring

### 🔧 Technical Implementation Notes

#### Controller Completion Checklist
- [ ] Complete all Admin controllers with full CRUD operations
- [ ] Implement AdminDaerah controllers with direct registration
- [ ] Build Peserta controllers with self-service features
- [ ] Create DewaHakim controllers with scoring functionality
- [ ] Add comprehensive validation and error handling

#### Frontend Development
- [ ] Create reusable Vue components for forms and tables
- [ ] Implement role-based navigation and UI elements
- [ ] Build responsive layouts for all screen sizes
- [ ] Add real-time notifications and status updates

#### Security Implementation
- [ ] Implement comprehensive input validation
- [ ] Add CSRF protection to all forms
- [ ] Create audit logging for sensitive operations
- [ ] Implement file upload security measures

#### Performance Optimization
- [ ] Add database indexing for frequently queried fields
- [ ] Implement caching for static data
- [ ] Optimize queries with eager loading
- [ ] Add pagination for large datasets

This architecture provides a solid foundation for the MTQ system while maintaining flexibility for future enhancements and scalability requirements.
