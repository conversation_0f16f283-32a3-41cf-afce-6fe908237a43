import{P as i,a as n,b as o}from"./AppLogoIcon.vue_vue_type_script_setup_true_lang-wzz4PWna.js";import{d,c,o as v,u as e,E as u,w as l,D as g}from"./app-B_pmlBSQ.js";import{l as p}from"./useForwardExpose-CO14IhkA.js";const y=d({__name:"Badge",props:{asChild:{type:Boolean},as:{type:[String,Object,Function]},variant:{},class:{}},setup(t){const r=t,s=p(r,"class");return(a,b)=>(v(),c(e(i),u({"data-slot":"badge",class:e(n)(e(f)({variant:a.variant}),r.class)},e(s)),{default:l(()=>[g(a.$slots,"default")]),_:3},16,["class"]))}}),f=o("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});export{y as _};
