#!/bin/bash

# MTQ Verification System Setup Script
echo "🚀 Setting up MTQ Verification System..."

# Check if we're in the correct directory
if [ ! -f "artisan" ]; then
    echo "❌ Error: Please run this script from the Laravel project root directory"
    exit 1
fi

echo "📁 Current directory: $(pwd)"

# Run migrations
echo "🔄 Running database migrations..."
php artisan migrate --force

if [ $? -eq 0 ]; then
    echo "✅ Migrations completed successfully"
else
    echo "❌ Migration failed"
    exit 1
fi

# Run seeders
echo "🌱 Running database seeders..."
php artisan db:seed --class=JenisDokumenSeeder --force

if [ $? -eq 0 ]; then
    echo "✅ JenisDokumenSeeder completed successfully"
else
    echo "❌ Seeder failed"
    exit 1
fi

# Generate Ziggy routes for frontend
echo "🛣️  Generating Ziggy routes..."
php artisan ziggy:generate

if [ $? -eq 0 ]; then
    echo "✅ Ziggy routes generated successfully"
else
    echo "❌ Ziggy route generation failed"
    exit 1
fi

# Clear and cache config
echo "🧹 Clearing and caching configuration..."
php artisan config:clear
php artisan config:cache
php artisan route:clear
php artisan route:cache

echo "✅ Configuration cached successfully"

# Build frontend assets (if needed)
echo "🎨 Building frontend assets..."
if [ -f "package.json" ]; then
    if command -v npm &> /dev/null; then
        npm run build
        echo "✅ Frontend assets built successfully"
    else
        echo "⚠️  npm not found, skipping frontend build"
    fi
else
    echo "⚠️  package.json not found, skipping frontend build"
fi

echo ""
echo "🎉 MTQ Verification System setup completed!"
echo ""
echo "📋 Next steps:"
echo "1. Test admin daerah registration: /admin-daerah/pendaftaran/create"
echo "2. Test admin provinsi verification: /admin/verifikasi-provinsi"
echo "3. Verify document preview functionality"
echo ""
echo "🔗 Key routes:"
echo "   - Admin Daerah Dashboard: /admin-daerah/dashboard"
echo "   - Admin Provinsi Verification: /admin/verifikasi-provinsi"
echo "   - Document Preview: /admin/verifikasi-provinsi/dokumen/{id}/preview"
echo ""
echo "📊 Database tables added:"
echo "   - jenis_dokumen"
echo "   - verifikasi_pendaftaran" 
echo "   - verifikasi_dokumen"
echo ""
echo "🔧 Features implemented:"
echo "   ✅ Auto-verification for admin daerah registrations"
echo "   ✅ NIK and document verification for admin provinsi"
echo "   ✅ Document preview system"
echo "   ✅ Modal-based verification interface"
echo "   ✅ Background number generation (nomor_urut, nomor_pendaftaran, nomor_peserta)"
echo ""
echo "Happy testing! 🚀"
