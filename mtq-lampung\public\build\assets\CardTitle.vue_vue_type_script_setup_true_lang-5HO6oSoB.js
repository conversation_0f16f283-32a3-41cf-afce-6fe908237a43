import{a as e}from"./AppLogoIcon.vue_vue_type_script_setup_true_lang-wzz4PWna.js";import{d as o,h as t,o as n,D as c,n as d,u as l}from"./app-B_pmlBSQ.js";const u=o({__name:"CardHeader",props:{class:{}},setup(a){const s=a;return(r,i)=>(n(),t("div",{"data-slot":"card-header",class:d(l(e)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:p-3 border-b",s.class))},[c(r.$slots,"default")],2))}}),_=o({__name:"CardTitle",props:{class:{}},setup(a){const s=a;return(r,i)=>(n(),t("h3",{"data-slot":"card-title",class:d(l(e)("leading-none font-semibold",s.class))},[c(r.$slots,"default")],2))}});export{u as _,_ as a};
