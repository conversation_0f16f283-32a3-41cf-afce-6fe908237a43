<template>
  <AppLayout :breadcrumbs="breadcrumbItems">
    <Head title="Manajemen Wilayah" />
    <Heading title="Manajemen Wilayah" />

    <div class="space-y-6">
      <!-- Filters and Search -->
      <Card>
        <CardContent class="p-6">
          <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <Label for="search">Pencarian</Label>
              <Input
                id="search"
                v-model="filters.search"
                placeholder="Nama wilayah, kode..."
                @input="search"
              />
            </div>
            <div>
              <Label for="level">Level Wilayah</Label>
              <Select v-model="filters.level" @update:modelValue="search">
                <SelectTrigger>
                  <SelectValue placeholder="Semua Level" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Semua Level</SelectItem>
                  <SelectItem v-for="(label, value) in levels" :key="value" :value="value">
                    {{ label }}
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label for="status">Status</Label>
              <Select v-model="filters.status" @update:modelValue="search">
                <SelectTrigger>
                  <SelectValue placeholder="Semua Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Semua Status</SelectItem>
                  <SelectItem value="aktif">Aktif</SelectItem>
                  <SelectItem value="non_aktif">Non Aktif</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div class="flex items-end">
              <Button @click="clearFilters" variant="outline" class="w-full">
                <Icon name="x" class="w-4 h-4 mr-2" />
                Clear
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- Actions -->
      <div class="flex justify-between items-center">
        <div class="text-sm text-gray-600">
          Menampilkan {{ wilayah.from }} - {{ wilayah.to }} dari {{ wilayah.total }} wilayah
        </div>
        <Button @click="$inertia.visit(route('admin.wilayah.create'))">
          <Icon name="plus" class="w-4 h-4 mr-2" />
          Tambah Wilayah
        </Button>
      </div>

      <!-- Wilayah Table -->
      <Card>
        <CardContent class="p-0">
          <div class="overflow-x-auto">
            <table class="w-full">
              <thead class="bg-gray-50 border-b">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Wilayah
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Level
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Parent
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Dibuat
                  </th>
                  <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Aksi
                  </th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr v-for="item in wilayah.data" :key="item.id_wilayah" class="hover:bg-gray-50">
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div class="text-sm font-medium text-gray-900">{{ item.nama_wilayah }}</div>
                      <div class="text-sm text-gray-500">{{ item.kode_wilayah }}</div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <Badge :variant="getLevelVariant(item.level_wilayah)">
                      {{ levels[item.level_wilayah] || item.level_wilayah }}
                    </Badge>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {{ item.parent?.nama_wilayah || '-' }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <Badge :variant="getStatusVariant(item.status)">
                      {{ getStatusLabel(item.status) }}
                    </Badge>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {{ formatDate(item.created_at) }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium space-x-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      @click="$inertia.visit(route('admin.wilayah.show', item.id_wilayah))"
                    >
                      <Icon name="eye" class="w-4 h-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      @click="$inertia.visit(route('admin.wilayah.edit', item.id_wilayah))"
                    >
                      <Icon name="edit" class="w-4 h-4" />
                    </Button>
                    <DropdownMenu>
                      <DropdownMenuTrigger as-child>
                        <Button variant="ghost" size="sm">
                          <Icon name="more-vertical" class="w-4 h-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem @click="viewChildren(item)">
                          <Icon name="list" class="w-4 h-4 mr-2" />
                          Lihat Anak Wilayah
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          @click="deleteWilayah(item)"
                          class="text-red-600"
                        >
                          <Icon name="trash" class="w-4 h-4 mr-2" />
                          Hapus
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      <!-- Pagination -->
      <Pagination :links="wilayah.links" />
    </div>

    <!-- Children Dialog -->
    <Dialog v-model:open="childrenDialog.show">
      <DialogContent class="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Anak Wilayah: {{ childrenDialog.parent?.nama_wilayah }}</DialogTitle>
          <DialogDescription>
            Daftar wilayah yang berada di bawah {{ childrenDialog.parent?.nama_wilayah }}
          </DialogDescription>
        </DialogHeader>
        <div v-if="childrenDialog.loading" class="flex justify-center py-8">
          <Icon name="loader-2" class="w-6 h-6 animate-spin" />
        </div>
        <div v-else-if="childrenDialog.children.length === 0" class="text-center py-8 text-gray-500">
          Tidak ada anak wilayah
        </div>
        <div v-else class="space-y-2 max-h-96 overflow-y-auto">
          <div
            v-for="child in childrenDialog.children"
            :key="child.id_wilayah"
            class="flex justify-between items-center p-3 border rounded-lg"
          >
            <div>
              <div class="font-medium">{{ child.nama_wilayah }}</div>
              <div class="text-sm text-gray-500">{{ child.kode_wilayah }}</div>
            </div>
            <Badge :variant="getStatusVariant(child.status)">
              {{ getStatusLabel(child.status) }}
            </Badge>
          </div>
        </div>
        <DialogFooter>
          <Button @click="childrenDialog.show = false">Tutup</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  </AppLayout>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { Head, router } from '@inertiajs/vue3'
import AppLayout from '@/layouts/AppLayout.vue'
import Heading from '@/components/Heading.vue'
import Button from '@/components/ui/button/Button.vue'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import Icon from '@/components/Icon.vue'
import Pagination from '@/components/Pagination.vue'
import { debounce } from 'lodash'
import { type BreadcrumbItem } from '@/types'

const breadcrumbItems: BreadcrumbItem[] = [
  { title: 'Dashboard', href: '/admin/dashboard' },
  { title: 'Manajemen Wilayah', href: '/admin/wilayah' }
]

interface Wilayah {
  id_wilayah: number
  kode_wilayah: string
  nama_wilayah: string
  level_wilayah: string
  status: string
  created_at: string
  parent?: {
    nama_wilayah: string
  }
}

interface PaginatedWilayah {
  data: Wilayah[]
  links: any[]
  from: number
  to: number
  total: number
}

const props = defineProps<{
  wilayah: PaginatedWilayah
  filters: {
    search?: string
    level?: string
    status?: string
  }
  levels: Record<string, string>
}>()

const filters = reactive({ ...props.filters })

const childrenDialog = ref({
  show: false,
  parent: null as Wilayah | null,
  children: [] as Wilayah[],
  loading: false
})

const search = debounce(() => {
  router.get(route('admin.wilayah.index'), filters, {
    preserveState: true,
    replace: true
  })
}, 300)

const clearFilters = () => {
  filters.search = ''
  filters.level = ''
  filters.status = ''
  search()
}

const viewChildren = async (wilayah: Wilayah) => {
  childrenDialog.value.parent = wilayah
  childrenDialog.value.show = true
  childrenDialog.value.loading = true
  childrenDialog.value.children = []

  try {
    const response = await fetch(route('admin.wilayah.children', wilayah.id_wilayah))
    const children = await response.json()
    childrenDialog.value.children = children
  } catch (error) {
    console.error('Error fetching children:', error)
  } finally {
    childrenDialog.value.loading = false
  }
}

const deleteWilayah = (wilayah: Wilayah) => {
  if (confirm(`Apakah Anda yakin ingin menghapus wilayah ${wilayah.nama_wilayah}?`)) {
    router.delete(route('admin.wilayah.destroy', wilayah.id_wilayah))
  }
}

const getLevelVariant = (level: string) => {
  const variants: Record<string, string> = {
    provinsi: 'destructive',
    kabupaten: 'default',
    kota: 'secondary'
  }
  return variants[level] || 'secondary'
}

const getStatusVariant = (status: string) => {
  const variants: Record<string, string> = {
    aktif: 'default',
    non_aktif: 'secondary'
  }
  return variants[status] || 'secondary'
}

const getStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    aktif: 'Aktif',
    non_aktif: 'Non Aktif'
  }
  return labels[status] || status
}

const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString('id-ID', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}
</script>
