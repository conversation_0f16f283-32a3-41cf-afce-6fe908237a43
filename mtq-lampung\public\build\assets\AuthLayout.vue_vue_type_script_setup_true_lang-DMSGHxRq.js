import{d}from"./AppLogoIcon.vue_vue_type_script_setup_true_lang-wzz4PWna.js";import{d as i,h as p,o as a,b as t,D as r,a as o,w as c,t as s,u as _,Z as f,c as m}from"./app-B_pmlBSQ.js";const u={class:"flex min-h-svh flex-col items-center justify-center gap-6 bg-background p-6 md:p-10"},h={class:"w-full max-w-sm"},x={class:"flex flex-col gap-8"},g={class:"flex flex-col items-center gap-4"},v={class:"mb-1 flex h-9 w-9 items-center justify-center rounded-md"},y={class:"sr-only"},w={class:"space-y-2 text-center"},k={class:"text-xl font-medium"},b={class:"text-center text-sm text-muted-foreground"},B=i({__name:"AuthSimpleLayout",props:{title:{},description:{}},setup(n){return(e,l)=>(a(),p("div",u,[t("div",h,[t("div",x,[t("div",g,[o(_(f),{href:e.route("home"),class:"flex flex-col items-center gap-2 font-medium"},{default:c(()=>[t("div",v,[o(d,{class:"size-9 fill-current text-[var(--foreground)] dark:text-white"})]),t("span",y,s(e.title),1)]),_:1},8,["href"]),t("div",w,[t("h1",k,s(e.title),1),t("p",b,s(e.description),1)])]),r(e.$slots,"default")])])]))}}),j=i({__name:"AuthLayout",props:{title:{},description:{}},setup(n){return(e,l)=>(a(),m(B,{title:e.title,description:e.description},{default:c(()=>[r(e.$slots,"default")]),_:3},8,["title","description"]))}});export{j as _};
