import{e as b,h as $}from"./RovingFocusGroup-lsWyU4xZ.js";import{J as h,K as x,N as w,O as B,Q as v,X as O,R as D,V as P}from"./AppLayout.vue_vue_type_script_setup_true_lang-BRf8lUiM.js";import{d as c,c as p,o as d,u as e,E as u,w as r,D as i,k as g,a as m,b as C,h as k,n as F}from"./app-B_pmlBSQ.js";import{a as f}from"./AppLogoIcon.vue_vue_type_script_setup_true_lang-wzz4PWna.js";const V=c({__name:"Dialog",props:{open:{type:Boolean},defaultOpen:{type:Boolean},modal:{type:Boolean}},emits:["update:open"],setup(a,{emit:t}){const s=b(a,t);return(l,y)=>(d(),p(e(h),u({"data-slot":"dialog"},e(s)),{default:r(()=>[i(l.$slots,"default")]),_:3},16))}}),z=c({__name:"DialogOverlay",props:{forceMount:{type:Boolean},asChild:{type:Boolean},as:{type:[String,Object,Function]},class:{}},setup(a){const t=a,o=g(()=>{const{class:n,...s}=t;return s});return(n,s)=>(d(),p(e(x),u({"data-slot":"dialog-overlay"},o.value,{class:e(f)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/80",t.class)}),{default:r(()=>[i(n.$slots,"default")]),_:3},16,["class"]))}}),A=c({__name:"DialogContent",props:{forceMount:{type:Boolean},disableOutsidePointerEvents:{type:Boolean},asChild:{type:Boolean},as:{type:[String,Object,Function]},class:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","openAutoFocus","closeAutoFocus"],setup(a,{emit:t}){const o=a,n=t,s=g(()=>{const{class:y,..._}=o;return _}),l=b(s,n);return(y,_)=>(d(),p(e(w),null,{default:r(()=>[m(z),m(e(B),u({"data-slot":"dialog-content"},e(l),{class:e(f)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",o.class)}),{default:r(()=>[i(y.$slots,"default"),m(e(v),{class:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4"},{default:r(()=>[m(e(O)),_[0]||(_[0]=C("span",{class:"sr-only"},"Close",-1))]),_:1,__:[0]})]),_:3},16,["class"])]),_:3}))}}),K=c({__name:"DialogDescription",props:{asChild:{type:Boolean},as:{type:[String,Object,Function]},class:{}},setup(a){const t=a,o=g(()=>{const{class:s,...l}=t;return l}),n=$(o);return(s,l)=>(d(),p(e(D),u({"data-slot":"dialog-description"},e(n),{class:e(f)("text-muted-foreground text-sm",t.class)}),{default:r(()=>[i(s.$slots,"default")]),_:3},16,["class"]))}}),M=c({__name:"DialogHeader",props:{class:{}},setup(a){const t=a;return(o,n)=>(d(),k("div",{"data-slot":"dialog-header",class:F(e(f)("flex flex-col gap-2 text-center sm:text-left",t.class))},[i(o.$slots,"default")],2))}}),H=c({__name:"DialogTitle",props:{asChild:{type:Boolean},as:{type:[String,Object,Function]},class:{}},setup(a){const t=a,o=g(()=>{const{class:s,...l}=t;return l}),n=$(o);return(s,l)=>(d(),p(e(P),u({"data-slot":"dialog-title"},e(n),{class:e(f)("text-lg leading-none font-semibold",t.class)}),{default:r(()=>[i(s.$slots,"default")]),_:3},16,["class"]))}});export{V as _,A as a,M as b,H as c,K as d};
