import{d as k,x as v,c as f,o as u,w as r,a as s,b as i,u as e,g as V,f as y,e as t,j as p,q as _,v as h,h as b,F as U,m as q,t as w,i as L}from"./app-B_pmlBSQ.js";import{_ as n}from"./InputError.vue_vue_type_script_setup_true_lang-A-vdzbq9.js";import{_ as N}from"./TextLink.vue_vue_type_script_setup_true_lang-CC2Og0lP.js";import{_ as T}from"./AppLogoIcon.vue_vue_type_script_setup_true_lang-wzz4PWna.js";import{_ as m}from"./Input.vue_vue_type_script_setup_true_lang-DB1-RsTn.js";import{_ as d}from"./Label.vue_vue_type_script_setup_true_lang-rj_fhE7R.js";import{_ as j}from"./AuthLayout.vue_vue_type_script_setup_true_lang-DMSGHxRq.js";import{L as M}from"./loader-circle-6d8QrWFr.js";import"./useForwardExpose-CO14IhkA.js";const P={class:"grid gap-6"},C={class:"grid gap-2"},$={class:"grid gap-2"},S={class:"grid gap-2"},B={class:"grid gap-2"},D={class:"grid gap-2"},K={class:"grid gap-2"},A={class:"grid grid-cols-2 gap-4"},F={class:"grid gap-2"},c={class:"grid gap-2"},E={class:"grid gap-2"},I={class:"grid gap-2"},J={class:"grid gap-2"},Q=["value"],R={class:"grid gap-2"},W={class:"text-center text-sm text-muted-foreground"},se=k({__name:"Register",props:{wilayah:{}},setup(O){const l=v({username:"",nama_lengkap:"",email:"",password:"",password_confirmation:"",nik:"",tempat_lahir:"",tanggal_lahir:"",jenis_kelamin:"",alamat:"",id_wilayah:"",no_telepon:""}),x=()=>{l.post(route("register"),{onFinish:()=>l.reset("password","password_confirmation")})};return(g,a)=>(u(),f(j,{title:"Daftar Peserta MTQ",description:"Lengkapi data diri Anda untuk mendaftar sebagai peserta MTQ Lampung"},{default:r(()=>[s(e(V),{title:"Register"}),i("form",{onSubmit:y(x,["prevent"]),class:"flex flex-col gap-6"},[i("div",P,[i("div",C,[s(e(d),{for:"username"},{default:r(()=>a[12]||(a[12]=[t("Username")])),_:1,__:[12]}),s(e(m),{id:"username",type:"text",required:"",autofocus:"",tabindex:1,autocomplete:"username",modelValue:e(l).username,"onUpdate:modelValue":a[0]||(a[0]=o=>e(l).username=o),placeholder:"Username"},null,8,["modelValue"]),s(n,{message:e(l).errors.username},null,8,["message"])]),i("div",$,[s(e(d),{for:"nama_lengkap"},{default:r(()=>a[13]||(a[13]=[t("Nama Lengkap")])),_:1,__:[13]}),s(e(m),{id:"nama_lengkap",type:"text",required:"",tabindex:2,autocomplete:"name",modelValue:e(l).nama_lengkap,"onUpdate:modelValue":a[1]||(a[1]=o=>e(l).nama_lengkap=o),placeholder:"Nama lengkap"},null,8,["modelValue"]),s(n,{message:e(l).errors.nama_lengkap},null,8,["message"])]),i("div",S,[s(e(d),{for:"email"},{default:r(()=>a[14]||(a[14]=[t("Email address")])),_:1,__:[14]}),s(e(m),{id:"email",type:"email",required:"",tabindex:3,autocomplete:"email",modelValue:e(l).email,"onUpdate:modelValue":a[2]||(a[2]=o=>e(l).email=o),placeholder:"<EMAIL>"},null,8,["modelValue"]),s(n,{message:e(l).errors.email},null,8,["message"])]),i("div",B,[s(e(d),{for:"password"},{default:r(()=>a[15]||(a[15]=[t("Password")])),_:1,__:[15]}),s(e(m),{id:"password",type:"password",required:"",tabindex:4,autocomplete:"new-password",modelValue:e(l).password,"onUpdate:modelValue":a[3]||(a[3]=o=>e(l).password=o),placeholder:"Password"},null,8,["modelValue"]),s(n,{message:e(l).errors.password},null,8,["message"])]),i("div",D,[s(e(d),{for:"password_confirmation"},{default:r(()=>a[16]||(a[16]=[t("Confirm password")])),_:1,__:[16]}),s(e(m),{id:"password_confirmation",type:"password",required:"",tabindex:5,autocomplete:"new-password",modelValue:e(l).password_confirmation,"onUpdate:modelValue":a[4]||(a[4]=o=>e(l).password_confirmation=o),placeholder:"Confirm password"},null,8,["modelValue"]),s(n,{message:e(l).errors.password_confirmation},null,8,["message"])]),i("div",K,[s(e(d),{for:"nik"},{default:r(()=>a[17]||(a[17]=[t("NIK")])),_:1,__:[17]}),s(e(m),{id:"nik",type:"text",required:"",tabindex:6,modelValue:e(l).nik,"onUpdate:modelValue":a[5]||(a[5]=o=>e(l).nik=o),placeholder:"Nomor Induk Kependudukan (16 digit)",maxlength:"16"},null,8,["modelValue"]),s(n,{message:e(l).errors.nik},null,8,["message"])]),i("div",A,[i("div",F,[s(e(d),{for:"tempat_lahir"},{default:r(()=>a[18]||(a[18]=[t("Tempat Lahir")])),_:1,__:[18]}),s(e(m),{id:"tempat_lahir",type:"text",required:"",tabindex:7,modelValue:e(l).tempat_lahir,"onUpdate:modelValue":a[6]||(a[6]=o=>e(l).tempat_lahir=o),placeholder:"Tempat lahir"},null,8,["modelValue"]),s(n,{message:e(l).errors.tempat_lahir},null,8,["message"])]),i("div",c,[s(e(d),{for:"tanggal_lahir"},{default:r(()=>a[19]||(a[19]=[t("Tanggal Lahir")])),_:1,__:[19]}),s(e(m),{id:"tanggal_lahir",type:"date",required:"",tabindex:8,modelValue:e(l).tanggal_lahir,"onUpdate:modelValue":a[7]||(a[7]=o=>e(l).tanggal_lahir=o)},null,8,["modelValue"]),s(n,{message:e(l).errors.tanggal_lahir},null,8,["message"])])]),i("div",E,[s(e(d),{for:"jenis_kelamin"},{default:r(()=>a[20]||(a[20]=[t("Jenis Kelamin")])),_:1,__:[20]}),p(i("select",{id:"jenis_kelamin",required:"",tabindex:9,"onUpdate:modelValue":a[8]||(a[8]=o=>e(l).jenis_kelamin=o),class:"flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50"},a[21]||(a[21]=[i("option",{value:""},"Pilih Jenis Kelamin",-1),i("option",{value:"L"},"Laki-laki",-1),i("option",{value:"P"},"Perempuan",-1)]),512),[[_,e(l).jenis_kelamin]]),s(n,{message:e(l).errors.jenis_kelamin},null,8,["message"])]),i("div",I,[s(e(d),{for:"alamat"},{default:r(()=>a[22]||(a[22]=[t("Alamat")])),_:1,__:[22]}),p(i("textarea",{id:"alamat",required:"",tabindex:10,"onUpdate:modelValue":a[9]||(a[9]=o=>e(l).alamat=o),placeholder:"Alamat lengkap",rows:"3",class:"flex min-h-[60px] w-full rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50"},null,512),[[h,e(l).alamat]]),s(n,{message:e(l).errors.alamat},null,8,["message"])]),i("div",J,[s(e(d),{for:"id_wilayah"},{default:r(()=>a[23]||(a[23]=[t("Wilayah")])),_:1,__:[23]}),p(i("select",{id:"id_wilayah",required:"",tabindex:11,"onUpdate:modelValue":a[10]||(a[10]=o=>e(l).id_wilayah=o),class:"flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50"},[a[24]||(a[24]=i("option",{value:""},"Pilih Wilayah",-1)),(u(!0),b(U,null,q(g.wilayah,o=>(u(),b("option",{key:o.id_wilayah,value:o.id_wilayah},w(o.nama_wilayah),9,Q))),128))],512),[[_,e(l).id_wilayah]]),s(n,{message:e(l).errors.id_wilayah},null,8,["message"])]),i("div",R,[s(e(d),{for:"no_telepon"},{default:r(()=>a[25]||(a[25]=[t("No. Telepon (Opsional)")])),_:1,__:[25]}),s(e(m),{id:"no_telepon",type:"tel",tabindex:12,modelValue:e(l).no_telepon,"onUpdate:modelValue":a[11]||(a[11]=o=>e(l).no_telepon=o),placeholder:"Nomor telepon"},null,8,["modelValue"]),s(n,{message:e(l).errors.no_telepon},null,8,["message"])]),s(e(T),{type:"submit",class:"mt-2 w-full",tabindex:"13",disabled:e(l).processing},{default:r(()=>[e(l).processing?(u(),f(e(M),{key:0,class:"h-4 w-4 animate-spin"})):L("",!0),t(" "+w(e(l).processing?"Mendaftar...":"Daftar Sekarang"),1)]),_:1},8,["disabled"])]),i("div",W,[a[27]||(a[27]=t(" Sudah punya akun? ")),s(N,{href:g.route("login"),class:"underline underline-offset-4",tabindex:14},{default:r(()=>a[26]||(a[26]=[t("Masuk")])),_:1,__:[26]},8,["href"])])],32)]),_:1}))}});export{se as default};
