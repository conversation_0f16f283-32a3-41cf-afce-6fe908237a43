import{d as Z,x as B,l as C,c as v,o,w as n,a,b as i,u as t,g as ee,i as k,e as r,t as u,f as P,j as ae,h as p,F,m as M,q as te,n as z}from"./app-B_pmlBSQ.js";import{_ as ne}from"./AppLayout.vue_vue_type_script_setup_true_lang-BRf8lUiM.js";import{_ as se}from"./Heading.vue_vue_type_script_setup_true_lang-is4VBSCS.js";import{_}from"./AppLogoIcon.vue_vue_type_script_setup_true_lang-wzz4PWna.js";import{_ as G}from"./Input.vue_vue_type_script_setup_true_lang-DB1-RsTn.js";import{_ as x}from"./Label.vue_vue_type_script_setup_true_lang-rj_fhE7R.js";import{_ as K}from"./Textarea.vue_vue_type_script_setup_true_lang-jqOjZ7RT.js";import{_ as V,a as N}from"./CardContent.vue_vue_type_script_setup_true_lang-ClvLNBXC.js";import{_ as h}from"./CardDescription.vue_vue_type_script_setup_true_lang-DboywBIK.js";import{_ as S,a as U}from"./CardTitle.vue_vue_type_script_setup_true_lang-5HO6oSoB.js";import{_ as le}from"./index-CMGr3-bt.js";import{_ as ie,a as re}from"./index-Cae_Ab9-.js";import{_ as A,a as T,b as J,c as O,d as E}from"./DialogTitle.vue_vue_type_script_setup_true_lang-MyozO0uv.js";import{_ as oe}from"./DialogTrigger.vue_vue_type_script_setup_true_lang-oTNwh9lu.js";import{_ as b}from"./InputError.vue_vue_type_script_setup_true_lang-A-vdzbq9.js";import{_ as f}from"./Icon.vue_vue_type_script_setup_true_lang-BuYzjckr.js";import"./useForwardExpose-CO14IhkA.js";import"./RovingFocusGroup-lsWyU4xZ.js";import"./check-DtmHjdxf.js";import"./loader-circle-6d8QrWFr.js";import"./sun-CHIV9RJ8.js";const de={class:"flex items-center space-x-4"},ue={class:"space-y-6"},fe={class:"flex items-center justify-between"},me={class:"grid gap-2"},pe=["value"],_e={class:"grid gap-2"},ge={class:"grid gap-2"},ke={class:"flex justify-end space-x-2"},ve={key:0,class:"text-center py-8"},ce={key:1,class:"space-y-4"},ye={class:"flex items-center space-x-4"},xe={class:"font-medium"},be={class:"text-sm text-gray-600"},we={class:"text-xs text-gray-500"},De={key:0,class:"text-xs text-gray-500 mt-1"},je={key:1,class:"text-xs text-red-600 mt-1"},$e={class:"flex items-center space-x-2"},Be={class:"flex space-x-1"},Ce={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},Fe={class:"text-sm"},Me={class:"grid gap-2"},Ve={class:"grid gap-2"},he={class:"flex justify-end space-x-2"},Ze=Z({__name:"Index",props:{pendaftaran:{},requiredDocuments:{}},setup(R){const c=R,d=B({jenis_dokumen:"",file:null,keterangan:""}),m=B({file:null,keterangan:""}),w=C(null),D=C(!1),y=C(!1);function I(){d.post(route("peserta.dokumen.store",c.pendaftaran.id_pendaftaran),{onSuccess:()=>{d.reset(),D.value=!1}})}function L(){w.value&&m.post(route("peserta.dokumen.replace",[c.pendaftaran.id_pendaftaran,w.value.id_dokumen]),{onSuccess:()=>{m.reset(),y.value=!1,w.value=null}})}function H(l){confirm("Apakah Anda yakin ingin menghapus dokumen ini?")&&B({}).delete(route("peserta.dokumen.destroy",[c.pendaftaran.id_pendaftaran,l.id_dokumen]))}function Q(l){w.value=l,m.keterangan=l.keterangan||"",y.value=!0}function W(l){return{pending:"bg-yellow-100 text-yellow-800",approved:"bg-green-100 text-green-800",rejected:"bg-red-100 text-red-800"}[l]||"bg-gray-100 text-gray-800"}function X(l){return{pending:"Menunggu Verifikasi",approved:"Disetujui",rejected:"Ditolak"}[l]||l}function Y(l){if(l===0)return"0 Bytes";const e=1024,s=["Bytes","KB","MB","GB"],g=Math.floor(Math.log(l)/Math.log(e));return parseFloat((l/Math.pow(e,g)).toFixed(2))+" "+s[g]}function q(){const l=c.pendaftaran.dokumen_peserta.map(e=>e.jenis_dokumen);return Object.entries(c.requiredDocuments).filter(([e])=>!l.includes(e)).map(([e,s])=>({value:e,label:s}))}function j(){return["draft","payment_pending","paid"].includes(c.pendaftaran.status_pendaftaran)}return(l,e)=>(o(),v(ne,null,{header:n(()=>[i("div",de,[a(_,{as:"link",href:l.route("peserta.pendaftaran.show",l.pendaftaran.id_pendaftaran),variant:"ghost",size:"sm"},{default:n(()=>[a(f,{name:"arrow-left",class:"w-4 h-4 mr-2"}),e[9]||(e[9]=r(" Kembali "))]),_:1,__:[9]},8,["href"]),a(se,null,{default:n(()=>e[10]||(e[10]=[r("Kelola Dokumen")])),_:1,__:[10]})])]),default:n(()=>[a(t(ee),{title:"Kelola Dokumen"}),i("div",ue,[a(t(V),null,{default:n(()=>[a(t(S),null,{default:n(()=>[a(t(U),null,{default:n(()=>[r(u(l.pendaftaran.golongan.nama_golongan),1)]),_:1}),a(t(h),null,{default:n(()=>[r(u(l.pendaftaran.golongan.cabang_lomba.nama_cabang)+" - "+u(l.pendaftaran.nomor_pendaftaran),1)]),_:1})]),_:1})]),_:1}),j()?k("",!0):(o(),v(t(ie),{key:0,class:"border-yellow-200 bg-yellow-50"},{default:n(()=>[a(f,{name:"info",class:"h-4 w-4"}),a(t(re),null,{default:n(()=>e[11]||(e[11]=[r(" Dokumen tidak dapat diubah karena pendaftaran sudah dalam tahap verifikasi atau telah disetujui. ")])),_:1,__:[11]})]),_:1})),a(t(V),null,{default:n(()=>[a(t(S),null,{default:n(()=>[i("div",fe,[a(t(U),null,{default:n(()=>e[12]||(e[12]=[r("Dokumen yang Diupload")])),_:1,__:[12]}),a(t(A),{open:D.value,"onUpdate:open":e[4]||(e[4]=s=>D.value=s)},{default:n(()=>[a(t(oe),{"as-child":""},{default:n(()=>[j()&&q().length>0?(o(),v(_,{key:0},{default:n(()=>[a(f,{name:"plus",class:"w-4 h-4 mr-2"}),e[13]||(e[13]=r(" Upload Dokumen "))]),_:1,__:[13]})):k("",!0)]),_:1}),a(t(T),null,{default:n(()=>[a(t(J),null,{default:n(()=>[a(t(O),null,{default:n(()=>e[14]||(e[14]=[r("Upload Dokumen Baru")])),_:1,__:[14]}),a(t(E),null,{default:n(()=>e[15]||(e[15]=[r(" Pilih jenis dokumen dan upload file yang diperlukan. ")])),_:1,__:[15]})]),_:1}),i("form",{onSubmit:P(I,["prevent"]),class:"space-y-4"},[i("div",me,[a(t(x),{for:"jenis_dokumen"},{default:n(()=>e[16]||(e[16]=[r("Jenis Dokumen")])),_:1,__:[16]}),ae(i("select",{id:"jenis_dokumen","onUpdate:modelValue":e[0]||(e[0]=s=>t(d).jenis_dokumen=s),required:"",class:"flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50"},[e[17]||(e[17]=i("option",{value:""},"Pilih jenis dokumen",-1)),(o(!0),p(F,null,M(q(),s=>(o(),p("option",{key:s.value,value:s.value},u(s.label),9,pe))),128))],512),[[te,t(d).jenis_dokumen]]),a(b,{message:t(d).errors.jenis_dokumen},null,8,["message"])]),i("div",_e,[a(t(x),{for:"file"},{default:n(()=>e[18]||(e[18]=[r("File")])),_:1,__:[18]}),a(t(G),{id:"file",type:"file",accept:".jpg,.jpeg,.png,.pdf",onChange:e[1]||(e[1]=s=>t(d).file=s.target.files[0]),required:""}),e[19]||(e[19]=i("p",{class:"text-xs text-gray-500"},"Format: JPG, PNG, PDF. Maksimal 5MB.",-1)),a(b,{message:t(d).errors.file},null,8,["message"])]),i("div",ge,[a(t(x),{for:"keterangan"},{default:n(()=>e[20]||(e[20]=[r("Keterangan (Opsional)")])),_:1,__:[20]}),a(t(K),{id:"keterangan",modelValue:t(d).keterangan,"onUpdate:modelValue":e[2]||(e[2]=s=>t(d).keterangan=s),placeholder:"Tambahkan keterangan jika diperlukan...",rows:"3"},null,8,["modelValue"]),a(b,{message:t(d).errors.keterangan},null,8,["message"])]),i("div",ke,[a(_,{type:"button",variant:"outline",onClick:e[3]||(e[3]=s=>D.value=!1)},{default:n(()=>e[21]||(e[21]=[r(" Batal ")])),_:1,__:[21]}),a(_,{type:"submit",disabled:t(d).processing},{default:n(()=>[t(d).processing?(o(),v(f,{key:0,name:"loader-2",class:"w-4 h-4 mr-2 animate-spin"})):k("",!0),e[22]||(e[22]=r(" Upload "))]),_:1,__:[22]},8,["disabled"])])],32)]),_:1})]),_:1},8,["open"])]),a(t(h),null,{default:n(()=>e[23]||(e[23]=[r("Daftar dokumen yang telah Anda upload")])),_:1,__:[23]})]),_:1}),a(t(N),null,{default:n(()=>[l.pendaftaran.dokumen_peserta.length===0?(o(),p("div",ve,[a(f,{name:"file-x",class:"h-12 w-12 text-gray-400 mx-auto mb-4"}),e[24]||(e[24]=i("h3",{class:"text-lg font-medium text-gray-900 mb-2"},"Belum Ada Dokumen",-1)),e[25]||(e[25]=i("p",{class:"text-gray-600 mb-4"},"Silakan upload dokumen yang diperlukan untuk melengkapi pendaftaran.",-1))])):(o(),p("div",ce,[(o(!0),p(F,null,M(l.pendaftaran.dokumen_peserta,s=>(o(),p("div",{key:s.id_dokumen,class:"flex items-center justify-between p-4 border rounded-lg"},[i("div",ye,[a(f,{name:"file-text",class:"h-8 w-8 text-gray-500"}),i("div",null,[i("h4",xe,u(l.requiredDocuments[s.jenis_dokumen]||s.jenis_dokumen),1),i("p",be,u(s.nama_file),1),i("p",we,u(Y(s.ukuran_file)),1),s.keterangan?(o(),p("p",De,u(s.keterangan),1)):k("",!0),s.catatan_verifikasi?(o(),p("p",je," Catatan: "+u(s.catatan_verifikasi),1)):k("",!0)])]),i("div",$e,[a(t(le),{class:z(W(s.status_verifikasi))},{default:n(()=>[r(u(X(s.status_verifikasi)),1)]),_:2},1032,["class"]),i("div",Be,[a(_,{as:"link",href:l.route("peserta.dokumen.download",[l.pendaftaran.id_pendaftaran,s.id_dokumen]),size:"sm",variant:"outline"},{default:n(()=>[a(f,{name:"download",class:"w-4 h-4"})]),_:2},1032,["href"]),j()&&s.status_verifikasi!=="approved"?(o(),v(_,{key:0,onClick:g=>Q(s),size:"sm",variant:"outline"},{default:n(()=>[a(f,{name:"edit",class:"w-4 h-4"})]),_:2},1032,["onClick"])):k("",!0),j()&&s.status_verifikasi!=="approved"?(o(),v(_,{key:1,onClick:g=>H(s),size:"sm",variant:"outline",class:"text-red-600 hover:text-red-700"},{default:n(()=>[a(f,{name:"trash-2",class:"w-4 h-4"})]),_:2},1032,["onClick"])):k("",!0)])])]))),128))]))]),_:1})]),_:1}),a(t(V),null,{default:n(()=>[a(t(S),null,{default:n(()=>[a(t(U),null,{default:n(()=>e[26]||(e[26]=[r("Dokumen yang Diperlukan")])),_:1,__:[26]}),a(t(h),null,{default:n(()=>e[27]||(e[27]=[r("Daftar dokumen yang harus dilengkapi")])),_:1,__:[27]})]),_:1}),a(t(N),null,{default:n(()=>[i("div",Ce,[(o(!0),p(F,null,M(l.requiredDocuments,(s,g)=>(o(),p("div",{key:g,class:"flex items-center space-x-3"},[a(f,{name:l.pendaftaran.dokumen_peserta.some($=>$.jenis_dokumen===g)?"check-circle":"circle",class:z([l.pendaftaran.dokumen_peserta.some($=>$.jenis_dokumen===g)?"text-green-600":"text-gray-400","h-5 w-5"])},null,8,["name","class"]),i("span",Fe,u(s),1)]))),128))])]),_:1})]),_:1}),a(t(A),{open:y.value,"onUpdate:open":e[8]||(e[8]=s=>y.value=s)},{default:n(()=>[a(t(T),null,{default:n(()=>[a(t(J),null,{default:n(()=>[a(t(O),null,{default:n(()=>e[28]||(e[28]=[r("Ganti Dokumen")])),_:1,__:[28]}),a(t(E),null,{default:n(()=>e[29]||(e[29]=[r(" Upload file baru untuk mengganti dokumen yang sudah ada. ")])),_:1,__:[29]})]),_:1}),i("form",{onSubmit:P(L,["prevent"]),class:"space-y-4"},[i("div",Me,[a(t(x),{for:"replace_file"},{default:n(()=>e[30]||(e[30]=[r("File Baru")])),_:1,__:[30]}),a(t(G),{id:"replace_file",type:"file",accept:".jpg,.jpeg,.png,.pdf",onChange:e[5]||(e[5]=s=>t(m).file=s.target.files[0]),required:""}),e[31]||(e[31]=i("p",{class:"text-xs text-gray-500"},"Format: JPG, PNG, PDF. Maksimal 5MB.",-1)),a(b,{message:t(m).errors.file},null,8,["message"])]),i("div",Ve,[a(t(x),{for:"replace_keterangan"},{default:n(()=>e[32]||(e[32]=[r("Keterangan (Opsional)")])),_:1,__:[32]}),a(t(K),{id:"replace_keterangan",modelValue:t(m).keterangan,"onUpdate:modelValue":e[6]||(e[6]=s=>t(m).keterangan=s),placeholder:"Tambahkan keterangan jika diperlukan...",rows:"3"},null,8,["modelValue"]),a(b,{message:t(m).errors.keterangan},null,8,["message"])]),i("div",he,[a(_,{type:"button",variant:"outline",onClick:e[7]||(e[7]=s=>y.value=!1)},{default:n(()=>e[33]||(e[33]=[r(" Batal ")])),_:1,__:[33]}),a(_,{type:"submit",disabled:t(m).processing},{default:n(()=>[t(m).processing?(o(),v(f,{key:0,name:"loader-2",class:"w-4 h-4 mr-2 animate-spin"})):k("",!0),e[34]||(e[34]=r(" Ganti Dokumen "))]),_:1,__:[34]},8,["disabled"])])],32)]),_:1})]),_:1},8,["open"])])]),_:1}))}});export{Ze as default};
