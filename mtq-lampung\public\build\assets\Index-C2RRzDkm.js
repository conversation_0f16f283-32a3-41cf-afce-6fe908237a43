import{d as E,l as H,c as g,o as n,w as a,a as e,b as s,u as m,g as K,h as f,i as C,t as o,e as d,f as R,F as b,m as y,W as w}from"./app-B_pmlBSQ.js";import{e as q,f as G,a as J,b as O,c as Q,d as k,g as X,_ as Y}from"./AppLayout.vue_vue_type_script_setup_true_lang-BRf8lUiM.js";import{_ as u}from"./AppLogoIcon.vue_vue_type_script_setup_true_lang-wzz4PWna.js";import{_ as p,a as v}from"./CardContent.vue_vue_type_script_setup_true_lang-ClvLNBXC.js";import{_ as V,a as T}from"./CardTitle.vue_vue_type_script_setup_true_lang-5HO6oSoB.js";import{_ as Z}from"./Input.vue_vue_type_script_setup_true_lang-DB1-RsTn.js";import{_ as $}from"./Label.vue_vue_type_script_setup_true_lang-rj_fhE7R.js";import{_ as ee}from"./index-CMGr3-bt.js";import{_ as r}from"./Icon.vue_vue_type_script_setup_true_lang-BuYzjckr.js";import{_ as h}from"./TextLink.vue_vue_type_script_setup_true_lang-CC2Og0lP.js";import{_ as P,a as I,b as N,c as z,d as _}from"./SelectValue.vue_vue_type_script_setup_true_lang-IzlIronZ.js";import"./useForwardExpose-CO14IhkA.js";import"./RovingFocusGroup-lsWyU4xZ.js";import"./check-DtmHjdxf.js";import"./loader-circle-6d8QrWFr.js";import"./sun-CHIV9RJ8.js";import"./useFormControl-mZRLifGx.js";const te={class:"space-y-6"},se={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},ae={class:"flex items-center"},le={class:"p-2 bg-blue-100 rounded-lg"},ie={class:"ml-4"},de={class:"text-2xl font-bold"},re={class:"flex items-center"},oe={class:"p-2 bg-green-100 rounded-lg"},ne={class:"ml-4"},ue={class:"text-2xl font-bold"},me={class:"flex items-center"},fe={class:"p-2 bg-yellow-100 rounded-lg"},_e={class:"ml-4"},ce={class:"text-2xl font-bold"},pe={class:"flex items-center"},ve={class:"p-2 bg-red-100 rounded-lg"},he={class:"ml-4"},ge={class:"text-2xl font-bold"},xe={class:"grid grid-cols-1 md:grid-cols-4 gap-2"},be={class:"space-y-2"},ye={class:"relative"},we={class:"space-y-2"},ke={class:"space-y-2"},$e={class:"flex flex-col sm:flex-row gap-2 sm:justify-between"},De={class:"flex gap-2"},je={class:"flex gap-2"},Se={class:"flex justify-between items-center"},Ce={class:"flex items-center gap-2 text-sm text-muted-foreground"},Ve={class:"overflow-x-auto"},Te={class:"w-full"},Pe={class:"py-1 px-3"},Ie={class:"flex items-center gap-3"},Ne={class:"font-medium"},ze={class:"text-sm text-muted-foreground"},Ae={class:"font-mono text-sm"},Me={class:""},We={class:"flex items-center gap-2"},Be={class:""},Fe={class:"text-sm text-muted-foreground"},Le={class:"px-2"},Ue={class:"flex justify-end gap-1"},Ee={key:0,class:"text-center py-12"},He={class:"flex flex-col items-center gap-4"},Ke={class:"p-4 bg-muted rounded-full"},Re={key:0,class:"flex justify-center"},qe={class:"flex space-x-2"},Ge=["innerHTML"],mt=E({__name:"Index",props:{peserta:{},wilayah:{},filters:{},stats:{}},setup(A){const x=A,c=H({search:x.filters.search||"",status:x.filters.status||"all",wilayah:x.filters.wilayah||"all"});function M(){w.get(route("admin.peserta.index"),c.value,{preserveState:!0,replace:!0})}function W(i){return i.split(" ").map(t=>t.charAt(0)).join("").toUpperCase().slice(0,2)}function D(i){return{draft:"Draft",submitted:"Disubmit",verified:"Diverifikasi",approved:"Disetujui",rejected:"Ditolak"}[i]||i}function B(i){return new Date(i).toLocaleDateString("id-ID",{year:"numeric",month:"short",day:"numeric"})}function F(i){confirm(`Apakah Anda yakin ingin menghapus peserta ${i.nama_lengkap}?`)&&w.delete(route("admin.peserta.destroy",i.id_peserta))}function L(i){return{draft:"secondary",submitted:"secondary",verified:"secondary",approved:"default",rejected:"destructive"}[i]||"secondary"}function j(i,t){confirm(`Apakah Anda yakin ingin mengubah status peserta ${i.nama_lengkap} menjadi ${D(t)}?`)&&w.put(route("admin.peserta.update",i.id_peserta),{status_peserta:t,tipe:"update_status"})}const U=[{title:"Manajemen Peserta",href:"/admin/peserta"}];return(i,t)=>(n(),g(Y,{breadcrumbs:U},{default:a(()=>[e(m(K),{title:"Manajemen Peserta"}),s("div",te,[s("div",se,[e(p,{class:"bg-muted"},{default:a(()=>[e(v,{class:"p-2"},{default:a(()=>[s("div",ae,[s("div",le,[e(r,{name:"users",class:"w-5 h-5 text-blue-600"})]),s("div",ie,[t[3]||(t[3]=s("p",{class:"text-sm font-medium text-muted-foreground"},"Total Peserta",-1)),s("p",de,o(i.stats.total_peserta),1)])])]),_:1})]),_:1}),e(p,{class:"bg-muted"},{default:a(()=>[e(v,{class:"p-2"},{default:a(()=>[s("div",re,[s("div",oe,[e(r,{name:"checkCircle",class:"w-5 h-5 text-green-600"})]),s("div",ne,[t[4]||(t[4]=s("p",{class:"text-sm font-medium text-muted-foreground"},"Disetujui",-1)),s("p",ue,o(i.stats.total_peserta_approved),1)])])]),_:1})]),_:1}),e(p,{class:"bg-muted"},{default:a(()=>[e(v,{class:"p-2"},{default:a(()=>[s("div",me,[s("div",fe,[e(r,{name:"clock",class:"w-5 h-5 text-yellow-600"})]),s("div",_e,[t[5]||(t[5]=s("p",{class:"text-sm font-medium text-muted-foreground"},"Menunggu",-1)),s("p",ce,o(i.stats.total_peserta_submitted),1)])])]),_:1})]),_:1}),e(p,{class:"bg-muted"},{default:a(()=>[e(v,{class:"p-2"},{default:a(()=>[s("div",pe,[s("div",ve,[e(r,{name:"xCircle",class:"w-5 h-5 text-red-600"})]),s("div",he,[t[6]||(t[6]=s("p",{class:"text-sm font-medium text-muted-foreground"},"Ditolak",-1)),s("p",ge,o(i.stats.total_peserta_rejected),1)])])]),_:1})]),_:1})]),e(p,{class:"pb-3"},{default:a(()=>[e(V,null,{default:a(()=>[e(T,{class:"flex items-center gap-2"},{default:a(()=>[e(r,{name:"filter",class:"w-4 h-4"}),t[7]||(t[7]=d(" Filter & Pencarian "))]),_:1,__:[7]})]),_:1}),e(v,{class:"space-y-4"},{default:a(()=>[s("form",{onSubmit:R(M,["prevent"]),class:"space-y-2"},[s("div",xe,[s("div",be,[e($,{for:"search"},{default:a(()=>t[8]||(t[8]=[d("Pencarian")])),_:1,__:[8]}),s("div",ye,[e(r,{name:"search",class:"absolute left-3 top-3 w-4 h-4 text-muted-foreground"}),e(Z,{id:"search",modelValue:c.value.search,"onUpdate:modelValue":t[0]||(t[0]=l=>c.value.search=l),placeholder:"Nama, NIK, atau Email...",class:"pl-9"},null,8,["modelValue"])])]),s("div",we,[e($,{for:"status"},{default:a(()=>t[9]||(t[9]=[d("Status")])),_:1,__:[9]}),e(P,{modelValue:c.value.status,"onUpdate:modelValue":t[1]||(t[1]=l=>c.value.status=l)},{default:a(()=>[e(I,{class:"w-full"},{default:a(()=>[e(N,{placeholder:"Semua Status"})]),_:1}),e(z,null,{default:a(()=>[e(_,{value:"all"},{default:a(()=>t[10]||(t[10]=[d("Semua Status")])),_:1,__:[10]}),e(_,{value:"draft"},{default:a(()=>t[11]||(t[11]=[d("Draft")])),_:1,__:[11]}),e(_,{value:"submitted"},{default:a(()=>t[12]||(t[12]=[d("Disubmit")])),_:1,__:[12]}),e(_,{value:"verified"},{default:a(()=>t[13]||(t[13]=[d("Diverifikasi")])),_:1,__:[13]}),e(_,{value:"approved"},{default:a(()=>t[14]||(t[14]=[d("Disetujui")])),_:1,__:[14]}),e(_,{value:"rejected"},{default:a(()=>t[15]||(t[15]=[d("Ditolak")])),_:1,__:[15]})]),_:1})]),_:1},8,["modelValue"])]),s("div",ke,[e($,{for:"wilayah"},{default:a(()=>t[16]||(t[16]=[d("Wilayah")])),_:1,__:[16]}),e(P,{modelValue:c.value.wilayah,"onUpdate:modelValue":t[2]||(t[2]=l=>c.value.wilayah=l)},{default:a(()=>[e(I,{class:"w-full"},{default:a(()=>[e(N,{placeholder:"Semua Wilayah"})]),_:1}),e(z,null,{default:a(()=>[e(_,{value:"all"},{default:a(()=>t[17]||(t[17]=[d("Semua Wilayah")])),_:1,__:[17]}),(n(!0),f(b,null,y(i.wilayah,l=>(n(),g(_,{key:l.id_wilayah,value:l.id_wilayah.toString()},{default:a(()=>[d(o(l.nama_wilayah),1)]),_:2},1032,["value"]))),128))]),_:1})]),_:1},8,["modelValue"])])]),s("div",$e,[s("div",De,[e(u,{type:"submit",variant:"secondary"},{default:a(()=>[e(r,{name:"search",class:"w-4 h-4 mr-2"}),t[18]||(t[18]=d(" Cari "))]),_:1,__:[18]}),e(u,{type:"button",variant:"outline",onClick:i.resetFilters},{default:a(()=>[e(r,{name:"x",class:"w-4 h-4 mr-2"}),t[19]||(t[19]=d(" Reset "))]),_:1,__:[19]},8,["onClick"])]),s("div",je,[e(u,{variant:"outline",size:"sm"},{default:a(()=>[e(r,{name:"download",class:"w-4 h-4 mr-2"}),t[20]||(t[20]=d(" Export "))]),_:1,__:[20]}),e(u,{variant:"outline",size:"sm"},{default:a(()=>[e(r,{name:"upload",class:"w-4 h-4 mr-2"}),t[21]||(t[21]=d(" Import "))]),_:1,__:[21]})])])],32)]),_:1})]),_:1}),e(p,{class:"gap-0"},{default:a(()=>[e(V,null,{default:a(()=>[s("div",Se,[e(T,null,{default:a(()=>t[22]||(t[22]=[d("Daftar Peserta")])),_:1,__:[22]}),s("div",Ce,[s("span",null,o(i.peserta.data.length)+" dari "+o(i.peserta.total||0)+" peserta",1)]),e(u,{"as-child":""},{default:a(()=>[e(h,{href:i.route("admin.peserta.create")},{default:a(()=>[e(r,{name:"plus",class:"w-4 h-4 mr-2"}),t[23]||(t[23]=d(" Tambah Peserta "))]),_:1,__:[23]},8,["href"])]),_:1})])]),_:1}),e(v,{class:"p-0"},{default:a(()=>[s("div",Ve,[s("table",Te,[t[27]||(t[27]=s("thead",null,[s("tr",{class:"border-b bg-slate-50"},[s("th",{class:"text-left p-4 font-medium"},"Peserta"),s("th",{class:"text-left p-4 font-medium"},"NIK"),s("th",{class:"text-left p-4 font-medium"},"Wilayah"),s("th",{class:"text-left p-4 font-medium"},"Status"),s("th",{class:"text-left p-4 font-medium"},"Terdaftar"),s("th",{class:"text-right p-4 font-medium"},"Aksi")])],-1)),s("tbody",null,[(n(!0),f(b,null,y(i.peserta.data,l=>(n(),f("tr",{key:l.id_peserta,class:"border-b hover:bg-white transition-colors bg-slate-100"},[s("td",Pe,[s("div",Ie,[e(q,{class:"h-8 w-8 shadow"},{default:a(()=>[e(G,null,{default:a(()=>[d(o(W(l.nama_lengkap)),1)]),_:2},1024)]),_:2},1024),s("div",null,[s("div",Ne,o(l.nama_lengkap),1),s("div",ze,o(l.email),1)])])]),s("td",Ae,o(l.nik),1),s("td",Me,[s("div",We,[e(r,{name:"map-pin",class:"w-4 h-4 text-muted-foreground"}),d(" "+o(l.wilayah.nama_wilayah),1)])]),s("td",Be,[e(ee,{variant:L(l.status_peserta)},{default:a(()=>[d(o(D(l.status_peserta)),1)]),_:2},1032,["variant"])]),s("td",Fe,o(B(l.created_at)),1),s("td",Le,[s("div",Ue,[e(u,{variant:"outline",size:"sm","as-child":""},{default:a(()=>[e(h,{href:i.route("admin.peserta.show",l.id_peserta)},{default:a(()=>[e(r,{name:"eye",class:"w-3 h-3"})]),_:2},1032,["href"])]),_:2},1024),e(u,{variant:"outline",size:"sm","as-child":""},{default:a(()=>[e(h,{href:i.route("admin.peserta.edit",l.id_peserta)},{default:a(()=>[e(r,{name:"edit",class:"w-3 h-3"})]),_:2},1032,["href"])]),_:2},1024),(n(),g(m(J),{key:l.id_peserta},{default:a(()=>[e(m(O),null,{default:a(()=>[e(u,{variant:"outline",size:"sm"},{default:a(()=>[e(r,{name:"moreVertical",class:"w-3 h-3"})]),_:1})]),_:1}),e(m(Q),{align:"end",class:"bg-yellow-50"},{default:a(()=>[e(m(k),{onClick:S=>j(l,"approved")},{default:a(()=>[e(r,{name:"check",class:"w-3 h-3 mr-2"}),t[24]||(t[24]=d(" Setujui "))]),_:2,__:[24]},1032,["onClick"]),e(m(k),{onClick:S=>j(l,"rejected")},{default:a(()=>[e(r,{name:"x",class:"w-3 h-3 mr-2"}),t[25]||(t[25]=d(" Tolak "))]),_:2,__:[25]},1032,["onClick"]),e(m(X)),e(m(k),{onClick:S=>F(l),class:"text-destructive focus:text-destructive"},{default:a(()=>[e(r,{name:"trash-2",class:"w-3 h-3 mr-2"}),t[26]||(t[26]=d(" Hapus "))]),_:2,__:[26]},1032,["onClick"])]),_:2},1024)]),_:2},1024))])])]))),128))])])]),i.peserta.data.length===0?(n(),f("div",Ee,[s("div",He,[s("div",Ke,[e(r,{name:"users",class:"w-8 h-8 text-muted-foreground"})]),t[29]||(t[29]=s("div",null,[s("h3",{class:"text-lg font-semibold"},"Tidak ada peserta ditemukan"),s("p",{class:"text-sm text-muted-foreground"}," Coba ubah filter pencarian atau tambah peserta baru ")],-1)),e(u,{"as-child":""},{default:a(()=>[e(h,{href:i.route("admin.peserta.create")},{default:a(()=>[e(r,{name:"plus",class:"w-4 h-4 mr-2"}),t[28]||(t[28]=d(" Tambah Peserta "))]),_:1,__:[28]},8,["href"])]),_:1})])])):C("",!0)]),_:1})]),_:1}),i.peserta.links?(n(),f("div",Re,[s("nav",qe,[(n(!0),f(b,null,y(i.peserta.links,l=>(n(),f("div",{key:l.label},[l.url?(n(),g(u,{key:0,"as-child":"",variant:l.active?"primary":"ghost",size:"sm"},{default:a(()=>[e(h,{href:l.url},{default:a(()=>[d(o(l.label),1)]),_:2},1032,["href"])]),_:2},1032,["variant"])):(n(),f("span",{key:1,class:"px-3 py-2 text-sm text-gray-500",innerHTML:l.label},null,8,Ge))]))),128))])])):C("",!0)])]),_:1}))}});export{mt as default};
