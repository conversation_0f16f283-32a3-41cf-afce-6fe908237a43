import{d as y,x as C,c as _,o as i,w as r,a as l,b as a,u as s,g as $,e,f as T,h as m,i as d,n as u,t as b,j as V,v as L}from"./app-B_pmlBSQ.js";import{_ as S}from"./AppLayout.vue_vue_type_script_setup_true_lang-BRf8lUiM.js";import{_ as w}from"./Heading.vue_vue_type_script_setup_true_lang-is4VBSCS.js";import{_ as g}from"./AppLogoIcon.vue_vue_type_script_setup_true_lang-wzz4PWna.js";import{_ as N,a as A}from"./CardContent.vue_vue_type_script_setup_true_lang-ClvLNBXC.js";import{_ as Q}from"./CardDescription.vue_vue_type_script_setup_true_lang-DboywBIK.js";import{_ as D,a as I}from"./CardTitle.vue_vue_type_script_setup_true_lang-5HO6oSoB.js";import{_ as k}from"./Input.vue_vue_type_script_setup_true_lang-DB1-RsTn.js";import{_ as p}from"./Label.vue_vue_type_script_setup_true_lang-rj_fhE7R.js";import{_ as B,a as h,b as K,c as M,d as c}from"./SelectValue.vue_vue_type_script_setup_true_lang-IzlIronZ.js";import{_ as j}from"./Icon.vue_vue_type_script_setup_true_lang-BuYzjckr.js";import"./useForwardExpose-CO14IhkA.js";import"./RovingFocusGroup-lsWyU4xZ.js";import"./check-DtmHjdxf.js";import"./useFormControl-mZRLifGx.js";import"./loader-circle-6d8QrWFr.js";import"./sun-CHIV9RJ8.js";const H={class:"max-w-2xl mx-auto"},U={class:"space-y-4"},q={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},z={key:0,class:"text-sm text-red-600 mt-1"},F={key:0,class:"text-sm text-red-600 mt-1"},E={key:0,class:"text-sm text-red-600 mt-1"},P={key:0,class:"text-sm text-red-600 mt-1"},R={class:"flex justify-end space-x-4 pt-6 border-t"},ua=y({__name:"Create",setup(Y){const x=[{title:"Dashboard",href:"/admin/dashboard"},{title:"Manajemen Cabang Lomba",href:"/admin/cabang-lomba"},{title:"Tambah Cabang Lomba",href:"/admin/cabang-lomba/create"}],n=C({kode_cabang:"",nama_cabang:"",deskripsi:"",status:"aktif"}),v=()=>{n.post(route("admin.cabang-lomba.store"),{onSuccess:()=>{}})};return(f,t)=>(i(),_(S,{breadcrumbs:x},{default:r(()=>[l(s($),{title:"Tambah Cabang Lomba"}),l(w,{title:"Tambah Cabang Lomba"}),a("div",H,[l(s(N),null,{default:r(()=>[l(s(D),null,{default:r(()=>[l(s(I),null,{default:r(()=>t[5]||(t[5]=[e("Informasi Cabang Lomba Baru")])),_:1,__:[5]}),l(s(Q),null,{default:r(()=>t[6]||(t[6]=[e(" Lengkapi form di bawah untuk menambahkan cabang lomba baru ")])),_:1,__:[6]})]),_:1}),l(s(A),null,{default:r(()=>[a("form",{onSubmit:T(v,["prevent"]),class:"space-y-6"},[a("div",U,[t[13]||(t[13]=a("h3",{class:"text-lg font-medium"},"Informasi Dasar",-1)),a("div",q,[a("div",null,[l(s(p),{for:"kode_cabang"},{default:r(()=>t[7]||(t[7]=[e("Kode Cabang *")])),_:1,__:[7]}),l(s(k),{id:"kode_cabang",modelValue:s(n).kode_cabang,"onUpdate:modelValue":t[0]||(t[0]=o=>s(n).kode_cabang=o),type:"text",required:"",placeholder:"Contoh: TIL, TAH, FAH",class:u({"border-red-500":s(n).errors.kode_cabang})},null,8,["modelValue","class"]),s(n).errors.kode_cabang?(i(),m("p",z,b(s(n).errors.kode_cabang),1)):d("",!0)]),a("div",null,[l(s(p),{for:"status"},{default:r(()=>t[8]||(t[8]=[e("Status *")])),_:1,__:[8]}),l(s(B),{modelValue:s(n).status,"onUpdate:modelValue":t[1]||(t[1]=o=>s(n).status=o),required:""},{default:r(()=>[l(s(h),{class:u({"border-red-500":s(n).errors.status})},{default:r(()=>[l(s(K),{placeholder:"Pilih Status"})]),_:1},8,["class"]),l(s(M),null,{default:r(()=>[l(s(c),{value:"aktif"},{default:r(()=>t[9]||(t[9]=[e("Aktif")])),_:1,__:[9]}),l(s(c),{value:"non_aktif"},{default:r(()=>t[10]||(t[10]=[e("Non Aktif")])),_:1,__:[10]})]),_:1})]),_:1},8,["modelValue"]),s(n).errors.status?(i(),m("p",F,b(s(n).errors.status),1)):d("",!0)])]),a("div",null,[l(s(p),{for:"nama_cabang"},{default:r(()=>t[11]||(t[11]=[e("Nama Cabang Lomba *")])),_:1,__:[11]}),l(s(k),{id:"nama_cabang",modelValue:s(n).nama_cabang,"onUpdate:modelValue":t[2]||(t[2]=o=>s(n).nama_cabang=o),type:"text",required:"",placeholder:"Contoh: Tilawatil Quran, Tahfidzul Quran",class:u({"border-red-500":s(n).errors.nama_cabang})},null,8,["modelValue","class"]),s(n).errors.nama_cabang?(i(),m("p",E,b(s(n).errors.nama_cabang),1)):d("",!0)]),a("div",null,[l(s(p),{for:"deskripsi"},{default:r(()=>t[12]||(t[12]=[e("Deskripsi")])),_:1,__:[12]}),V(a("textarea",{id:"deskripsi","onUpdate:modelValue":t[3]||(t[3]=o=>s(n).deskripsi=o),rows:"4",class:u(["w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",{"border-red-500":s(n).errors.deskripsi}]),placeholder:"Deskripsi cabang lomba..."},null,2),[[L,s(n).deskripsi]]),s(n).errors.deskripsi?(i(),m("p",P,b(s(n).errors.deskripsi),1)):d("",!0)])]),t[16]||(t[16]=a("div",{class:"bg-blue-50 p-4 rounded-lg"},[a("h4",{class:"font-medium text-blue-900 mb-2"},"Informasi Cabang Lomba"),a("div",{class:"text-sm text-blue-800 space-y-1"},[a("p",null,[a("strong",null,"Kode Cabang:"),e(" Singkatan unik untuk identifikasi cabang lomba")]),a("p",null,[a("strong",null,"Nama Cabang:"),e(" Nama lengkap cabang lomba yang akan ditampilkan")]),a("p",null,[a("strong",null,"Deskripsi:"),e(" Penjelasan detail tentang cabang lomba ini")]),a("p",null,[a("strong",null,"Status:"),e(" Menentukan apakah cabang lomba dapat digunakan untuk pendaftaran")])])],-1)),t[17]||(t[17]=a("div",{class:"bg-gray-50 p-4 rounded-lg"},[a("h4",{class:"font-medium text-gray-900 mb-2"},"Contoh Cabang Lomba MTQ"),a("div",{class:"text-sm text-gray-600 space-y-2"},[a("div",{class:"grid grid-cols-2 gap-4"},[a("div",null,[a("p",null,[a("strong",null,"TIL"),e(" - Tilawatil Quran")]),a("p",null,[a("strong",null,"TAH"),e(" - Tahfidzul Quran")]),a("p",null,[a("strong",null,"FAH"),e(" - Fahmil Quran")]),a("p",null,[a("strong",null,"SYA"),e(" - Syarhil Quran")])]),a("div",null,[a("p",null,[a("strong",null,"KAL"),e(" - Kaligrafi")]),a("p",null,[a("strong",null,"NAS"),e(" - Nasyid")]),a("p",null,[a("strong",null,"CER"),e(" - Ceramah")]),a("p",null,[a("strong",null,"QIS"),e(" - Qiraatul Kutub")])])])])],-1)),a("div",R,[l(g,{type:"button",variant:"outline",onClick:t[4]||(t[4]=o=>f.$inertia.visit(f.route("admin.cabang-lomba.index")))},{default:r(()=>t[14]||(t[14]=[e(" Batal ")])),_:1,__:[14]}),l(g,{type:"submit",disabled:s(n).processing},{default:r(()=>[s(n).processing?(i(),_(j,{key:0,name:"loader-2",class:"w-4 h-4 mr-2 animate-spin"})):d("",!0),t[15]||(t[15]=e(" Simpan Cabang Lomba "))]),_:1,__:[15]},8,["disabled"])])],32)]),_:1})]),_:1})])]),_:1}))}});export{ua as default};
