<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Pendaftaran;
use App\Models\Peserta;
use App\Models\Golongan;
use App\Models\Mimbar;
use Illuminate\Http\Request;
use Inertia\Inertia;

/**
 * PendaftaranController
 * handle pendaftaran dari admin daerah maupun peserta
 */

class PendaftaranController extends Controller
{
    public function index(Request $request)
    {
        $query = Pendaftaran::with(['peserta', 'golongan.cabangLomba']);

        // Search functionality
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->whereHas('peserta', function ($q) use ($search) {
                    $q->where('nama_lengkap', 'like', "%{$search}%");
                })
                ->orWhere('nomor_pendaftaran', 'like', "%{$search}%");
            });
        }

        // Filter by status
        if ($request->has('status') && $request->status !== 'all') {
            $query->where('status_pendaftaran', $request->status);
        }

        // Filter by golongan
        if ($request->has('golongan') && $request->golongan !== 'all') {
            $query->where('id_golongan', $request->golongan);
        }

        $pendaftaran = $query->orderBy('created_at', 'desc')
            ->paginate(10)
            ->withQueryString();

        $golongan = Golongan::aktif()->get();

        return Inertia::render('Admin/Pendaftaran/Index', [
            'pendaftaran' => $pendaftaran,
            'golongan' => $golongan,
            'filters' => $request->only(['search', 'status', 'golongan'])
        ]);
    }

    public function show(string $id)
    {
        $pendaftaran = Pendaftaran::with(['peserta', 'golongan.cabangLomba', 'mimbar', 'dokumenPeserta', 'pembayaran'])->find($id);

        return Inertia::render('Admin/Pendaftaran/Show', [
            'pendaftaran' => $pendaftaran
        ]);
    }

    public function create()
    {
        $peserta = Peserta::all();
        $golongan = Golongan::aktif()->get();
        $mimbar = Mimbar::all();

        return Inertia::render('Admin/Pendaftaran/Create', [
            'peserta' => $peserta,
            'golongan' => $golongan,
            'mimbar' => $mimbar
        ]);
    }
}
