<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('verifikasi_pendaftaran', function (Blueprint $table) {
            $table->id('id_verifikasi');
            $table->unsignedBigInteger('id_pendaftaran');
            $table->unsignedBigInteger('verified_by');
            
            // NIK Verification
            $table->enum('status_nik', ['pending', 'sesuai', 'tidak_sesuai'])->default('pending');
            $table->text('catatan_nik')->nullable();
            $table->timestamp('verified_nik_at')->nullable();
            
            // Document Verification
            $table->enum('status_dokumen', ['pending', 'berkas_sesuai', 'berkas_tidak_sesuai'])->default('pending');
            $table->text('catatan_dokumen')->nullable();
            $table->timestamp('verified_dokumen_at')->nullable();
            
            // Overall Status
            $table->enum('status_verifikasi', ['pending', 'approved', 'rejected'])->default('pending');
            $table->text('catatan_verifikasi')->nullable();
            $table->timestamp('verified_at')->nullable();
            
            $table->timestamps();
            
            // Foreign keys
            $table->foreign('id_pendaftaran')->references('id_pendaftaran')->on('pendaftaran')->onDelete('cascade');
            $table->foreign('verified_by')->references('id_user')->on('users')->onDelete('restrict');
            
            // Indexes
            $table->index(['status_verifikasi']);
            $table->index(['status_nik']);
            $table->index(['status_dokumen']);
            $table->index(['verified_by']);
            $table->unique(['id_pendaftaran']); // One verification record per registration
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('verifikasi_pendaftaran');
    }
};
