<script setup lang="ts">
import { ref, computed } from 'vue'
import { useForm, router } from '@inertiajs/vue3'
import AppLayout from '@/layouts/AppLayout.vue'
import { Head, Link } from '@inertiajs/vue3'
import Heading from '@/components/Heading.vue'
import Button from '@/components/ui/button/Button.vue'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Checkbox } from '@/components/ui/checkbox'
import Icon from '@/components/Icon.vue'

interface Wilayah {
  nama_wilayah: string
}

interface Peserta {
  nama_lengkap: string
  wilayah: Wilayah
}

interface CabangLomba {
  nama_cabang: string
}

interface Golongan {
  nama_golongan: string
  cabang_lomba: CabangLomba
}

interface Pendaftaran {
  nomor_pendaftaran: string
  peserta: Peserta
  golongan: Golongan
}

interface User {
  name: string
}

interface DokumenPeserta {
  id_dokumen: number
  jenis_dokumen: string
  nama_file: string
  ukuran_file: number
  status_verifikasi: string
  catatan_verifikasi: string | null
  verified_at: string | null
  created_at: string
  pendaftaran: Pendaftaran
  uploaded_by: User
  verified_by: User | null
}

interface PaginatedData {
  data: DokumenPeserta[]
  current_page: number
  last_page: number
  per_page: number
  total: number
  links: Array<{
    url: string | null
    label: string
    active: boolean
  }>
}

const props = defineProps<{
  dokumen: PaginatedData
  filters: {
    status?: string
    jenis_dokumen?: string
    search?: string
  }
  jenisOptions: Record<string, string>
}>()

const searchForm = useForm({
  status: props.filters.status || '',
  jenis_dokumen: props.filters.jenis_dokumen || '',
  search: props.filters.search || ''
})

const verifyForm = useForm({
  status_verifikasi: '',
  catatan_verifikasi: ''
})

const bulkForm = useForm({
  dokumen_ids: [] as number[],
  status_verifikasi: '',
  catatan_verifikasi: ''
})

const selectedDocument = ref<DokumenPeserta | null>(null)
const showVerifyDialog = ref(false)
const showBulkDialog = ref(false)
const selectedDokumen = ref<number[]>([])

function search() {
  searchForm.get(route('admin.dokumen-verifikasi.index'), {
    preserveState: true,
    replace: true
  })
}

function clearFilters() {
  searchForm.reset()
  search()
}

function openVerifyDialog(dokumen: DokumenPeserta) {
  selectedDocument.value = dokumen
  verifyForm.reset()
  showVerifyDialog.value = true
}

function verifyDocument() {
  if (!selectedDocument.value) return

  verifyForm.post(route('admin.dokumen-verifikasi.verify', selectedDocument.value.id_dokumen), {
    onSuccess: () => {
      showVerifyDialog.value = false
      selectedDocument.value = null
      verifyForm.reset()
    }
  })
}

function openBulkDialog() {
  if (selectedDokumen.value.length === 0) {
    alert('Pilih dokumen yang akan diverifikasi')
    return
  }

  bulkForm.dokumen_ids = selectedDokumen.value
  bulkForm.reset('status_verifikasi', 'catatan_verifikasi')
  showBulkDialog.value = true
}

function bulkVerify() {
  bulkForm.post(route('admin.dokumen-verifikasi.bulk-verify'), {
    onSuccess: () => {
      showBulkDialog.value = false
      selectedDokumen.value = []
      bulkForm.reset()
    }
  })
}

function toggleSelectAll() {
  if (selectedDokumen.value.length === props.dokumen.data.length) {
    selectedDokumen.value = []
  } else {
    selectedDokumen.value = props.dokumen.data.map(d => d.id_dokumen)
  }
}

function getStatusColor(status: string): string {
  const colors = {
    pending: 'bg-yellow-100 text-yellow-800',
    approved: 'bg-green-100 text-green-800',
    rejected: 'bg-red-100 text-red-800'
  }
  return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800'
}

function getStatusText(status: string): string {
  const texts = {
    pending: 'Menunggu Verifikasi',
    approved: 'Disetujui',
    rejected: 'Ditolak'
  }
  return texts[status as keyof typeof texts] || status
}

function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

function formatDate(dateString: string): string {
  return new Date(dateString).toLocaleDateString('id-ID', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const allSelected = computed(() =>
  props.dokumen.data.length > 0 && selectedDokumen.value.length === props.dokumen.data.length
)

const someSelected = computed(() =>
  selectedDokumen.value.length > 0 && selectedDokumen.value.length < props.dokumen.data.length
)
</script>

<template>
  <AppLayout>
    <template #header>
      <Heading>Verifikasi Dokumen</Heading>
    </template>

    <Head title="Verifikasi Dokumen" />

    <div class="space-y-6">
      <!-- Filters -->
      <Card>
        <CardHeader>
          <CardTitle>Filter Dokumen</CardTitle>
        </CardHeader>
        <CardContent>
          <form @submit.prevent="search" class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <Label for="search">Cari</Label>
              <Input
                id="search"
                v-model="searchForm.search"
                placeholder="Nama peserta atau nomor pendaftaran..."
              />
            </div>

            <div>
              <Label for="status">Status</Label>
              <select
                id="status"
                v-model="searchForm.status"
                class="flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50"
              >
                <option value="">Semua Status</option>
                <option value="pending">Menunggu Verifikasi</option>
                <option value="approved">Disetujui</option>
                <option value="rejected">Ditolak</option>
              </select>
            </div>

            <div>
              <Label for="jenis_dokumen">Jenis Dokumen</Label>
              <select
                id="jenis_dokumen"
                v-model="searchForm.jenis_dokumen"
                class="flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50"
              >
                <option value="">Semua Jenis</option>
                <option v-for="(label, key) in jenisOptions" :key="key" :value="key">
                  {{ label }}
                </option>
              </select>
            </div>

            <div class="flex items-end space-x-2">
              <Button type="submit" :disabled="searchForm.processing">
                <Icon name="search" class="w-4 h-4 mr-2" />
                Cari
              </Button>
              <Button type="button" variant="outline" @click="clearFilters">
                <Icon name="x" class="w-4 h-4 mr-2" />
                Reset
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>

      <!-- Bulk Actions -->
      <Card v-if="selectedDokumen.length > 0">
        <CardContent class="pt-6">
          <div class="flex items-center justify-between">
            <span class="text-sm text-gray-600">
              {{ selectedDokumen.length }} dokumen dipilih
            </span>
            <Button @click="openBulkDialog">
              <Icon name="check-circle" class="w-4 h-4 mr-2" />
              Verifikasi Massal
            </Button>
          </div>
        </CardContent>
      </Card>

      <!-- Documents List -->
      <Card>
        <CardHeader>
          <div class="flex items-center justify-between">
            <div>
              <CardTitle>Daftar Dokumen</CardTitle>
              <CardDescription>
                Total: {{ dokumen.total }} dokumen
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div v-if="dokumen.data.length === 0" class="text-center py-8">
            <Icon name="file-x" class="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 class="text-lg font-medium text-gray-900 mb-2">Tidak Ada Dokumen</h3>
            <p class="text-gray-600">Tidak ada dokumen yang sesuai dengan filter yang dipilih.</p>
          </div>

          <div v-else class="space-y-4">
            <!-- Select All -->
            <div class="flex items-center space-x-2 pb-2 border-b">
              <Checkbox
                :checked="allSelected"
                :indeterminate="someSelected"
                @update:checked="toggleSelectAll"
              />
              <Label class="text-sm font-medium">Pilih Semua</Label>
            </div>

            <!-- Document Items -->
            <div
              v-for="dokumen in dokumen.data"
              :key="dokumen.id_dokumen"
              class="flex items-center space-x-4 p-4 border rounded-lg hover:bg-gray-50"
            >
              <Checkbox
                :checked="selectedDokumen.includes(dokumen.id_dokumen)"
                @update:checked="(checked) => {
                  if (checked) {
                    selectedDokumen.push(dokumen.id_dokumen)
                  } else {
                    const index = selectedDokumen.indexOf(dokumen.id_dokumen)
                    if (index > -1) selectedDokumen.splice(index, 1)
                  }
                }"
              />

              <Icon name="file-text" class="h-8 w-8 text-gray-500 flex-shrink-0" />

              <div class="flex-1 min-w-0">
                <div class="flex items-center space-x-2 mb-1">
                  <h4 class="font-medium truncate">{{ jenisOptions[dokumen.jenis_dokumen] || dokumen.jenis_dokumen }}</h4>
                  <Badge :class="getStatusColor(dokumen.status_verifikasi)">
                    {{ getStatusText(dokumen.status_verifikasi) }}
                  </Badge>
                </div>
                <p class="text-sm text-gray-600 truncate">{{ dokumen.nama_file }}</p>
                <p class="text-xs text-gray-500">{{ formatFileSize(dokumen.ukuran_file) }}</p>
                <div class="text-xs text-gray-500 mt-1">
                  <span>{{ dokumen.pendaftaran.peserta.nama_lengkap }}</span>
                  <span class="mx-1">•</span>
                  <span>{{ dokumen.pendaftaran.nomor_pendaftaran }}</span>
                  <span class="mx-1">•</span>
                  <span>{{ dokumen.pendaftaran.golongan.nama_golongan }}</span>
                </div>
                <p class="text-xs text-gray-400 mt-1">
                  Diupload: {{ formatDate(dokumen.created_at) }}
                </p>
                <p v-if="dokumen.catatan_verifikasi" class="text-xs text-red-600 mt-1">
                  Catatan: {{ dokumen.catatan_verifikasi }}
                </p>
              </div>

              <div class="flex space-x-1 flex-shrink-0">
                <Button
                  as="link"
                  :href="route('admin.dokumen-verifikasi.show', dokumen.id_dokumen)"
                  size="sm"
                  variant="outline"
                >
                  <Icon name="eye" class="w-4 h-4" />
                </Button>

                <Button
                  as="link"
                  :href="route('admin.dokumen-verifikasi.download', dokumen.id_dokumen)"
                  size="sm"
                  variant="outline"
                >
                  <Icon name="download" class="w-4 h-4" />
                </Button>

                <Button
                  v-if="dokumen.status_verifikasi === 'pending'"
                  @click="openVerifyDialog(dokumen)"
                  size="sm"
                  variant="outline"
                >
                  <Icon name="check-circle" class="w-4 h-4" />
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- Pagination -->
      <div v-if="dokumen.last_page > 1" class="flex justify-center">
        <nav class="flex items-center space-x-1">
          <Link
            v-for="link in dokumen.links"
            :key="link.label"
            :href="link.url"
            :class="[
              'px-3 py-2 text-sm rounded-md',
              link.active
                ? 'bg-emerald-600 text-white'
                : link.url
                  ? 'text-gray-700 hover:bg-gray-100'
                  : 'text-gray-400 cursor-not-allowed'
            ]"
            v-html="link.label"
          />
        </nav>
      </div>

      <!-- Verify Dialog -->
      <Dialog v-model:open="showVerifyDialog">
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Verifikasi Dokumen</DialogTitle>
            <DialogDescription>
              Setujui atau tolak dokumen yang diupload peserta.
            </DialogDescription>
          </DialogHeader>
          <form @submit.prevent="verifyDocument" class="space-y-4">
            <div class="grid gap-2">
              <Label for="status_verifikasi">Status Verifikasi</Label>
              <select
                id="status_verifikasi"
                v-model="verifyForm.status_verifikasi"
                required
                class="flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50"
              >
                <option value="">Pilih status</option>
                <option value="approved">Setujui</option>
                <option value="rejected">Tolak</option>
              </select>
            </div>

            <div class="grid gap-2">
              <Label for="catatan_verifikasi">Catatan (Opsional)</Label>
              <Textarea
                id="catatan_verifikasi"
                v-model="verifyForm.catatan_verifikasi"
                placeholder="Tambahkan catatan verifikasi..."
                rows="3"
              />
            </div>

            <div class="flex justify-end space-x-2">
              <Button type="button" variant="outline" @click="showVerifyDialog = false">
                Batal
              </Button>
              <Button type="submit" :disabled="verifyForm.processing">
                <Icon v-if="verifyForm.processing" name="loader-2" class="w-4 h-4 mr-2 animate-spin" />
                Verifikasi
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>

      <!-- Bulk Verify Dialog -->
      <Dialog v-model:open="showBulkDialog">
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Verifikasi Massal</DialogTitle>
            <DialogDescription>
              Verifikasi {{ selectedDokumen.length }} dokumen sekaligus.
            </DialogDescription>
          </DialogHeader>
          <form @submit.prevent="bulkVerify" class="space-y-4">
            <div class="grid gap-2">
              <Label for="bulk_status_verifikasi">Status Verifikasi</Label>
              <select
                id="bulk_status_verifikasi"
                v-model="bulkForm.status_verifikasi"
                required
                class="flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50"
              >
                <option value="">Pilih status</option>
                <option value="approved">Setujui Semua</option>
                <option value="rejected">Tolak Semua</option>
              </select>
            </div>

            <div class="grid gap-2">
              <Label for="bulk_catatan_verifikasi">Catatan (Opsional)</Label>
              <Textarea
                id="bulk_catatan_verifikasi"
                v-model="bulkForm.catatan_verifikasi"
                placeholder="Tambahkan catatan verifikasi untuk semua dokumen..."
                rows="3"
              />
            </div>

            <div class="flex justify-end space-x-2">
              <Button type="button" variant="outline" @click="showBulkDialog = false">
                Batal
              </Button>
              <Button type="submit" :disabled="bulkForm.processing">
                <Icon v-if="bulkForm.processing" name="loader-2" class="w-4 h-4 mr-2 animate-spin" />
                Verifikasi {{ selectedDokumen.length }} Dokumen
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  </AppLayout>
</template>
