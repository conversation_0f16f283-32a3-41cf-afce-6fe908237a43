import{d as I,x as N,s as P,c as y,o as d,w as i,a as t,b as s,u as a,g as K,e as o,f as T,h as m,i as u,n as f,t as _,F as V,m as $}from"./app-B_pmlBSQ.js";import{_ as U}from"./AppLayout.vue_vue_type_script_setup_true_lang-BRf8lUiM.js";import{_ as q}from"./Heading.vue_vue_type_script_setup_true_lang-is4VBSCS.js";import{_ as c}from"./AppLogoIcon.vue_vue_type_script_setup_true_lang-wzz4PWna.js";import{_ as D,a as j}from"./CardContent.vue_vue_type_script_setup_true_lang-ClvLNBXC.js";import{_ as A}from"./CardDescription.vue_vue_type_script_setup_true_lang-DboywBIK.js";import{_ as M,a as F}from"./CardTitle.vue_vue_type_script_setup_true_lang-5HO6oSoB.js";import{_ as W}from"./Input.vue_vue_type_script_setup_true_lang-DB1-RsTn.js";import{_ as p}from"./Label.vue_vue_type_script_setup_true_lang-rj_fhE7R.js";import{_ as w,a as v,b as k,c as b,d as h}from"./SelectValue.vue_vue_type_script_setup_true_lang-IzlIronZ.js";import{_ as z}from"./Icon.vue_vue_type_script_setup_true_lang-BuYzjckr.js";import"./useForwardExpose-CO14IhkA.js";import"./RovingFocusGroup-lsWyU4xZ.js";import"./check-DtmHjdxf.js";import"./useFormControl-mZRLifGx.js";import"./loader-circle-6d8QrWFr.js";import"./sun-CHIV9RJ8.js";const E={class:"max-w-2xl mx-auto"},H={class:"space-y-4"},R={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},G={key:0,class:"text-sm text-red-600 mt-1"},J={key:0,class:"text-sm text-red-600 mt-1"},O={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},Q={key:0,class:"text-sm text-red-600 mt-1"},X={key:0,class:"text-sm text-red-600 mt-1"},Y={key:0},Z={key:0,class:"text-sm text-red-600 mt-1"},aa={class:"flex justify-end space-x-4 pt-6 border-t"},va=I({__name:"Create",props:{parentWilayah:{},levels:{}},setup(B){const L=[{title:"Dashboard",href:"/admin/dashboard"},{title:"Manajemen Wilayah",href:"/admin/wilayah"},{title:"Tambah Wilayah",href:"/admin/wilayah/create"}],g=B,l=N({kode_wilayah:"",nama_wilayah:"",level_wilayah:"",parent_id:"",status:"aktif"});P(()=>l.level_wilayah,n=>{n==="provinsi"&&(l.parent_id="")});const C=()=>l.level_wilayah==="provinsi"?[]:l.level_wilayah==="kabupaten"||l.level_wilayah==="kota"?g.parentWilayah.filter(n=>n.level_wilayah==="provinsi"):g.parentWilayah.filter(n=>n.level_wilayah==="kabupaten"||n.level_wilayah==="kota"),S=()=>{l.post(route("admin.wilayah.store"),{onSuccess:()=>{}})};return(n,e)=>(d(),y(U,{breadcrumbs:L},{default:i(()=>[t(a(K),{title:"Tambah Wilayah"}),t(q,{title:"Tambah Wilayah"}),s("div",E,[t(a(D),null,{default:i(()=>[t(a(M),null,{default:i(()=>[t(a(F),null,{default:i(()=>e[6]||(e[6]=[o("Informasi Wilayah Baru")])),_:1,__:[6]}),t(a(A),null,{default:i(()=>e[7]||(e[7]=[o(" Lengkapi form di bawah untuk menambahkan wilayah baru ")])),_:1,__:[7]})]),_:1}),t(a(j),null,{default:i(()=>[s("form",{onSubmit:T(S,["prevent"]),class:"space-y-6"},[s("div",H,[e[16]||(e[16]=s("h3",{class:"text-lg font-medium"},"Informasi Dasar",-1)),s("div",R,[s("div",null,[t(a(p),{for:"kode_wilayah"},{default:i(()=>e[8]||(e[8]=[o("Kode Wilayah *")])),_:1,__:[8]}),t(a(W),{id:"kode_wilayah",modelValue:a(l).kode_wilayah,"onUpdate:modelValue":e[0]||(e[0]=r=>a(l).kode_wilayah=r),type:"text",required:"",placeholder:"Contoh: LP, BDL, MTR",class:f({"border-red-500":a(l).errors.kode_wilayah})},null,8,["modelValue","class"]),a(l).errors.kode_wilayah?(d(),m("p",G,_(a(l).errors.kode_wilayah),1)):u("",!0)]),s("div",null,[t(a(p),{for:"nama_wilayah"},{default:i(()=>e[9]||(e[9]=[o("Nama Wilayah *")])),_:1,__:[9]}),t(a(W),{id:"nama_wilayah",modelValue:a(l).nama_wilayah,"onUpdate:modelValue":e[1]||(e[1]=r=>a(l).nama_wilayah=r),type:"text",required:"",placeholder:"Contoh: Lampung, Bandar Lampung",class:f({"border-red-500":a(l).errors.nama_wilayah})},null,8,["modelValue","class"]),a(l).errors.nama_wilayah?(d(),m("p",J,_(a(l).errors.nama_wilayah),1)):u("",!0)])]),s("div",O,[s("div",null,[t(a(p),{for:"level_wilayah"},{default:i(()=>e[10]||(e[10]=[o("Level Wilayah *")])),_:1,__:[10]}),t(a(w),{modelValue:a(l).level_wilayah,"onUpdate:modelValue":e[2]||(e[2]=r=>a(l).level_wilayah=r),required:""},{default:i(()=>[t(a(v),{class:f({"border-red-500":a(l).errors.level_wilayah})},{default:i(()=>[t(a(k),{placeholder:"Pilih Level"})]),_:1},8,["class"]),t(a(b),null,{default:i(()=>[(d(!0),m(V,null,$(n.levels,(r,x)=>(d(),y(a(h),{key:x,value:x},{default:i(()=>[o(_(r),1)]),_:2},1032,["value"]))),128))]),_:1})]),_:1},8,["modelValue"]),a(l).errors.level_wilayah?(d(),m("p",Q,_(a(l).errors.level_wilayah),1)):u("",!0)]),s("div",null,[t(a(p),{for:"status"},{default:i(()=>e[11]||(e[11]=[o("Status *")])),_:1,__:[11]}),t(a(w),{modelValue:a(l).status,"onUpdate:modelValue":e[3]||(e[3]=r=>a(l).status=r),required:""},{default:i(()=>[t(a(v),{class:f({"border-red-500":a(l).errors.status})},{default:i(()=>[t(a(k),{placeholder:"Pilih Status"})]),_:1},8,["class"]),t(a(b),null,{default:i(()=>[t(a(h),{value:"aktif"},{default:i(()=>e[12]||(e[12]=[o("Aktif")])),_:1,__:[12]}),t(a(h),{value:"non_aktif"},{default:i(()=>e[13]||(e[13]=[o("Non Aktif")])),_:1,__:[13]})]),_:1})]),_:1},8,["modelValue"]),a(l).errors.status?(d(),m("p",X,_(a(l).errors.status),1)):u("",!0)])]),a(l).level_wilayah!=="provinsi"?(d(),m("div",Y,[t(a(p),{for:"parent_id"},{default:i(()=>e[14]||(e[14]=[o("Wilayah Induk")])),_:1,__:[14]}),t(a(w),{modelValue:a(l).parent_id,"onUpdate:modelValue":e[4]||(e[4]=r=>a(l).parent_id=r)},{default:i(()=>[t(a(v),{class:f({"border-red-500":a(l).errors.parent_id})},{default:i(()=>[t(a(k),{placeholder:"Pilih Wilayah Induk"})]),_:1},8,["class"]),t(a(b),null,{default:i(()=>[(d(!0),m(V,null,$(C(),r=>(d(),y(a(h),{key:r.id_wilayah,value:r.id_wilayah.toString()},{default:i(()=>[o(_(r.nama_wilayah),1)]),_:2},1032,["value"]))),128))]),_:1})]),_:1},8,["modelValue"]),a(l).errors.parent_id?(d(),m("p",Z,_(a(l).errors.parent_id),1)):u("",!0),e[15]||(e[15]=s("p",{class:"text-sm text-gray-500 mt-1"}," Kosongkan jika wilayah ini tidak memiliki induk ",-1))])):u("",!0)]),e[19]||(e[19]=s("div",{class:"bg-blue-50 p-4 rounded-lg"},[s("h4",{class:"font-medium text-blue-900 mb-2"},"Informasi Hierarki"),s("div",{class:"text-sm text-blue-800 space-y-1"},[s("p",null,[s("strong",null,"Provinsi:"),o(" Level tertinggi dalam hierarki wilayah")]),s("p",null,[s("strong",null,"Kabupaten/Kota:"),o(" Berada di bawah provinsi")]),s("p",null,[s("strong",null,"Kecamatan:"),o(" Berada di bawah kabupaten/kota (opsional)")])])],-1)),s("div",aa,[t(c,{type:"button",variant:"outline",onClick:e[5]||(e[5]=r=>n.$inertia.visit(n.route("admin.wilayah.index")))},{default:i(()=>e[17]||(e[17]=[o(" Batal ")])),_:1,__:[17]}),t(c,{type:"submit",disabled:a(l).processing},{default:i(()=>[a(l).processing?(d(),y(z,{key:0,name:"loader-2",class:"w-4 h-4 mr-2 animate-spin"})):u("",!0),e[18]||(e[18]=o(" Simpan Wilayah "))]),_:1,__:[18]},8,["disabled"])])],32)]),_:1})]),_:1})])]),_:1}))}});export{va as default};
