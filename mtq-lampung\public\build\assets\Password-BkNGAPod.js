import{d as g,l as f,x as v,c as y,o as V,w as a,a as r,u as s,g as b,b as t,f as x,e as d,aj as k,j as I,L as S}from"./app-B_pmlBSQ.js";import{_ as u}from"./InputError.vue_vue_type_script_setup_true_lang-A-vdzbq9.js";import{_ as $}from"./AppLayout.vue_vue_type_script_setup_true_lang-BRf8lUiM.js";import{_ as C,a as P}from"./Layout.vue_vue_type_script_setup_true_lang-DmO1Dm59.js";import{_ as N}from"./AppLogoIcon.vue_vue_type_script_setup_true_lang-wzz4PWna.js";import{_ as m}from"./Input.vue_vue_type_script_setup_true_lang-DB1-RsTn.js";import{_ as c}from"./Label.vue_vue_type_script_setup_true_lang-rj_fhE7R.js";import"./useForwardExpose-CO14IhkA.js";import"./RovingFocusGroup-lsWyU4xZ.js";import"./Heading.vue_vue_type_script_setup_true_lang-is4VBSCS.js";const E={class:"space-y-6"},T={class:"grid gap-2"},U={class:"grid gap-2"},B={class:"grid gap-2"},L={class:"flex items-center gap-4"},M={class:"text-sm text-neutral-600"},O=g({__name:"Password",setup(j){const w=[{title:"Password settings",href:"/settings/password"}],l=f(null),p=f(null),e=v({current_password:"",password:"",password_confirmation:""}),_=()=>{e.put(route("password.update"),{preserveScroll:!0,onSuccess:()=>e.reset(),onError:i=>{i.password&&(e.reset("password","password_confirmation"),l.value instanceof HTMLInputElement&&l.value.focus()),i.current_password&&(e.reset("current_password"),p.value instanceof HTMLInputElement&&p.value.focus())}})};return(i,o)=>(V(),y($,{breadcrumbs:w},{default:a(()=>[r(s(b),{title:"Password settings"}),r(C,null,{default:a(()=>[t("div",E,[r(P,{title:"Update password",description:"Ensure your account is using a long, random password to stay secure"}),t("form",{onSubmit:x(_,["prevent"]),class:"space-y-6"},[t("div",T,[r(s(c),{for:"current_password"},{default:a(()=>o[3]||(o[3]=[d("Current password")])),_:1,__:[3]}),r(s(m),{id:"current_password",ref_key:"currentPasswordInput",ref:p,modelValue:s(e).current_password,"onUpdate:modelValue":o[0]||(o[0]=n=>s(e).current_password=n),type:"password",class:"mt-1 block w-full",autocomplete:"current-password",placeholder:"Current password"},null,8,["modelValue"]),r(u,{message:s(e).errors.current_password},null,8,["message"])]),t("div",U,[r(s(c),{for:"password"},{default:a(()=>o[4]||(o[4]=[d("New password")])),_:1,__:[4]}),r(s(m),{id:"password",ref_key:"passwordInput",ref:l,modelValue:s(e).password,"onUpdate:modelValue":o[1]||(o[1]=n=>s(e).password=n),type:"password",class:"mt-1 block w-full",autocomplete:"new-password",placeholder:"New password"},null,8,["modelValue"]),r(u,{message:s(e).errors.password},null,8,["message"])]),t("div",B,[r(s(c),{for:"password_confirmation"},{default:a(()=>o[5]||(o[5]=[d("Confirm password")])),_:1,__:[5]}),r(s(m),{id:"password_confirmation",modelValue:s(e).password_confirmation,"onUpdate:modelValue":o[2]||(o[2]=n=>s(e).password_confirmation=n),type:"password",class:"mt-1 block w-full",autocomplete:"new-password",placeholder:"Confirm password"},null,8,["modelValue"]),r(u,{message:s(e).errors.password_confirmation},null,8,["message"])]),t("div",L,[r(s(N),{disabled:s(e).processing},{default:a(()=>o[6]||(o[6]=[d("Save password")])),_:1,__:[6]},8,["disabled"]),r(k,{"enter-active-class":"transition ease-in-out","enter-from-class":"opacity-0","leave-active-class":"transition ease-in-out","leave-to-class":"opacity-0"},{default:a(()=>[I(t("p",M,"Saved.",512),[[S,s(e).recentlySuccessful]])]),_:1})])],32)])]),_:1})]),_:1}))}});export{O as default};
