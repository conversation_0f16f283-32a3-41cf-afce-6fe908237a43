import{d as D,x as N,c as g,o as d,w as i,a as t,b as l,u as a,g as S,e as o,f as I,h as m,i as u,n as p,F as V,m as w,t as n,j as U,v as $}from"./app-B_pmlBSQ.js";import{_ as P}from"./AppLayout.vue_vue_type_script_setup_true_lang-BRf8lUiM.js";import{_ as A}from"./Heading.vue_vue_type_script_setup_true_lang-is4VBSCS.js";import{_ as T}from"./AppLogoIcon.vue_vue_type_script_setup_true_lang-wzz4PWna.js";import{_ as C,a as B}from"./CardContent.vue_vue_type_script_setup_true_lang-ClvLNBXC.js";import{_ as K}from"./CardDescription.vue_vue_type_script_setup_true_lang-DboywBIK.js";import{_ as L,a as M}from"./CardTitle.vue_vue_type_script_setup_true_lang-5HO6oSoB.js";import{_ as f}from"./Input.vue_vue_type_script_setup_true_lang-DB1-RsTn.js";import{_}from"./Label.vue_vue_type_script_setup_true_lang-rj_fhE7R.js";import{_ as y,a as b,b as h,c as v,d as x}from"./SelectValue.vue_vue_type_script_setup_true_lang-IzlIronZ.js";import{_ as z}from"./Icon.vue_vue_type_script_setup_true_lang-BuYzjckr.js";import"./useForwardExpose-CO14IhkA.js";import"./RovingFocusGroup-lsWyU4xZ.js";import"./check-DtmHjdxf.js";import"./useFormControl-mZRLifGx.js";import"./loader-circle-6d8QrWFr.js";import"./sun-CHIV9RJ8.js";const F={class:"max-w-4xl mx-auto"},W={class:"space-y-4"},E={key:0,class:"text-sm text-red-600 mt-1"},G={class:"space-y-4"},Q={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},R={key:0,class:"text-sm text-red-600 mt-1"},J={key:0,class:"text-sm text-red-600 mt-1"},O={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},X={key:0,class:"text-sm text-red-600 mt-1"},Y={key:0,class:"text-sm text-red-600 mt-1"},Z={key:0,class:"text-sm text-red-600 mt-1"},c={class:"space-y-4"},aa={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},ea={key:0,class:"text-sm text-red-600 mt-1"},ra={key:0,class:"text-sm text-red-600 mt-1"},ta={key:0,class:"text-sm text-red-600 mt-1"},la={class:"space-y-4"},sa={key:0,class:"text-sm text-red-600 mt-1"},ia={key:0,class:"text-sm text-red-600 mt-1"},oa={class:"space-y-4"},da={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},na={key:0,class:"text-sm text-red-600 mt-1"},ma={key:0,class:"text-sm text-red-600 mt-1"},ua={key:0,class:"text-sm text-red-600 mt-1"},pa={class:"flex justify-end space-x-4 pt-6 border-t"},Na=D({__name:"Create",props:{availableUsers:{},wilayah:{},tipeHakim:{}},setup(_a){const H=[{title:"Dashboard",href:"/admin/dashboard"},{title:"Manajemen Dewan Hakim",href:"/admin/dewan-hakim"},{title:"Tambah Dewan Hakim",href:"/admin/dewan-hakim/create"}],r=N({id_user:"",nik:"",nama_lengkap:"",tempat_lahir:"",tanggal_lahir:"",pekerjaan:"",unit_kerja:"",alamat_rumah:"",alamat_kantor:"",no_telepon:"",spesialisasi:"",tipe_hakim:"",id_wilayah:"",status:"aktif"}),q=()=>{r.post(route("admin.dewan-hakim.store"),{onSuccess:()=>{}})};return(k,e)=>(d(),g(P,{breadcrumbs:H},{default:i(()=>[t(a(S),{title:"Tambah Dewan Hakim"}),t(A,{title:"Tambah Dewan Hakim"}),l("div",F,[t(a(C),null,{default:i(()=>[t(a(L),null,{default:i(()=>[t(a(M),null,{default:i(()=>e[15]||(e[15]=[o("Informasi Dewan Hakim Baru")])),_:1,__:[15]}),t(a(K),null,{default:i(()=>e[16]||(e[16]=[o(" Lengkapi form di bawah untuk menambahkan dewan hakim baru ")])),_:1,__:[16]})]),_:1}),t(a(B),null,{default:i(()=>[l("form",{onSubmit:I(q,["prevent"]),class:"space-y-8"},[l("div",W,[e[19]||(e[19]=l("h3",{class:"text-lg font-medium"},"Akun User",-1)),l("div",null,[t(a(_),{for:"id_user"},{default:i(()=>e[17]||(e[17]=[o("Pilih User *")])),_:1,__:[17]}),t(a(y),{modelValue:a(r).id_user,"onUpdate:modelValue":e[0]||(e[0]=s=>a(r).id_user=s),required:""},{default:i(()=>[t(a(b),{class:p({"border-red-500":a(r).errors.id_user})},{default:i(()=>[t(a(h),{placeholder:"Pilih user yang akan dijadikan dewan hakim"})]),_:1},8,["class"]),t(a(v),null,{default:i(()=>[(d(!0),m(V,null,w(k.availableUsers,s=>(d(),g(a(x),{key:s.id_user,value:s.id_user.toString()},{default:i(()=>[o(n(s.nama_lengkap)+" ("+n(s.username)+" - "+n(s.email)+") ",1)]),_:2},1032,["value"]))),128))]),_:1})]),_:1},8,["modelValue"]),a(r).errors.id_user?(d(),m("p",E,n(a(r).errors.id_user),1)):u("",!0),e[18]||(e[18]=l("p",{class:"text-sm text-gray-500 mt-1"},' Hanya user dengan role "dewan_hakim" yang belum memiliki profil yang ditampilkan ',-1))])]),l("div",G,[e[25]||(e[25]=l("h3",{class:"text-lg font-medium"},"Informasi Pribadi",-1)),l("div",Q,[l("div",null,[t(a(_),{for:"nik"},{default:i(()=>e[20]||(e[20]=[o("NIK *")])),_:1,__:[20]}),t(a(f),{id:"nik",modelValue:a(r).nik,"onUpdate:modelValue":e[1]||(e[1]=s=>a(r).nik=s),type:"text",required:"",maxlength:"16",placeholder:"16 digit NIK",class:p({"border-red-500":a(r).errors.nik})},null,8,["modelValue","class"]),a(r).errors.nik?(d(),m("p",R,n(a(r).errors.nik),1)):u("",!0)]),l("div",null,[t(a(_),{for:"nama_lengkap"},{default:i(()=>e[21]||(e[21]=[o("Nama Lengkap *")])),_:1,__:[21]}),t(a(f),{id:"nama_lengkap",modelValue:a(r).nama_lengkap,"onUpdate:modelValue":e[2]||(e[2]=s=>a(r).nama_lengkap=s),type:"text",required:"",placeholder:"Nama lengkap sesuai KTP",class:p({"border-red-500":a(r).errors.nama_lengkap})},null,8,["modelValue","class"]),a(r).errors.nama_lengkap?(d(),m("p",J,n(a(r).errors.nama_lengkap),1)):u("",!0)])]),l("div",O,[l("div",null,[t(a(_),{for:"tempat_lahir"},{default:i(()=>e[22]||(e[22]=[o("Tempat Lahir *")])),_:1,__:[22]}),t(a(f),{id:"tempat_lahir",modelValue:a(r).tempat_lahir,"onUpdate:modelValue":e[3]||(e[3]=s=>a(r).tempat_lahir=s),type:"text",required:"",placeholder:"Kota tempat lahir",class:p({"border-red-500":a(r).errors.tempat_lahir})},null,8,["modelValue","class"]),a(r).errors.tempat_lahir?(d(),m("p",X,n(a(r).errors.tempat_lahir),1)):u("",!0)]),l("div",null,[t(a(_),{for:"tanggal_lahir"},{default:i(()=>e[23]||(e[23]=[o("Tanggal Lahir *")])),_:1,__:[23]}),t(a(f),{id:"tanggal_lahir",modelValue:a(r).tanggal_lahir,"onUpdate:modelValue":e[4]||(e[4]=s=>a(r).tanggal_lahir=s),type:"date",required:"",class:p({"border-red-500":a(r).errors.tanggal_lahir})},null,8,["modelValue","class"]),a(r).errors.tanggal_lahir?(d(),m("p",Y,n(a(r).errors.tanggal_lahir),1)):u("",!0)])]),l("div",null,[t(a(_),{for:"no_telepon"},{default:i(()=>e[24]||(e[24]=[o("No. Telepon *")])),_:1,__:[24]}),t(a(f),{id:"no_telepon",modelValue:a(r).no_telepon,"onUpdate:modelValue":e[5]||(e[5]=s=>a(r).no_telepon=s),type:"tel",required:"",placeholder:"08xxxxxxxxxx",class:p({"border-red-500":a(r).errors.no_telepon})},null,8,["modelValue","class"]),a(r).errors.no_telepon?(d(),m("p",Z,n(a(r).errors.no_telepon),1)):u("",!0)])]),l("div",c,[e[29]||(e[29]=l("h3",{class:"text-lg font-medium"},"Informasi Profesi",-1)),l("div",aa,[l("div",null,[t(a(_),{for:"pekerjaan"},{default:i(()=>e[26]||(e[26]=[o("Pekerjaan *")])),_:1,__:[26]}),t(a(f),{id:"pekerjaan",modelValue:a(r).pekerjaan,"onUpdate:modelValue":e[6]||(e[6]=s=>a(r).pekerjaan=s),type:"text",required:"",placeholder:"Contoh: Guru, Dosen, Ustadz",class:p({"border-red-500":a(r).errors.pekerjaan})},null,8,["modelValue","class"]),a(r).errors.pekerjaan?(d(),m("p",ea,n(a(r).errors.pekerjaan),1)):u("",!0)]),l("div",null,[t(a(_),{for:"unit_kerja"},{default:i(()=>e[27]||(e[27]=[o("Unit Kerja *")])),_:1,__:[27]}),t(a(f),{id:"unit_kerja",modelValue:a(r).unit_kerja,"onUpdate:modelValue":e[7]||(e[7]=s=>a(r).unit_kerja=s),type:"text",required:"",placeholder:"Nama instansi/lembaga",class:p({"border-red-500":a(r).errors.unit_kerja})},null,8,["modelValue","class"]),a(r).errors.unit_kerja?(d(),m("p",ra,n(a(r).errors.unit_kerja),1)):u("",!0)])]),l("div",null,[t(a(_),{for:"spesialisasi"},{default:i(()=>e[28]||(e[28]=[o("Spesialisasi *")])),_:1,__:[28]}),t(a(f),{id:"spesialisasi",modelValue:a(r).spesialisasi,"onUpdate:modelValue":e[8]||(e[8]=s=>a(r).spesialisasi=s),type:"text",required:"",placeholder:"Contoh: Tilawah, Tahfidz, Tafsir",class:p({"border-red-500":a(r).errors.spesialisasi})},null,8,["modelValue","class"]),a(r).errors.spesialisasi?(d(),m("p",ta,n(a(r).errors.spesialisasi),1)):u("",!0)])]),l("div",la,[e[32]||(e[32]=l("h3",{class:"text-lg font-medium"},"Informasi Alamat",-1)),l("div",null,[t(a(_),{for:"alamat_rumah"},{default:i(()=>e[30]||(e[30]=[o("Alamat Rumah *")])),_:1,__:[30]}),U(l("textarea",{id:"alamat_rumah","onUpdate:modelValue":e[9]||(e[9]=s=>a(r).alamat_rumah=s),rows:"3",required:"",class:p(["w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",{"border-red-500":a(r).errors.alamat_rumah}]),placeholder:"Alamat lengkap tempat tinggal"},null,2),[[$,a(r).alamat_rumah]]),a(r).errors.alamat_rumah?(d(),m("p",sa,n(a(r).errors.alamat_rumah),1)):u("",!0)]),l("div",null,[t(a(_),{for:"alamat_kantor"},{default:i(()=>e[31]||(e[31]=[o("Alamat Kantor")])),_:1,__:[31]}),U(l("textarea",{id:"alamat_kantor","onUpdate:modelValue":e[10]||(e[10]=s=>a(r).alamat_kantor=s),rows:"3",class:p(["w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",{"border-red-500":a(r).errors.alamat_kantor}]),placeholder:"Alamat tempat bekerja (opsional)"},null,2),[[$,a(r).alamat_kantor]]),a(r).errors.alamat_kantor?(d(),m("p",ia,n(a(r).errors.alamat_kantor),1)):u("",!0)])]),l("div",oa,[e[38]||(e[38]=l("h3",{class:"text-lg font-medium"},"Informasi Hakim",-1)),l("div",da,[l("div",null,[t(a(_),{for:"tipe_hakim"},{default:i(()=>e[33]||(e[33]=[o("Tipe Hakim *")])),_:1,__:[33]}),t(a(y),{modelValue:a(r).tipe_hakim,"onUpdate:modelValue":e[11]||(e[11]=s=>a(r).tipe_hakim=s),required:""},{default:i(()=>[t(a(b),{class:p({"border-red-500":a(r).errors.tipe_hakim})},{default:i(()=>[t(a(h),{placeholder:"Pilih Tipe"})]),_:1},8,["class"]),t(a(v),null,{default:i(()=>[(d(!0),m(V,null,w(k.tipeHakim,(s,j)=>(d(),g(a(x),{key:j,value:j},{default:i(()=>[o(n(s),1)]),_:2},1032,["value"]))),128))]),_:1})]),_:1},8,["modelValue"]),a(r).errors.tipe_hakim?(d(),m("p",na,n(a(r).errors.tipe_hakim),1)):u("",!0)]),l("div",null,[t(a(_),{for:"id_wilayah"},{default:i(()=>e[34]||(e[34]=[o("Wilayah *")])),_:1,__:[34]}),t(a(y),{modelValue:a(r).id_wilayah,"onUpdate:modelValue":e[12]||(e[12]=s=>a(r).id_wilayah=s),required:""},{default:i(()=>[t(a(b),{class:p({"border-red-500":a(r).errors.id_wilayah})},{default:i(()=>[t(a(h),{placeholder:"Pilih Wilayah"})]),_:1},8,["class"]),t(a(v),null,{default:i(()=>[(d(!0),m(V,null,w(k.wilayah,s=>(d(),g(a(x),{key:s.id_wilayah,value:s.id_wilayah.toString()},{default:i(()=>[o(n(s.nama_wilayah),1)]),_:2},1032,["value"]))),128))]),_:1})]),_:1},8,["modelValue"]),a(r).errors.id_wilayah?(d(),m("p",ma,n(a(r).errors.id_wilayah),1)):u("",!0)]),l("div",null,[t(a(_),{for:"status"},{default:i(()=>e[35]||(e[35]=[o("Status *")])),_:1,__:[35]}),t(a(y),{modelValue:a(r).status,"onUpdate:modelValue":e[13]||(e[13]=s=>a(r).status=s),required:""},{default:i(()=>[t(a(b),{class:p({"border-red-500":a(r).errors.status})},{default:i(()=>[t(a(h),{placeholder:"Pilih Status"})]),_:1},8,["class"]),t(a(v),null,{default:i(()=>[t(a(x),{value:"aktif"},{default:i(()=>e[36]||(e[36]=[o("Aktif")])),_:1,__:[36]}),t(a(x),{value:"non_aktif"},{default:i(()=>e[37]||(e[37]=[o("Non Aktif")])),_:1,__:[37]})]),_:1})]),_:1},8,["modelValue"]),a(r).errors.status?(d(),m("p",ua,n(a(r).errors.status),1)):u("",!0)])])]),e[41]||(e[41]=l("div",{class:"bg-blue-50 p-4 rounded-lg"},[l("h4",{class:"font-medium text-blue-900 mb-2"},"Informasi Dewan Hakim"),l("div",{class:"text-sm text-blue-800 space-y-1"},[l("p",null,[l("strong",null,"Tipe Undangan:"),o(" Hakim yang diundang khusus dari luar daerah")]),l("p",null,[l("strong",null,"Tipe Kabupaten:"),o(" Hakim dari wilayah kabupaten/kota setempat")]),l("p",null,[l("strong",null,"Spesialisasi:"),o(" Bidang keahlian dalam penilaian MTQ")])])],-1)),l("div",pa,[t(T,{type:"button",variant:"outline",onClick:e[14]||(e[14]=s=>k.$inertia.visit(k.route("admin.dewan-hakim.index")))},{default:i(()=>e[39]||(e[39]=[o(" Batal ")])),_:1,__:[39]}),t(T,{type:"submit",disabled:a(r).processing},{default:i(()=>[a(r).processing?(d(),g(z,{key:0,name:"loader-2",class:"w-4 h-4 mr-2 animate-spin"})):u("",!0),e[40]||(e[40]=o(" Simpan Dewan Hakim "))]),_:1,__:[40]},8,["disabled"])])],32)]),_:1})]),_:1})])]),_:1}))}});export{Na as default};
