<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class JenisDokumen extends Model
{
    use HasFactory;

    protected $table = 'jenis_dokumen';
    protected $primaryKey = 'id_jenis_dokumen';

    protected $fillable = [
        'kode_dokumen',
        'nama_dokumen',
        'deskripsi',
        'kategori',
        'wajib',
        'format_file',
        'ukuran_max_kb',
        'status',
        'urutan'
    ];

    protected $casts = [
        'wajib' => 'boolean',
        'ukuran_max_kb' => 'integer',
        'urutan' => 'integer',
        'kategori' => 'string',
        'status' => 'string'
    ];

    // Relationships
    public function verifikasiDokumen(): HasMany
    {
        return $this->hasMany(VerifikasiDokumen::class, 'id_jenis_dokumen', 'id_jenis_dokumen');
    }

    // Scopes
    public function scopeAktif($query)
    {
        return $query->where('status', 'aktif');
    }

    public function scopeWajib($query)
    {
        return $query->where('wajib', true);
    }

    public function scopeByKategori($query, $kategori)
    {
        return $query->where('kategori', $kategori);
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('urutan')->orderBy('nama_dokumen');
    }

    // Accessors
    public function getFormatFileArrayAttribute()
    {
        return $this->format_file ? explode(',', $this->format_file) : [];
    }

    // Helper methods
    public function isFormatAllowed($extension)
    {
        $allowedFormats = $this->format_file_array;
        return empty($allowedFormats) || in_array(strtolower($extension), array_map('strtolower', $allowedFormats));
    }

    public function getMaxSizeBytes()
    {
        return $this->ukuran_max_kb * 1024;
    }
}
