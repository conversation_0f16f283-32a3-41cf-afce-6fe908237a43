import{d as P,x as c,c as k,o as u,w as l,a as r,b as t,u as a,g as j,e as n,f as B,h as d,i,n as _,t as m,F as G,m as L}from"./app-B_pmlBSQ.js";import{_ as q}from"./AppLayout.vue_vue_type_script_setup_true_lang-BRf8lUiM.js";import{_ as N}from"./Heading.vue_vue_type_script_setup_true_lang-is4VBSCS.js";import{_ as $}from"./AppLogoIcon.vue_vue_type_script_setup_true_lang-wzz4PWna.js";import{_ as K,a as T}from"./CardContent.vue_vue_type_script_setup_true_lang-ClvLNBXC.js";import{_ as S}from"./CardDescription.vue_vue_type_script_setup_true_lang-DboywBIK.js";import{_ as A,a as D}from"./CardTitle.vue_vue_type_script_setup_true_lang-5HO6oSoB.js";import{_ as g}from"./Input.vue_vue_type_script_setup_true_lang-DB1-RsTn.js";import{_ as f}from"./Label.vue_vue_type_script_setup_true_lang-rj_fhE7R.js";import{_ as x,a as y,b as V,c as v,d as p}from"./SelectValue.vue_vue_type_script_setup_true_lang-IzlIronZ.js";import{_ as I}from"./Icon.vue_vue_type_script_setup_true_lang-BuYzjckr.js";import"./useForwardExpose-CO14IhkA.js";import"./RovingFocusGroup-lsWyU4xZ.js";import"./check-DtmHjdxf.js";import"./useFormControl-mZRLifGx.js";import"./loader-circle-6d8QrWFr.js";import"./sun-CHIV9RJ8.js";const M={class:"max-w-2xl mx-auto"},J={class:"space-y-4"},z={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},h={key:0,class:"text-sm text-red-600 mt-1"},F={key:0,class:"text-sm text-red-600 mt-1"},R={key:0,class:"text-sm text-red-600 mt-1"},E={key:0,class:"text-sm text-red-600 mt-1"},H={class:"space-y-4"},O={key:0,class:"text-sm text-red-600 mt-1"},Q={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},W={key:0,class:"text-sm text-red-600 mt-1"},X={key:0,class:"text-sm text-red-600 mt-1"},Y={class:"space-y-4"},Z={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},aa={key:0,class:"text-sm text-red-600 mt-1"},ea={key:0,class:"text-sm text-red-600 mt-1"},oa={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},ra={key:0,class:"text-sm text-red-600 mt-1"},ta={key:0,class:"text-sm text-red-600 mt-1"},la={class:"flex justify-end space-x-4 pt-6 border-t"},wa=P({__name:"Create",props:{cabangLomba:{},selectedCabang:{}},setup(U){const w=[{title:"Dashboard",href:"/admin/dashboard"},{title:"Manajemen Golongan",href:"/admin/golongan"},{title:"Tambah Golongan",href:"/admin/golongan/create"}],o=c({kode_golongan:"",nama_golongan:"",id_cabang:U.selectedCabang||"",jenis_kelamin:"",batas_umur_min:"",batas_umur_max:"",kuota_max:"",biaya_pendaftaran:"",nomor_urut_awal:"",nomor_urut_akhir:"",status:"aktif"}),C=()=>{o.post(route("admin.golongan.store"),{onSuccess:()=>{}})};return(b,e)=>(u(),k(q,{breadcrumbs:w},{default:l(()=>[r(a(j),{title:"Tambah Golongan"}),r(N,{title:"Tambah Golongan"}),t("div",M,[r(a(K),null,{default:l(()=>[r(a(A),null,{default:l(()=>[r(a(D),null,{default:l(()=>e[12]||(e[12]=[n("Informasi Golongan Baru")])),_:1,__:[12]}),r(a(S),null,{default:l(()=>e[13]||(e[13]=[n(" Lengkapi form di bawah untuk menambahkan golongan baru ")])),_:1,__:[13]})]),_:1}),r(a(T),null,{default:l(()=>[t("form",{onSubmit:B(C,["prevent"]),class:"space-y-6"},[t("div",J,[e[20]||(e[20]=t("h3",{class:"text-lg font-medium"},"Informasi Dasar",-1)),t("div",z,[t("div",null,[r(a(f),{for:"kode_golongan"},{default:l(()=>e[14]||(e[14]=[n("Kode Golongan *")])),_:1,__:[14]}),r(a(g),{id:"kode_golongan",modelValue:a(o).kode_golongan,"onUpdate:modelValue":e[0]||(e[0]=s=>a(o).kode_golongan=s),type:"text",required:"",placeholder:"Contoh: TIL-1Pa, TAH-5Pi",class:_({"border-red-500":a(o).errors.kode_golongan})},null,8,["modelValue","class"]),a(o).errors.kode_golongan?(u(),d("p",h,m(a(o).errors.kode_golongan),1)):i("",!0)]),t("div",null,[r(a(f),{for:"status"},{default:l(()=>e[15]||(e[15]=[n("Status *")])),_:1,__:[15]}),r(a(x),{modelValue:a(o).status,"onUpdate:modelValue":e[1]||(e[1]=s=>a(o).status=s),required:""},{default:l(()=>[r(a(y),{class:_({"border-red-500":a(o).errors.status})},{default:l(()=>[r(a(V),{placeholder:"Pilih Status"})]),_:1},8,["class"]),r(a(v),null,{default:l(()=>[r(a(p),{value:"aktif"},{default:l(()=>e[16]||(e[16]=[n("Aktif")])),_:1,__:[16]}),r(a(p),{value:"non_aktif"},{default:l(()=>e[17]||(e[17]=[n("Non Aktif")])),_:1,__:[17]})]),_:1})]),_:1},8,["modelValue"]),a(o).errors.status?(u(),d("p",F,m(a(o).errors.status),1)):i("",!0)])]),t("div",null,[r(a(f),{for:"nama_golongan"},{default:l(()=>e[18]||(e[18]=[n("Nama Golongan *")])),_:1,__:[18]}),r(a(g),{id:"nama_golongan",modelValue:a(o).nama_golongan,"onUpdate:modelValue":e[2]||(e[2]=s=>a(o).nama_golongan=s),type:"text",required:"",placeholder:"Contoh: Tilawah 1 Putra, Tahfidz 5 Juz Putri",class:_({"border-red-500":a(o).errors.nama_golongan})},null,8,["modelValue","class"]),a(o).errors.nama_golongan?(u(),d("p",R,m(a(o).errors.nama_golongan),1)):i("",!0)]),t("div",null,[r(a(f),{for:"id_cabang"},{default:l(()=>e[19]||(e[19]=[n("Cabang Lomba *")])),_:1,__:[19]}),r(a(x),{modelValue:a(o).id_cabang,"onUpdate:modelValue":e[3]||(e[3]=s=>a(o).id_cabang=s),required:""},{default:l(()=>[r(a(y),{class:_({"border-red-500":a(o).errors.id_cabang})},{default:l(()=>[r(a(V),{placeholder:"Pilih Cabang Lomba"})]),_:1},8,["class"]),r(a(v),null,{default:l(()=>[(u(!0),d(G,null,L(b.cabangLomba,s=>(u(),k(a(p),{key:s.id_cabang,value:s.id_cabang.toString()},{default:l(()=>[n(m(s.nama_cabang)+" ("+m(s.kode_cabang)+") ",1)]),_:2},1032,["value"]))),128))]),_:1})]),_:1},8,["modelValue"]),a(o).errors.id_cabang?(u(),d("p",E,m(a(o).errors.id_cabang),1)):i("",!0)])]),t("div",H,[e[26]||(e[26]=t("h3",{class:"text-lg font-medium"},"Kriteria Peserta",-1)),t("div",null,[r(a(f),{for:"jenis_kelamin"},{default:l(()=>e[21]||(e[21]=[n("Jenis Kelamin *")])),_:1,__:[21]}),r(a(x),{modelValue:a(o).jenis_kelamin,"onUpdate:modelValue":e[4]||(e[4]=s=>a(o).jenis_kelamin=s),required:""},{default:l(()=>[r(a(y),{class:_({"border-red-500":a(o).errors.jenis_kelamin})},{default:l(()=>[r(a(V),{placeholder:"Pilih Jenis Kelamin"})]),_:1},8,["class"]),r(a(v),null,{default:l(()=>[r(a(p),{value:"L"},{default:l(()=>e[22]||(e[22]=[n("Laki-laki")])),_:1,__:[22]}),r(a(p),{value:"P"},{default:l(()=>e[23]||(e[23]=[n("Perempuan")])),_:1,__:[23]})]),_:1})]),_:1},8,["modelValue"]),a(o).errors.jenis_kelamin?(u(),d("p",O,m(a(o).errors.jenis_kelamin),1)):i("",!0)]),t("div",Q,[t("div",null,[r(a(f),{for:"batas_umur_min"},{default:l(()=>e[24]||(e[24]=[n("Batas Umur Minimum *")])),_:1,__:[24]}),r(a(g),{id:"batas_umur_min",modelValue:a(o).batas_umur_min,"onUpdate:modelValue":e[5]||(e[5]=s=>a(o).batas_umur_min=s),type:"number",required:"",min:"1",max:"100",placeholder:"Contoh: 7",class:_({"border-red-500":a(o).errors.batas_umur_min})},null,8,["modelValue","class"]),a(o).errors.batas_umur_min?(u(),d("p",W,m(a(o).errors.batas_umur_min),1)):i("",!0)]),t("div",null,[r(a(f),{for:"batas_umur_max"},{default:l(()=>e[25]||(e[25]=[n("Batas Umur Maksimum *")])),_:1,__:[25]}),r(a(g),{id:"batas_umur_max",modelValue:a(o).batas_umur_max,"onUpdate:modelValue":e[6]||(e[6]=s=>a(o).batas_umur_max=s),type:"number",required:"",min:"1",max:"100",placeholder:"Contoh: 12",class:_({"border-red-500":a(o).errors.batas_umur_max})},null,8,["modelValue","class"]),a(o).errors.batas_umur_max?(u(),d("p",X,m(a(o).errors.batas_umur_max),1)):i("",!0)])])]),t("div",Y,[e[31]||(e[31]=t("h3",{class:"text-lg font-medium"},"Pengaturan Lomba",-1)),t("div",Z,[t("div",null,[r(a(f),{for:"kuota_max"},{default:l(()=>e[27]||(e[27]=[n("Kuota Maksimum *")])),_:1,__:[27]}),r(a(g),{id:"kuota_max",modelValue:a(o).kuota_max,"onUpdate:modelValue":e[7]||(e[7]=s=>a(o).kuota_max=s),type:"number",required:"",min:"1",placeholder:"Contoh: 50",class:_({"border-red-500":a(o).errors.kuota_max})},null,8,["modelValue","class"]),a(o).errors.kuota_max?(u(),d("p",aa,m(a(o).errors.kuota_max),1)):i("",!0)]),t("div",null,[r(a(f),{for:"biaya_pendaftaran"},{default:l(()=>e[28]||(e[28]=[n("Biaya Pendaftaran *")])),_:1,__:[28]}),r(a(g),{id:"biaya_pendaftaran",modelValue:a(o).biaya_pendaftaran,"onUpdate:modelValue":e[8]||(e[8]=s=>a(o).biaya_pendaftaran=s),type:"number",required:"",min:"0",step:"1000",placeholder:"Contoh: 150000",class:_({"border-red-500":a(o).errors.biaya_pendaftaran})},null,8,["modelValue","class"]),a(o).errors.biaya_pendaftaran?(u(),d("p",ea,m(a(o).errors.biaya_pendaftaran),1)):i("",!0)])]),t("div",oa,[t("div",null,[r(a(f),{for:"nomor_urut_awal"},{default:l(()=>e[29]||(e[29]=[n("Nomor Urut Awal")])),_:1,__:[29]}),r(a(g),{id:"nomor_urut_awal",modelValue:a(o).nomor_urut_awal,"onUpdate:modelValue":e[9]||(e[9]=s=>a(o).nomor_urut_awal=s),type:"number",min:"1",placeholder:"Default: 1",class:_({"border-red-500":a(o).errors.nomor_urut_awal})},null,8,["modelValue","class"]),a(o).errors.nomor_urut_awal?(u(),d("p",ra,m(a(o).errors.nomor_urut_awal),1)):i("",!0)]),t("div",null,[r(a(f),{for:"nomor_urut_akhir"},{default:l(()=>e[30]||(e[30]=[n("Nomor Urut Akhir")])),_:1,__:[30]}),r(a(g),{id:"nomor_urut_akhir",modelValue:a(o).nomor_urut_akhir,"onUpdate:modelValue":e[10]||(e[10]=s=>a(o).nomor_urut_akhir=s),type:"number",min:"1",placeholder:"Default: 999",class:_({"border-red-500":a(o).errors.nomor_urut_akhir})},null,8,["modelValue","class"]),a(o).errors.nomor_urut_akhir?(u(),d("p",ta,m(a(o).errors.nomor_urut_akhir),1)):i("",!0)])])]),e[34]||(e[34]=t("div",{class:"bg-blue-50 p-4 rounded-lg"},[t("h4",{class:"font-medium text-blue-900 mb-2"},"Informasi Golongan"),t("div",{class:"text-sm text-blue-800 space-y-1"},[t("p",null,[t("strong",null,"Kode Golongan:"),n(" Kombinasi kode cabang + nomor + jenis kelamin (Pa/Pi)")]),t("p",null,[t("strong",null,"Batas Umur:"),n(" Rentang usia peserta yang boleh mengikuti golongan ini")]),t("p",null,[t("strong",null,"Kuota:"),n(" Jumlah maksimum peserta yang dapat mendaftar")]),t("p",null,[t("strong",null,"Nomor Urut:"),n(" Range nomor peserta untuk golongan ini (opsional)")])])],-1)),t("div",la,[r($,{type:"button",variant:"outline",onClick:e[11]||(e[11]=s=>b.$inertia.visit(b.route("admin.golongan.index")))},{default:l(()=>e[32]||(e[32]=[n(" Batal ")])),_:1,__:[32]}),r($,{type:"submit",disabled:a(o).processing},{default:l(()=>[a(o).processing?(u(),k(I,{key:0,name:"loader-2",class:"w-4 h-4 mr-2 animate-spin"})):i("",!0),e[33]||(e[33]=n(" Simpan Golongan "))]),_:1,__:[33]},8,["disabled"])])],32)]),_:1})]),_:1})])]),_:1}))}});export{wa as default};
