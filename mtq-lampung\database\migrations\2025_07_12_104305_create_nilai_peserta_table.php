<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('nilai_peserta', function (Blueprint $table) {
            $table->id('id_nilai');
            $table->unsignedBigInteger('id_pendaftaran');
            $table->unsignedBigInteger('id_jenis_nilai');
            $table->unsignedBigInteger('id_dewan_hakim');
            $table->decimal('nilai', 5, 2);
            $table->text('catatan')->nullable();
            $table->timestamps();

            // Foreign keys
            $table->foreign('id_pendaftaran')->references('id_pendaftaran')->on('pendaftaran')->onDelete('cascade');
            $table->foreign('id_jenis_nilai')->references('id_jenis_nilai')->on('jenis_nilai')->onDelete('restrict');
            $table->foreign('id_dewan_hakim')->references('id_dewan_hakim')->on('dewan_hakim')->onDelete('restrict');

            // Unique constraint
            $table->unique(['id_pendaftaran', 'id_jenis_nilai', 'id_dewan_hakim'], 'unique_nilai_peserta');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('nilai_peserta');
    }
};
