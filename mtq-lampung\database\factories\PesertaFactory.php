<?php

namespace Database\Factories;

use App\Models\User;
use App\Models\Wilayah;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Peserta>
 */
class PesertaFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $jenisKelamin = fake()->randomElement(['L', 'P']);
        $tanggalLahir = fake()->dateTimeBetween('-50 years', '-10 years');

        return [
            'id_user' => User::factory()->peserta(),
            'nik' => fake()->unique()->numerify('################'), // 16 digit NIK
            'nama_lengkap' => fake()->name(),
            'tempat_lahir' => fake()->city(),
            'tanggal_lahir' => $tanggalLahir,
            'jenis_kelamin' => $jenisKelamin,
            'alamat' => fake()->address(),
            'id_wilayah' => Wilayah::inRandomOrder()->first()?->id_wilayah,
            'no_telepon' => fake()->phoneNumber(),
            'email' => fake()->unique()->safeEmail(),
            'nama_ayah' => fake()->name('male'),
            'nama_ibu' => fake()->name('female'),
            'pekerjaan' => fake()->jobTitle(),
            'instansi_asal' => fake()->company(),
            'registered_by' => null, // Will be set by seeder if needed
            'registration_type' => fake()->randomElement(['mandiri', 'admin_daerah']),
            'status_peserta' => fake()->randomElement(['draft', 'submitted', 'verified', 'approved']),
            'nama_rekening' => fake()->name(),
            'no_rekening' => fake()->bankAccountNumber(),
        ];
    }

    /**
     * Create a peserta with mandiri registration.
     */
    public function mandiri(): static
    {
        return $this->state(fn (array $attributes) => [
            'registration_type' => 'mandiri',
            'registered_by' => null,
        ]);
    }

    /**
     * Create a peserta registered by admin daerah.
     */
    public function adminDaerahRegistered(): static
    {
        return $this->state(fn (array $attributes) => [
            'registration_type' => 'admin_daerah',
            'registered_by' => User::where('role', 'admin_daerah')->inRandomOrder()->first()?->id_user,
        ]);
    }

    /**
     * Create an approved peserta.
     */
    public function approved(): static
    {
        return $this->state(fn (array $attributes) => [
            'status_peserta' => 'approved',
        ]);
    }

    /**
     * Create a male peserta.
     */
    public function male(): static
    {
        return $this->state(fn (array $attributes) => [
            'jenis_kelamin' => 'L',
            'nama_ayah' => fake()->name('male'),
            'nama_ibu' => fake()->name('female'),
        ]);
    }

    /**
     * Create a female peserta.
     */
    public function female(): static
    {
        return $this->state(fn (array $attributes) => [
            'jenis_kelamin' => 'P',
            'nama_ayah' => fake()->name('male'),
            'nama_ibu' => fake()->name('female'),
        ]);
    }
}
