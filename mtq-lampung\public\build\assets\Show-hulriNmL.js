import{d as V,c as w,o as m,w as i,a as s,b as a,u as l,g as I,i as p,e as o,t as d,h as r,F as v,m as x,W as B}from"./app-B_pmlBSQ.js";import{_ as E}from"./AppLayout.vue_vue_type_script_setup_true_lang-BRf8lUiM.js";import{_ as W}from"./Heading.vue_vue_type_script_setup_true_lang-is4VBSCS.js";import{_ as c}from"./AppLogoIcon.vue_vue_type_script_setup_true_lang-wzz4PWna.js";import{_,a as f}from"./CardContent.vue_vue_type_script_setup_true_lang-ClvLNBXC.js";import{_ as D}from"./CardDescription.vue_vue_type_script_setup_true_lang-DboywBIK.js";import{_ as h,a as y}from"./CardTitle.vue_vue_type_script_setup_true_lang-5HO6oSoB.js";import{_ as P}from"./index-CMGr3-bt.js";import{_ as u}from"./Label.vue_vue_type_script_setup_true_lang-rj_fhE7R.js";import{_ as k}from"./Icon.vue_vue_type_script_setup_true_lang-BuYzjckr.js";import"./useForwardExpose-CO14IhkA.js";import"./RovingFocusGroup-lsWyU4xZ.js";import"./check-DtmHjdxf.js";import"./loader-circle-6d8QrWFr.js";import"./sun-CHIV9RJ8.js";const F={class:"max-w-6xl mx-auto space-y-6"},R={class:"flex justify-between items-start"},U={class:"w-12 h-12 bg-indigo-100 rounded-full flex items-center justify-center"},z={class:"flex gap-2"},M={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"},q={class:"space-y-4"},G={class:"space-y-2"},J={class:"text-sm"},O={class:"text-sm"},Q={class:"text-sm"},X={class:"text-sm"},Y={class:"space-y-4"},Z={class:"space-y-2"},aa={class:"text-sm"},ta={class:"text-sm"},ea={class:"text-sm"},sa={class:"text-sm"},ia={class:"space-y-4"},na={class:"space-y-2"},la={class:"text-sm"},da={key:0},oa={class:"text-sm"},ma={class:"grid grid-cols-1 md:grid-cols-4 gap-6"},ra={class:"flex items-center"},ua={class:"flex-shrink-0"},_a={class:"ml-4"},fa={class:"text-2xl font-semibold text-gray-900"},ka={class:"flex items-center"},pa={class:"flex-shrink-0"},ga={class:"ml-4"},wa={class:"text-2xl font-semibold text-gray-900"},ca={class:"flex items-center"},ha={class:"flex-shrink-0"},ya={class:"ml-4"},va={class:"text-2xl font-semibold text-gray-900"},xa={class:"flex items-center"},Ha={class:"flex-shrink-0"},ba={class:"ml-4"},$a={class:"text-2xl font-semibold text-gray-900"},ja={class:"space-y-4"},Da={class:"flex justify-between items-start"},Pa={class:"font-medium"},Sa={class:"text-sm text-gray-600"},Na={class:"text-sm text-gray-500"},Ca={class:"text-sm text-gray-500"},Ta={class:"space-y-4"},Aa={class:"flex justify-between items-start"},Ka={class:"font-medium"},La={class:"text-sm text-gray-600"},Va={class:"text-sm text-gray-500"},Ia={key:0,class:"text-sm text-gray-500 mt-2"},Ba={class:"space-y-4"},Ea={class:"flex justify-between items-start"},Wa={class:"font-medium"},Fa={class:"text-sm text-gray-600"},Ra={class:"text-sm text-gray-500"},Ua={key:0,class:"text-sm text-gray-500 mt-2"},za={class:"space-y-4"},Ma={class:"flex justify-between items-start"},qa={class:"font-medium"},Ga={class:"text-sm text-gray-600"},Ja={class:"text-sm text-gray-500"},Oa={class:"text-sm text-gray-500"},Qa={key:0,class:"text-center"},Xa={class:"flex justify-between"},Ya={class:"flex gap-2"},kt=V({__name:"Show",props:{dewaHakim:{}},setup(S){const H=S,N=[{title:"Dashboard",href:"/admin/dashboard"},{title:"Manajemen Dewan Hakim",href:"/admin/dewan-hakim"},{title:"Detail Dewan Hakim",href:`/admin/dewan-hakim/${H.dewaHakim.id_dewan_hakim}`}],C={undangan:"Undangan",kabupaten:"Kabupaten"},T=()=>{B.post(route("admin.dewan-hakim.toggle-status",H.dewaHakim.id_dewan_hakim),{},{preserveScroll:!0})},A=t=>({undangan:"default",kabupaten:"secondary"})[t]||"secondary",K=t=>({aktif:"default",non_aktif:"secondary"})[t]||"secondary",L=t=>({aktif:"Aktif",non_aktif:"Non Aktif"})[t]||t,b=t=>new Date(t).toLocaleDateString("id-ID",{year:"numeric",month:"long",day:"numeric"});return(t,e)=>(m(),w(E,{breadcrumbs:N},{default:i(()=>[s(l(I),{title:`Detail Dewan Hakim: ${t.dewaHakim.nama_lengkap}`},null,8,["title"]),s(W,{title:`Detail Dewan Hakim: ${t.dewaHakim.nama_lengkap}`},null,8,["title"]),a("div",F,[s(l(_),null,{default:i(()=>[s(l(h),null,{default:i(()=>[a("div",R,[a("div",null,[s(l(y),{class:"flex items-center gap-3"},{default:i(()=>[a("div",U,[s(k,{name:"user-check",class:"w-6 h-6 text-indigo-600"})]),o(" "+d(t.dewaHakim.nama_lengkap),1)]),_:1}),s(l(D),null,{default:i(()=>[o(d(t.dewaHakim.nik)+" • "+d(t.dewaHakim.pekerjaan),1)]),_:1})]),a("div",z,[s(l(P),{variant:A(t.dewaHakim.tipe_hakim)},{default:i(()=>[o(d(C[t.dewaHakim.tipe_hakim]||t.dewaHakim.tipe_hakim),1)]),_:1},8,["variant"]),s(l(P),{variant:K(t.dewaHakim.status)},{default:i(()=>[o(d(L(t.dewaHakim.status)),1)]),_:1},8,["variant"])])])]),_:1}),s(l(f),null,{default:i(()=>{var n,g;return[a("div",M,[a("div",q,[e[7]||(e[7]=a("h4",{class:"font-medium text-gray-900"},"Informasi Pribadi",-1)),a("div",G,[a("div",null,[s(l(u),{class:"text-sm font-medium text-gray-500"},{default:i(()=>e[3]||(e[3]=[o("NIK")])),_:1,__:[3]}),a("p",J,d(t.dewaHakim.nik),1)]),a("div",null,[s(l(u),{class:"text-sm font-medium text-gray-500"},{default:i(()=>e[4]||(e[4]=[o("Tempat, Tanggal Lahir")])),_:1,__:[4]}),a("p",O,d(t.dewaHakim.tempat_lahir)+", "+d(b(t.dewaHakim.tanggal_lahir)),1)]),a("div",null,[s(l(u),{class:"text-sm font-medium text-gray-500"},{default:i(()=>e[5]||(e[5]=[o("No. Telepon")])),_:1,__:[5]}),a("p",Q,d(t.dewaHakim.no_telepon),1)]),a("div",null,[s(l(u),{class:"text-sm font-medium text-gray-500"},{default:i(()=>e[6]||(e[6]=[o("Email")])),_:1,__:[6]}),a("p",X,d(((n=t.dewaHakim.user)==null?void 0:n.email)||"-"),1)])])]),a("div",Y,[e[12]||(e[12]=a("h4",{class:"font-medium text-gray-900"},"Informasi Profesi",-1)),a("div",Z,[a("div",null,[s(l(u),{class:"text-sm font-medium text-gray-500"},{default:i(()=>e[8]||(e[8]=[o("Pekerjaan")])),_:1,__:[8]}),a("p",aa,d(t.dewaHakim.pekerjaan),1)]),a("div",null,[s(l(u),{class:"text-sm font-medium text-gray-500"},{default:i(()=>e[9]||(e[9]=[o("Unit Kerja")])),_:1,__:[9]}),a("p",ta,d(t.dewaHakim.unit_kerja),1)]),a("div",null,[s(l(u),{class:"text-sm font-medium text-gray-500"},{default:i(()=>e[10]||(e[10]=[o("Spesialisasi")])),_:1,__:[10]}),a("p",ea,d(t.dewaHakim.spesialisasi),1)]),a("div",null,[s(l(u),{class:"text-sm font-medium text-gray-500"},{default:i(()=>e[11]||(e[11]=[o("Wilayah")])),_:1,__:[11]}),a("p",sa,d((g=t.dewaHakim.wilayah)==null?void 0:g.nama_wilayah),1)])])]),a("div",ia,[e[15]||(e[15]=a("h4",{class:"font-medium text-gray-900"},"Alamat",-1)),a("div",na,[a("div",null,[s(l(u),{class:"text-sm font-medium text-gray-500"},{default:i(()=>e[13]||(e[13]=[o("Alamat Rumah")])),_:1,__:[13]}),a("p",la,d(t.dewaHakim.alamat_rumah),1)]),t.dewaHakim.alamat_kantor?(m(),r("div",da,[s(l(u),{class:"text-sm font-medium text-gray-500"},{default:i(()=>e[14]||(e[14]=[o("Alamat Kantor")])),_:1,__:[14]}),a("p",oa,d(t.dewaHakim.alamat_kantor),1)])):p("",!0)])])])]}),_:1})]),_:1}),a("div",ma,[s(l(_),null,{default:i(()=>[s(l(f),{class:"p-6"},{default:i(()=>{var n;return[a("div",ra,[a("div",ua,[s(k,{name:"award",class:"h-8 w-8 text-blue-600"})]),a("div",_a,[e[16]||(e[16]=a("p",{class:"text-sm font-medium text-gray-500"},"Total Penilaian",-1)),a("p",fa,d(((n=t.dewaHakim.nilai_peserta)==null?void 0:n.length)||0),1)])])]}),_:1})]),_:1}),s(l(_),null,{default:i(()=>[s(l(f),{class:"p-6"},{default:i(()=>{var n;return[a("div",ka,[a("div",pa,[s(k,{name:"graduation-cap",class:"h-8 w-8 text-green-600"})]),a("div",ga,[e[17]||(e[17]=a("p",{class:"text-sm font-medium text-gray-500"},"Pendidikan",-1)),a("p",wa,d(((n=t.dewaHakim.pendidikan)==null?void 0:n.length)||0),1)])])]}),_:1})]),_:1}),s(l(_),null,{default:i(()=>[s(l(f),{class:"p-6"},{default:i(()=>{var n;return[a("div",ca,[a("div",ha,[s(k,{name:"briefcase",class:"h-8 w-8 text-yellow-600"})]),a("div",ya,[e[18]||(e[18]=a("p",{class:"text-sm font-medium text-gray-500"},"Pengalaman",-1)),a("p",va,d(((n=t.dewaHakim.pengalaman)==null?void 0:n.length)||0),1)])])]}),_:1})]),_:1}),s(l(_),null,{default:i(()=>[s(l(f),{class:"p-6"},{default:i(()=>{var n;return[a("div",xa,[a("div",Ha,[s(k,{name:"trophy",class:"h-8 w-8 text-purple-600"})]),a("div",ba,[e[19]||(e[19]=a("p",{class:"text-sm font-medium text-gray-500"},"Prestasi",-1)),a("p",$a,d(((n=t.dewaHakim.prestasi)==null?void 0:n.length)||0),1)])])]}),_:1})]),_:1})]),t.dewaHakim.pendidikan&&t.dewaHakim.pendidikan.length>0?(m(),w(l(_),{key:0},{default:i(()=>[s(l(h),null,{default:i(()=>[s(l(y),null,{default:i(()=>e[20]||(e[20]=[o("Riwayat Pendidikan")])),_:1,__:[20]})]),_:1}),s(l(f),null,{default:i(()=>[a("div",ja,[(m(!0),r(v,null,x(t.dewaHakim.pendidikan,n=>(m(),r("div",{key:n.id_pendidikan,class:"border rounded-lg p-4"},[a("div",Da,[a("div",null,[a("h4",Pa,d(n.jenjang_pendidikan),1),a("p",Sa,d(n.nama_institusi),1),a("p",Na,d(n.jurusan),1),a("p",Ca,d(n.tahun_lulus),1)])])]))),128))])]),_:1})]),_:1})):p("",!0),t.dewaHakim.pengalaman&&t.dewaHakim.pengalaman.length>0?(m(),w(l(_),{key:1},{default:i(()=>[s(l(h),null,{default:i(()=>[s(l(y),null,{default:i(()=>e[21]||(e[21]=[o("Pengalaman Kerja")])),_:1,__:[21]})]),_:1}),s(l(f),null,{default:i(()=>[a("div",Ta,[(m(!0),r(v,null,x(t.dewaHakim.pengalaman,n=>(m(),r("div",{key:n.id_pengalaman,class:"border rounded-lg p-4"},[a("div",Aa,[a("div",null,[a("h4",Ka,d(n.jabatan),1),a("p",La,d(n.nama_instansi),1),a("p",Va,d(n.tahun_mulai)+" - "+d(n.tahun_selesai||"Sekarang"),1),n.deskripsi?(m(),r("p",Ia,d(n.deskripsi),1)):p("",!0)])])]))),128))])]),_:1})]),_:1})):p("",!0),t.dewaHakim.prestasi&&t.dewaHakim.prestasi.length>0?(m(),w(l(_),{key:2},{default:i(()=>[s(l(h),null,{default:i(()=>[s(l(y),null,{default:i(()=>e[22]||(e[22]=[o("Prestasi & Penghargaan")])),_:1,__:[22]})]),_:1}),s(l(f),null,{default:i(()=>[a("div",Ba,[(m(!0),r(v,null,x(t.dewaHakim.prestasi,n=>(m(),r("div",{key:n.id_prestasi,class:"border rounded-lg p-4"},[a("div",Ea,[a("div",null,[a("h4",Wa,d(n.nama_prestasi),1),a("p",Fa,d(n.pemberi_prestasi),1),a("p",Ra,d(n.tahun_prestasi),1),n.deskripsi?(m(),r("p",Ua,d(n.deskripsi),1)):p("",!0)])])]))),128))])]),_:1})]),_:1})):p("",!0),t.dewaHakim.nilai_peserta&&t.dewaHakim.nilai_peserta.length>0?(m(),w(l(_),{key:3},{default:i(()=>[s(l(h),null,{default:i(()=>[s(l(y),null,{default:i(()=>e[23]||(e[23]=[o("Penilaian Terbaru")])),_:1,__:[23]}),s(l(D),null,{default:i(()=>e[24]||(e[24]=[o(" Daftar penilaian yang telah diberikan oleh dewan hakim ini ")])),_:1,__:[24]})]),_:1}),s(l(f),null,{default:i(()=>[a("div",za,[(m(!0),r(v,null,x(t.dewaHakim.nilai_peserta.slice(0,5),n=>{var g,$,j;return m(),r("div",{key:n.id_nilai,class:"border rounded-lg p-4"},[a("div",Ma,[a("div",null,[a("h4",qa,d(($=(g=n.pendaftaran)==null?void 0:g.peserta)==null?void 0:$.nama_lengkap),1),a("p",Ga,d((j=n.pendaftaran)==null?void 0:j.nomor_pendaftaran),1),a("p",Ja,"Nilai: "+d(n.nilai_total),1)]),a("div",Oa,d(b(n.created_at)),1)])])}),128)),t.dewaHakim.nilai_peserta.length>5?(m(),r("div",Qa,[s(c,{variant:"outline",size:"sm"},{default:i(()=>[o(" Lihat Semua Penilaian ("+d(t.dewaHakim.nilai_peserta.length)+") ",1)]),_:1})])):p("",!0)])]),_:1})]),_:1})):p("",!0),a("div",Xa,[s(c,{variant:"outline",onClick:e[0]||(e[0]=n=>t.$inertia.visit(t.route("admin.dewan-hakim.index")))},{default:i(()=>[s(k,{name:"arrow-left",class:"w-4 h-4 mr-2"}),e[25]||(e[25]=o(" Kembali "))]),_:1,__:[25]}),a("div",Ya,[s(c,{variant:"outline",onClick:e[1]||(e[1]=n=>t.$inertia.visit(t.route("admin.dewan-hakim.profile",t.dewaHakim.id_dewan_hakim)))},{default:i(()=>[s(k,{name:"user",class:"w-4 h-4 mr-2"}),e[26]||(e[26]=o(" Profil Lengkap "))]),_:1,__:[26]}),s(c,{variant:"outline",onClick:T},{default:i(()=>[s(k,{name:t.dewaHakim.status==="aktif"?"user-x":"user-check",class:"w-4 h-4 mr-2"},null,8,["name"]),o(" "+d(t.dewaHakim.status==="aktif"?"Non-aktifkan":"Aktifkan"),1)]),_:1}),s(c,{onClick:e[2]||(e[2]=n=>t.$inertia.visit(t.route("admin.dewan-hakim.edit",t.dewaHakim.id_dewan_hakim)))},{default:i(()=>[s(k,{name:"edit",class:"w-4 h-4 mr-2"}),e[27]||(e[27]=o(" Edit Dewan Hakim "))]),_:1,__:[27]})])])])]),_:1}))}});export{kt as default};
