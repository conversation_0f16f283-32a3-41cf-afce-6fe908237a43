<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\DokumenPeserta;
use App\Models\Pendaftaran;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Symfony\Component\HttpFoundation\BinaryFileResponse;

class DocumentController extends Controller
{
    /**
     * Display a listing of documents for a registration
     */
    public function index(string $registrationId): JsonResponse
    {
        $pendaftaran = Pendaftaran::findOrFail($registrationId);
        $user = Auth::user();

        // Check access permissions
        if ($user->role === 'peserta' && $pendaftaran->peserta->id_user !== $user->id_user) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied'
            ], 403);
        }

        if ($user->role === 'admin_daerah' && $pendaftaran->peserta->id_wilayah !== $user->id_wilayah) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied'
            ], 403);
        }

        $documents = $pendaftaran->dokumenPeserta()->orderBy('created_at', 'desc')->get();

        return response()->json([
            'success' => true,
            'data' => [
                'registration' => $pendaftaran->load(['peserta', 'golongan.cabangLomba']),
                'documents' => $documents
            ]
        ]);
    }

    /**
     * Store a newly created document
     */
    public function store(Request $request, string $registrationId): JsonResponse
    {
        $pendaftaran = Pendaftaran::findOrFail($registrationId);
        $user = Auth::user();

        // Check access permissions
        if ($user->role === 'peserta' && $pendaftaran->peserta->id_user !== $user->id_user) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied'
            ], 403);
        }

        $validated = $request->validate([
            'jenis_dokumen' => 'required|in:ktp,kk,ijazah,sertifikat,foto,lainnya',
            'nama_dokumen' => 'required|string|max:100',
            'file_dokumen' => 'required|file|mimes:jpg,jpeg,png,pdf|max:5120', // 5MB max
            'keterangan' => 'nullable|string|max:500',
        ]);

        // Store the file
        $file = $request->file('file_dokumen');
        $filename = $validated['jenis_dokumen'] . '_' . $pendaftaran->id_pendaftaran . '_' . time() . '.' . $file->getClientOriginalExtension();
        $path = $file->storeAs('documents', $filename, 'public');

        $document = DokumenPeserta::create([
            'id_pendaftaran' => $pendaftaran->id_pendaftaran,
            'jenis_dokumen' => $validated['jenis_dokumen'],
            'nama_dokumen' => $validated['nama_dokumen'],
            'file_path' => $path,
            'ukuran_file' => $file->getSize(),
            'mime_type' => $file->getMimeType(),
            'keterangan' => $validated['keterangan'],
            'status' => 'pending',
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Document uploaded successfully',
            'data' => $document
        ], 201);
    }

    /**
     * Update the specified document
     */
    public function update(Request $request, string $id): JsonResponse
    {
        $document = DokumenPeserta::findOrFail($id);
        $user = Auth::user();

        // Check access permissions
        if ($user->role === 'peserta' && $document->pendaftaran->peserta->id_user !== $user->id_user) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied'
            ], 403);
        }

        $validated = $request->validate([
            'nama_dokumen' => 'required|string|max:100',
            'keterangan' => 'nullable|string|max:500',
            'file_dokumen' => 'nullable|file|mimes:jpg,jpeg,png,pdf|max:5120',
        ]);

        // Update document info
        $document->update([
            'nama_dokumen' => $validated['nama_dokumen'],
            'keterangan' => $validated['keterangan'],
        ]);

        // Update file if provided
        if ($request->hasFile('file_dokumen')) {
            // Delete old file
            if ($document->file_path && Storage::disk('public')->exists($document->file_path)) {
                Storage::disk('public')->delete($document->file_path);
            }

            // Store new file
            $file = $request->file('file_dokumen');
            $filename = $document->jenis_dokumen . '_' . $document->id_pendaftaran . '_' . time() . '.' . $file->getClientOriginalExtension();
            $path = $file->storeAs('documents', $filename, 'public');

            $document->update([
                'file_path' => $path,
                'ukuran_file' => $file->getSize(),
                'mime_type' => $file->getMimeType(),
                'status' => 'pending', // Reset status when file is updated
            ]);
        }

        return response()->json([
            'success' => true,
            'message' => 'Document updated successfully',
            'data' => $document
        ]);
    }

    /**
     * Remove the specified document
     */
    public function destroy(string $id): JsonResponse
    {
        $document = DokumenPeserta::findOrFail($id);
        $user = Auth::user();

        // Check access permissions
        if ($user->role === 'peserta' && $document->pendaftaran->peserta->id_user !== $user->id_user) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied'
            ], 403);
        }

        // Delete file from storage
        if ($document->file_path && Storage::disk('public')->exists($document->file_path)) {
            Storage::disk('public')->delete($document->file_path);
        }

        $document->delete();

        return response()->json([
            'success' => true,
            'message' => 'Document deleted successfully'
        ]);
    }

    /**
     * Download the specified document
     */
    public function download(string $id): BinaryFileResponse|JsonResponse
    {
        $document = DokumenPeserta::findOrFail($id);
        $user = Auth::user();

        // Check access permissions
        if ($user->role === 'peserta' && $document->pendaftaran->peserta->id_user !== $user->id_user) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied'
            ], 403);
        }

        if ($user->role === 'admin_daerah' && $document->pendaftaran->peserta->id_wilayah !== $user->id_wilayah) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied'
            ], 403);
        }

        // Check if file exists
        if (!$document->file_path || !Storage::disk('public')->exists($document->file_path)) {
            return response()->json([
                'success' => false,
                'message' => 'File not found'
            ], 404);
        }

        $filePath = Storage::disk('public')->path($document->file_path);
        $fileName = $document->nama_dokumen . '.' . pathinfo($document->file_path, PATHINFO_EXTENSION);

        return response()->download($filePath, $fileName);
    }
}
