<?php

namespace App\Http\Controllers\AdminProvinsi;

use App\Http\Controllers\Controller;
use App\Models\Pendaftaran;
use App\Models\Peserta;
use App\Models\DokumenPeserta;
use App\Models\VerifikasiPendaftaran;
use App\Models\Wilayah;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class DashboardController extends Controller
{
    /**
     * Display admin provinsi dashboard
     */
    public function index(): Response
    {
        // Get statistics for verified registrations only
        $stats = [
            'total_pendaftaran_verified' => Pendaftaran::whereHas('peserta', function($q) {
                $q->where('status_peserta', 'verified');
            })->where('status_pendaftaran', 'verified')->count(),
            
            'pending_verification' => Pendaftaran::whereHas('peserta', function($q) {
                $q->where('status_peserta', 'verified');
            })
            ->where('status_pendaftaran', 'verified')
            ->whereDoesntHave('verifikasiPendaftaran')
            ->count(),
            
            'nik_pending' => VerifikasiPendaftaran::where('status_nik', 'pending')->count(),
            'dokumen_pending' => VerifikasiPendaftaran::where('status_dokumen', 'pending')->count(),
            'verification_approved' => VerifikasiPendaftaran::where('status_verifikasi', 'approved')->count(),
            'verification_rejected' => VerifikasiPendaftaran::where('status_verifikasi', 'rejected')->count(),
        ];

        // Get recent registrations that need verification
        $recentPendaftaran = Pendaftaran::with([
            'peserta.user', 
            'peserta.wilayah', 
            'golongan.cabangLomba',
            'verifikasiPendaftaran'
        ])
        ->whereHas('peserta', function($q) {
            $q->where('status_peserta', 'verified');
        })
        ->where('status_pendaftaran', 'verified')
        ->orderBy('created_at', 'desc')
        ->limit(10)
        ->get();

        // Get verification statistics by region
        $verificationByRegion = Wilayah::with(['peserta.pendaftaran.verifikasiPendaftaran'])
            ->where('level_wilayah', 'kabupaten')
            ->get()
            ->map(function($wilayah) {
                $totalPendaftaran = $wilayah->peserta->flatMap->pendaftaran->count();
                $verified = $wilayah->peserta->flatMap->pendaftaran
                    ->filter(fn($p) => $p->verifikasiPendaftaran?->status_verifikasi === 'approved')
                    ->count();
                $pending = $wilayah->peserta->flatMap->pendaftaran
                    ->filter(fn($p) => !$p->verifikasiPendaftaran || $p->verifikasiPendaftaran->status_verifikasi === 'pending')
                    ->count();

                return [
                    'wilayah' => $wilayah->nama_wilayah,
                    'total' => $totalPendaftaran,
                    'verified' => $verified,
                    'pending' => $pending,
                    'percentage' => $totalPendaftaran > 0 ? round(($verified / $totalPendaftaran) * 100, 1) : 0
                ];
            })
            ->sortByDesc('total')
            ->take(10);

        return Inertia::render('AdminProvinsi/Dashboard', [
            'stats' => $stats,
            'recentPendaftaran' => $recentPendaftaran,
            'verificationByRegion' => $verificationByRegion->values()
        ]);
    }
}
