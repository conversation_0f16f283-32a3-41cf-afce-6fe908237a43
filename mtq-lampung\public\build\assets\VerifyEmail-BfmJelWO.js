import{d as l,x as f,c as n,o as i,w as a,a as o,h as u,i as m,b as p,u as e,g as _,f as y,e as d}from"./app-B_pmlBSQ.js";import{_ as b}from"./TextLink.vue_vue_type_script_setup_true_lang-CC2Og0lP.js";import{_ as g}from"./AppLogoIcon.vue_vue_type_script_setup_true_lang-wzz4PWna.js";import{_ as k}from"./AuthLayout.vue_vue_type_script_setup_true_lang-DMSGHxRq.js";import{L as v}from"./loader-circle-6d8QrWFr.js";const x={key:0,class:"mb-4 text-center text-sm font-medium text-green-600"},E=l({__name:"VerifyEmail",props:{status:{}},setup(h){const s=f({}),c=()=>{s.post(route("verification.send"))};return(r,t)=>(i(),n(k,{title:"Verify email",description:"Please verify your email address by clicking on the link we just emailed to you."},{default:a(()=>[o(e(_),{title:"Email verification"}),r.status==="verification-link-sent"?(i(),u("div",x," A new verification link has been sent to the email address you provided during registration. ")):m("",!0),p("form",{onSubmit:y(c,["prevent"]),class:"space-y-6 text-center"},[o(e(g),{disabled:e(s).processing,variant:"secondary"},{default:a(()=>[e(s).processing?(i(),n(e(v),{key:0,class:"h-4 w-4 animate-spin"})):m("",!0),t[0]||(t[0]=d(" Resend verification email "))]),_:1,__:[0]},8,["disabled"]),o(b,{href:r.route("logout"),method:"post",as:"button",class:"mx-auto block text-sm"},{default:a(()=>t[1]||(t[1]=[d(" Log out ")])),_:1,__:[1]},8,["href"])],32)]),_:1}))}});export{E as default};
