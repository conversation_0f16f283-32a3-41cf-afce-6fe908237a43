import{d as B,x as M,l as A,c as $,o as _,w as t,a as l,b as o,u as e,g as F,e as r,f as I,j as O,q as S,h as k,F as E,m as J,n as P,i as D,t as p}from"./app-B_pmlBSQ.js";import{_ as z}from"./AppLayout.vue_vue_type_script_setup_true_lang-BRf8lUiM.js";import{_ as G}from"./Heading.vue_vue_type_script_setup_true_lang-is4VBSCS.js";import{_ as v}from"./AppLogoIcon.vue_vue_type_script_setup_true_lang-wzz4PWna.js";import{_ as u}from"./Input.vue_vue_type_script_setup_true_lang-DB1-RsTn.js";import{_ as d}from"./Label.vue_vue_type_script_setup_true_lang-rj_fhE7R.js";import{_ as q}from"./Textarea.vue_vue_type_script_setup_true_lang-jqOjZ7RT.js";import{_ as N}from"./Checkbox.vue_vue_type_script_setup_true_lang-D-SBgnQ-.js";import{_ as x,a as w}from"./CardContent.vue_vue_type_script_setup_true_lang-ClvLNBXC.js";import{_ as y}from"./CardDescription.vue_vue_type_script_setup_true_lang-DboywBIK.js";import{_ as V,a as h}from"./CardTitle.vue_vue_type_script_setup_true_lang-5HO6oSoB.js";import{_ as Y}from"./index-CMGr3-bt.js";import{_ as R,a as H}from"./index-Cae_Ab9-.js";import{_ as m}from"./InputError.vue_vue_type_script_setup_true_lang-A-vdzbq9.js";import{_ as b}from"./Icon.vue_vue_type_script_setup_true_lang-BuYzjckr.js";import"./useForwardExpose-CO14IhkA.js";import"./RovingFocusGroup-lsWyU4xZ.js";import"./useFormControl-mZRLifGx.js";import"./check-DtmHjdxf.js";import"./loader-circle-6d8QrWFr.js";import"./sun-CHIV9RJ8.js";const Q={class:"flex items-center space-x-4"},W={class:"space-y-6"},X={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},Z={class:"grid gap-2"},aa={class:"grid gap-2"},ea={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},la={class:"grid gap-2"},sa={class:"flex space-x-2"},na={class:"grid gap-2"},ta={class:"grid gap-2"},oa={class:"grid gap-2"},ra={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},ia={class:"grid gap-2"},da={class:"grid gap-2"},ma={class:"grid gap-2"},ua={class:"grid gap-2"},pa={class:"grid gap-2"},_a={key:0,class:"text-center py-8"},fa={key:1,class:"grid grid-cols-1 lg:grid-cols-2 gap-4"},ga=["onClick"],ka={class:"flex items-center space-x-3 mb-2"},ba={class:"flex-1"},ca={class:"font-medium"},va={class:"text-sm text-gray-600"},xa={class:"text-sm space-y-1"},wa={class:"font-medium text-green-600"},ya={key:0,class:"mt-2 text-xs text-red-600"},Va={class:"flex items-center space-x-2"},ha={class:"grid gap-2"},ja={class:"flex justify-end space-x-4 pt-6 border-t"},Ra=B({__name:"Create",props:{golongan:{},admin_wilayah:{}},setup(Ua){const s=M({username:"",nama_lengkap:"",email:"",password:"",password_confirmation:"",nik:"",tempat_lahir:"",tanggal_lahir:"",jenis_kelamin:"",alamat:"",no_telepon:"",golongan_ids:[],auto_approve:!1,keterangan:""}),f=A([]);function j(i){const a=f.value.indexOf(i);a>-1?f.value.splice(a,1):f.value.push(i),s.golongan_ids=[...f.value]}function g(i){if(!s.jenis_kelamin||!s.tanggal_lahir)return!0;if(s.jenis_kelamin!==i.jenis_kelamin)return!1;const a=L(s.tanggal_lahir);return!(a<i.batas_umur_min||a>i.batas_umur_max)}function L(i){if(!i)return 0;const a=new Date,n=new Date(i);let c=a.getFullYear()-n.getFullYear();const U=a.getMonth()-n.getMonth();return(U<0||U===0&&a.getDate()<n.getDate())&&c--,c}function C(i){return new Intl.NumberFormat("id-ID",{style:"currency",currency:"IDR",minimumFractionDigits:0}).format(i)}function K(){s.post(route("admin-daerah.peserta.store"))}function T(){const i="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";let a="";for(let n=0;n<8;n++)a+=i.charAt(Math.floor(Math.random()*i.length));s.password=a,s.password_confirmation=a}return(i,a)=>(_(),$(z,null,{header:t(()=>[o("div",Q,[l(v,{as:"link",href:i.route("admin-daerah.peserta.index"),variant:"ghost",size:"sm"},{default:t(()=>[l(b,{name:"arrow-left",class:"w-4 h-4 mr-2"}),a[13]||(a[13]=r(" Kembali "))]),_:1,__:[13]},8,["href"]),l(G,null,{default:t(()=>a[14]||(a[14]=[r("Daftarkan Peserta Baru")])),_:1,__:[14]})])]),default:t(()=>[l(e(F),{title:"Daftarkan Peserta Baru"}),o("div",W,[l(e(R),null,{default:t(()=>[l(b,{name:"info",class:"h-4 w-4"}),l(e(H),null,{default:t(()=>a[15]||(a[15]=[r(" Sebagai admin daerah, Anda dapat mendaftarkan peserta secara langsung dan memilih untuk menyetujui pendaftaran secara otomatis. ")])),_:1,__:[15]})]),_:1}),o("form",{onSubmit:I(K,["prevent"]),class:"space-y-6"},[l(e(x),null,{default:t(()=>[l(e(V),null,{default:t(()=>[l(e(h),null,{default:t(()=>a[16]||(a[16]=[r("Informasi Akun")])),_:1,__:[16]}),l(e(y),null,{default:t(()=>a[17]||(a[17]=[r("Data akun untuk login peserta")])),_:1,__:[17]})]),_:1}),l(e(w),{class:"space-y-4"},{default:t(()=>[o("div",X,[o("div",Z,[l(e(d),{for:"username"},{default:t(()=>a[18]||(a[18]=[r("Username")])),_:1,__:[18]}),l(e(u),{id:"username",modelValue:e(s).username,"onUpdate:modelValue":a[0]||(a[0]=n=>e(s).username=n),placeholder:"Username untuk login",required:""},null,8,["modelValue"]),l(m,{message:e(s).errors.username},null,8,["message"])]),o("div",aa,[l(e(d),{for:"email"},{default:t(()=>a[19]||(a[19]=[r("Email")])),_:1,__:[19]}),l(e(u),{id:"email",type:"email",modelValue:e(s).email,"onUpdate:modelValue":a[1]||(a[1]=n=>e(s).email=n),placeholder:"<EMAIL>",required:""},null,8,["modelValue"]),l(m,{message:e(s).errors.email},null,8,["message"])])]),o("div",ea,[o("div",la,[l(e(d),{for:"password"},{default:t(()=>a[20]||(a[20]=[r("Password")])),_:1,__:[20]}),o("div",sa,[l(e(u),{id:"password",type:"password",modelValue:e(s).password,"onUpdate:modelValue":a[2]||(a[2]=n=>e(s).password=n),placeholder:"Password",required:"",class:"flex-1"},null,8,["modelValue"]),l(v,{type:"button",variant:"outline",onClick:T},{default:t(()=>[l(b,{name:"refresh-cw",class:"w-4 h-4"})]),_:1})]),l(m,{message:e(s).errors.password},null,8,["message"])]),o("div",na,[l(e(d),{for:"password_confirmation"},{default:t(()=>a[21]||(a[21]=[r("Konfirmasi Password")])),_:1,__:[21]}),l(e(u),{id:"password_confirmation",type:"password",modelValue:e(s).password_confirmation,"onUpdate:modelValue":a[3]||(a[3]=n=>e(s).password_confirmation=n),placeholder:"Konfirmasi password",required:""},null,8,["modelValue"]),l(m,{message:e(s).errors.password_confirmation},null,8,["message"])])])]),_:1})]),_:1}),l(e(x),null,{default:t(()=>[l(e(V),null,{default:t(()=>[l(e(h),null,{default:t(()=>a[22]||(a[22]=[r("Data Pribadi")])),_:1,__:[22]}),l(e(y),null,{default:t(()=>a[23]||(a[23]=[r("Informasi pribadi peserta")])),_:1,__:[23]})]),_:1}),l(e(w),{class:"space-y-4"},{default:t(()=>[o("div",ta,[l(e(d),{for:"nama_lengkap"},{default:t(()=>a[24]||(a[24]=[r("Nama Lengkap")])),_:1,__:[24]}),l(e(u),{id:"nama_lengkap",modelValue:e(s).nama_lengkap,"onUpdate:modelValue":a[4]||(a[4]=n=>e(s).nama_lengkap=n),placeholder:"Nama lengkap peserta",required:""},null,8,["modelValue"]),l(m,{message:e(s).errors.nama_lengkap},null,8,["message"])]),o("div",oa,[l(e(d),{for:"nik"},{default:t(()=>a[25]||(a[25]=[r("NIK")])),_:1,__:[25]}),l(e(u),{id:"nik",modelValue:e(s).nik,"onUpdate:modelValue":a[5]||(a[5]=n=>e(s).nik=n),placeholder:"Nomor Induk Kependudukan (16 digit)",maxlength:"16",required:""},null,8,["modelValue"]),l(m,{message:e(s).errors.nik},null,8,["message"])]),o("div",ra,[o("div",ia,[l(e(d),{for:"tempat_lahir"},{default:t(()=>a[26]||(a[26]=[r("Tempat Lahir")])),_:1,__:[26]}),l(e(u),{id:"tempat_lahir",modelValue:e(s).tempat_lahir,"onUpdate:modelValue":a[6]||(a[6]=n=>e(s).tempat_lahir=n),placeholder:"Tempat lahir",required:""},null,8,["modelValue"]),l(m,{message:e(s).errors.tempat_lahir},null,8,["message"])]),o("div",da,[l(e(d),{for:"tanggal_lahir"},{default:t(()=>a[27]||(a[27]=[r("Tanggal Lahir")])),_:1,__:[27]}),l(e(u),{id:"tanggal_lahir",type:"date",modelValue:e(s).tanggal_lahir,"onUpdate:modelValue":a[7]||(a[7]=n=>e(s).tanggal_lahir=n),required:""},null,8,["modelValue"]),l(m,{message:e(s).errors.tanggal_lahir},null,8,["message"])])]),o("div",ma,[l(e(d),{for:"jenis_kelamin"},{default:t(()=>a[28]||(a[28]=[r("Jenis Kelamin")])),_:1,__:[28]}),O(o("select",{id:"jenis_kelamin","onUpdate:modelValue":a[8]||(a[8]=n=>e(s).jenis_kelamin=n),required:"",class:"flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50"},a[29]||(a[29]=[o("option",{value:""},"Pilih Jenis Kelamin",-1),o("option",{value:"L"},"Laki-laki",-1),o("option",{value:"P"},"Perempuan",-1)]),512),[[S,e(s).jenis_kelamin]]),l(m,{message:e(s).errors.jenis_kelamin},null,8,["message"])]),o("div",ua,[l(e(d),{for:"alamat"},{default:t(()=>a[30]||(a[30]=[r("Alamat")])),_:1,__:[30]}),l(e(q),{id:"alamat",modelValue:e(s).alamat,"onUpdate:modelValue":a[9]||(a[9]=n=>e(s).alamat=n),placeholder:"Alamat lengkap",rows:"3",required:""},null,8,["modelValue"]),l(m,{message:e(s).errors.alamat},null,8,["message"])]),o("div",pa,[l(e(d),{for:"no_telepon"},{default:t(()=>a[31]||(a[31]=[r("No. Telepon (Opsional)")])),_:1,__:[31]}),l(e(u),{id:"no_telepon",type:"tel",modelValue:e(s).no_telepon,"onUpdate:modelValue":a[10]||(a[10]=n=>e(s).no_telepon=n),placeholder:"Nomor telepon"},null,8,["modelValue"]),l(m,{message:e(s).errors.no_telepon},null,8,["message"])])]),_:1})]),_:1}),l(e(x),null,{default:t(()=>[l(e(V),null,{default:t(()=>[l(e(h),null,{default:t(()=>a[32]||(a[32]=[r("Pendaftaran Lomba (Opsional)")])),_:1,__:[32]}),l(e(y),null,{default:t(()=>a[33]||(a[33]=[r("Pilih golongan lomba yang akan diikuti peserta")])),_:1,__:[33]})]),_:1}),l(e(w),null,{default:t(()=>[!e(s).jenis_kelamin||!e(s).tanggal_lahir?(_(),k("div",_a,[l(b,{name:"info",class:"h-12 w-12 text-gray-400 mx-auto mb-4"}),a[34]||(a[34]=o("p",{class:"text-gray-600"},"Lengkapi jenis kelamin dan tanggal lahir untuk melihat golongan yang tersedia.",-1))])):(_(),k("div",fa,[(_(!0),k(E,null,J(i.golongan,n=>(_(),k("div",{key:n.id_golongan,class:P(["p-4 border rounded-lg transition-all",[g(n)?"hover:border-blue-500 cursor-pointer":"opacity-50 cursor-not-allowed",f.value.includes(n.id_golongan)?"border-blue-500 bg-blue-50":""]]),onClick:c=>g(n)&&j(n.id_golongan)},[o("div",ka,[l(e(N),{checked:f.value.includes(n.id_golongan),disabled:!g(n),"onUpdate:checked":c=>g(n)&&j(n.id_golongan)},null,8,["checked","disabled","onUpdate:checked"]),o("div",ba,[o("h4",ca,p(n.nama_golongan),1),o("p",va,p(n.cabang_lomba.nama_cabang),1)]),l(e(Y),{class:P(n.jenis_kelamin==="L"?"bg-blue-100 text-blue-800":"bg-pink-100 text-pink-800")},{default:t(()=>[r(p(n.jenis_kelamin==="L"?"Putra":"Putri"),1)]),_:2},1032,["class"])]),o("div",xa,[o("p",null,"Usia: "+p(n.batas_umur_min)+" - "+p(n.batas_umur_max)+" tahun",1),o("p",null,"Kuota: "+p(n.kuota_max)+" peserta",1),o("p",wa,p(C(n.biaya_pendaftaran)),1)]),g(n)?D("",!0):(_(),k("div",ya," Tidak memenuhi syarat (usia atau jenis kelamin) "))],10,ga))),128))]))]),_:1})]),_:1}),l(e(x),null,{default:t(()=>[l(e(V),null,{default:t(()=>[l(e(h),null,{default:t(()=>a[35]||(a[35]=[r("Opsi Tambahan")])),_:1,__:[35]}),l(e(y),null,{default:t(()=>a[36]||(a[36]=[r("Pengaturan khusus untuk pendaftaran")])),_:1,__:[36]})]),_:1}),l(e(w),{class:"space-y-4"},{default:t(()=>[o("div",Va,[l(e(N),{id:"auto_approve",checked:e(s).auto_approve,"onUpdate:checked":a[11]||(a[11]=n=>e(s).auto_approve=n)},null,8,["checked"]),l(e(d),{for:"auto_approve",class:"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"},{default:t(()=>a[37]||(a[37]=[r(" Setujui pendaftaran secara otomatis ")])),_:1,__:[37]})]),a[39]||(a[39]=o("p",{class:"text-xs text-gray-500"}," Jika dicentang, peserta dan pendaftaran lomba akan langsung disetujui tanpa perlu verifikasi manual. ",-1)),o("div",ha,[l(e(d),{for:"keterangan"},{default:t(()=>a[38]||(a[38]=[r("Keterangan (Opsional)")])),_:1,__:[38]}),l(e(q),{id:"keterangan",modelValue:e(s).keterangan,"onUpdate:modelValue":a[12]||(a[12]=n=>e(s).keterangan=n),placeholder:"Tambahkan catatan atau keterangan khusus...",rows:"3"},null,8,["modelValue"]),l(m,{message:e(s).errors.keterangan},null,8,["message"])])]),_:1,__:[39]})]),_:1}),o("div",ja,[l(v,{as:"link",href:i.route("admin-daerah.peserta.index"),variant:"outline"},{default:t(()=>a[40]||(a[40]=[r(" Batal ")])),_:1,__:[40]},8,["href"]),l(v,{type:"submit",disabled:e(s).processing},{default:t(()=>[e(s).processing?(_(),$(b,{key:0,name:"loader-2",class:"w-4 h-4 mr-2 animate-spin"})):D("",!0),r(" "+p(e(s).processing?"Mendaftarkan...":"Daftarkan Peserta"),1)]),_:1},8,["disabled"])])],32)])]),_:1}))}});export{Ra as default};
