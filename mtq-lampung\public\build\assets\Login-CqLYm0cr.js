import{d as b,x as c,c as n,o as d,w as r,a as o,h as x,i as p,b as a,u as s,g as V,t as y,f as v,e as i}from"./app-B_pmlBSQ.js";import{_ as f}from"./InputError.vue_vue_type_script_setup_true_lang-A-vdzbq9.js";import{_}from"./TextLink.vue_vue_type_script_setup_true_lang-CC2Og0lP.js";import{_ as $}from"./AppLogoIcon.vue_vue_type_script_setup_true_lang-wzz4PWna.js";import{_ as k}from"./Checkbox.vue_vue_type_script_setup_true_lang-D-SBgnQ-.js";import{_ as g}from"./Input.vue_vue_type_script_setup_true_lang-DB1-RsTn.js";import{_ as u}from"./Label.vue_vue_type_script_setup_true_lang-rj_fhE7R.js";import{_ as L}from"./AuthLayout.vue_vue_type_script_setup_true_lang-DMSGHxRq.js";import{L as B}from"./loader-circle-6d8QrWFr.js";import"./RovingFocusGroup-lsWyU4xZ.js";import"./useForwardExpose-CO14IhkA.js";import"./useFormControl-mZRLifGx.js";import"./check-DtmHjdxf.js";const C={key:0,class:"mb-4 text-center text-sm font-medium text-green-600"},N={class:"grid gap-6"},P={class:"grid gap-2"},q={class:"grid gap-2"},E={class:"flex items-center justify-between"},R={class:"flex items-center justify-between"},S={class:"text-center text-sm text-muted-foreground"},O=b({__name:"Login",props:{status:{},canResetPassword:{type:Boolean}},setup(U){const t=c({email:"",password:"",remember:!1}),w=()=>{t.post(route("login"),{onFinish:()=>t.reset("password")})};return(m,e)=>(d(),n(L,{title:"Log in to your account",description:"Enter your email and password below to log in"},{default:r(()=>[o(s(V),{title:"Log in"}),m.status?(d(),x("div",C,y(m.status),1)):p("",!0),a("form",{onSubmit:v(w,["prevent"]),class:"flex flex-col gap-6"},[a("div",N,[a("div",P,[o(s(u),{for:"email"},{default:r(()=>e[3]||(e[3]=[i("Email address")])),_:1,__:[3]}),o(s(g),{id:"email",type:"email",required:"",autofocus:"",tabindex:1,autocomplete:"email",modelValue:s(t).email,"onUpdate:modelValue":e[0]||(e[0]=l=>s(t).email=l),placeholder:"<EMAIL>"},null,8,["modelValue"]),o(f,{message:s(t).errors.email},null,8,["message"])]),a("div",q,[a("div",E,[o(s(u),{for:"password"},{default:r(()=>e[4]||(e[4]=[i("Password")])),_:1,__:[4]}),m.canResetPassword?(d(),n(_,{key:0,href:m.route("password.request"),class:"text-sm",tabindex:5},{default:r(()=>e[5]||(e[5]=[i(" Forgot password? ")])),_:1,__:[5]},8,["href"])):p("",!0)]),o(s(g),{id:"password",type:"password",required:"",tabindex:2,autocomplete:"current-password",modelValue:s(t).password,"onUpdate:modelValue":e[1]||(e[1]=l=>s(t).password=l),placeholder:"Password"},null,8,["modelValue"]),o(f,{message:s(t).errors.password},null,8,["message"])]),a("div",R,[o(s(u),{for:"remember",class:"flex items-center space-x-3"},{default:r(()=>[o(s(k),{id:"remember",modelValue:s(t).remember,"onUpdate:modelValue":e[2]||(e[2]=l=>s(t).remember=l),tabindex:3},null,8,["modelValue"]),e[6]||(e[6]=a("span",null,"Remember me",-1))]),_:1,__:[6]})]),o(s($),{type:"submit",class:"mt-4 w-full",tabindex:4,disabled:s(t).processing},{default:r(()=>[s(t).processing?(d(),n(s(B),{key:0,class:"h-4 w-4 animate-spin"})):p("",!0),e[7]||(e[7]=i(" Log in "))]),_:1,__:[7]},8,["disabled"])]),a("div",S,[e[9]||(e[9]=i(" Don't have an account? ")),o(_,{href:m.route("register"),tabindex:5},{default:r(()=>e[8]||(e[8]=[i("Sign up")])),_:1,__:[8]},8,["href"])])],32)]),_:1}))}});export{O as default};
