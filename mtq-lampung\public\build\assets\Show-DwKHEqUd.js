import{d as $,c as y,o as m,w as a,b as e,a as s,e as n,n as h,t as l,h as f,F as N,m as S,W as K}from"./app-B_pmlBSQ.js";import{_ as M}from"./AppLayout.vue_vue_type_script_setup_true_lang-BRf8lUiM.js";import{_ as B}from"./Heading.vue_vue_type_script_setup_true_lang-is4VBSCS.js";import{_ as b}from"./AppLogoIcon.vue_vue_type_script_setup_true_lang-wzz4PWna.js";import{_ as p,a as _}from"./CardContent.vue_vue_type_script_setup_true_lang-ClvLNBXC.js";import{_ as u,a as g}from"./CardTitle.vue_vue_type_script_setup_true_lang-5HO6oSoB.js";import{_ as o}from"./Label.vue_vue_type_script_setup_true_lang-rj_fhE7R.js";import{_ as d}from"./Icon.vue_vue_type_script_setup_true_lang-BuYzjckr.js";import{_ as L}from"./TextLink.vue_vue_type_script_setup_true_lang-CC2Og0lP.js";import"./useForwardExpose-CO14IhkA.js";import"./RovingFocusGroup-lsWyU4xZ.js";import"./check-DtmHjdxf.js";import"./loader-circle-6d8QrWFr.js";import"./sun-CHIV9RJ8.js";const C={class:"min-h-screen bg-gray-50"},E={class:"bg-white shadow-sm border-b"},F={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4"},V={class:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4"},W={class:"flex items-center space-x-4"},z={class:"flex space-x-2"},R={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"},U={class:"flex justify-center mb-8"},Y={class:"flex items-center space-x-2"},H={class:"grid grid-cols-1 xl:grid-cols-4 gap-6"},J={class:"xl:col-span-3 space-y-6"},O={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"},q={class:"space-y-1"},G={class:"text-sm text-gray-900 font-mono bg-gray-50 px-2 py-1 rounded"},Q={class:"space-y-1"},X={class:"text-sm text-gray-900 font-semibold"},Z={class:"space-y-1"},tt={class:"text-sm text-gray-900"},et={class:"inline-flex items-center"},st={class:"space-y-1"},at={class:"text-sm text-gray-900"},rt={class:"space-y-1"},nt={class:"text-sm text-gray-900"},lt={class:"space-y-1"},ot={class:"text-sm text-gray-900"},it={class:"inline-flex items-center px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs font-medium"},dt={class:"mt-6 pt-6 border-t"},mt={class:"mt-1 text-sm text-gray-900 leading-relaxed"},pt={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"},_t={class:"space-y-1"},ut={class:"text-sm text-gray-900 flex items-center"},gt={class:"space-y-1"},ft={class:"text-sm text-gray-900"},ct={key:0,class:"inline-flex items-center"},xt={key:1,class:"text-gray-400"},yt={class:"space-y-1"},ht={class:"text-sm text-gray-900"},bt={key:0,class:"inline-flex items-center"},vt={key:1,class:"text-gray-400"},wt={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},kt={class:"space-y-1"},Dt={class:"text-sm text-gray-900"},It={class:"space-y-1"},Lt={class:"text-sm text-gray-900"},Pt={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},jt={class:"space-y-1"},At={class:"text-sm text-gray-900"},Tt={class:"space-y-1"},$t={class:"text-sm text-gray-900"},Nt={class:"xl:col-span-1 space-y-6"},St={class:"space-y-4"},Kt={class:"text-center"},Mt={class:"text-2xl font-bold text-blue-600"},Bt={class:"text-center"},Ct={class:"text-lg font-semibold text-green-600"},Et={class:"space-y-1"},Ft={class:"text-sm text-gray-900 font-mono bg-gray-50 px-2 py-1 rounded"},Vt={class:"space-y-1"},Wt={class:"text-sm text-gray-900 break-all"},zt={class:"space-y-1"},Rt={class:"inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800"},Ut={class:"space-y-1"},Yt={class:"space-y-1"},Ht={class:"space-y-1"},Jt={class:"text-sm text-gray-900"},Ot={class:"space-y-1"},qt={class:"text-sm text-gray-900"},Gt={class:"ml-2 inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800"},Qt={class:"flex justify-between items-start"},Xt={class:"flex-1"},Zt={class:"font-semibold text-sm text-gray-900"},te={class:"text-xs text-gray-600 mt-1"},ee={class:"text-xs text-gray-500 font-mono mt-1"},ce=$({__name:"Show",props:{peserta:{}},setup(P){const x=P;function v(r){return{draft:"bg-gray-100 text-gray-800",submitted:"bg-blue-100 text-blue-800",payment_pending:"bg-yellow-100 text-yellow-800",paid:"bg-green-100 text-green-800",verified:"bg-indigo-100 text-indigo-800",approved:"bg-emerald-100 text-emerald-800",rejected:"bg-red-100 text-red-800"}[r]||"bg-gray-100 text-gray-800"}function w(r){return{draft:"Draft",submitted:"Disubmit",payment_pending:"Menunggu Pembayaran",paid:"Dibayar",verified:"Diverifikasi",approved:"Disetujui",rejected:"Ditolak"}[r]||r}function k(r){return new Date(r).toLocaleDateString("id-ID",{year:"numeric",month:"long",day:"numeric"})}function j(r){return new Date(r).toLocaleDateString("id-ID",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"})}function D(r){const t=new Date,i=new Date(r);let c=t.getFullYear()-i.getFullYear();const I=t.getMonth()-i.getMonth();return(I<0||I===0&&t.getDate()<i.getDate())&&c--,c}function A(){confirm(`Apakah Anda yakin ingin menghapus peserta ${x.peserta.nama_lengkap}?`)&&K.delete(route("admin.peserta.destroy",x.peserta.id_peserta))}const T=[{title:"Manajemen Peserta",href:"/admin/peserta"},{title:"Detail Peserta",href:"/admin/peserta/"+x.peserta.id_peserta}];return(r,t)=>(m(),y(M,{breadcrumbs:T},{default:a(()=>[e("div",C,[e("div",E,[e("div",F,[e("div",V,[e("div",W,[s(b,{"as-child":"",variant:"ghost",size:"sm",class:"hover:bg-gray-100 transition-colors text-muted-foreground bg-gray-100 cursor-pointer hover:text-foreground"},{default:a(()=>[s(L,{href:r.route("admin.peserta.index")},{default:a(()=>[s(d,{name:"arrowLeft",class:"w-4 h-4 mr-2"}),t[0]||(t[0]=n(" Kembali "))]),_:1,__:[0]},8,["href"])]),_:1}),e("div",null,[s(B,{title:"Detail Peserta",subtitle:r.peserta.nama_lengkap,description:"NIK: "+r.peserta.nik+" • Terdaftar "+k(r.peserta.created_at)},null,8,["subtitle","description"])])]),e("div",z,[s(b,{"as-child":"",variant:"outline",class:"hover:bg-gray-50 transition-colors"},{default:a(()=>[s(L,{href:r.route("admin.peserta.edit",r.peserta.id_peserta)},{default:a(()=>[s(d,{name:"edit",class:"w-4 h-4 mr-2"}),t[1]||(t[1]=n(" Edit "))]),_:1,__:[1]},8,["href"])]),_:1}),s(b,{onClick:A,variant:"destructive",class:"hover:bg-red-600 transition-colors"},{default:a(()=>[s(d,{name:"trash-2",class:"w-4 h-4 mr-2"}),t[2]||(t[2]=n(" Hapus "))]),_:1,__:[2]})])])])]),e("div",R,[e("div",U,[e("div",Y,[e("span",{class:h([v(r.peserta.status_peserta),"inline-flex items-center px-4 py-2 text-sm font-semibold rounded-full"])},[t[3]||(t[3]=e("div",{class:"w-2 h-2 rounded-full bg-current mr-2 opacity-75"},null,-1)),n(" "+l(w(r.peserta.status_peserta)),1)],2),t[4]||(t[4]=e("span",{class:"text-sm text-gray-500"}," Status terakhir diperbarui ",-1))])]),e("div",H,[e("div",J,[s(p,{class:"hover:shadow-md transition-shadow"},{default:a(()=>[s(u,{class:"bg-gradient-to-r from-blue-50 to-indigo-50 rounded-t-lg"},{default:a(()=>[s(g,{class:"flex items-center text-blue-900"},{default:a(()=>[s(d,{name:"user",class:"w-5 h-5 mr-2"}),t[5]||(t[5]=n(" Informasi Pribadi "))]),_:1,__:[5]})]),_:1}),s(_,{class:"p-6"},{default:a(()=>[e("div",O,[e("div",q,[s(o,{class:"text-sm font-medium text-gray-500"},{default:a(()=>t[6]||(t[6]=[n("NIK")])),_:1,__:[6]}),e("p",G,l(r.peserta.nik),1)]),e("div",Q,[s(o,{class:"text-sm font-medium text-gray-500"},{default:a(()=>t[7]||(t[7]=[n("Nama Lengkap")])),_:1,__:[7]}),e("p",X,l(r.peserta.nama_lengkap),1)]),e("div",Z,[s(o,{class:"text-sm font-medium text-gray-500"},{default:a(()=>t[8]||(t[8]=[n("Jenis Kelamin")])),_:1,__:[8]}),e("p",tt,[e("span",et,[s(d,{name:r.peserta.jenis_kelamin==="L"?"male":"female",class:"w-4 h-4 mr-1"},null,8,["name"]),n(" "+l(r.peserta.jenis_kelamin==="L"?"Laki-laki":"Perempuan"),1)])])]),e("div",st,[s(o,{class:"text-sm font-medium text-gray-500"},{default:a(()=>t[9]||(t[9]=[n("Tempat Lahir")])),_:1,__:[9]}),e("p",at,l(r.peserta.tempat_lahir),1)]),e("div",rt,[s(o,{class:"text-sm font-medium text-gray-500"},{default:a(()=>t[10]||(t[10]=[n("Tanggal Lahir")])),_:1,__:[10]}),e("p",nt,l(k(r.peserta.tanggal_lahir)),1)]),e("div",lt,[s(o,{class:"text-sm font-medium text-gray-500"},{default:a(()=>t[11]||(t[11]=[n("Umur")])),_:1,__:[11]}),e("p",ot,[e("span",it,l(D(r.peserta.tanggal_lahir))+" tahun ",1)])])]),e("div",dt,[s(o,{class:"text-sm font-medium text-gray-500"},{default:a(()=>t[12]||(t[12]=[n("Alamat")])),_:1,__:[12]}),e("p",mt,l(r.peserta.alamat),1)])]),_:1})]),_:1}),s(p,{class:"hover:shadow-md transition-shadow"},{default:a(()=>[s(u,{class:"bg-gradient-to-r from-green-50 to-emerald-50 rounded-t-lg"},{default:a(()=>[s(g,{class:"flex items-center text-green-900"},{default:a(()=>[s(d,{name:"phone",class:"w-5 h-5 mr-2"}),t[13]||(t[13]=n(" Informasi Kontak "))]),_:1,__:[13]})]),_:1}),s(_,{class:"p-6"},{default:a(()=>{var i;return[e("div",pt,[e("div",_t,[s(o,{class:"text-sm font-medium text-gray-500"},{default:a(()=>t[14]||(t[14]=[n("Wilayah")])),_:1,__:[14]}),e("p",ut,[s(d,{name:"map-pin",class:"w-4 h-4 mr-1 text-gray-400"}),n(" "+l(((i=r.peserta.wilayah)==null?void 0:i.nama_wilayah)||"-"),1)])]),e("div",gt,[s(o,{class:"text-sm font-medium text-gray-500"},{default:a(()=>t[15]||(t[15]=[n("No. Telepon")])),_:1,__:[15]}),e("p",ft,[r.peserta.no_telepon?(m(),f("span",ct,[s(d,{name:"phone",class:"w-4 h-4 mr-1 text-gray-400"}),n(" "+l(r.peserta.no_telepon),1)])):(m(),f("span",xt,"-"))])]),e("div",yt,[s(o,{class:"text-sm font-medium text-gray-500"},{default:a(()=>t[16]||(t[16]=[n("Email")])),_:1,__:[16]}),e("p",ht,[r.peserta.email?(m(),f("span",bt,[s(d,{name:"mail",class:"w-4 h-4 mr-1 text-gray-400"}),n(" "+l(r.peserta.email),1)])):(m(),f("span",vt,"-"))])])])]}),_:1})]),_:1}),s(p,{class:"hover:shadow-md transition-shadow"},{default:a(()=>[s(u,{class:"bg-gradient-to-r from-purple-50 to-pink-50 rounded-t-lg"},{default:a(()=>[s(g,{class:"flex items-center text-purple-900"},{default:a(()=>[s(d,{name:"users",class:"w-5 h-5 mr-2"}),t[17]||(t[17]=n(" Informasi Keluarga "))]),_:1,__:[17]})]),_:1}),s(_,{class:"p-6"},{default:a(()=>[e("div",wt,[e("div",kt,[s(o,{class:"text-sm font-medium text-gray-500"},{default:a(()=>t[18]||(t[18]=[n("Nama Ayah")])),_:1,__:[18]}),e("p",Dt,l(r.peserta.nama_ayah||"-"),1)]),e("div",It,[s(o,{class:"text-sm font-medium text-gray-500"},{default:a(()=>t[19]||(t[19]=[n("Nama Ibu")])),_:1,__:[19]}),e("p",Lt,l(r.peserta.nama_ibu||"-"),1)])])]),_:1})]),_:1}),s(p,{class:"hover:shadow-md transition-shadow"},{default:a(()=>[s(u,{class:"bg-gradient-to-r from-orange-50 to-yellow-50 rounded-t-lg"},{default:a(()=>[s(g,{class:"flex items-center text-orange-900"},{default:a(()=>[s(d,{name:"briefcase",class:"w-5 h-5 mr-2"}),t[20]||(t[20]=n(" Informasi Tambahan "))]),_:1,__:[20]})]),_:1}),s(_,{class:"p-6"},{default:a(()=>[e("div",Pt,[e("div",jt,[s(o,{class:"text-sm font-medium text-gray-500"},{default:a(()=>t[21]||(t[21]=[n("Pekerjaan")])),_:1,__:[21]}),e("p",At,l(r.peserta.pekerjaan||"-"),1)]),e("div",Tt,[s(o,{class:"text-sm font-medium text-gray-500"},{default:a(()=>t[22]||(t[22]=[n("Instansi Asal")])),_:1,__:[22]}),e("p",$t,l(r.peserta.instansi_asal||"-"),1)])])]),_:1})]),_:1})]),e("div",Nt,[s(p,{class:"hover:shadow-md transition-shadow"},{default:a(()=>[s(u,{class:"text-center"},{default:a(()=>[s(g,{class:"text-lg"},{default:a(()=>t[23]||(t[23]=[n("Ringkasan")])),_:1,__:[23]})]),_:1}),s(_,{class:"p-6"},{default:a(()=>{var i;return[e("div",St,[e("div",Kt,[e("div",Mt,l(((i=r.peserta.pendaftaran)==null?void 0:i.length)||0),1),t[24]||(t[24]=e("div",{class:"text-sm text-gray-500"},"Lomba Diikuti",-1))]),e("div",Bt,[e("div",Ct,l(D(r.peserta.tanggal_lahir)),1),t[25]||(t[25]=e("div",{class:"text-sm text-gray-500"},"Tahun",-1))])])]}),_:1})]),_:1}),s(p,{class:"hover:shadow-md transition-shadow"},{default:a(()=>[s(u,{class:"bg-gradient-to-r from-indigo-50 to-blue-50 rounded-t-lg"},{default:a(()=>[s(g,{class:"flex items-center text-indigo-900"},{default:a(()=>[s(d,{name:"user-check",class:"w-5 h-5 mr-2"}),t[26]||(t[26]=n(" Informasi Akun "))]),_:1,__:[26]})]),_:1}),s(_,{class:"p-6 space-y-4"},{default:a(()=>{var i,c;return[e("div",Et,[s(o,{class:"text-sm font-medium text-gray-500"},{default:a(()=>t[27]||(t[27]=[n("Username")])),_:1,__:[27]}),e("p",Ft,l((i=r.peserta.user)==null?void 0:i.username),1)]),e("div",Vt,[s(o,{class:"text-sm font-medium text-gray-500"},{default:a(()=>t[28]||(t[28]=[n("Email Akun")])),_:1,__:[28]}),e("p",Wt,l((c=r.peserta.user)==null?void 0:c.email),1)]),e("div",zt,[s(o,{class:"text-sm font-medium text-gray-500"},{default:a(()=>t[29]||(t[29]=[n("Role")])),_:1,__:[29]}),e("span",Rt,[s(d,{name:"user",class:"w-3 h-3 mr-1"}),t[30]||(t[30]=n(" Peserta "))])]),e("div",Ut,[s(o,{class:"text-sm font-medium text-gray-500"},{default:a(()=>t[31]||(t[31]=[n("Status Akun")])),_:1,__:[31]}),t[32]||(t[32]=e("span",{class:"inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800"},[e("div",{class:"w-2 h-2 rounded-full bg-green-500 mr-1"}),n(" Aktif ")],-1))])]}),_:1})]),_:1}),s(p,{class:"hover:shadow-md transition-shadow"},{default:a(()=>[s(u,{class:"bg-gradient-to-r from-teal-50 to-cyan-50 rounded-t-lg"},{default:a(()=>[s(g,{class:"flex items-center text-teal-900"},{default:a(()=>[s(d,{name:"clipboard-check",class:"w-5 h-5 mr-2"}),t[33]||(t[33]=n(" Informasi Pendaftaran "))]),_:1,__:[33]})]),_:1}),s(_,{class:"p-6 space-y-4"},{default:a(()=>[e("div",Yt,[s(o,{class:"text-sm font-medium text-gray-500"},{default:a(()=>t[34]||(t[34]=[n("Tipe Pendaftaran")])),_:1,__:[34]}),e("span",{class:h(["inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full",r.peserta.registration_type==="mandiri"?"bg-blue-100 text-blue-800":"bg-purple-100 text-purple-800"])},[s(d,{name:r.peserta.registration_type==="mandiri"?"user":"users",class:"w-3 h-3 mr-1"},null,8,["name"]),n(" "+l(r.peserta.registration_type==="mandiri"?"Mandiri":"Admin Daerah"),1)],2)]),e("div",Ht,[s(o,{class:"text-sm font-medium text-gray-500"},{default:a(()=>t[35]||(t[35]=[n("Didaftarkan Oleh")])),_:1,__:[35]}),e("p",Jt,l(r.peserta.registered_by?r.peserta.registered_by.nama_lengkap:"Mandiri"),1)]),e("div",Ot,[s(o,{class:"text-sm font-medium text-gray-500"},{default:a(()=>t[36]||(t[36]=[n("Tanggal Daftar")])),_:1,__:[36]}),e("p",qt,l(j(r.peserta.created_at)),1)])]),_:1})]),_:1}),r.peserta.pendaftaran&&r.peserta.pendaftaran.length>0?(m(),y(p,{key:0,class:"hover:shadow-md transition-shadow"},{default:a(()=>[s(u,{class:"bg-gradient-to-r from-red-50 to-pink-50 rounded-t-lg"},{default:a(()=>[s(g,{class:"flex items-center text-red-900"},{default:a(()=>[s(d,{name:"trophy",class:"w-5 h-5 mr-2"}),t[37]||(t[37]=n(" Pendaftaran Lomba ")),e("span",Gt,l(r.peserta.pendaftaran.length),1)]),_:1,__:[37]})]),_:1}),s(_,{class:"p-6 space-y-3"},{default:a(()=>[(m(!0),f(N,null,S(r.peserta.pendaftaran,i=>(m(),f("div",{key:i.id_pendaftaran,class:"p-4 border rounded-lg hover:bg-gray-50 transition-colors"},[e("div",Qt,[e("div",Xt,[e("h4",Zt,l(i.golongan.cabang_lomba.nama_cabang),1),e("p",te,l(i.golongan.nama_golongan),1),e("p",ee,l(i.nomor_pendaftaran),1)]),e("span",{class:h([v(i.status_pendaftaran),"inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full ml-2"])},[t[38]||(t[38]=e("div",{class:"w-2 h-2 rounded-full bg-current mr-1 opacity-75"},null,-1)),n(" "+l(w(i.status_pendaftaran)),1)],2)])]))),128))]),_:1})]),_:1})):(m(),y(p,{key:1,class:"hover:shadow-md transition-shadow"},{default:a(()=>[s(u,{class:"bg-gradient-to-r from-gray-50 to-gray-100 rounded-t-lg"},{default:a(()=>[s(g,{class:"flex items-center text-gray-700"},{default:a(()=>[s(d,{name:"trophy",class:"w-5 h-5 mr-2"}),t[39]||(t[39]=n(" Pendaftaran Lomba "))]),_:1,__:[39]})]),_:1}),s(_,{class:"p-6 text-center"},{default:a(()=>[s(d,{name:"trophy",class:"w-12 h-12 mx-auto text-gray-300 mb-2"}),t[40]||(t[40]=e("p",{class:"text-sm text-gray-500"},"Belum ada pendaftaran lomba",-1))]),_:1,__:[40]})]),_:1}))])])])])]),_:1}))}});export{ce as default};
