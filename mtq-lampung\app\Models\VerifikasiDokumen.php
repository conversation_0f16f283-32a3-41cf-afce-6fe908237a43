<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class VerifikasiDokumen extends Model
{
    use HasFactory;

    protected $table = 'verifikasi_dokumen';
    protected $primaryKey = 'id_verifikasi_dokumen';

    protected $fillable = [
        'id_dokumen',
        'id_jenis_dokumen',
        'verified_by',
        'status_verifikasi',
        'catatan_verifikasi',
        'detail_verifikasi',
        'verified_at',
        'kualitas_gambar',
        'kelengkapan_data',
        'kejelasan_teks'
    ];

    protected $casts = [
        'status_verifikasi' => 'string',
        'detail_verifikasi' => 'array',
        'verified_at' => 'datetime',
        'kualitas_gambar' => 'boolean',
        'kelengkapan_data' => 'boolean',
        'kejelasan_teks' => 'boolean'
    ];

    // Relationships
    public function dokumen(): BelongsTo
    {
        return $this->belongsTo(DokumenPeserta::class, 'id_dokumen', 'id_dokumen');
    }

    public function jenisDokumen(): BelongsTo
    {
        return $this->belongsTo(JenisDokumen::class, 'id_jenis_dokumen', 'id_jenis_dokumen');
    }

    public function verifiedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'verified_by', 'id_user');
    }

    // Scopes
    public function scopePending($query)
    {
        return $query->where('status_verifikasi', 'pending');
    }

    public function scopeSesuai($query)
    {
        return $query->where('status_verifikasi', 'sesuai');
    }

    public function scopeTidakSesuai($query)
    {
        return $query->where('status_verifikasi', 'tidak_sesuai');
    }

    public function scopePerluPerbaikan($query)
    {
        return $query->where('status_verifikasi', 'perlu_perbaikan');
    }

    // Helper methods
    public function isApproved()
    {
        return $this->status_verifikasi === 'sesuai';
    }

    public function isRejected()
    {
        return $this->status_verifikasi === 'tidak_sesuai';
    }

    public function needsRevision()
    {
        return $this->status_verifikasi === 'perlu_perbaikan';
    }

    public function getQualityScore()
    {
        $score = 0;
        $total = 0;

        if (!is_null($this->kualitas_gambar)) {
            $score += $this->kualitas_gambar ? 1 : 0;
            $total++;
        }

        if (!is_null($this->kelengkapan_data)) {
            $score += $this->kelengkapan_data ? 1 : 0;
            $total++;
        }

        if (!is_null($this->kejelasan_teks)) {
            $score += $this->kejelasan_teks ? 1 : 0;
            $total++;
        }

        return $total > 0 ? ($score / $total) * 100 : 0;
    }

    public function getStatusBadgeClass()
    {
        return match($this->status_verifikasi) {
            'pending' => 'bg-yellow-100 text-yellow-800',
            'sesuai' => 'bg-green-100 text-green-800',
            'tidak_sesuai' => 'bg-red-100 text-red-800',
            'perlu_perbaikan' => 'bg-orange-100 text-orange-800',
            default => 'bg-gray-100 text-gray-800'
        };
    }
}
