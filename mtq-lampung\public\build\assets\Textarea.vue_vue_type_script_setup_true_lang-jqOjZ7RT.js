import{d as l,j as n,v as d,u as s,o as u,h as m,n as c,Q as p}from"./app-B_pmlBSQ.js";import{u as f}from"./useForwardExpose-CO14IhkA.js";import{a as v}from"./AppLogoIcon.vue_vue_type_script_setup_true_lang-wzz4PWna.js";const w=l({__name:"Textarea",props:{class:{},defaultValue:{},modelValue:{}},emits:["update:modelValue"],setup(o,{emit:r}){const e=o,a=f(e,"modelValue",r,{passive:!0,defaultValue:e.defaultValue});return(b,t)=>n((u(),m("textarea",{"onUpdate:modelValue":t[0]||(t[0]=i=>p(a)?a.value=i:null),"data-slot":"textarea",class:c(s(v)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e.class))},null,2)),[[d,s(a)]])}});export{w as _};
