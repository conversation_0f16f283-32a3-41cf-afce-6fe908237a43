<template>
  <AppLayout :breadcrumbs="breadcrumbItems">
    <Head title="Edit Dewan Ha<PERSON>" />
    <Heading :title="`Edit Dewan Hakim: ${dewaHakim.nama_lengkap}`" />

    <div class="max-w-4xl mx-auto">
      <Card>
        <CardHeader>
          <CardTitle>Edit Informasi Dewan Hakim</CardTitle>
          <CardDescription>
            Perbarui informasi dewan hakim di bawah ini
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form @submit.prevent="submit" class="space-y-8">
            <!-- Personal Information -->
            <div class="space-y-4">
              <h3 class="text-lg font-medium">Informasi Pribadi</h3>
              
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label for="nik">NIK *</Label>
                  <Input
                    id="nik"
                    v-model="form.nik"
                    type="text"
                    required
                    maxlength="16"
                    placeholder="16 digit NIK"
                    :class="{ 'border-red-500': form.errors.nik }"
                  />
                  <p v-if="form.errors.nik" class="text-sm text-red-600 mt-1">
                    {{ form.errors.nik }}
                  </p>
                </div>

                <div>
                  <Label for="nama_lengkap">Nama Lengkap *</Label>
                  <Input
                    id="nama_lengkap"
                    v-model="form.nama_lengkap"
                    type="text"
                    required
                    placeholder="Nama lengkap sesuai KTP"
                    :class="{ 'border-red-500': form.errors.nama_lengkap }"
                  />
                  <p v-if="form.errors.nama_lengkap" class="text-sm text-red-600 mt-1">
                    {{ form.errors.nama_lengkap }}
                  </p>
                </div>
              </div>

              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label for="tempat_lahir">Tempat Lahir *</Label>
                  <Input
                    id="tempat_lahir"
                    v-model="form.tempat_lahir"
                    type="text"
                    required
                    placeholder="Kota tempat lahir"
                    :class="{ 'border-red-500': form.errors.tempat_lahir }"
                  />
                  <p v-if="form.errors.tempat_lahir" class="text-sm text-red-600 mt-1">
                    {{ form.errors.tempat_lahir }}
                  </p>
                </div>

                <div>
                  <Label for="tanggal_lahir">Tanggal Lahir *</Label>
                  <Input
                    id="tanggal_lahir"
                    v-model="form.tanggal_lahir"
                    type="date"
                    required
                    :class="{ 'border-red-500': form.errors.tanggal_lahir }"
                  />
                  <p v-if="form.errors.tanggal_lahir" class="text-sm text-red-600 mt-1">
                    {{ form.errors.tanggal_lahir }}
                  </p>
                </div>
              </div>

              <div>
                <Label for="no_telepon">No. Telepon *</Label>
                <Input
                  id="no_telepon"
                  v-model="form.no_telepon"
                  type="tel"
                  required
                  placeholder="08xxxxxxxxxx"
                  :class="{ 'border-red-500': form.errors.no_telepon }"
                />
                <p v-if="form.errors.no_telepon" class="text-sm text-red-600 mt-1">
                  {{ form.errors.no_telepon }}
                </p>
              </div>
            </div>

            <!-- Professional Information -->
            <div class="space-y-4">
              <h3 class="text-lg font-medium">Informasi Profesi</h3>
              
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label for="pekerjaan">Pekerjaan *</Label>
                  <Input
                    id="pekerjaan"
                    v-model="form.pekerjaan"
                    type="text"
                    required
                    placeholder="Contoh: Guru, Dosen, Ustadz"
                    :class="{ 'border-red-500': form.errors.pekerjaan }"
                  />
                  <p v-if="form.errors.pekerjaan" class="text-sm text-red-600 mt-1">
                    {{ form.errors.pekerjaan }}
                  </p>
                </div>

                <div>
                  <Label for="unit_kerja">Unit Kerja *</Label>
                  <Input
                    id="unit_kerja"
                    v-model="form.unit_kerja"
                    type="text"
                    required
                    placeholder="Nama instansi/lembaga"
                    :class="{ 'border-red-500': form.errors.unit_kerja }"
                  />
                  <p v-if="form.errors.unit_kerja" class="text-sm text-red-600 mt-1">
                    {{ form.errors.unit_kerja }}
                  </p>
                </div>
              </div>

              <div>
                <Label for="spesialisasi">Spesialisasi *</Label>
                <Input
                  id="spesialisasi"
                  v-model="form.spesialisasi"
                  type="text"
                  required
                  placeholder="Contoh: Tilawah, Tahfidz, Tafsir"
                  :class="{ 'border-red-500': form.errors.spesialisasi }"
                />
                <p v-if="form.errors.spesialisasi" class="text-sm text-red-600 mt-1">
                  {{ form.errors.spesialisasi }}
                </p>
              </div>
            </div>

            <!-- Address Information -->
            <div class="space-y-4">
              <h3 class="text-lg font-medium">Informasi Alamat</h3>
              
              <div>
                <Label for="alamat_rumah">Alamat Rumah *</Label>
                <textarea
                  id="alamat_rumah"
                  v-model="form.alamat_rumah"
                  rows="3"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Alamat lengkap tempat tinggal"
                  :class="{ 'border-red-500': form.errors.alamat_rumah }"
                ></textarea>
                <p v-if="form.errors.alamat_rumah" class="text-sm text-red-600 mt-1">
                  {{ form.errors.alamat_rumah }}
                </p>
              </div>

              <div>
                <Label for="alamat_kantor">Alamat Kantor</Label>
                <textarea
                  id="alamat_kantor"
                  v-model="form.alamat_kantor"
                  rows="3"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Alamat tempat bekerja (opsional)"
                  :class="{ 'border-red-500': form.errors.alamat_kantor }"
                ></textarea>
                <p v-if="form.errors.alamat_kantor" class="text-sm text-red-600 mt-1">
                  {{ form.errors.alamat_kantor }}
                </p>
              </div>
            </div>

            <!-- Judge Information -->
            <div class="space-y-4">
              <h3 class="text-lg font-medium">Informasi Hakim</h3>
              
              <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <Label for="tipe_hakim">Tipe Hakim *</Label>
                  <Select v-model="form.tipe_hakim" required>
                    <SelectTrigger :class="{ 'border-red-500': form.errors.tipe_hakim }">
                      <SelectValue placeholder="Pilih Tipe" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem v-for="(label, value) in tipeHakim" :key="value" :value="value">
                        {{ label }}
                      </SelectItem>
                    </SelectContent>
                  </Select>
                  <p v-if="form.errors.tipe_hakim" class="text-sm text-red-600 mt-1">
                    {{ form.errors.tipe_hakim }}
                  </p>
                </div>

                <div>
                  <Label for="id_wilayah">Wilayah *</Label>
                  <Select v-model="form.id_wilayah" required>
                    <SelectTrigger :class="{ 'border-red-500': form.errors.id_wilayah }">
                      <SelectValue placeholder="Pilih Wilayah" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem v-for="w in wilayah" :key="w.id_wilayah" :value="w.id_wilayah.toString()">
                        {{ w.nama_wilayah }}
                      </SelectItem>
                    </SelectContent>
                  </Select>
                  <p v-if="form.errors.id_wilayah" class="text-sm text-red-600 mt-1">
                    {{ form.errors.id_wilayah }}
                  </p>
                </div>

                <div>
                  <Label for="status">Status *</Label>
                  <Select v-model="form.status" required>
                    <SelectTrigger :class="{ 'border-red-500': form.errors.status }">
                      <SelectValue placeholder="Pilih Status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="aktif">Aktif</SelectItem>
                      <SelectItem value="non_aktif">Non Aktif</SelectItem>
                    </SelectContent>
                  </Select>
                  <p v-if="form.errors.status" class="text-sm text-red-600 mt-1">
                    {{ form.errors.status }}
                  </p>
                </div>
              </div>
            </div>

            <!-- Current Info -->
            <div class="bg-gray-50 p-4 rounded-lg">
              <h4 class="font-medium text-gray-900 mb-2">Informasi Saat Ini</h4>
              <div class="text-sm text-gray-600 space-y-1">
                <p><strong>User Account:</strong> {{ dewaHakim.user?.email }}</p>
                <p><strong>Dibuat:</strong> {{ formatDate(dewaHakim.created_at) }}</p>
                <p><strong>Diperbarui:</strong> {{ formatDate(dewaHakim.updated_at) }}</p>
                <p><strong>Jumlah Penilaian:</strong> {{ dewaHakim.nilai_peserta?.length || 0 }} penilaian</p>
              </div>
            </div>

            <!-- Warning for changes -->
            <div v-if="hasNilaiPeserta" class="bg-yellow-50 border border-yellow-200 p-4 rounded-lg">
              <div class="flex">
                <Icon name="alert-triangle" class="w-5 h-5 text-yellow-600 mr-2 mt-0.5" />
                <div>
                  <h4 class="font-medium text-yellow-800">Perhatian</h4>
                  <p class="text-sm text-yellow-700 mt-1">
                    Dewan hakim ini memiliki {{ dewaHakim.nilai_peserta?.length || 0 }} data penilaian. 
                    Perubahan status dapat mempengaruhi proses penilaian yang sedang berlangsung.
                  </p>
                </div>
              </div>
            </div>

            <!-- Actions -->
            <div class="flex justify-end space-x-4 pt-6 border-t">
              <Button
                type="button"
                variant="outline"
                @click="$inertia.visit(route('admin.dewan-hakim.index'))"
              >
                Batal
              </Button>
              <Button type="submit" :disabled="form.processing">
                <Icon v-if="form.processing" name="loader-2" class="w-4 h-4 mr-2 animate-spin" />
                Simpan Perubahan
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Head, useForm } from '@inertiajs/vue3'
import AppLayout from '@/layouts/AppLayout.vue'
import Heading from '@/components/Heading.vue'
import Button from '@/components/ui/button/Button.vue'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import Icon from '@/components/Icon.vue'
import { type BreadcrumbItem } from '@/types'

interface User {
  email: string
}

interface Wilayah {
  id_wilayah: number
  nama_wilayah: string
}

interface DewaHakim {
  id_dewan_hakim: number
  nik: string
  nama_lengkap: string
  tempat_lahir: string
  tanggal_lahir: string
  pekerjaan: string
  unit_kerja: string
  alamat_rumah: string
  alamat_kantor?: string
  no_telepon: string
  spesialisasi: string
  tipe_hakim: string
  id_wilayah: number
  status: string
  created_at: string
  updated_at: string
  user?: User
  nilai_peserta?: any[]
}

const props = defineProps<{
  dewaHakim: DewaHakim
  wilayah: Wilayah[]
  tipeHakim: Record<string, string>
}>()

const breadcrumbItems: BreadcrumbItem[] = [
  { title: 'Dashboard', href: '/admin/dashboard' },
  { title: 'Manajemen Dewan Hakim', href: '/admin/dewan-hakim' },
  { title: 'Edit Dewan Hakim', href: `/admin/dewan-hakim/${props.dewaHakim.id_dewan_hakim}/edit` }
]

const form = useForm({
  nik: props.dewaHakim.nik,
  nama_lengkap: props.dewaHakim.nama_lengkap,
  tempat_lahir: props.dewaHakim.tempat_lahir,
  tanggal_lahir: props.dewaHakim.tanggal_lahir,
  pekerjaan: props.dewaHakim.pekerjaan,
  unit_kerja: props.dewaHakim.unit_kerja,
  alamat_rumah: props.dewaHakim.alamat_rumah,
  alamat_kantor: props.dewaHakim.alamat_kantor || '',
  no_telepon: props.dewaHakim.no_telepon,
  spesialisasi: props.dewaHakim.spesialisasi,
  tipe_hakim: props.dewaHakim.tipe_hakim,
  id_wilayah: props.dewaHakim.id_wilayah.toString(),
  status: props.dewaHakim.status
})

const hasNilaiPeserta = computed(() => {
  return props.dewaHakim.nilai_peserta && props.dewaHakim.nilai_peserta.length > 0
})

const submit = () => {
  form.put(route('admin.dewan-hakim.update', props.dewaHakim.id_dewan_hakim), {
    onSuccess: () => {
      // Redirect handled by controller
    }
  })
}

const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString('id-ID', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}
</script>
