import{_ as L}from"./AppLayout.vue_vue_type_script_setup_true_lang-BRf8lUiM.js";import{d as A,c as S,o as g,w as s,a as t,b as e,u as l,g as C,e as o,t as i,n as N,h as p}from"./app-B_pmlBSQ.js";import{_ as F}from"./Heading.vue_vue_type_script_setup_true_lang-is4VBSCS.js";import{_ as d}from"./AppLogoIcon.vue_vue_type_script_setup_true_lang-wzz4PWna.js";import{_ as k,a as y}from"./CardContent.vue_vue_type_script_setup_true_lang-ClvLNBXC.js";import{_ as v}from"./CardDescription.vue_vue_type_script_setup_true_lang-DboywBIK.js";import{_ as h,a as x}from"./CardTitle.vue_vue_type_script_setup_true_lang-5HO6oSoB.js";import{_ as w}from"./index-CMGr3-bt.js";import{_ as $,a as D}from"./index-Cae_Ab9-.js";import{_ as u}from"./Icon.vue_vue_type_script_setup_true_lang-BuYzjckr.js";import{_ as c}from"./TextLink.vue_vue_type_script_setup_true_lang-CC2Og0lP.js";import"./useForwardExpose-CO14IhkA.js";import"./RovingFocusGroup-lsWyU4xZ.js";import"./check-DtmHjdxf.js";import"./loader-circle-6d8QrWFr.js";import"./sun-CHIV9RJ8.js";const T={class:"flex items-center space-x-4"},z={class:"space-y-6"},E={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},G={class:"space-y-4"},I={class:"flex items-center space-x-3"},K={class:"text-sm text-gray-600"},R={class:"flex items-center space-x-3"},U={class:"text-sm text-gray-600"},V={class:"space-y-4"},Y={class:"flex items-center space-x-3"},H={class:"text-lg font-bold text-green-600"},J={class:"flex items-center space-x-3"},q={key:0,class:"text-center py-8"},O={class:"space-x-4"},Q={key:1,class:"text-center py-8"},W={key:2,class:"text-center py-8"},X={key:3,class:"space-y-4"},Z={class:"text-center"},aa={key:4,class:"space-y-4"},ta={class:"bg-gray-50 p-4 rounded-lg"},ea={class:"text-sm space-y-1"},na={class:"text-center"},sa={class:"text-gray-700"},xa=A({__name:"Golongan",props:{golongan:{},canRegister:{type:Boolean},peserta:{}},setup(P){const r=P;function j(n){return new Intl.NumberFormat("id-ID",{style:"currency",currency:"IDR",minimumFractionDigits:0}).format(n)}function b(n){const a=new Date,m=new Date(n);let f=a.getFullYear()-m.getFullYear();const _=a.getMonth()-m.getMonth();return(_<0||_===0&&a.getDate()<m.getDate())&&f--,f}function M(){if(!r.peserta)return!1;const n=b(r.peserta.tanggal_lahir),a=r.peserta.jenis_kelamin===r.golongan.jenis_kelamin,m=n>=r.golongan.batas_umur_min&&n<=r.golongan.batas_umur_max;return a&&m}function B(){if(!r.peserta)return"";const n=b(r.peserta.tanggal_lahir),a=r.peserta.jenis_kelamin===r.golongan.jenis_kelamin,m=n>=r.golongan.batas_umur_min&&n<=r.golongan.batas_umur_max;return a?m?"":`Usia Anda (${n} tahun) tidak sesuai dengan persyaratan golongan (${r.golongan.batas_umur_min}-${r.golongan.batas_umur_max} tahun)`:`Golongan ini khusus untuk ${r.golongan.jenis_kelamin==="L"?"putra":"putri"}`}return(n,a)=>(g(),S(L,null,{header:s(()=>[e("div",T,[t(d,{as:"link",href:n.route("competition.index"),variant:"ghost",size:"sm"},{default:s(()=>[t(u,{name:"arrow-left",class:"w-4 h-4 mr-2"}),a[0]||(a[0]=o(" Kembali "))]),_:1,__:[0]},8,["href"]),t(F,null,{default:s(()=>[o(i(n.golongan.nama_golongan),1)]),_:1})])]),default:s(()=>[t(l(C),{title:n.golongan.nama_golongan},null,8,["title"]),e("div",z,[t(l(k),null,{default:s(()=>[t(l(h),null,{default:s(()=>[t(l(x),{class:"flex items-center justify-between"},{default:s(()=>[o(i(n.golongan.nama_golongan)+" ",1),t(l(w),{class:N(n.golongan.jenis_kelamin==="L"?"bg-blue-100 text-blue-800":"bg-pink-100 text-pink-800")},{default:s(()=>[o(i(n.golongan.jenis_kelamin==="L"?"Putra":"Putri"),1)]),_:1},8,["class"])]),_:1}),t(l(v),null,{default:s(()=>[o(" Cabang Lomba: "+i(n.golongan.cabang_lomba.nama_cabang),1)]),_:1})]),_:1}),t(l(y),null,{default:s(()=>[e("div",E,[e("div",G,[e("div",I,[t(u,{name:"calendar",class:"h-5 w-5 text-gray-500"}),e("div",null,[a[1]||(a[1]=e("p",{class:"font-medium"},"Batas Usia",-1)),e("p",K,i(n.golongan.batas_umur_min)+" - "+i(n.golongan.batas_umur_max)+" tahun",1)])]),e("div",R,[t(u,{name:"users",class:"h-5 w-5 text-gray-500"}),e("div",null,[a[2]||(a[2]=e("p",{class:"font-medium"},"Kuota Peserta",-1)),e("p",U,"Maksimal "+i(n.golongan.kuota_max)+" peserta",1)])])]),e("div",V,[e("div",Y,[t(u,{name:"credit-card",class:"h-5 w-5 text-gray-500"}),e("div",null,[a[3]||(a[3]=e("p",{class:"font-medium"},"Biaya Pendaftaran",-1)),e("p",H,i(j(n.golongan.biaya_pendaftaran)),1)])]),e("div",J,[t(u,{name:"check-circle",class:"h-5 w-5 text-gray-500"}),e("div",null,[a[4]||(a[4]=e("p",{class:"font-medium"},"Status",-1)),t(l(w),{variant:"secondary"},{default:s(()=>[o(i(n.golongan.status==="aktif"?"Pendaftaran Dibuka":"Pendaftaran Ditutup"),1)]),_:1})])])])])]),_:1})]),_:1}),t(l(k),null,{default:s(()=>[t(l(h),null,{default:s(()=>[t(l(x),null,{default:s(()=>a[5]||(a[5]=[o("Pendaftaran")])),_:1,__:[5]}),t(l(v),null,{default:s(()=>[o(" Daftar untuk mengikuti lomba "+i(n.golongan.nama_golongan),1)]),_:1})]),_:1}),t(l(y),null,{default:s(()=>{var m,f,_;return[n.$page.props.auth.user?n.$page.props.auth.user.role!=="peserta"?(g(),p("div",Q,[t(u,{name:"shield-x",class:"h-12 w-12 text-gray-400 mx-auto mb-4"}),a[10]||(a[10]=e("h3",{class:"text-lg font-medium text-gray-900 mb-2"},"Akses Terbatas",-1)),a[11]||(a[11]=e("p",{class:"text-gray-600"},"Hanya peserta yang dapat mendaftar lomba.",-1))])):n.canRegister?M()?(g(),p("div",aa,[t(l($),null,{default:s(()=>[t(u,{name:"check-circle",class:"h-4 w-4"}),t(l(D),null,{default:s(()=>a[16]||(a[16]=[o(" Anda memenuhi syarat untuk mendaftar di golongan ini. ")])),_:1,__:[16]})]),_:1}),e("div",ta,[a[20]||(a[20]=e("h4",{class:"font-medium mb-2"},"Data Peserta:",-1)),e("div",ea,[e("p",null,[a[17]||(a[17]=e("span",{class:"font-medium"},"Nama:",-1)),o(" "+i((m=n.peserta)==null?void 0:m.nama_lengkap),1)]),e("p",null,[a[18]||(a[18]=e("span",{class:"font-medium"},"Jenis Kelamin:",-1)),o(" "+i(((f=n.peserta)==null?void 0:f.jenis_kelamin)==="L"?"Laki-laki":"Perempuan"),1)]),e("p",null,[a[19]||(a[19]=e("span",{class:"font-medium"},"Usia:",-1)),o(" "+i(b(((_=n.peserta)==null?void 0:_.tanggal_lahir)||""))+" tahun",1)])])]),e("div",na,[t(d,{"as-child":"",size:"lg",class:"w-full md:w-auto"},{default:s(()=>[t(c,{href:n.route("peserta.pendaftaran.create",{golongan:n.golongan.id_golongan})},{default:s(()=>[t(u,{name:"plus",class:"w-4 h-4 mr-2"}),a[21]||(a[21]=o(" Daftar Sekarang "))]),_:1,__:[21]},8,["href"])]),_:1})])])):(g(),p("div",X,[t(l($),null,{default:s(()=>[t(u,{name:"alert-triangle",class:"h-4 w-4"}),t(l(D),null,{default:s(()=>[o(i(B()),1)]),_:1})]),_:1}),e("div",Z,[t(d,{"as-child":""},{default:s(()=>[t(c,{href:n.route("competition.index"),variant:"outline"},{default:s(()=>a[15]||(a[15]=[o(" Lihat Golongan Lain ")])),_:1,__:[15]},8,["href"])]),_:1})])])):(g(),p("div",W,[t(u,{name:"clock",class:"h-12 w-12 text-gray-400 mx-auto mb-4"}),a[13]||(a[13]=e("h3",{class:"text-lg font-medium text-gray-900 mb-2"},"Profil Belum Disetujui",-1)),a[14]||(a[14]=e("p",{class:"text-gray-600 mb-4"},"Profil peserta Anda masih dalam proses verifikasi. Silakan lengkapi data dan tunggu persetujuan admin.",-1)),t(d,{"as-child":""},{default:s(()=>[t(c,{href:n.route("peserta.dashboard"),variant:"outline"},{default:s(()=>a[12]||(a[12]=[o(" Lihat Status Profil ")])),_:1,__:[12]},8,["href"])]),_:1})])):(g(),p("div",q,[t(u,{name:"user-x",class:"h-12 w-12 text-gray-400 mx-auto mb-4"}),a[8]||(a[8]=e("h3",{class:"text-lg font-medium text-gray-900 mb-2"},"Silakan Masuk Terlebih Dahulu",-1)),a[9]||(a[9]=e("p",{class:"text-gray-600 mb-4"},"Anda perlu masuk ke akun untuk mendaftar lomba.",-1)),e("div",O,[t(d,{"as-child":""},{default:s(()=>[t(c,{href:n.route("login")},{default:s(()=>a[6]||(a[6]=[o(" Masuk ")])),_:1,__:[6]},8,["href"])]),_:1}),t(d,{"as-child":""},{default:s(()=>[t(c,{href:n.route("register"),variant:"outline"},{default:s(()=>a[7]||(a[7]=[o(" Daftar Akun Baru ")])),_:1,__:[7]},8,["href"])]),_:1})])]))]}),_:1})]),_:1}),t(l(k),null,{default:s(()=>[t(l(h),null,{default:s(()=>[t(l(x),null,{default:s(()=>[o("Tentang "+i(n.golongan.cabang_lomba.nama_cabang),1)]),_:1})]),_:1}),t(l(y),null,{default:s(()=>[e("p",sa,i(n.golongan.cabang_lomba.deskripsi),1)]),_:1})]),_:1})])]),_:1}))}});export{xa as default};
