<template>
  <AppLayout :breadcrumbs="breadcrumbItems">
    <Head title="Tambah Dewan Hakim" />
    <Heading title="Tambah Dewan Hakim" />

    <div class="max-w-4xl mx-auto">
      <Card>
        <CardHeader>
          <CardTitle>Informasi Dewan Hakim Baru</CardTitle>
          <CardDescription>
            Lengkapi form di bawah untuk menambahkan dewan hakim baru
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form @submit.prevent="submit" class="space-y-8">
            <!-- User Selection -->
            <div class="space-y-4">
              <h3 class="text-lg font-medium">Akun User</h3>
              
              <div>
                <Label for="id_user">Pilih User *</Label>
                <Select v-model="form.id_user" required>
                  <SelectTrigger :class="{ 'border-red-500': form.errors.id_user }">
                    <SelectValue placeholder="Pilih user yang akan dijadikan dewan hakim" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem v-for="user in availableUsers" :key="user.id_user" :value="user.id_user.toString()">
                      {{ user.nama_lengkap }} ({{ user.username }} - {{ user.email }})
                    </SelectItem>
                  </SelectContent>
                </Select>
                <p v-if="form.errors.id_user" class="text-sm text-red-600 mt-1">
                  {{ form.errors.id_user }}
                </p>
                <p class="text-sm text-gray-500 mt-1">
                  Hanya user dengan role "dewan_hakim" yang belum memiliki profil yang ditampilkan
                </p>
              </div>
            </div>

            <!-- Personal Information -->
            <div class="space-y-4">
              <h3 class="text-lg font-medium">Informasi Pribadi</h3>
              
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label for="nik">NIK *</Label>
                  <Input
                    id="nik"
                    v-model="form.nik"
                    type="text"
                    required
                    maxlength="16"
                    placeholder="16 digit NIK"
                    :class="{ 'border-red-500': form.errors.nik }"
                  />
                  <p v-if="form.errors.nik" class="text-sm text-red-600 mt-1">
                    {{ form.errors.nik }}
                  </p>
                </div>

                <div>
                  <Label for="nama_lengkap">Nama Lengkap *</Label>
                  <Input
                    id="nama_lengkap"
                    v-model="form.nama_lengkap"
                    type="text"
                    required
                    placeholder="Nama lengkap sesuai KTP"
                    :class="{ 'border-red-500': form.errors.nama_lengkap }"
                  />
                  <p v-if="form.errors.nama_lengkap" class="text-sm text-red-600 mt-1">
                    {{ form.errors.nama_lengkap }}
                  </p>
                </div>
              </div>

              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label for="tempat_lahir">Tempat Lahir *</Label>
                  <Input
                    id="tempat_lahir"
                    v-model="form.tempat_lahir"
                    type="text"
                    required
                    placeholder="Kota tempat lahir"
                    :class="{ 'border-red-500': form.errors.tempat_lahir }"
                  />
                  <p v-if="form.errors.tempat_lahir" class="text-sm text-red-600 mt-1">
                    {{ form.errors.tempat_lahir }}
                  </p>
                </div>

                <div>
                  <Label for="tanggal_lahir">Tanggal Lahir *</Label>
                  <Input
                    id="tanggal_lahir"
                    v-model="form.tanggal_lahir"
                    type="date"
                    required
                    :class="{ 'border-red-500': form.errors.tanggal_lahir }"
                  />
                  <p v-if="form.errors.tanggal_lahir" class="text-sm text-red-600 mt-1">
                    {{ form.errors.tanggal_lahir }}
                  </p>
                </div>
              </div>

              <div>
                <Label for="no_telepon">No. Telepon *</Label>
                <Input
                  id="no_telepon"
                  v-model="form.no_telepon"
                  type="tel"
                  required
                  placeholder="08xxxxxxxxxx"
                  :class="{ 'border-red-500': form.errors.no_telepon }"
                />
                <p v-if="form.errors.no_telepon" class="text-sm text-red-600 mt-1">
                  {{ form.errors.no_telepon }}
                </p>
              </div>
            </div>

            <!-- Professional Information -->
            <div class="space-y-4">
              <h3 class="text-lg font-medium">Informasi Profesi</h3>
              
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label for="pekerjaan">Pekerjaan *</Label>
                  <Input
                    id="pekerjaan"
                    v-model="form.pekerjaan"
                    type="text"
                    required
                    placeholder="Contoh: Guru, Dosen, Ustadz"
                    :class="{ 'border-red-500': form.errors.pekerjaan }"
                  />
                  <p v-if="form.errors.pekerjaan" class="text-sm text-red-600 mt-1">
                    {{ form.errors.pekerjaan }}
                  </p>
                </div>

                <div>
                  <Label for="unit_kerja">Unit Kerja *</Label>
                  <Input
                    id="unit_kerja"
                    v-model="form.unit_kerja"
                    type="text"
                    required
                    placeholder="Nama instansi/lembaga"
                    :class="{ 'border-red-500': form.errors.unit_kerja }"
                  />
                  <p v-if="form.errors.unit_kerja" class="text-sm text-red-600 mt-1">
                    {{ form.errors.unit_kerja }}
                  </p>
                </div>
              </div>

              <div>
                <Label for="spesialisasi">Spesialisasi *</Label>
                <Input
                  id="spesialisasi"
                  v-model="form.spesialisasi"
                  type="text"
                  required
                  placeholder="Contoh: Tilawah, Tahfidz, Tafsir"
                  :class="{ 'border-red-500': form.errors.spesialisasi }"
                />
                <p v-if="form.errors.spesialisasi" class="text-sm text-red-600 mt-1">
                  {{ form.errors.spesialisasi }}
                </p>
              </div>
            </div>

            <!-- Address Information -->
            <div class="space-y-4">
              <h3 class="text-lg font-medium">Informasi Alamat</h3>
              
              <div>
                <Label for="alamat_rumah">Alamat Rumah *</Label>
                <textarea
                  id="alamat_rumah"
                  v-model="form.alamat_rumah"
                  rows="3"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Alamat lengkap tempat tinggal"
                  :class="{ 'border-red-500': form.errors.alamat_rumah }"
                ></textarea>
                <p v-if="form.errors.alamat_rumah" class="text-sm text-red-600 mt-1">
                  {{ form.errors.alamat_rumah }}
                </p>
              </div>

              <div>
                <Label for="alamat_kantor">Alamat Kantor</Label>
                <textarea
                  id="alamat_kantor"
                  v-model="form.alamat_kantor"
                  rows="3"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Alamat tempat bekerja (opsional)"
                  :class="{ 'border-red-500': form.errors.alamat_kantor }"
                ></textarea>
                <p v-if="form.errors.alamat_kantor" class="text-sm text-red-600 mt-1">
                  {{ form.errors.alamat_kantor }}
                </p>
              </div>
            </div>

            <!-- Judge Information -->
            <div class="space-y-4">
              <h3 class="text-lg font-medium">Informasi Hakim</h3>
              
              <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <Label for="tipe_hakim">Tipe Hakim *</Label>
                  <Select v-model="form.tipe_hakim" required>
                    <SelectTrigger :class="{ 'border-red-500': form.errors.tipe_hakim }">
                      <SelectValue placeholder="Pilih Tipe" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem v-for="(label, value) in tipeHakim" :key="value" :value="value">
                        {{ label }}
                      </SelectItem>
                    </SelectContent>
                  </Select>
                  <p v-if="form.errors.tipe_hakim" class="text-sm text-red-600 mt-1">
                    {{ form.errors.tipe_hakim }}
                  </p>
                </div>

                <div>
                  <Label for="id_wilayah">Wilayah *</Label>
                  <Select v-model="form.id_wilayah" required>
                    <SelectTrigger :class="{ 'border-red-500': form.errors.id_wilayah }">
                      <SelectValue placeholder="Pilih Wilayah" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem v-for="w in wilayah" :key="w.id_wilayah" :value="w.id_wilayah.toString()">
                        {{ w.nama_wilayah }}
                      </SelectItem>
                    </SelectContent>
                  </Select>
                  <p v-if="form.errors.id_wilayah" class="text-sm text-red-600 mt-1">
                    {{ form.errors.id_wilayah }}
                  </p>
                </div>

                <div>
                  <Label for="status">Status *</Label>
                  <Select v-model="form.status" required>
                    <SelectTrigger :class="{ 'border-red-500': form.errors.status }">
                      <SelectValue placeholder="Pilih Status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="aktif">Aktif</SelectItem>
                      <SelectItem value="non_aktif">Non Aktif</SelectItem>
                    </SelectContent>
                  </Select>
                  <p v-if="form.errors.status" class="text-sm text-red-600 mt-1">
                    {{ form.errors.status }}
                  </p>
                </div>
              </div>
            </div>

            <!-- Information -->
            <div class="bg-blue-50 p-4 rounded-lg">
              <h4 class="font-medium text-blue-900 mb-2">Informasi Dewan Hakim</h4>
              <div class="text-sm text-blue-800 space-y-1">
                <p><strong>Tipe Undangan:</strong> Hakim yang diundang khusus dari luar daerah</p>
                <p><strong>Tipe Kabupaten:</strong> Hakim dari wilayah kabupaten/kota setempat</p>
                <p><strong>Spesialisasi:</strong> Bidang keahlian dalam penilaian MTQ</p>
              </div>
            </div>

            <!-- Actions -->
            <div class="flex justify-end space-x-4 pt-6 border-t">
              <Button
                type="button"
                variant="outline"
                @click="$inertia.visit(route('admin.dewan-hakim.index'))"
              >
                Batal
              </Button>
              <Button type="submit" :disabled="form.processing">
                <Icon v-if="form.processing" name="loader-2" class="w-4 h-4 mr-2 animate-spin" />
                Simpan Dewan Hakim
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import { Head, useForm } from '@inertiajs/vue3'
import AppLayout from '@/layouts/AppLayout.vue'
import Heading from '@/components/Heading.vue'
import Button from '@/components/ui/button/Button.vue'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import Icon from '@/components/Icon.vue'
import { type BreadcrumbItem } from '@/types'

const breadcrumbItems: BreadcrumbItem[] = [
  { title: 'Dashboard', href: '/admin/dashboard' },
  { title: 'Manajemen Dewan Hakim', href: '/admin/dewan-hakim' },
  { title: 'Tambah Dewan Hakim', href: '/admin/dewan-hakim/create' }
]

interface User {
  id_user: number
  username: string
  email: string
  nama_lengkap: string
}

interface Wilayah {
  id_wilayah: number
  nama_wilayah: string
}

const props = defineProps<{
  availableUsers: User[]
  wilayah: Wilayah[]
  tipeHakim: Record<string, string>
}>()

const form = useForm({
  id_user: '',
  nik: '',
  nama_lengkap: '',
  tempat_lahir: '',
  tanggal_lahir: '',
  pekerjaan: '',
  unit_kerja: '',
  alamat_rumah: '',
  alamat_kantor: '',
  no_telepon: '',
  spesialisasi: '',
  tipe_hakim: '',
  id_wilayah: '',
  status: 'aktif'
})

const submit = () => {
  form.post(route('admin.dewan-hakim.store'), {
    onSuccess: () => {
      // Redirect handled by controller
    }
  })
}
</script>
