<?php

namespace App\Http\Controllers\DewaHakim;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Inertia\Inertia;

class PenilaianController extends Controller
{
    public function index()
    {
        return Inertia::render('DewaHakim/Penilaian/Index');
    }

    public function show($id)
    {
        return Inertia::render('DewaHakim/Penilaian/Show');
    }

    public function store(Request $request)
    {
        return back()->with('success', 'Penilaian berhasil disimpan');
    }
}
