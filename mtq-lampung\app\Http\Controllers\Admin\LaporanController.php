<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Inertia\Inertia;

class LaporanController extends Controller
{
    public function peserta()
    {
        return Inertia::render('Admin/Laporan/Peserta');
    }

    public function pendaftaran()
    {
        return Inertia::render('Admin/Laporan/Pendaftaran');
    }

    public function exportPeserta()
    {
        return response()->json(['message' => 'Export peserta feature coming soon']);
    }
}
