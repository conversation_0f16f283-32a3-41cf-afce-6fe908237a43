import{_ as $}from"./AppLayout.vue_vue_type_script_setup_true_lang-BRf8lUiM.js";import{d as T,c as L,o as u,w as a,b as t,a as s,e as r,u as l,g as S,t as d,h as _,F as y,m as b,n as v,i as B}from"./app-B_pmlBSQ.js";import{_ as A}from"./Heading.vue_vue_type_script_setup_true_lang-is4VBSCS.js";import{_ as f}from"./AppLogoIcon.vue_vue_type_script_setup_true_lang-wzz4PWna.js";import{_ as c,a as m}from"./CardContent.vue_vue_type_script_setup_true_lang-ClvLNBXC.js";import{_ as w}from"./CardDescription.vue_vue_type_script_setup_true_lang-DboywBIK.js";import{_ as k,a as D}from"./CardTitle.vue_vue_type_script_setup_true_lang-5HO6oSoB.js";import{_ as x}from"./index-CMGr3-bt.js";import{_ as o}from"./Icon.vue_vue_type_script_setup_true_lang-BuYzjckr.js";import{_ as p}from"./TextLink.vue_vue_type_script_setup_true_lang-CC2Og0lP.js";import"./useForwardExpose-CO14IhkA.js";import"./RovingFocusGroup-lsWyU4xZ.js";import"./check-DtmHjdxf.js";import"./loader-circle-6d8QrWFr.js";import"./sun-CHIV9RJ8.js";const C={class:"flex items-center justify-between mb-6"},V={class:"islamic-gradient p-6 rounded-lg islamic-shadow"},M={class:"space-y-6"},N={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},z={class:"flex items-center space-x-2"},K={class:"p-3 bg-islamic-500 rounded-full"},F={class:"text-2xl font-bold text-islamic-700"},E={class:"flex items-center space-x-2"},I={class:"p-3 bg-emerald-500 rounded-full"},J={class:"text-2xl font-bold text-emerald-700"},Q={class:"flex items-center space-x-2"},R={class:"p-3 bg-jade-500 rounded-full"},q={class:"text-2xl font-bold text-jade-700"},G={class:"flex items-center space-x-2"},H={class:"p-3 bg-islamic-400 rounded-full"},O={class:"text-2xl font-bold text-islamic-700"},U={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},W={class:"flex items-center space-x-2"},X={class:"text-2xl font-bold"},Y={class:"flex items-center space-x-2"},Z={class:"text-2xl font-bold"},tt={class:"flex items-center space-x-2"},st={class:"text-2xl font-bold"},et={class:"grid grid-cols-1 md:grid-cols-3 gap-6"},at={class:"flex items-center space-x-4"},lt={class:"flex-shrink-0"},it={class:"mt-4"},nt={class:"flex items-center space-x-4"},dt={class:"flex-shrink-0"},rt={class:"mt-4"},ot={class:"flex items-center space-x-4"},ct={class:"flex-shrink-0"},mt={class:"mt-4"},ut={class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},_t={class:"flex items-center justify-between"},ft={key:0,class:"text-center py-8"},pt={key:1,class:"space-y-4"},xt={class:"flex-1"},gt={class:"font-medium"},ht={class:"text-sm text-gray-600"},vt={class:"text-xs text-gray-500"},yt={class:"text-right"},bt={class:"mt-1"},wt={class:"flex items-center justify-between"},kt={key:0,class:"text-center py-8"},Dt={key:1,class:"space-y-4"},Pt={class:"flex-1"},jt={class:"font-medium"},$t={class:"text-sm text-gray-600"},Tt={class:"text-xs text-gray-500"},Lt={class:"text-right"},St={key:0,class:"mt-1"},Ht=T({__name:"Dashboard",props:{stats:{},recentPeserta:{},recentPendaftaran:{}},setup(Bt){function g(i){return{draft:"bg-gray-100 text-gray-800",submitted:"bg-blue-100 text-blue-800",verified:"bg-indigo-100 text-indigo-800",approved:"bg-green-100 text-green-800",rejected:"bg-red-100 text-red-800",payment_pending:"bg-yellow-100 text-yellow-800",paid:"bg-green-100 text-green-800"}[i]||"bg-gray-100 text-gray-800"}function h(i){return{draft:"Draft",submitted:"Disubmit",verified:"Terverifikasi",approved:"Disetujui",rejected:"Ditolak",payment_pending:"Menunggu Pembayaran",paid:"Sudah Dibayar"}[i]||i}function P(i){return{mandiri:"Mandiri",admin_daerah:"Admin Daerah"}[i]||i}function j(i){return new Date(i).toLocaleDateString("id-ID",{year:"numeric",month:"short",day:"numeric"})}return(i,e)=>(u(),L($,null,{default:a(()=>[t("div",C,[t("div",V,[s(A,{title:"Dashboard Admin Daerah",class:"text-white"}),e[0]||(e[0]=t("p",{class:"text-green-100 mt-2"},"Kelola peserta MTQ di wilayah Anda",-1))]),s(f,{"as-child":"",class:"islamic-shadow"},{default:a(()=>[s(p,{href:i.route("admin-daerah.peserta.create")},{default:a(()=>[s(o,{name:"userPlus",class:"w-4 h-4 mr-2"}),e[1]||(e[1]=r(" Daftarkan Peserta Baru "))]),_:1,__:[1]},8,["href"])]),_:1})]),s(l(S),{title:"Dashboard Admin Daerah"}),t("div",M,[t("div",N,[s(l(c),{class:"islamic-shadow hover:islamic-shadow-lg transition-all duration-300 geometric-pattern"},{default:a(()=>[s(l(m),{class:"p-6"},{default:a(()=>[t("div",z,[t("div",K,[s(o,{name:"users",class:"h-6 w-6 text-white"})]),t("div",null,[t("p",F,d(i.stats.total_peserta),1),e[2]||(e[2]=t("p",{class:"text-sm text-islamic-600"},"Total Peserta",-1))])])]),_:1})]),_:1}),s(l(c),{class:"islamic-shadow hover:islamic-shadow-lg transition-all duration-300 geometric-pattern"},{default:a(()=>[s(l(m),{class:"p-6"},{default:a(()=>[t("div",E,[t("div",I,[s(o,{name:"check-circle",class:"h-6 w-6 text-white"})]),t("div",null,[t("p",J,d(i.stats.peserta_approved),1),e[3]||(e[3]=t("p",{class:"text-sm text-emerald-600"},"Peserta Disetujui",-1))])])]),_:1})]),_:1}),s(l(c),{class:"islamic-shadow hover:islamic-shadow-lg transition-all duration-300 geometric-pattern"},{default:a(()=>[s(l(m),{class:"p-6"},{default:a(()=>[t("div",Q,[t("div",R,[s(o,{name:"clipboard-list",class:"h-6 w-6 text-white"})]),t("div",null,[t("p",q,d(i.stats.total_pendaftaran),1),e[4]||(e[4]=t("p",{class:"text-sm text-jade-600"},"Total Pendaftaran",-1))])])]),_:1})]),_:1}),s(l(c),{class:"islamic-shadow hover:islamic-shadow-lg transition-all duration-300 geometric-pattern"},{default:a(()=>[s(l(m),{class:"p-6"},{default:a(()=>[t("div",G,[t("div",H,[s(o,{name:"clock",class:"h-6 w-6 text-white"})]),t("div",null,[t("p",O,d(i.stats.peserta_pending),1),e[5]||(e[5]=t("p",{class:"text-sm text-gray-600"},"Menunggu Verifikasi",-1))])])]),_:1})]),_:1})]),t("div",U,[s(l(c),null,{default:a(()=>[s(l(m),{class:"p-6"},{default:a(()=>[t("div",W,[s(o,{name:"trophy",class:"h-8 w-8 text-orange-600"}),t("div",null,[t("p",X,d(i.stats.pendaftaran_approved),1),e[6]||(e[6]=t("p",{class:"text-sm text-gray-600"},"Pendaftaran Disetujui",-1))])])]),_:1})]),_:1}),s(l(c),null,{default:a(()=>[s(l(m),{class:"p-6"},{default:a(()=>[t("div",Y,[s(o,{name:"file-text",class:"h-8 w-8 text-indigo-600"}),t("div",null,[t("p",Z,d(i.stats.dokumen_pending),1),e[7]||(e[7]=t("p",{class:"text-sm text-gray-600"},"Dokumen Pending",-1))])])]),_:1})]),_:1}),s(l(c),null,{default:a(()=>[s(l(m),{class:"p-6"},{default:a(()=>[t("div",tt,[s(o,{name:"credit-card",class:"h-8 w-8 text-red-600"}),t("div",null,[t("p",st,d(i.stats.pembayaran_pending),1),e[8]||(e[8]=t("p",{class:"text-sm text-gray-600"},"Pembayaran Pending",-1))])])]),_:1})]),_:1})]),t("div",et,[s(l(c),{class:"hover:shadow-lg transition-shadow"},{default:a(()=>[s(l(m),{class:"p-6"},{default:a(()=>[t("div",at,[t("div",lt,[s(o,{name:"user-plus",class:"h-8 w-8 text-blue-600"})]),e[9]||(e[9]=t("div",{class:"flex-1"},[t("h3",{class:"text-lg font-medium text-gray-900"},"Daftarkan Peserta"),t("p",{class:"text-sm text-gray-500"},"Daftarkan peserta baru dari wilayah Anda")],-1))]),t("div",it,[s(f,{"as-child":"",class:"w-full"},{default:a(()=>[s(p,{href:i.route("admin-daerah.peserta.create")},{default:a(()=>e[10]||(e[10]=[r(" Daftarkan Sekarang ")])),_:1,__:[10]},8,["href"])]),_:1})])]),_:1})]),_:1}),s(l(c),{class:"hover:shadow-lg transition-shadow"},{default:a(()=>[s(l(m),{class:"p-6"},{default:a(()=>[t("div",nt,[t("div",dt,[s(o,{name:"users",class:"h-8 w-8 text-green-600"})]),e[11]||(e[11]=t("div",{class:"flex-1"},[t("h3",{class:"text-lg font-medium text-gray-900"},"Kelola Peserta"),t("p",{class:"text-sm text-gray-500"},"Lihat dan kelola data peserta")],-1))]),t("div",rt,[s(f,{"as-child":"",variant:"outline",class:"w-full"},{default:a(()=>[s(p,{href:i.route("admin-daerah.peserta.index")},{default:a(()=>e[12]||(e[12]=[r(" Kelola Peserta ")])),_:1,__:[12]},8,["href"])]),_:1})])]),_:1})]),_:1}),s(l(c),{class:"hover:shadow-lg transition-shadow"},{default:a(()=>[s(l(m),{class:"p-6"},{default:a(()=>[t("div",ot,[t("div",ct,[s(o,{name:"search",class:"h-8 w-8 text-purple-600"})]),e[13]||(e[13]=t("div",{class:"flex-1"},[t("h3",{class:"text-lg font-medium text-gray-900"},"Lihat Lomba"),t("p",{class:"text-sm text-gray-500"},"Jelajahi cabang lomba yang tersedia")],-1))]),t("div",mt,[s(f,{"as-child":"",variant:"outline",class:"w-full"},{default:a(()=>[s(p,{href:i.route("competition.index")},{default:a(()=>e[14]||(e[14]=[r("Lihat Lomba")])),_:1,__:[14]},8,["href"])]),_:1})])]),_:1})]),_:1})]),t("div",ut,[s(l(c),null,{default:a(()=>[s(l(k),null,{default:a(()=>[t("div",_t,[s(l(D),null,{default:a(()=>e[15]||(e[15]=[r("Peserta Terbaru")])),_:1,__:[15]}),s(f,{"as-child":"",variant:"outline",size:"sm"},{default:a(()=>[s(p,{href:i.route("admin-daerah.peserta.index")},{default:a(()=>e[16]||(e[16]=[r("Lihat Semua")])),_:1,__:[16]},8,["href"])]),_:1})]),s(l(w),null,{default:a(()=>e[17]||(e[17]=[r("Peserta yang baru didaftarkan")])),_:1,__:[17]})]),_:1}),s(l(m),null,{default:a(()=>[i.recentPeserta.length===0?(u(),_("div",ft,[s(o,{name:"users",class:"h-12 w-12 text-gray-400 mx-auto mb-4"}),e[18]||(e[18]=t("p",{class:"text-gray-600"},"Belum ada peserta yang didaftarkan.",-1))])):(u(),_("div",pt,[(u(!0),_(y,null,b(i.recentPeserta,n=>(u(),_("div",{key:n.id_peserta,class:"flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50 transition-colors"},[t("div",xt,[t("h4",gt,d(n.nama_lengkap),1),t("p",ht,d(n.user.username),1),t("p",vt,d(j(n.created_at)),1)]),t("div",yt,[s(l(x),{class:v(g(n.status_peserta))},{default:a(()=>[r(d(h(n.status_peserta)),1)]),_:2},1032,["class"]),t("div",bt,[s(l(x),{variant:"outline",class:"text-xs"},{default:a(()=>[r(d(P(n.registration_type)),1)]),_:2},1024)])])]))),128))]))]),_:1})]),_:1}),s(l(c),null,{default:a(()=>[s(l(k),null,{default:a(()=>[t("div",wt,[s(l(D),null,{default:a(()=>e[19]||(e[19]=[r("Pendaftaran Terbaru")])),_:1,__:[19]}),s(f,{"as-child":"",variant:"outline",size:"sm"},{default:a(()=>[s(p,{href:i.route("admin.pendaftaran.index")},{default:a(()=>e[20]||(e[20]=[r("Lihat Semua")])),_:1,__:[20]},8,["href"])]),_:1})]),s(l(w),null,{default:a(()=>e[21]||(e[21]=[r("Pendaftaran lomba terbaru")])),_:1,__:[21]})]),_:1}),s(l(m),null,{default:a(()=>[i.recentPendaftaran.length===0?(u(),_("div",kt,[s(o,{name:"clipboard-list",class:"h-12 w-12 text-gray-400 mx-auto mb-4"}),e[22]||(e[22]=t("p",{class:"text-gray-600"},"Belum ada pendaftaran lomba.",-1))])):(u(),_("div",Dt,[(u(!0),_(y,null,b(i.recentPendaftaran,n=>(u(),_("div",{key:n.id_pendaftaran,class:"flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50 transition-colors"},[t("div",Pt,[t("h4",jt,d(n.peserta.nama_lengkap),1),t("p",$t,d(n.golongan.nama_golongan),1),t("p",Tt,d(n.nomor_pendaftaran),1)]),t("div",Lt,[s(l(x),{class:v(g(n.status_pendaftaran))},{default:a(()=>[r(d(h(n.status_pendaftaran)),1)]),_:2},1032,["class"]),n.pembayaran?(u(),_("div",St,[s(l(x),{class:v([g(n.pembayaran.status_pembayaran),"text-xs"]),variant:"outline"},{default:a(()=>[r(d(h(n.pembayaran.status_pembayaran)),1)]),_:2},1032,["class"])])):B("",!0)])]))),128))]))]),_:1})]),_:1})])])]),_:1}))}});export{Ht as default};
