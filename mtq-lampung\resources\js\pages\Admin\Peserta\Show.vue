<template>
  <AppLayout :breadcrumbs="breadcrumbItems">
    <div class="min-h-screen bg-gray-50">
      <!-- Header Section -->
      <div class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div class="flex items-center space-x-4">
              <Button
                as-child
                variant="ghost"
                size="sm"
                class="hover:bg-gray-100 transition-colors text-muted-foreground bg-gray-100 cursor-pointer hover:text-foreground"
              >
                <TextLink :href="route('admin.peserta.index')">
                  <Icon name="arrowLeft" class="w-4 h-4 mr-2" />
                  Kembali
                </TextLink>
              </Button>
              <div>
                <Heading title="Detail Peserta" :subtitle="peserta.nama_lengkap" :description="'NIK: ' + peserta.nik + ' • Terdaftar ' + formatDate(peserta.created_at)" />
              </div>
            </div>
            <div class="flex space-x-2">
              <Button
                as-child
                variant="outline"
                class="hover:bg-gray-50 transition-colors"
              >
                <TextLink :href="route('admin.peserta.edit', peserta.id_peserta)">
                  <Icon name="edit" class="w-4 h-4 mr-2" />
                  Edit
                </TextLink>
              </Button>
              <Button
                @click="confirmDelete"
                variant="destructive"
                class="hover:bg-red-600 transition-colors"
              >
                <Icon name="trash-2" class="w-4 h-4 mr-2" />
                Hapus
              </Button>
            </div>
          </div>
        </div>
      </div>

      <!-- Main Content -->
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Status Badge -->
        <div class="flex justify-center mb-8">
          <div class="flex items-center space-x-2">
            <span :class="getStatusClass(peserta.status_peserta)" class="inline-flex items-center px-4 py-2 text-sm font-semibold rounded-full">
              <div class="w-2 h-2 rounded-full bg-current mr-2 opacity-75"></div>
              {{ getStatusText(peserta.status_peserta) }}
            </span>
            <span class="text-sm text-gray-500">
              Status terakhir diperbarui
            </span>
          </div>
        </div>

        <div class="grid grid-cols-1 xl:grid-cols-4 gap-6">
          <!-- Main Information (Left Column) -->
          <div class="xl:col-span-3 space-y-6">
            <!-- Personal Information -->
            <Card class="hover:shadow-md transition-shadow">
              <CardHeader class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-t-lg">
                <CardTitle class="flex items-center text-blue-900">
                  <Icon name="user" class="w-5 h-5 mr-2" />
                  Informasi Pribadi
                </CardTitle>
              </CardHeader>
              <CardContent class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <div class="space-y-1">
                    <Label class="text-sm font-medium text-gray-500">NIK</Label>
                    <p class="text-sm text-gray-900 font-mono bg-gray-50 px-2 py-1 rounded">{{ peserta.nik }}</p>
                  </div>
                  <div class="space-y-1">
                    <Label class="text-sm font-medium text-gray-500">Nama Lengkap</Label>
                    <p class="text-sm text-gray-900 font-semibold">{{ peserta.nama_lengkap }}</p>
                  </div>
                  <div class="space-y-1">
                    <Label class="text-sm font-medium text-gray-500">Jenis Kelamin</Label>
                    <p class="text-sm text-gray-900">
                      <span class="inline-flex items-center">
                        <Icon :name="peserta.jenis_kelamin === 'L' ? 'male' : 'female'" class="w-4 h-4 mr-1" />
                        {{ peserta.jenis_kelamin === 'L' ? 'Laki-laki' : 'Perempuan' }}
                      </span>
                    </p>
                  </div>
                  <div class="space-y-1">
                    <Label class="text-sm font-medium text-gray-500">Tempat Lahir</Label>
                    <p class="text-sm text-gray-900">{{ peserta.tempat_lahir }}</p>
                  </div>
                  <div class="space-y-1">
                    <Label class="text-sm font-medium text-gray-500">Tanggal Lahir</Label>
                    <p class="text-sm text-gray-900">{{ formatDate(peserta.tanggal_lahir) }}</p>
                  </div>
                  <div class="space-y-1">
                    <Label class="text-sm font-medium text-gray-500">Umur</Label>
                    <p class="text-sm text-gray-900">
                      <span class="inline-flex items-center px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs font-medium">
                        {{ calculateAge(peserta.tanggal_lahir) }} tahun
                      </span>
                    </p>
                  </div>
                </div>
                <div class="mt-6 pt-6 border-t">
                  <Label class="text-sm font-medium text-gray-500">Alamat</Label>
                  <p class="mt-1 text-sm text-gray-900 leading-relaxed">{{ peserta.alamat }}</p>
                </div>
              </CardContent>
            </Card>

            <!-- Contact Information -->
            <Card class="hover:shadow-md transition-shadow">
              <CardHeader class="bg-gradient-to-r from-green-50 to-emerald-50 rounded-t-lg">
                <CardTitle class="flex items-center text-green-900">
                  <Icon name="phone" class="w-5 h-5 mr-2" />
                  Informasi Kontak
                </CardTitle>
              </CardHeader>
              <CardContent class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <div class="space-y-1">
                    <Label class="text-sm font-medium text-gray-500">Wilayah</Label>
                    <p class="text-sm text-gray-900 flex items-center">
                      <Icon name="map-pin" class="w-4 h-4 mr-1 text-gray-400" />
                      {{ peserta.wilayah?.nama_wilayah || '-' }}
                    </p>
                  </div>
                  <div class="space-y-1">
                    <Label class="text-sm font-medium text-gray-500">No. Telepon</Label>
                    <p class="text-sm text-gray-900">
                      <span v-if="peserta.no_telepon" class="inline-flex items-center">
                        <Icon name="phone" class="w-4 h-4 mr-1 text-gray-400" />
                        {{ peserta.no_telepon }}
                      </span>
                      <span v-else class="text-gray-400">-</span>
                    </p>
                  </div>
                  <div class="space-y-1">
                    <Label class="text-sm font-medium text-gray-500">Email</Label>
                    <p class="text-sm text-gray-900">
                      <span v-if="peserta.email" class="inline-flex items-center">
                        <Icon name="mail" class="w-4 h-4 mr-1 text-gray-400" />
                        {{ peserta.email }}
                      </span>
                      <span v-else class="text-gray-400">-</span>
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <!-- Family Information -->
            <Card class="hover:shadow-md transition-shadow">
              <CardHeader class="bg-gradient-to-r from-purple-50 to-pink-50 rounded-t-lg">
                <CardTitle class="flex items-center text-purple-900">
                  <Icon name="users" class="w-5 h-5 mr-2" />
                  Informasi Keluarga
                </CardTitle>
              </CardHeader>
              <CardContent class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div class="space-y-1">
                    <Label class="text-sm font-medium text-gray-500">Nama Ayah</Label>
                    <p class="text-sm text-gray-900">{{ peserta.nama_ayah || '-' }}</p>
                  </div>
                  <div class="space-y-1">
                    <Label class="text-sm font-medium text-gray-500">Nama Ibu</Label>
                    <p class="text-sm text-gray-900">{{ peserta.nama_ibu || '-' }}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <!-- Additional Information -->
            <Card class="hover:shadow-md transition-shadow">
              <CardHeader class="bg-gradient-to-r from-orange-50 to-yellow-50 rounded-t-lg">
                <CardTitle class="flex items-center text-orange-900">
                  <Icon name="briefcase" class="w-5 h-5 mr-2" />
                  Informasi Tambahan
                </CardTitle>
              </CardHeader>
              <CardContent class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div class="space-y-1">
                    <Label class="text-sm font-medium text-gray-500">Pekerjaan</Label>
                    <p class="text-sm text-gray-900">{{ peserta.pekerjaan || '-' }}</p>
                  </div>
                  <div class="space-y-1">
                    <Label class="text-sm font-medium text-gray-500">Instansi Asal</Label>
                    <p class="text-sm text-gray-900">{{ peserta.instansi_asal || '-' }}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <!-- Sidebar (Right Column) -->
          <div class="xl:col-span-1 space-y-6">
            <!-- Quick Stats -->
            <Card class="hover:shadow-md transition-shadow">
              <CardHeader class="text-center">
                <CardTitle class="text-lg">Ringkasan</CardTitle>
              </CardHeader>
              <CardContent class="p-6">
                <div class="space-y-4">
                  <div class="text-center">
                    <div class="text-2xl font-bold text-blue-600">{{ peserta.pendaftaran?.length || 0 }}</div>
                    <div class="text-sm text-gray-500">Lomba Diikuti</div>
                  </div>
                  <div class="text-center">
                    <div class="text-lg font-semibold text-green-600">{{ calculateAge(peserta.tanggal_lahir) }}</div>
                    <div class="text-sm text-gray-500">Tahun</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <!-- Account Information -->
            <Card class="hover:shadow-md transition-shadow">
              <CardHeader class="bg-gradient-to-r from-indigo-50 to-blue-50 rounded-t-lg">
                <CardTitle class="flex items-center text-indigo-900">
                  <Icon name="user-check" class="w-5 h-5 mr-2" />
                  Informasi Akun
                </CardTitle>
              </CardHeader>
              <CardContent class="p-6 space-y-4">
                <div class="space-y-1">
                  <Label class="text-sm font-medium text-gray-500">Username</Label>
                  <p class="text-sm text-gray-900 font-mono bg-gray-50 px-2 py-1 rounded">{{ peserta.user?.username }}</p>
                </div>
                <div class="space-y-1">
                  <Label class="text-sm font-medium text-gray-500">Email Akun</Label>
                  <p class="text-sm text-gray-900 break-all">{{ peserta.user?.email }}</p>
                </div>
                <div class="space-y-1">
                  <Label class="text-sm font-medium text-gray-500">Role</Label>
                  <span class="inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                    <Icon name="user" class="w-3 h-3 mr-1" />
                    Peserta
                  </span>
                </div>
                <div class="space-y-1">
                  <Label class="text-sm font-medium text-gray-500">Status Akun</Label>
                  <span class="inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                    <div class="w-2 h-2 rounded-full bg-green-500 mr-1"></div>
                    Aktif
                  </span>
                </div>
              </CardContent>
            </Card>

            <!-- Registration Information -->
            <Card class="hover:shadow-md transition-shadow">
              <CardHeader class="bg-gradient-to-r from-teal-50 to-cyan-50 rounded-t-lg">
                <CardTitle class="flex items-center text-teal-900">
                  <Icon name="clipboard-check" class="w-5 h-5 mr-2" />
                  Informasi Pendaftaran
                </CardTitle>
              </CardHeader>
              <CardContent class="p-6 space-y-4">
                <div class="space-y-1">
                  <Label class="text-sm font-medium text-gray-500">Tipe Pendaftaran</Label>
                  <span class="inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full"
                        :class="peserta.registration_type === 'mandiri' ? 'bg-blue-100 text-blue-800' : 'bg-purple-100 text-purple-800'">
                    <Icon :name="peserta.registration_type === 'mandiri' ? 'user' : 'users'" class="w-3 h-3 mr-1" />
                    {{ peserta.registration_type === 'mandiri' ? 'Mandiri' : 'Admin Daerah' }}
                  </span>
                </div>
                <div class="space-y-1">
                  <Label class="text-sm font-medium text-gray-500">Didaftarkan Oleh</Label>
                  <p class="text-sm text-gray-900">
                    {{ peserta.registered_by ? peserta.registered_by.nama_lengkap : 'Mandiri' }}
                  </p>
                </div>
                <div class="space-y-1">
                  <Label class="text-sm font-medium text-gray-500">Tanggal Daftar</Label>
                  <p class="text-sm text-gray-900">{{ formatDateTime(peserta.created_at) }}</p>
                </div>
              </CardContent>
            </Card>

            <!-- Competition Registrations -->
            <Card v-if="peserta.pendaftaran && peserta.pendaftaran.length > 0" class="hover:shadow-md transition-shadow">
              <CardHeader class="bg-gradient-to-r from-red-50 to-pink-50 rounded-t-lg">
                <CardTitle class="flex items-center text-red-900">
                  <Icon name="trophy" class="w-5 h-5 mr-2" />
                  Pendaftaran Lomba
                  <span class="ml-2 inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
                    {{ peserta.pendaftaran.length }}
                  </span>
                </CardTitle>
              </CardHeader>
              <CardContent class="p-6 space-y-3">
                <div v-for="pendaftaran in peserta.pendaftaran" :key="pendaftaran.id_pendaftaran"
                     class="p-4 border rounded-lg hover:bg-gray-50 transition-colors">
                  <div class="flex justify-between items-start">
                    <div class="flex-1">
                      <h4 class="font-semibold text-sm text-gray-900">{{ pendaftaran.golongan.cabang_lomba.nama_cabang }}</h4>
                      <p class="text-xs text-gray-600 mt-1">{{ pendaftaran.golongan.nama_golongan }}</p>
                      <p class="text-xs text-gray-500 font-mono mt-1">{{ pendaftaran.nomor_pendaftaran }}</p>
                    </div>
                    <span :class="getStatusClass(pendaftaran.status_pendaftaran)" class="inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full ml-2">
                      <div class="w-2 h-2 rounded-full bg-current mr-1 opacity-75"></div>
                      {{ getStatusText(pendaftaran.status_pendaftaran) }}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <!-- Empty State for Competitions -->
            <Card v-else class="hover:shadow-md transition-shadow">
              <CardHeader class="bg-gradient-to-r from-gray-50 to-gray-100 rounded-t-lg">
                <CardTitle class="flex items-center text-gray-700">
                  <Icon name="trophy" class="w-5 h-5 mr-2" />
                  Pendaftaran Lomba
                </CardTitle>
              </CardHeader>
              <CardContent class="p-6 text-center">
                <Icon name="trophy" class="w-12 h-12 mx-auto text-gray-300 mb-2" />
                <p class="text-sm text-gray-500">Belum ada pendaftaran lomba</p>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import { router } from '@inertiajs/vue3'
import AppLayout from '@/layouts/AppLayout.vue'
import Heading from '@/components/Heading.vue'
import Button from '@/components/ui/button/Button.vue'
import Card from '@/components/ui/card/Card.vue'
import CardHeader from '@/components/ui/card/CardHeader.vue'
import CardTitle from '@/components/ui/card/CardTitle.vue'
import CardContent from '@/components/ui/card/CardContent.vue'
import Label from '@/components/ui/label/Label.vue'
import Icon from '@/components/Icon.vue'
import TextLink from '@/components/TextLink.vue'

interface Peserta {
  id_peserta: number
  nik: string
  nama_lengkap: string
  tempat_lahir: string
  tanggal_lahir: string
  jenis_kelamin: string
  alamat: string
  no_telepon: string | null
  email: string | null
  nama_ayah: string | null
  nama_ibu: string | null
  pekerjaan: string | null
  instansi_asal: string | null
  registration_type: string
  status_peserta: string
  created_at: string
  user: {
    username: string
    email: string
  }
  wilayah: {
    nama_wilayah: string
  }
  registered_by?: {
    nama_lengkap: string
  }
  pendaftaran?: Array<{
    id_pendaftaran: number
    nomor_pendaftaran: string
    status_pendaftaran: string
    golongan: {
      nama_golongan: string
      cabang_lomba: {
        nama_cabang: string
      }
    }
  }>
}

const props = defineProps<{
  peserta: Peserta
}>()

function getStatusClass(status: string): string {
  const classes = {
    draft: 'bg-gray-100 text-gray-800',
    submitted: 'bg-blue-100 text-blue-800',
    payment_pending: 'bg-yellow-100 text-yellow-800',
    paid: 'bg-green-100 text-green-800',
    verified: 'bg-indigo-100 text-indigo-800',
    approved: 'bg-emerald-100 text-emerald-800',
    rejected: 'bg-red-100 text-red-800'
  }
  return classes[status as keyof typeof classes] || 'bg-gray-100 text-gray-800'
}

function getStatusText(status: string): string {
  const texts = {
    draft: 'Draft',
    submitted: 'Disubmit',
    payment_pending: 'Menunggu Pembayaran',
    paid: 'Dibayar',
    verified: 'Diverifikasi',
    approved: 'Disetujui',
    rejected: 'Ditolak'
  }
  return texts[status as keyof typeof texts] || status
}

function formatDate(dateString: string): string {
  return new Date(dateString).toLocaleDateString('id-ID', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

function formatDateTime(dateString: string): string {
  return new Date(dateString).toLocaleDateString('id-ID', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

function calculateAge(birthDate: string): number {
  const today = new Date()
  const birth = new Date(birthDate)
  let age = today.getFullYear() - birth.getFullYear()
  const monthDiff = today.getMonth() - birth.getMonth()

  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
    age--
  }

  return age
}

function confirmDelete() {
  if (confirm(`Apakah Anda yakin ingin menghapus peserta ${props.peserta.nama_lengkap}?`)) {
    router.delete(route('admin.peserta.destroy', props.peserta.id_peserta))
  }
}

const breadcrumbItems: BreadcrumbItem[] = [
  {
    title: 'Manajemen Peserta',
    href: '/admin/peserta',
  },
  {
    title: 'Detail Peserta',
    href: '/admin/peserta/' + props.peserta.id_peserta,
  },
]
</script>
