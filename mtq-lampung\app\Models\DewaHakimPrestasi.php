<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class DewaHakimPrestasi extends Model
{
    protected $table = 'dewan_hakim_prestasi';
    protected $primaryKey = 'id_prestasi';

    public $timestamps = false;

    protected $fillable = [
        'id_dewan_hakim',
        'juara_ke',
        'cabang_golongan',
        'nama_kegiatan',
        'tahun',
        'tingkat'
    ];

    protected $casts = [
        'juara_ke' => 'integer',
        'tahun' => 'integer',
        'tingkat' => 'string'
    ];

    // Relationships
    public function dewaHakim(): BelongsTo
    {
        return $this->belongsTo(DewaHakim::class, 'id_dewan_hakim', 'id_dewan_hakim');
    }

    // Scopes
    public function scopeByTingkat($query, $tingkat)
    {
        return $query->where('tingkat', $tingkat);
    }

    public function scopeByTahun($query, $tahun)
    {
        return $query->where('tahun', $tahun);
    }

    public function scopeByJuara($query, $juara)
    {
        return $query->where('juara_ke', $juara);
    }

    // Helper methods
    public function isKabupaten(): bool
    {
        return $this->tingkat === 'kabupaten';
    }

    public function isProvinsi(): bool
    {
        return $this->tingkat === 'provinsi';
    }

    public function isNasional(): bool
    {
        return $this->tingkat === 'nasional';
    }

    public function isInternasional(): bool
    {
        return $this->tingkat === 'internasional';
    }

    public function getTingkatLabelAttribute(): string
    {
        return match($this->tingkat) {
            'kabupaten' => 'Kabupaten/Kota',
            'provinsi' => 'Provinsi',
            'nasional' => 'Nasional',
            'internasional' => 'Internasional',
            default => ucfirst($this->tingkat)
        };
    }

    public function getJuaraLabelAttribute(): string
    {
        return match($this->juara_ke) {
            1 => 'Juara 1',
            2 => 'Juara 2',
            3 => 'Juara 3',
            default => "Juara {$this->juara_ke}"
        };
    }

    public function getFormattedPrestasiAttribute(): string
    {
        return "{$this->juara_label} {$this->cabang_golongan} - {$this->nama_kegiatan} ({$this->tahun})";
    }
}
