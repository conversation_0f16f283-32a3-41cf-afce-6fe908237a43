import{d as V,l as P,c as b,o as u,w as s,b as e,a as t,e as d,i as h,t as n,u as l,g as I,h as g,j as B,q as F,F as v,m as y,n as G,W as N}from"./app-B_pmlBSQ.js";import{_ as S}from"./AppLayout.vue_vue_type_script_setup_true_lang-BRf8lUiM.js";import{_ as T}from"./Heading.vue_vue_type_script_setup_true_lang-is4VBSCS.js";import{_ as k}from"./AppLogoIcon.vue_vue_type_script_setup_true_lang-wzz4PWna.js";import{_ as M}from"./Input.vue_vue_type_script_setup_true_lang-DB1-RsTn.js";import{_ as w}from"./Label.vue_vue_type_script_setup_true_lang-rj_fhE7R.js";import{_ as m,a as _}from"./CardContent.vue_vue_type_script_setup_true_lang-ClvLNBXC.js";import{_ as U}from"./CardDescription.vue_vue_type_script_setup_true_lang-DboywBIK.js";import{_ as z,a as K}from"./CardTitle.vue_vue_type_script_setup_true_lang-5HO6oSoB.js";import{_ as C}from"./index-CMGr3-bt.js";import{_ as c}from"./Icon.vue_vue_type_script_setup_true_lang-BuYzjckr.js";import"./useForwardExpose-CO14IhkA.js";import"./RovingFocusGroup-lsWyU4xZ.js";import"./check-DtmHjdxf.js";import"./loader-circle-6d8QrWFr.js";import"./sun-CHIV9RJ8.js";const Q={class:"flex items-center justify-between"},W={class:"space-y-6"},q={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},A={class:"flex items-center space-x-2"},E={class:"text-2xl font-bold"},J={class:"flex items-center space-x-2"},R={class:"text-2xl font-bold"},H={class:"flex items-center space-x-2"},O={class:"text-2xl font-bold"},X={class:"flex items-center space-x-2"},Y={class:"text-2xl font-bold"},Z={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},ee={class:"md:col-span-2"},te={class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},ae={class:"space-y-3"},se={class:"flex items-center justify-between mb-2"},le={class:"font-medium"},ne={class:"text-sm text-gray-600 space-y-1"},oe={class:"font-medium text-green-600"},ie={class:"mt-3"},re={key:0,class:"text-center py-12"},Le=V({__name:"Index",props:{cabangLomba:{},stats:{},filters:{},auth:{}},setup(L){const p=L,f=P({search:p.filters.search||"",jenis_kelamin:p.filters.jenis_kelamin||"all"});function x(){N.get(route("competition.index"),f.value,{preserveState:!0,replace:!0})}function j(o){return new Intl.NumberFormat("id-ID",{style:"currency",currency:"IDR",minimumFractionDigits:0}).format(o)}function $(o){return o==="L"?"bg-blue-100 text-blue-800":"bg-pink-100 text-pink-800"}const D=[{title:"Cabang Lomba",href:route("competition.index")}];return(o,a)=>(u(),b(S,{breadcrumbs:D},{default:s(()=>[e("div",Q,[t(T,{title:"Cabang Lomba MTQ"}),d(" "+n(p.auth.user)+" ",1),p.auth.user?(u(),b(k,{key:0,as:"link",href:o.route("peserta.dashboard"),variant:"outline"},{default:s(()=>[t(c,{name:"user",class:"w-4 h-4 mr-2"}),a[2]||(a[2]=d(" Dashboard Saya "))]),_:1,__:[2]},8,["href"])):h("",!0)]),t(l(I),{title:"Cabang Lomba MTQ"}),e("div",W,[e("div",q,[t(l(m),null,{default:s(()=>[t(l(_),{class:"p-6"},{default:s(()=>[e("div",A,[t(c,{name:"trophy",class:"h-8 w-8 text-yellow-600"}),e("div",null,[e("p",E,n(o.stats.total_cabang),1),a[3]||(a[3]=e("p",{class:"text-sm text-gray-600"},"Cabang Lomba",-1))])])]),_:1})]),_:1}),t(l(m),null,{default:s(()=>[t(l(_),{class:"p-6"},{default:s(()=>[e("div",J,[t(c,{name:"users",class:"h-8 w-8 text-blue-600"}),e("div",null,[e("p",R,n(o.stats.total_golongan),1),a[4]||(a[4]=e("p",{class:"text-sm text-gray-600"},"Total Golongan",-1))])])]),_:1})]),_:1}),t(l(m),null,{default:s(()=>[t(l(_),{class:"p-6"},{default:s(()=>[e("div",H,[t(c,{name:"user",class:"h-8 w-8 text-blue-600"}),e("div",null,[e("p",O,n(o.stats.total_golongan_laki),1),a[5]||(a[5]=e("p",{class:"text-sm text-gray-600"},"Golongan Putra",-1))])])]),_:1})]),_:1}),t(l(m),null,{default:s(()=>[t(l(_),{class:"p-6"},{default:s(()=>[e("div",X,[t(c,{name:"user",class:"h-8 w-8 text-pink-600"}),e("div",null,[e("p",Y,n(o.stats.total_golongan_perempuan),1),a[6]||(a[6]=e("p",{class:"text-sm text-gray-600"},"Golongan Putri",-1))])])]),_:1})]),_:1})]),t(l(m),null,{default:s(()=>[t(l(_),{class:"p-6"},{default:s(()=>[e("div",Z,[e("div",ee,[t(l(w),{for:"search"},{default:s(()=>a[7]||(a[7]=[d("Cari Cabang Lomba")])),_:1,__:[7]}),t(l(M),{id:"search",modelValue:f.value.search,"onUpdate:modelValue":a[0]||(a[0]=i=>f.value.search=i),placeholder:"Cari berdasarkan nama cabang atau golongan...",onInput:x},null,8,["modelValue"])]),e("div",null,[t(l(w),{for:"jenis_kelamin"},{default:s(()=>a[8]||(a[8]=[d("Filter Jenis Kelamin")])),_:1,__:[8]}),B(e("select",{id:"jenis_kelamin","onUpdate:modelValue":a[1]||(a[1]=i=>f.value.jenis_kelamin=i),onChange:x,class:"flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50"},a[9]||(a[9]=[e("option",{value:"all"},"Semua",-1),e("option",{value:"L"},"Putra",-1),e("option",{value:"P"},"Putri",-1)]),544),[[F,f.value.jenis_kelamin]])])])]),_:1})]),_:1}),e("div",te,[(u(!0),g(v,null,y(o.cabangLomba,i=>(u(),b(l(m),{key:i.id_cabang,class:"hover:shadow-lg transition-shadow"},{default:s(()=>[t(l(z),null,{default:s(()=>[t(l(K),{class:"flex items-center justify-between"},{default:s(()=>[d(n(i.nama_cabang)+" ",1),t(l(C),{variant:"secondary"},{default:s(()=>[d(n(i.golongan.length)+" Golongan",1)]),_:2},1024)]),_:2},1024),t(l(U),null,{default:s(()=>[d(n(i.deskripsi),1)]),_:2},1024)]),_:2},1024),t(l(_),null,{default:s(()=>[e("div",ae,[(u(!0),g(v,null,y(i.golongan,r=>(u(),g("div",{key:r.id_golongan,class:"p-3 border rounded-lg hover:bg-gray-50 transition-colors"},[e("div",se,[e("h4",le,n(r.nama_golongan),1),t(l(C),{class:G($(r.jenis_kelamin))},{default:s(()=>[d(n(r.jenis_kelamin==="L"?"Putra":"Putri"),1)]),_:2},1032,["class"])]),e("div",ne,[e("p",null,"Usia: "+n(r.batas_umur_min)+" - "+n(r.batas_umur_max)+" tahun",1),e("p",null,"Kuota: "+n(r.kuota_max)+" peserta",1),e("p",oe,n(j(r.biaya_pendaftaran)),1)]),e("div",ie,[t(k,{as:"link",href:o.route("competition.golongan",r.id_golongan),size:"sm",class:"w-full"},{default:s(()=>a[10]||(a[10]=[d(" Lihat Detail & Daftar ")])),_:2,__:[10]},1032,["href"])])]))),128))])]),_:2},1024)]),_:2},1024))),128))]),o.cabangLomba.length===0?(u(),g("div",re,[t(c,{name:"search",class:"h-12 w-12 text-gray-400 mx-auto mb-4"}),a[11]||(a[11]=e("h3",{class:"text-lg font-medium text-gray-900 mb-2"},"Tidak ada cabang lomba ditemukan",-1)),a[12]||(a[12]=e("p",{class:"text-gray-600"},"Coba ubah kata kunci pencarian atau filter yang Anda gunakan.",-1))])):h("",!0)])]),_:1}))}});export{Le as default};
