import{d as S,x as B,s as C,c as w,o as i,w as t,a,b as l,u as e,g as P,e as d,f as T,h as n,i as m,n as _,t as u,F as $,m as U}from"./app-B_pmlBSQ.js";import{_ as h}from"./AppLayout.vue_vue_type_script_setup_true_lang-BRf8lUiM.js";import{_ as A}from"./Heading.vue_vue_type_script_setup_true_lang-is4VBSCS.js";import{_ as c}from"./AppLogoIcon.vue_vue_type_script_setup_true_lang-wzz4PWna.js";import{_ as D,a as I}from"./CardContent.vue_vue_type_script_setup_true_lang-ClvLNBXC.js";import{_ as L}from"./CardDescription.vue_vue_type_script_setup_true_lang-DboywBIK.js";import{_ as R,a as j}from"./CardTitle.vue_vue_type_script_setup_true_lang-5HO6oSoB.js";import{_ as y}from"./Input.vue_vue_type_script_setup_true_lang-DB1-RsTn.js";import{_ as p}from"./Label.vue_vue_type_script_setup_true_lang-rj_fhE7R.js";import{_ as g,a as b,b as V,c as x,d as k}from"./SelectValue.vue_vue_type_script_setup_true_lang-IzlIronZ.js";import{_ as E}from"./Icon.vue_vue_type_script_setup_true_lang-BuYzjckr.js";import"./useForwardExpose-CO14IhkA.js";import"./RovingFocusGroup-lsWyU4xZ.js";import"./check-DtmHjdxf.js";import"./useFormControl-mZRLifGx.js";import"./loader-circle-6d8QrWFr.js";import"./sun-CHIV9RJ8.js";const F={class:"max-w-2xl mx-auto"},M={class:"space-y-4"},W={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},z={key:0,class:"text-sm text-red-600 mt-1"},K={key:0,class:"text-sm text-red-600 mt-1"},G={key:0,class:"text-sm text-red-600 mt-1"},H={key:0,class:"text-sm text-red-600 mt-1"},J={class:"space-y-4"},O={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},Q={key:0,class:"text-sm text-red-600 mt-1"},X={key:0,class:"text-sm text-red-600 mt-1"},Y={class:"space-y-4"},Z={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},ee={key:0,class:"text-sm text-red-600 mt-1"},se={key:0,class:"text-sm text-red-600 mt-1"},re={key:0},ae={key:0,class:"text-sm text-red-600 mt-1"},te={class:"flex justify-end space-x-4 pt-6 border-t"},$e=S({__name:"Create",props:{wilayah:{},roles:{}},setup(oe){const q=[{title:"Dashboard",href:"/admin/dashboard"},{title:"Manajemen User",href:"/admin/users"},{title:"Tambah User",href:"/admin/users/create"}],r=B({username:"",email:"",password:"",password_confirmation:"",role:"",nama_lengkap:"",no_telepon:"",id_wilayah:"",status:"aktif"});C(()=>r.role,f=>{f==="admin"&&(r.id_wilayah="")});const N=()=>{r.post(route("admin.users.store"),{onSuccess:()=>{}})};return(f,s)=>(i(),w(h,{breadcrumbs:q},{default:t(()=>[a(e(P),{title:"Tambah User"}),a(A,{title:"Tambah User"}),l("div",F,[a(e(D),null,{default:t(()=>[a(e(R),null,{default:t(()=>[a(e(j),null,{default:t(()=>s[10]||(s[10]=[d("Informasi User Baru")])),_:1,__:[10]}),a(e(L),null,{default:t(()=>s[11]||(s[11]=[d(" Lengkapi form di bawah untuk menambahkan user baru ke sistem ")])),_:1,__:[11]})]),_:1}),a(e(I),null,{default:t(()=>[l("form",{onSubmit:T(N,["prevent"]),class:"space-y-6"},[l("div",M,[s[16]||(s[16]=l("h3",{class:"text-lg font-medium"},"Informasi Dasar",-1)),l("div",W,[l("div",null,[a(e(p),{for:"username"},{default:t(()=>s[12]||(s[12]=[d("Username *")])),_:1,__:[12]}),a(e(y),{id:"username",modelValue:e(r).username,"onUpdate:modelValue":s[0]||(s[0]=o=>e(r).username=o),type:"text",required:"",class:_({"border-red-500":e(r).errors.username})},null,8,["modelValue","class"]),e(r).errors.username?(i(),n("p",z,u(e(r).errors.username),1)):m("",!0)]),l("div",null,[a(e(p),{for:"email"},{default:t(()=>s[13]||(s[13]=[d("Email *")])),_:1,__:[13]}),a(e(y),{id:"email",modelValue:e(r).email,"onUpdate:modelValue":s[1]||(s[1]=o=>e(r).email=o),type:"email",required:"",class:_({"border-red-500":e(r).errors.email})},null,8,["modelValue","class"]),e(r).errors.email?(i(),n("p",K,u(e(r).errors.email),1)):m("",!0)])]),l("div",null,[a(e(p),{for:"nama_lengkap"},{default:t(()=>s[14]||(s[14]=[d("Nama Lengkap *")])),_:1,__:[14]}),a(e(y),{id:"nama_lengkap",modelValue:e(r).nama_lengkap,"onUpdate:modelValue":s[2]||(s[2]=o=>e(r).nama_lengkap=o),type:"text",required:"",class:_({"border-red-500":e(r).errors.nama_lengkap})},null,8,["modelValue","class"]),e(r).errors.nama_lengkap?(i(),n("p",G,u(e(r).errors.nama_lengkap),1)):m("",!0)]),l("div",null,[a(e(p),{for:"no_telepon"},{default:t(()=>s[15]||(s[15]=[d("No. Telepon")])),_:1,__:[15]}),a(e(y),{id:"no_telepon",modelValue:e(r).no_telepon,"onUpdate:modelValue":s[3]||(s[3]=o=>e(r).no_telepon=o),type:"tel",class:_({"border-red-500":e(r).errors.no_telepon})},null,8,["modelValue","class"]),e(r).errors.no_telepon?(i(),n("p",H,u(e(r).errors.no_telepon),1)):m("",!0)])]),l("div",J,[s[19]||(s[19]=l("h3",{class:"text-lg font-medium"},"Password",-1)),l("div",O,[l("div",null,[a(e(p),{for:"password"},{default:t(()=>s[17]||(s[17]=[d("Password *")])),_:1,__:[17]}),a(e(y),{id:"password",modelValue:e(r).password,"onUpdate:modelValue":s[4]||(s[4]=o=>e(r).password=o),type:"password",required:"",class:_({"border-red-500":e(r).errors.password})},null,8,["modelValue","class"]),e(r).errors.password?(i(),n("p",Q,u(e(r).errors.password),1)):m("",!0)]),l("div",null,[a(e(p),{for:"password_confirmation"},{default:t(()=>s[18]||(s[18]=[d("Konfirmasi Password *")])),_:1,__:[18]}),a(e(y),{id:"password_confirmation",modelValue:e(r).password_confirmation,"onUpdate:modelValue":s[5]||(s[5]=o=>e(r).password_confirmation=o),type:"password",required:"",class:_({"border-red-500":e(r).errors.password_confirmation})},null,8,["modelValue","class"]),e(r).errors.password_confirmation?(i(),n("p",X,u(e(r).errors.password_confirmation),1)):m("",!0)])])]),l("div",Y,[s[25]||(s[25]=l("h3",{class:"text-lg font-medium"},"Role dan Akses",-1)),l("div",Z,[l("div",null,[a(e(p),{for:"role"},{default:t(()=>s[20]||(s[20]=[d("Role *")])),_:1,__:[20]}),a(e(g),{modelValue:e(r).role,"onUpdate:modelValue":s[6]||(s[6]=o=>e(r).role=o),required:""},{default:t(()=>[a(e(b),{class:_({"border-red-500":e(r).errors.role})},{default:t(()=>[a(e(V),{placeholder:"Pilih Role"})]),_:1},8,["class"]),a(e(x),null,{default:t(()=>[(i(!0),n($,null,U(f.roles,(o,v)=>(i(),w(e(k),{key:v,value:v},{default:t(()=>[d(u(o),1)]),_:2},1032,["value"]))),128))]),_:1})]),_:1},8,["modelValue"]),e(r).errors.role?(i(),n("p",ee,u(e(r).errors.role),1)):m("",!0)]),l("div",null,[a(e(p),{for:"status"},{default:t(()=>s[21]||(s[21]=[d("Status *")])),_:1,__:[21]}),a(e(g),{modelValue:e(r).status,"onUpdate:modelValue":s[7]||(s[7]=o=>e(r).status=o),required:""},{default:t(()=>[a(e(b),{class:_({"border-red-500":e(r).errors.status})},{default:t(()=>[a(e(V),{placeholder:"Pilih Status"})]),_:1},8,["class"]),a(e(x),null,{default:t(()=>[a(e(k),{value:"aktif"},{default:t(()=>s[22]||(s[22]=[d("Aktif")])),_:1,__:[22]}),a(e(k),{value:"non_aktif"},{default:t(()=>s[23]||(s[23]=[d("Non Aktif")])),_:1,__:[23]})]),_:1})]),_:1},8,["modelValue"]),e(r).errors.status?(i(),n("p",se,u(e(r).errors.status),1)):m("",!0)])]),e(r).role==="admin_daerah"||e(r).role==="dewan_hakim"?(i(),n("div",re,[a(e(p),{for:"id_wilayah"},{default:t(()=>s[24]||(s[24]=[d("Wilayah *")])),_:1,__:[24]}),a(e(g),{modelValue:e(r).id_wilayah,"onUpdate:modelValue":s[8]||(s[8]=o=>e(r).id_wilayah=o),required:""},{default:t(()=>[a(e(b),{class:_({"border-red-500":e(r).errors.id_wilayah})},{default:t(()=>[a(e(V),{placeholder:"Pilih Wilayah"})]),_:1},8,["class"]),a(e(x),null,{default:t(()=>[(i(!0),n($,null,U(f.wilayah,o=>(i(),w(e(k),{key:o.id_wilayah,value:o.id_wilayah.toString()},{default:t(()=>[d(u(o.nama_wilayah),1)]),_:2},1032,["value"]))),128))]),_:1})]),_:1},8,["modelValue"]),e(r).errors.id_wilayah?(i(),n("p",ae,u(e(r).errors.id_wilayah),1)):m("",!0)])):m("",!0)]),l("div",te,[a(c,{type:"button",variant:"outline",onClick:s[9]||(s[9]=o=>f.$inertia.visit(f.route("admin.users.index")))},{default:t(()=>s[26]||(s[26]=[d(" Batal ")])),_:1,__:[26]}),a(c,{type:"submit",disabled:e(r).processing},{default:t(()=>[e(r).processing?(i(),w(E,{key:0,name:"loader-2",class:"w-4 h-4 mr-2 animate-spin"})):m("",!0),s[27]||(s[27]=d(" Simpan User "))]),_:1,__:[27]},8,["disabled"])])],32)]),_:1})]),_:1})])]),_:1}))}});export{$e as default};
