<?php

namespace Database\Factories;

use App\Models\Wilayah;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\User>
 */
class UserFactory extends Factory
{
    /**
     * The current password being used by the factory.
     */
    protected static ?string $password;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $roles = ['admin', 'admin_daerah', 'peserta', 'dewan_hakim'];
        $role = fake()->randomElement($roles);

        return [
            'username' => fake()->unique()->userName(),
            'email' => fake()->unique()->safeEmail(),
            'email_verified_at' => now(),
            'password' => static::$password ??= Hash::make('password'),
            'remember_token' => Str::random(10),
            'role' => $role,
            'id_wilayah' => $role === 'admin_daerah' ? Wilayah::inRandomOrder()->first()?->id_wilayah : null,
            'nama_lengkap' => fake()->name(),
            'no_telepon' => fake()->phoneNumber(),
            'status' => fake()->randomElement(['aktif', 'non_aktif']),
            'last_login' => fake()->optional(0.7)->dateTimeBetween('-1 month', 'now'),
            'created_by' => null, // Will be set by seeder if needed
        ];
    }

    /**
     * Indicate that the model's email address should be unverified.
     */
    public function unverified(): static
    {
        return $this->state(fn (array $attributes) => [
            'email_verified_at' => null,
        ]);
    }

    /**
     * Create a superadmin user.
     */
    public function superadmin(): static
    {
        return $this->state(fn (array $attributes) => [
            'role' => 'superadmin',
            'id_wilayah' => null,
            'status' => 'aktif',
        ]);
    }

    /**
     * Create an admin user.
     */
    public function admin(): static
    {
        return $this->state(fn (array $attributes) => [
            'role' => 'admin',
            'id_wilayah' => null,
            'status' => 'aktif',
        ]);
    }

    /**
     * Create an admin daerah user.
     */
    public function adminDaerah(): static
    {
        return $this->state(fn (array $attributes) => [
            'role' => 'admin_daerah',
            'id_wilayah' => Wilayah::inRandomOrder()->first()?->id_wilayah,
            'status' => 'aktif',
        ]);
    }

    /**
     * Create a peserta user.
     */
    public function peserta(): static
    {
        return $this->state(fn (array $attributes) => [
            'role' => 'peserta',
            'id_wilayah' => Wilayah::inRandomOrder()->first()?->id_wilayah,
            'status' => 'aktif',
        ]);
    }

    /**
     * Create a dewan hakim user.
     */
    public function dewaHakim(): static
    {
        return $this->state(fn (array $attributes) => [
            'role' => 'dewan_hakim',
            'id_wilayah' => null,
            'status' => 'aktif',
        ]);
    }

    /**
     * Create an active user.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'aktif',
        ]);
    }
}
