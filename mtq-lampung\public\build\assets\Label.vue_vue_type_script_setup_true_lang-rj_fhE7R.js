import{P as i,a as f}from"./AppLogoIcon.vue_vue_type_script_setup_true_lang-wzz4PWna.js";import{a as m}from"./useForwardExpose-CO14IhkA.js";import{d as l,c as n,o as d,w as p,D as u,E as c,u as r,k as _}from"./app-B_pmlBSQ.js";const b=l({__name:"Label",props:{for:{},asChild:{type:Boolean},as:{default:"label"}},setup(t){const s=t;return m(),(o,a)=>(d(),n(r(i),c(s,{onMousedown:a[0]||(a[0]=e=>{!e.defaultPrevented&&e.detail>1&&e.preventDefault()})}),{default:p(()=>[u(o.$slots,"default")]),_:3},16))}}),y=l({__name:"Label",props:{for:{},asChild:{type:Boolean},as:{type:[String,Object,Function]},class:{}},setup(t){const s=t,o=_(()=>{const{class:a,...e}=s;return e});return(a,e)=>(d(),n(r(b),c({"data-slot":"label"},o.value,{class:r(f)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",s.class)}),{default:p(()=>[u(a.$slots,"default")]),_:3},16,["class"]))}});export{y as _};
