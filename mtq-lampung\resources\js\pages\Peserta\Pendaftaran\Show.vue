<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue'
import { Head } from '@inertiajs/vue3'
import Heading from '@/components/Heading.vue'
import Button from '@/components/ui/button/Button.vue'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import Icon from '@/components/Icon.vue'

interface CabangLomba {
    nama_cabang: string
    deskripsi: string
}

interface Golongan {
    nama_golongan: string
    jenis_kelamin: string
    batas_umur_min: number
    batas_umur_max: number
    kuota_max: number
    biaya_pendaftaran: number
    cabang_lomba: CabangLomba
}

interface Pembayaran {
    id_pembayaran: number
    nomor_transaksi: string
    jumlah_bayar: number
    metode_pembayaran: string
    status_pembayaran: string
    tanggal_bayar: string | null
    bukti_pembayaran: string | null
    catatan: string | null
}

interface DokumenPeserta {
    id_dokumen: number
    jenis_dokumen: string
    nama_file: string
    status_verifikasi: string
    catatan_verifikasi: string | null
    verified_at: string | null
}

interface User {
    nama_lengkap: string
}

interface Pendaftaran {
    id_pendaftaran: number
    nomor_pendaftaran: string
    nomor_peserta: string
    status_pendaftaran: string
    tanggal_daftar: string
    verified_at: string | null
    approved_at: string | null
    catatan_verifikasi: string | null
    catatan_approval: string | null
    keterangan: string | null
    golongan: Golongan
    pembayaran: Pembayaran | null
    dokumen_peserta: DokumenPeserta[]
    verified_by: User | null
    approved_by: User | null
}

const props = defineProps<{
    pendaftaran: Pendaftaran
}>()

function getStatusColor(status: string): string {
    const colors = {
        draft: 'bg-gray-100 text-gray-800',
        submitted: 'bg-blue-100 text-blue-800',
        payment_pending: 'bg-yellow-100 text-yellow-800',
        paid: 'bg-green-100 text-green-800',
        verified: 'bg-indigo-100 text-indigo-800',
        approved: 'bg-green-100 text-green-800',
        rejected: 'bg-red-100 text-red-800'
    }
    return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800'
}

function getStatusText(status: string): string {
    const texts = {
        draft: 'Draft',
        submitted: 'Disubmit',
        payment_pending: 'Menunggu Pembayaran',
        paid: 'Sudah Dibayar',
        verified: 'Terverifikasi',
        approved: 'Disetujui',
        rejected: 'Ditolak'
    }
    return texts[status as keyof typeof texts] || status
}

function getPaymentStatusColor(status: string): string {
    const colors = {
        pending: 'bg-yellow-100 text-yellow-800',
        paid: 'bg-green-100 text-green-800',
        failed: 'bg-red-100 text-red-800',
        expired: 'bg-gray-100 text-gray-800'
    }
    return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800'
}

function getDocumentStatusColor(status: string): string {
    const colors = {
        pending: 'bg-yellow-100 text-yellow-800',
        approved: 'bg-green-100 text-green-800',
        rejected: 'bg-red-100 text-red-800'
    }
    return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800'
}

function formatCurrency(amount: number): string {
    return new Intl.NumberFormat('id-ID', {
        style: 'currency',
        currency: 'IDR',
        minimumFractionDigits: 0
    }).format(amount)
}

function formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString('id-ID', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    })
}

function getDocumentTypeText(type: string): string {
    const types = {
        foto: 'Foto',
        ktp: 'KTP',
        kartu_keluarga: 'Kartu Keluarga',
        surat_rekomendasi: 'Surat Rekomendasi',
        ijazah: 'Ijazah',
        sertifikat: 'Sertifikat',
        lainnya: 'Lainnya'
    }
    return types[type as keyof typeof types] || type
}

function canEdit(): boolean {
    return ['draft', 'payment_pending'].includes(props.pendaftaran.status_pendaftaran)
}

function needsPayment(): boolean {
    return !props.pendaftaran.pembayaran || props.pendaftaran.pembayaran.status_pembayaran === 'pending'
}

function needsDocuments(): boolean {
    const requiredDocs = ['foto', 'ktp', 'kartu_keluarga']
    return requiredDocs.some(type =>
        !props.pendaftaran.dokumen_peserta.some(doc =>
            doc.jenis_dokumen === type && doc.status_verifikasi === 'approved'
        )
    )
}
</script>

<template>
    <AppLayout>
        <template #header>
            <div class="flex items-center space-x-4">
                <Button
                    as="link"
                    :href="route('peserta.pendaftaran.index')"
                    variant="ghost"
                    size="sm"
                >
                    <Icon name="arrow-left" class="w-4 h-4 mr-2" />
                    Kembali
                </Button>
                <Heading>Detail Pendaftaran</Heading>
            </div>
        </template>

        <Head title="Detail Pendaftaran" />

        <div class="space-y-6">
            <!-- Registration Status -->
            <Card>
                <CardHeader>
                    <div class="flex items-center justify-between">
                        <CardTitle>{{ pendaftaran.golongan.nama_golongan }}</CardTitle>
                        <Badge :class="getStatusColor(pendaftaran.status_pendaftaran)">
                            {{ getStatusText(pendaftaran.status_pendaftaran) }}
                        </Badge>
                    </div>
                    <CardDescription>{{ pendaftaran.golongan.cabang_lomba.nama_cabang }}</CardDescription>
                </CardHeader>
                <CardContent>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <div>
                            <p class="text-sm font-medium text-gray-500">No. Pendaftaran</p>
                            <p class="font-mono text-lg">{{ pendaftaran.nomor_pendaftaran }}</p>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-500">No. Peserta</p>
                            <p class="font-mono text-lg">{{ pendaftaran.nomor_peserta }}</p>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-500">Tanggal Daftar</p>
                            <p>{{ formatDate(pendaftaran.tanggal_daftar) }}</p>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-500">Biaya Pendaftaran</p>
                            <p class="text-lg font-bold text-green-600">{{ formatCurrency(pendaftaran.golongan.biaya_pendaftaran) }}</p>
                        </div>
                    </div>
                </CardContent>
            </Card>

            <!-- Action Alerts -->
            <div class="space-y-4">
                <Alert v-if="needsPayment()" class="border-yellow-200 bg-yellow-50">
                    <Icon name="credit-card" class="h-4 w-4" />
                    <AlertDescription>
                        Pembayaran belum dilakukan. Silakan lakukan pembayaran untuk melanjutkan proses pendaftaran.
                    </AlertDescription>
                </Alert>

                <Alert v-if="needsDocuments()" class="border-blue-200 bg-blue-50">
                    <Icon name="file-text" class="h-4 w-4" />
                    <AlertDescription>
                        Dokumen belum lengkap. Silakan upload dokumen yang diperlukan.
                    </AlertDescription>
                </Alert>

                <Alert v-if="pendaftaran.status_pendaftaran === 'rejected'" class="border-red-200 bg-red-50">
                    <Icon name="x-circle" class="h-4 w-4" />
                    <AlertDescription>
                        Pendaftaran ditolak. {{ pendaftaran.catatan_approval || 'Silakan hubungi admin untuk informasi lebih lanjut.' }}
                    </AlertDescription>
                </Alert>
            </div>

            <!-- Payment Information -->
            <Card v-if="pendaftaran.pembayaran">
                <CardHeader>
                    <CardTitle>Informasi Pembayaran</CardTitle>
                </CardHeader>
                <CardContent>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="space-y-4">
                            <div>
                                <p class="text-sm font-medium text-gray-500">No. Transaksi</p>
                                <p class="font-mono">{{ pendaftaran.pembayaran.nomor_transaksi }}</p>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-500">Jumlah</p>
                                <p class="text-lg font-bold">{{ formatCurrency(pendaftaran.pembayaran.jumlah_bayar) }}</p>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-500">Metode Pembayaran</p>
                                <p>{{ pendaftaran.pembayaran.metode_pembayaran }}</p>
                            </div>
                        </div>
                        <div class="space-y-4">
                            <div>
                                <p class="text-sm font-medium text-gray-500">Status</p>
                                <Badge :class="getPaymentStatusColor(pendaftaran.pembayaran.status_pembayaran)">
                                    {{ pendaftaran.pembayaran.status_pembayaran }}
                                </Badge>
                            </div>
                            <div v-if="pendaftaran.pembayaran.tanggal_bayar">
                                <p class="text-sm font-medium text-gray-500">Tanggal Bayar</p>
                                <p>{{ formatDate(pendaftaran.pembayaran.tanggal_bayar) }}</p>
                            </div>
                            <div v-if="pendaftaran.pembayaran.catatan">
                                <p class="text-sm font-medium text-gray-500">Catatan</p>
                                <p class="text-sm">{{ pendaftaran.pembayaran.catatan }}</p>
                            </div>
                        </div>
                    </div>
                </CardContent>
            </Card>

            <!-- Documents -->
            <Card>
                <CardHeader>
                    <CardTitle>Dokumen</CardTitle>
                    <CardDescription>Status dokumen yang telah diupload</CardDescription>
                </CardHeader>
                <CardContent>
                    <div v-if="pendaftaran.dokumen_peserta.length === 0" class="text-center py-8">
                        <Icon name="file-x" class="h-12 w-12 text-gray-400 mx-auto mb-4" />
                        <h3 class="text-lg font-medium text-gray-900 mb-2">Belum Ada Dokumen</h3>
                        <p class="text-gray-600 mb-4">Silakan upload dokumen yang diperlukan.</p>
                        <Button v-if="canEdit()" as="link" :href="route('peserta.pendaftaran.edit', pendaftaran.id_pendaftaran)">
                            Upload Dokumen
                        </Button>
                    </div>

                    <div v-else class="space-y-4">
                        <div
                            v-for="doc in pendaftaran.dokumen_peserta"
                            :key="doc.id_dokumen"
                            class="flex items-center justify-between p-4 border rounded-lg"
                        >
                            <div class="flex items-center space-x-3">
                                <Icon name="file-text" class="h-5 w-5 text-gray-500" />
                                <div>
                                    <p class="font-medium">{{ getDocumentTypeText(doc.jenis_dokumen) }}</p>
                                    <p class="text-sm text-gray-600">{{ doc.nama_file }}</p>
                                    <p v-if="doc.catatan_verifikasi" class="text-xs text-gray-500 mt-1">
                                        {{ doc.catatan_verifikasi }}
                                    </p>
                                </div>
                            </div>
                            <div class="text-right">
                                <Badge :class="getDocumentStatusColor(doc.status_verifikasi)">
                                    {{ doc.status_verifikasi }}
                                </Badge>
                                <p v-if="doc.verified_at" class="text-xs text-gray-500 mt-1">
                                    {{ formatDate(doc.verified_at) }}
                                </p>
                            </div>
                        </div>
                    </div>
                </CardContent>
            </Card>

            <!-- Additional Information -->
            <Card v-if="pendaftaran.keterangan || pendaftaran.catatan_verifikasi || pendaftaran.catatan_approval">
                <CardHeader>
                    <CardTitle>Informasi Tambahan</CardTitle>
                </CardHeader>
                <CardContent>
                    <div class="space-y-4">
                        <div v-if="pendaftaran.keterangan">
                            <p class="text-sm font-medium text-gray-500">Keterangan Pendaftaran</p>
                            <p class="text-sm">{{ pendaftaran.keterangan }}</p>
                        </div>
                        <div v-if="pendaftaran.catatan_verifikasi">
                            <p class="text-sm font-medium text-gray-500">Catatan Verifikasi</p>
                            <p class="text-sm">{{ pendaftaran.catatan_verifikasi }}</p>
                        </div>
                        <div v-if="pendaftaran.catatan_approval">
                            <p class="text-sm font-medium text-gray-500">Catatan Persetujuan</p>
                            <p class="text-sm">{{ pendaftaran.catatan_approval }}</p>
                        </div>
                    </div>
                </CardContent>
            </Card>

            <!-- Actions -->
            <Card>
                <CardContent class="p-6">
                    <div class="flex flex-wrap gap-4">
                        <Button
                            v-if="canEdit()"
                            as="link"
                            :href="route('peserta.pendaftaran.edit', pendaftaran.id_pendaftaran)"
                        >
                            <Icon name="edit" class="w-4 h-4 mr-2" />
                            Edit Pendaftaran
                        </Button>

                        <Button
                            as="link"
                            :href="route('peserta.dokumen.index', pendaftaran.id_pendaftaran)"
                            variant="outline"
                        >
                            <Icon name="file-text" class="w-4 h-4 mr-2" />
                            Kelola Dokumen
                        </Button>

                        <Button
                            v-if="needsPayment()"
                            as="link"
                            :href="route('peserta.pembayaran.create', pendaftaran.id_pendaftaran)"
                            variant="outline"
                        >
                            <Icon name="credit-card" class="w-4 h-4 mr-2" />
                            Bayar Sekarang
                        </Button>

                        <Button
                            as="link"
                            :href="route('competition.golongan', pendaftaran.golongan.id_golongan)"
                            variant="outline"
                        >
                            <Icon name="info" class="w-4 h-4 mr-2" />
                            Info Golongan
                        </Button>
                    </div>
                </CardContent>
            </Card>
        </div>
    </AppLayout>
</template>
