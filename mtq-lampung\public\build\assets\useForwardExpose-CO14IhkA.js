import{k as y,s as E,z as Q,A as U,y as b,a1 as R,I as d,Q as W,r as k,u as V,a2 as w,a3 as L,a4 as F,a5 as B,a6 as H,a0 as X,G as D,C as z,l as G,H as Y,J as Z}from"./app-B_pmlBSQ.js";function me(e,t){var o;const n=b();return D(()=>{n.value=e()},{...t,flush:(o=void 0)!=null?o:"sync"}),R(n)}function g(e){return Q()?(U(e),!0):!1}function ve(){const e=new Set,t=i=>{e.delete(i)};return{on:i=>{e.add(i);const s=()=>t(i);return g(s),{off:s}},off:t,trigger:(...i)=>Promise.all(Array.from(e).map(s=>s(...i))),clear:()=>{e.clear()}}}function he(e){let t=!1,o;const n=B(!0);return(...r)=>(t||(o=n.run(()=>e(...r)),t=!0),o)}const j=new WeakMap,q=(...e)=>{var t;const o=e[0],n=(t=w())==null?void 0:t.proxy;if(n==null&&!L())throw new Error("injectLocal must be called in setup");return n&&j.has(n)&&o in j.get(n)?j.get(n)[o]:F(...e)};function ye(e){let t=0,o,n;const r=()=>{t-=1,n&&t<=0&&(n.stop(),o=void 0,n=void 0)};return(...i)=>(t+=1,n||(n=B(!0),o=n.run(()=>e(...i))),g(r),o)}function ee(e){if(!W(e))return k(e);const t=new Proxy({},{get(o,n,r){return V(Reflect.get(e.value,n,r))},set(o,n,r){return W(e.value[n])&&!W(r)?e.value[n].value=r:e.value[n]=r,!0},deleteProperty(o,n){return Reflect.deleteProperty(e.value,n)},has(o,n){return Reflect.has(e.value,n)},ownKeys(){return Object.keys(e.value)},getOwnPropertyDescriptor(){return{enumerable:!0,configurable:!0}}});return k(t)}function te(e){return ee(y(e))}function be(e,...t){const o=t.flat(),n=o[0];return te(()=>Object.fromEntries(typeof n=="function"?Object.entries(z(e)).filter(([r,i])=>!n(d(i),r)):Object.entries(z(e)).filter(r=>!o.includes(r[0]))))}const C=typeof window<"u"&&typeof document<"u";typeof WorkerGlobalScope<"u"&&globalThis instanceof WorkerGlobalScope;const ne=e=>typeof e<"u",oe=Object.prototype.toString,re=e=>oe.call(e)==="[object Object]",ge=se();function se(){var e,t;return C&&((e=window==null?void 0:window.navigator)==null?void 0:e.userAgent)&&(/iP(?:ad|hone|od)/.test(window.navigator.userAgent)||((t=window==null?void 0:window.navigator)==null?void 0:t.maxTouchPoints)>2&&/iPad|Macintosh/.test(window==null?void 0:window.navigator.userAgent))}function I(e){return e.endsWith("rem")?Number.parseFloat(e)*16:Number.parseFloat(e)}function ie(e){return w()}function T(e){return Array.isArray(e)?e:[e]}function we(e,t=1e4){return H((o,n)=>{let r=d(e),i;const s=()=>setTimeout(()=>{r=d(e),n()},d(t));return g(()=>{clearTimeout(i)}),{get(){return o(),r},set(u){r=u,n(),clearTimeout(i),i=s()}}})}const Oe=d;function Se(e,t){ie()&&X(e,t)}function xe(e,t,o={}){const{immediate:n=!0,immediateCallback:r=!1}=o,i=b(!1);let s=null;function u(){s&&(clearTimeout(s),s=null)}function a(){i.value=!1,u()}function l(...c){r&&e(),u(),i.value=!0,s=setTimeout(()=>{i.value=!1,s=null,e(...c)},d(t))}return n&&(i.value=!0,C&&l()),g(a),{isPending:R(i),start:l,stop:a}}function ue(e,t,o){return E(e,t,{...o,immediate:!0})}const A=C?window:void 0;function P(e){var t;const o=d(e);return(t=o==null?void 0:o.$el)!=null?t:o}function J(...e){const t=[],o=()=>{t.forEach(u=>u()),t.length=0},n=(u,a,l,c)=>(u.addEventListener(a,l,c),()=>u.removeEventListener(a,l,c)),r=y(()=>{const u=T(d(e[0])).filter(a=>a!=null);return u.every(a=>typeof a!="string")?u:void 0}),i=ue(()=>{var u,a;return[(a=(u=r.value)==null?void 0:u.map(l=>P(l)))!=null?a:[A].filter(l=>l!=null),T(d(r.value?e[1]:e[0])),T(V(r.value?e[2]:e[1])),d(r.value?e[3]:e[2])]},([u,a,l,c])=>{if(o(),!(u!=null&&u.length)||!(a!=null&&a.length)||!(l!=null&&l.length))return;const m=re(c)?{...c}:c;t.push(...u.flatMap(p=>a.flatMap(f=>l.map(v=>n(p,f,v,m)))))},{flush:"post"}),s=()=>{i(),o()};return g(o),s}function ae(){const e=b(!1),t=w();return t&&Z(()=>{e.value=!0},t),e}function K(e){const t=ae();return y(()=>(t.value,!!e()))}function le(e){return typeof e=="function"?e:typeof e=="string"?t=>t.key===e:Array.isArray(e)?t=>e.includes(t.key):()=>!0}function Ee(...e){let t,o,n={};e.length===3?(t=e[0],o=e[1],n=e[2]):e.length===2?typeof e[1]=="object"?(t=!0,o=e[0],n=e[1]):(t=e[0],o=e[1]):(t=!0,o=e[0]);const{target:r=A,eventName:i="keydown",passive:s=!1,dedupe:u=!1}=n,a=le(t);return J(r,i,c=>{c.repeat&&d(u)||a(c)&&o(c)},s)}const ce=Symbol("vueuse-ssr-width");function fe(){const e=L()?q(ce,null):null;return typeof e=="number"?e:void 0}function Pe(e,t={}){const{window:o=A,ssrWidth:n=fe()}=t,r=K(()=>o&&"matchMedia"in o&&typeof o.matchMedia=="function"),i=b(typeof n=="number"),s=b(),u=b(!1),a=l=>{u.value=l.matches};return D(()=>{if(i.value){i.value=!r.value;const l=d(e).split(",");u.value=l.some(c=>{const m=c.includes("not all"),p=c.match(/\(\s*min-width:\s*(-?\d+(?:\.\d*)?[a-z]+\s*)\)/),f=c.match(/\(\s*max-width:\s*(-?\d+(?:\.\d*)?[a-z]+\s*)\)/);let v=!!(p||f);return p&&v&&(v=n>=I(p[1])),f&&v&&(v=n<=I(f[1])),m?!v:v});return}r.value&&(s.value=o.matchMedia(d(e)),u.value=s.value.matches)}),J(s,"change",a,{passive:!0}),y(()=>u.value)}function de(e){return JSON.parse(JSON.stringify(e))}function Ae(e,t,o={}){const{window:n=A,...r}=o;let i;const s=K(()=>n&&"ResizeObserver"in n),u=()=>{i&&(i.disconnect(),i=void 0)},a=y(()=>{const m=d(e);return Array.isArray(m)?m.map(p=>P(p)):[P(m)]}),l=E(a,m=>{if(u(),s.value&&n){i=new ResizeObserver(t);for(const p of m)p&&i.observe(p,r)}},{immediate:!0,flush:"post"}),c=()=>{u(),l()};return g(c),{isSupported:s,stop:c}}function Me(e,t,o,n={}){var r,i,s;const{clone:u=!1,passive:a=!1,eventName:l,deep:c=!1,defaultValue:m,shouldEmit:p}=n,f=w(),v=o||(f==null?void 0:f.emit)||((r=f==null?void 0:f.$emit)==null?void 0:r.bind(f))||((s=(i=f==null?void 0:f.proxy)==null?void 0:i.$emit)==null?void 0:s.bind(f==null?void 0:f.proxy));let O=l;t||(t="modelValue"),O=O||`update:${t.toString()}`;const $=h=>u?typeof u=="function"?u(h):de(h):h,_=()=>ne(e[t])?$(e[t]):m,N=h=>{p?p(h)&&v(O,h):v(O,h)};if(a){const h=_(),M=G(h);let S=!1;return E(()=>e[t],x=>{S||(S=!0,M.value=$(x),Y(()=>S=!1))}),E(M,x=>{!S&&(x!==e[t]||c)&&N(x)},{deep:c}),M}else return y({get(){return _()},set(h){N(h)}})}function We(){const e=w(),t=G(),o=y(()=>{var s,u;return["#text","#comment"].includes((s=t.value)==null?void 0:s.$el.nodeName)?(u=t.value)==null?void 0:u.$el.nextElementSibling:P(t)}),n=Object.assign({},e.exposed),r={};for(const s in e.props)Object.defineProperty(r,s,{enumerable:!0,configurable:!0,get:()=>e.props[s]});if(Object.keys(n).length>0)for(const s in n)Object.defineProperty(r,s,{enumerable:!0,configurable:!0,get:()=>n[s]});Object.defineProperty(r,"$el",{enumerable:!0,configurable:!0,get:()=>e.vnode.el}),e.exposed=r;function i(s){t.value=s,s&&(Object.defineProperty(r,"$el",{enumerable:!0,configurable:!0,get:()=>s instanceof Element?s:s.$el}),e.exposed=r)}return{forwardRef:i,currentRef:t,currentElement:o}}export{We as a,P as b,he as c,ye as d,ge as e,J as f,ae as g,me as h,C as i,ve as j,xe as k,be as l,Pe as m,Ae as n,Ee as o,A as p,Oe as q,we as r,Se as t,Me as u};
