import{d as h,h as p,o as _,b as l,D as y,n as g,u as t,l as z,bj as I,c as j,w as a,a as e,g as A,t as o,e as i,j as U,q as H,i as C,F as P,m as S,W as V}from"./app-B_pmlBSQ.js";import{_ as J}from"./AppLayout.vue_vue_type_script_setup_true_lang-BRf8lUiM.js";import{_ as R}from"./Heading.vue_vue_type_script_setup_true_lang-is4VBSCS.js";import{a as w,_ as D}from"./AppLogoIcon.vue_vue_type_script_setup_true_lang-wzz4PWna.js";import{_ as W}from"./Input.vue_vue_type_script_setup_true_lang-DB1-RsTn.js";import{_ as M}from"./Label.vue_vue_type_script_setup_true_lang-rj_fhE7R.js";import{_ as x,a as k}from"./CardContent.vue_vue_type_script_setup_true_lang-ClvLNBXC.js";import{_ as Y}from"./CardDescription.vue_vue_type_script_setup_true_lang-DboywBIK.js";import{_ as q,a as E}from"./CardTitle.vue_vue_type_script_setup_true_lang-5HO6oSoB.js";import{_ as B}from"./index-CMGr3-bt.js";import{_ as v}from"./Icon.vue_vue_type_script_setup_true_lang-BuYzjckr.js";import"./useForwardExpose-CO14IhkA.js";import"./RovingFocusGroup-lsWyU4xZ.js";import"./check-DtmHjdxf.js";import"./loader-circle-6d8QrWFr.js";import"./sun-CHIV9RJ8.js";const G={"data-slot":"table-container",class:"relative w-full overflow-auto"},O=h({__name:"Table",props:{class:{}},setup(f){const d=f;return(u,b)=>(_(),p("div",G,[l("table",{"data-slot":"table",class:g(t(w)("w-full caption-bottom text-sm",d.class))},[y(u.$slots,"default")],2)]))}}),Q=h({__name:"TableBody",props:{class:{}},setup(f){const d=f;return(u,b)=>(_(),p("tbody",{"data-slot":"table-body",class:g(t(w)("[&_tr:last-child]:border-0",d.class))},[y(u.$slots,"default")],2))}}),m=h({__name:"TableCell",props:{class:{}},setup(f){const d=f;return(u,b)=>(_(),p("td",{"data-slot":"table-cell",class:g(t(w)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",d.class))},[y(u.$slots,"default")],2))}}),F=h({__name:"TableRow",props:{class:{}},setup(f){const d=f;return(u,b)=>(_(),p("tr",{"data-slot":"table-row",class:g(t(w)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",d.class))},[y(u.$slots,"default")],2))}}),c=h({__name:"TableHead",props:{class:{}},setup(f){const d=f;return(u,b)=>(_(),p("th",{"data-slot":"table-head",class:g(t(w)("text-muted-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",d.class))},[y(u.$slots,"default")],2))}}),X=h({__name:"TableHeader",props:{class:{}},setup(f){const d=f;return(u,b)=>(_(),p("thead",{"data-slot":"table-header",class:g(t(w)("[&_tr]:border-b",d.class))},[y(u.$slots,"default")],2))}}),Z={class:"flex items-center justify-between"},ee={class:"space-y-6"},te={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},ae={class:"flex items-center space-x-2"},se={class:"text-2xl font-bold"},le={class:"flex items-center space-x-2"},ne={class:"text-2xl font-bold"},re={class:"flex items-center space-x-2"},oe={class:"text-2xl font-bold"},ie={class:"flex items-center space-x-2"},de={class:"text-2xl font-bold"},ue={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},_e={class:"md:col-span-2"},fe={class:"overflow-x-auto"},pe={class:"font-medium"},me={class:"text-sm text-gray-500"},ce={class:"font-mono text-sm"},ge={class:"text-sm"},be={class:"text-sm"},ve={class:"font-medium"},he={key:0,class:"text-gray-500"},xe={key:0},ke={class:"flex space-x-2"},ye={key:0,class:"flex items-center justify-between mt-6"},we={class:"text-sm text-gray-500"},$e={class:"flex space-x-2"},Ue=h({__name:"Index",props:{peserta:{},stats:{}},setup(f){const d=z({search:"",status:"all"});function u(){V.get(route("admin-daerah.peserta.index"),d.value,{preserveState:!0,replace:!0})}function b(r){return{draft:"bg-gray-100 text-gray-800",submitted:"bg-blue-100 text-blue-800",verified:"bg-indigo-100 text-indigo-800",approved:"bg-green-100 text-green-800",rejected:"bg-red-100 text-red-800"}[r]||"bg-gray-100 text-gray-800"}function L(r){return{draft:"Draft",submitted:"Disubmit",verified:"Terverifikasi",approved:"Disetujui",rejected:"Ditolak"}[r]||r}function N(r){return{mandiri:"Mandiri",admin_daerah:"Admin Daerah"}[r]||r}function K(r){const s=new Date,$=new Date(r);let n=s.getFullYear()-$.getFullYear();const T=s.getMonth()-$.getMonth();return(T<0||T===0&&s.getDate()<$.getDate())&&n--,n}return(r,s)=>{const $=I("TextLink");return _(),j(J,null,{header:a(()=>[l("div",Z,[e(R,null,{default:a(()=>s[2]||(s[2]=[i("Kelola Peserta Daerah")])),_:1,__:[2]}),e(D,{"as-child":""},{default:a(()=>[e($,{href:r.route("admin-daerah.peserta.create")},{default:a(()=>[e(v,{name:"plus",class:"w-4 h-4 mr-2"}),s[3]||(s[3]=i(" Daftarkan Peserta Baru "))]),_:1,__:[3]},8,["href"])]),_:1})])]),default:a(()=>[e(t(A),{title:"Kelola Peserta Daerah"}),l("div",ee,[l("div",te,[e(t(x),null,{default:a(()=>[e(t(k),{class:"p-6"},{default:a(()=>[l("div",ae,[e(v,{name:"users",class:"h-8 w-8 text-blue-600"}),l("div",null,[l("p",se,o(r.stats.total_peserta),1),s[4]||(s[4]=l("p",{class:"text-sm text-gray-600"},"Total Peserta",-1))])])]),_:1})]),_:1}),e(t(x),null,{default:a(()=>[e(t(k),{class:"p-6"},{default:a(()=>[l("div",le,[e(v,{name:"check-circle",class:"h-8 w-8 text-green-600"}),l("div",null,[l("p",ne,o(r.stats.peserta_approved),1),s[5]||(s[5]=l("p",{class:"text-sm text-gray-600"},"Disetujui",-1))])])]),_:1})]),_:1}),e(t(x),null,{default:a(()=>[e(t(k),{class:"p-6"},{default:a(()=>[l("div",re,[e(v,{name:"clock",class:"h-8 w-8 text-yellow-600"}),l("div",null,[l("p",oe,o(r.stats.peserta_pending),1),s[6]||(s[6]=l("p",{class:"text-sm text-gray-600"},"Menunggu",-1))])])]),_:1})]),_:1}),e(t(x),null,{default:a(()=>[e(t(k),{class:"p-6"},{default:a(()=>[l("div",ie,[e(v,{name:"clipboard-list",class:"h-8 w-8 text-purple-600"}),l("div",null,[l("p",de,o(r.stats.total_pendaftaran),1),s[7]||(s[7]=l("p",{class:"text-sm text-gray-600"},"Total Pendaftaran",-1))])])]),_:1})]),_:1})]),e(t(x),null,{default:a(()=>[e(t(k),{class:"p-6"},{default:a(()=>[l("div",ue,[l("div",_e,[e(t(M),{for:"search"},{default:a(()=>s[8]||(s[8]=[i("Cari Peserta")])),_:1,__:[8]}),e(t(W),{id:"search",modelValue:d.value.search,"onUpdate:modelValue":s[0]||(s[0]=n=>d.value.search=n),placeholder:"Cari berdasarkan nama, NIK, atau email...",onInput:u},null,8,["modelValue"])]),l("div",null,[e(t(M),{for:"status"},{default:a(()=>s[9]||(s[9]=[i("Filter Status")])),_:1,__:[9]}),U(l("select",{id:"status","onUpdate:modelValue":s[1]||(s[1]=n=>d.value.status=n),onChange:u,class:"flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50"},s[10]||(s[10]=[l("option",{value:"all"},"Semua Status",-1),l("option",{value:"draft"},"Draft",-1),l("option",{value:"submitted"},"Disubmit",-1),l("option",{value:"verified"},"Terverifikasi",-1),l("option",{value:"approved"},"Disetujui",-1),l("option",{value:"rejected"},"Ditolak",-1)]),544),[[H,d.value.status]])])])]),_:1})]),_:1}),e(t(x),null,{default:a(()=>[e(t(q),null,{default:a(()=>[e(t(E),null,{default:a(()=>s[11]||(s[11]=[i("Daftar Peserta")])),_:1,__:[11]}),e(t(Y),null,{default:a(()=>[i(" Menampilkan "+o(r.peserta.from)+"-"+o(r.peserta.to)+" dari "+o(r.peserta.total)+" peserta ",1)]),_:1})]),_:1}),e(t(k),null,{default:a(()=>[l("div",fe,[e(t(O),null,{default:a(()=>[e(t(X),null,{default:a(()=>[e(t(F),null,{default:a(()=>[e(t(c),null,{default:a(()=>s[12]||(s[12]=[i("Peserta")])),_:1,__:[12]}),e(t(c),null,{default:a(()=>s[13]||(s[13]=[i("NIK")])),_:1,__:[13]}),e(t(c),null,{default:a(()=>s[14]||(s[14]=[i("Jenis Kelamin")])),_:1,__:[14]}),e(t(c),null,{default:a(()=>s[15]||(s[15]=[i("Usia")])),_:1,__:[15]}),e(t(c),null,{default:a(()=>s[16]||(s[16]=[i("Status")])),_:1,__:[16]}),e(t(c),null,{default:a(()=>s[17]||(s[17]=[i("Jenis Daftar")])),_:1,__:[17]}),e(t(c),null,{default:a(()=>s[18]||(s[18]=[i("Pendaftaran")])),_:1,__:[18]}),e(t(c),null,{default:a(()=>s[19]||(s[19]=[i("Aksi")])),_:1,__:[19]})]),_:1})]),_:1}),e(t(Q),null,{default:a(()=>[(_(!0),p(P,null,S(r.peserta.data,n=>(_(),j(t(F),{key:n.id_peserta},{default:a(()=>[e(t(m),null,{default:a(()=>[l("div",null,[l("div",pe,o(n.nama_lengkap),1),l("div",me,o(n.user.email),1)])]),_:2},1024),e(t(m),null,{default:a(()=>[l("span",ce,o(n.nik),1)]),_:2},1024),e(t(m),null,{default:a(()=>[e(t(B),{class:g(n.jenis_kelamin==="L"?"bg-blue-100 text-blue-800":"bg-pink-100 text-pink-800")},{default:a(()=>[i(o(n.jenis_kelamin==="L"?"Laki-laki":"Perempuan"),1)]),_:2},1032,["class"])]),_:2},1024),e(t(m),null,{default:a(()=>[i(o(K(n.tanggal_lahir))+" tahun ",1)]),_:2},1024),e(t(m),null,{default:a(()=>[e(t(B),{class:g(b(n.status_peserta))},{default:a(()=>[i(o(L(n.status_peserta)),1)]),_:2},1032,["class"])]),_:2},1024),e(t(m),null,{default:a(()=>[l("span",ge,o(N(n.registration_type)),1)]),_:2},1024),e(t(m),null,{default:a(()=>[l("div",be,[l("div",ve,o(n.pendaftaran.length)+" lomba",1),n.pendaftaran.length>0?(_(),p("div",he,[i(o(n.pendaftaran[0].golongan.nama_golongan)+" ",1),n.pendaftaran.length>1?(_(),p("span",xe,"+"+o(n.pendaftaran.length-1)+" lainnya",1)):C("",!0)])):C("",!0)])]),_:2},1024),e(t(m),null,{default:a(()=>[l("div",ke,[e(D,{as:"link",href:r.route("admin-daerah.peserta.show",n.id_peserta),size:"sm",variant:"outline"},{default:a(()=>[e(v,{name:"eye",class:"w-4 h-4"})]),_:2},1032,["href"]),e(D,{as:"link",href:r.route("admin-daerah.peserta.edit",n.id_peserta),size:"sm",variant:"outline"},{default:a(()=>[e(v,{name:"edit",class:"w-4 h-4"})]),_:2},1032,["href"])])]),_:2},1024)]),_:2},1024))),128))]),_:1})]),_:1})]),r.peserta.last_page>1?(_(),p("div",ye,[l("div",we," Menampilkan "+o(r.peserta.from)+"-"+o(r.peserta.to)+" dari "+o(r.peserta.total)+" peserta ",1),l("div",$e,[(_(!0),p(P,null,S(r.peserta.last_page,n=>(_(),j(D,{key:n,variant:n===r.peserta.current_page?"default":"outline",size:"sm",onClick:T=>t(V).get(r.route("admin-daerah.peserta.index",{page:n}))},{default:a(()=>[i(o(n),1)]),_:2},1032,["variant","onClick"]))),128))])])):C("",!0)]),_:1})]),_:1})])]),_:1})}}});export{Ue as default};
