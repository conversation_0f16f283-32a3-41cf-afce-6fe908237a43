<?php

namespace App\Http\Controllers\AdminDaerah;

use App\Http\Controllers\Controller;
use App\Models\Peserta;
use App\Models\Pendaftaran;
use App\Models\DokumenPeserta;
use App\Models\Pembayaran;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;
use Inertia\Response;

class DashboardController extends Controller
{
    /**
     * Display the admin daerah dashboard
     */
    public function index(): Response
    {
        $adminWilayah = Auth::user()->id_wilayah;
        
        // Get statistics for admin's wilayah
        $stats = [
            'total_peserta' => Peserta::where('id_wilayah', $adminWilayah)->count(),
            'peserta_approved' => Peserta::where('id_wilayah', $adminWilayah)->where('status_peserta', 'approved')->count(),
            'peserta_pending' => Peserta::where('id_wilayah', $adminWilayah)->whereIn('status_peserta', ['draft', 'submitted'])->count(),
            'total_pendaftaran' => Pendaftaran::whereHas('peserta', function($q) use ($adminWilayah) {
                $q->where('id_wilayah', $adminWilayah);
            })->count(),
            'pendaftaran_approved' => Pendaftaran::whereHas('peserta', function($q) use ($adminWilayah) {
                $q->where('id_wilayah', $adminWilayah);
            })->where('status_pendaftaran', 'approved')->count(),
            'dokumen_pending' => DokumenPeserta::whereHas('pendaftaran.peserta', function($q) use ($adminWilayah) {
                $q->where('id_wilayah', $adminWilayah);
            })->where('status_verifikasi', 'pending')->count(),
            'pembayaran_pending' => Pembayaran::whereHas('pendaftaran.peserta', function($q) use ($adminWilayah) {
                $q->where('id_wilayah', $adminWilayah);
            })->where('status_pembayaran', 'pending')->count(),
        ];

        // Get recent peserta
        $recentPeserta = Peserta::with(['user', 'pendaftaran.golongan.cabangLomba'])
            ->where('id_wilayah', $adminWilayah)
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();

        // Get recent registrations
        $recentPendaftaran = Pendaftaran::with(['peserta.user', 'golongan.cabangLomba', 'pembayaran'])
            ->whereHas('peserta', function($q) use ($adminWilayah) {
                $q->where('id_wilayah', $adminWilayah);
            })
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();

        return Inertia::render('AdminDaerah/Dashboard', [
            'stats' => $stats,
            'recentPeserta' => $recentPeserta,
            'recentPendaftaran' => $recentPendaftaran
        ]);
    }
}
