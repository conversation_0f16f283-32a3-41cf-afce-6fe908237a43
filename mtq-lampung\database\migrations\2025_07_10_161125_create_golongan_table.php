<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('golongan', function (Blueprint $table) {
            $table->id('id_golongan');
            $table->string('kode_golongan', 10)->unique();
            $table->string('nama_golongan', 100);
            $table->unsignedBigInteger('id_cabang');
            $table->enum('jenis_kelamin', ['L', 'P']);
            $table->integer('batas_umur_min');
            $table->integer('batas_umur_max');
            $table->integer('kuota_max')->default(0);
            $table->decimal('biaya_pendaftaran', 10, 2)->default(0);
            $table->integer('nomor_urut_awal')->default(1);
            $table->integer('nomor_urut_akhir')->default(999);
            $table->enum('status', ['aktif', 'non_aktif'])->default('aktif');
            $table->timestamps();

            $table->foreign('id_cabang')->references('id_cabang')->on('cabang_lomba')->onDelete('cascade');
            $table->index(['id_cabang', 'jenis_kelamin', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('golongan');
    }
};
