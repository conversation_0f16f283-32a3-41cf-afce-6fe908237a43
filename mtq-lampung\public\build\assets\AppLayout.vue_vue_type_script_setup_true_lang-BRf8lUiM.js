import{P as V,c as N,a as I,_ as eo,b as to,d as no}from"./AppLogoIcon.vue_vue_type_script_setup_true_lang-wzz4PWna.js";import{k as A,l as E,y as oo,s as _e,z as ao,A as so,B as De,u as r,d as v,C as Re,D as b,c as w,o as y,w as m,E as S,r as dn,G as ne,H as ae,I as Ie,p as Je,J as Be,a as O,K as Dt,i as ke,T as ro,j as io,L as lo,M as cn,h as F,N as fn,f as pn,O as X,P as oe,Q as uo,R as co,S as fo,U as po,e as ce,t as he,V as et,X as mo,b as G,n as K,F as xe,Y as tt,m as Et,Z as ze,W as ho}from"./app-B_pmlBSQ.js";import{u as Ke,a as R,c as go,i as Pe,o as yo,b as mn,d as hn,t as vo,e as Vt,f as Te,g as bo,h as wo,r as gn,j as _o,k as yn,l as nt,m as xo}from"./useForwardExpose-CO14IhkA.js";import{c as ie,g as te,u as He,a as ot,P as at,i as Co,b as vn,_ as Oo,d as ko,e as le,f as $o,h as st}from"./RovingFocusGroup-lsWyU4xZ.js";const Bo=["top","right","bottom","left"],Ce=Math.min,J=Math.max,Xe=Math.round,je=Math.floor,de=e=>({x:e,y:e}),Po={left:"right",right:"left",bottom:"top",top:"bottom"},Ao={start:"end",end:"start"};function xt(e,n,t){return J(e,Ce(n,t))}function ge(e,n){return typeof e=="function"?e(n):e}function ye(e){return e.split("-")[0]}function Fe(e){return e.split("-")[1]}function St(e){return e==="x"?"y":"x"}function Mt(e){return e==="y"?"height":"width"}function ue(e){return["top","bottom"].includes(ye(e))?"y":"x"}function Tt(e){return St(ue(e))}function Do(e,n,t){t===void 0&&(t=!1);const o=Fe(e),a=Tt(e),s=Mt(a);let i=a==="x"?o===(t?"end":"start")?"right":"left":o==="start"?"bottom":"top";return n.reference[s]>n.floating[s]&&(i=Ze(i)),[i,Ze(i)]}function Eo(e){const n=Ze(e);return[Ct(e),n,Ct(n)]}function Ct(e){return e.replace(/start|end/g,n=>Ao[n])}function So(e,n,t){const o=["left","right"],a=["right","left"],s=["top","bottom"],i=["bottom","top"];switch(e){case"top":case"bottom":return t?n?a:o:n?o:a;case"left":case"right":return n?s:i;default:return[]}}function Mo(e,n,t,o){const a=Fe(e);let s=So(ye(e),t==="start",o);return a&&(s=s.map(i=>i+"-"+a),n&&(s=s.concat(s.map(Ct)))),s}function Ze(e){return e.replace(/left|right|bottom|top/g,n=>Po[n])}function To(e){return{top:0,right:0,bottom:0,left:0,...e}}function bn(e){return typeof e!="number"?To(e):{top:e,right:e,bottom:e,left:e}}function Qe(e){const{x:n,y:t,width:o,height:a}=e;return{width:o,height:a,top:t,left:n,right:n+o,bottom:t+a,x:n,y:t}}function jt(e,n,t){let{reference:o,floating:a}=e;const s=ue(n),i=Tt(n),l=Mt(i),d=ye(n),f=s==="y",u=o.x+o.width/2-a.width/2,c=o.y+o.height/2-a.height/2,p=o[l]/2-a[l]/2;let h;switch(d){case"top":h={x:u,y:o.y-a.height};break;case"bottom":h={x:u,y:o.y+o.height};break;case"right":h={x:o.x+o.width,y:c};break;case"left":h={x:o.x-a.width,y:c};break;default:h={x:o.x,y:o.y}}switch(Fe(n)){case"start":h[i]-=p*(t&&f?-1:1);break;case"end":h[i]+=p*(t&&f?-1:1);break}return h}const Lo=async(e,n,t)=>{const{placement:o="bottom",strategy:a="absolute",middleware:s=[],platform:i}=t,l=s.filter(Boolean),d=await(i.isRTL==null?void 0:i.isRTL(n));let f=await i.getElementRects({reference:e,floating:n,strategy:a}),{x:u,y:c}=jt(f,o,d),p=o,h={},g=0;for(let _=0;_<l.length;_++){const{name:x,fn:k}=l[_],{x:$,y:B,data:D,reset:L}=await k({x:u,y:c,initialPlacement:o,placement:p,strategy:a,middlewareData:h,rects:f,platform:i,elements:{reference:e,floating:n}});u=$??u,c=B??c,h={...h,[x]:{...h[x],...D}},L&&g<=50&&(g++,typeof L=="object"&&(L.placement&&(p=L.placement),L.rects&&(f=L.rects===!0?await i.getElementRects({reference:e,floating:n,strategy:a}):L.rects),{x:u,y:c}=jt(f,p,d)),_=-1)}return{x:u,y:c,placement:p,strategy:a,middlewareData:h}};async function Ne(e,n){var t;n===void 0&&(n={});const{x:o,y:a,platform:s,rects:i,elements:l,strategy:d}=e,{boundary:f="clippingAncestors",rootBoundary:u="viewport",elementContext:c="floating",altBoundary:p=!1,padding:h=0}=ge(n,e),g=bn(h),x=l[p?c==="floating"?"reference":"floating":c],k=Qe(await s.getClippingRect({element:(t=await(s.isElement==null?void 0:s.isElement(x)))==null||t?x:x.contextElement||await(s.getDocumentElement==null?void 0:s.getDocumentElement(l.floating)),boundary:f,rootBoundary:u,strategy:d})),$=c==="floating"?{x:o,y:a,width:i.floating.width,height:i.floating.height}:i.reference,B=await(s.getOffsetParent==null?void 0:s.getOffsetParent(l.floating)),D=await(s.isElement==null?void 0:s.isElement(B))?await(s.getScale==null?void 0:s.getScale(B))||{x:1,y:1}:{x:1,y:1},L=Qe(s.convertOffsetParentRelativeRectToViewportRelativeRect?await s.convertOffsetParentRelativeRectToViewportRelativeRect({elements:l,rect:$,offsetParent:B,strategy:d}):$);return{top:(k.top-L.top+g.top)/D.y,bottom:(L.bottom-k.bottom+g.bottom)/D.y,left:(k.left-L.left+g.left)/D.x,right:(L.right-k.right+g.right)/D.x}}const Ro=e=>({name:"arrow",options:e,async fn(n){const{x:t,y:o,placement:a,rects:s,platform:i,elements:l,middlewareData:d}=n,{element:f,padding:u=0}=ge(e,n)||{};if(f==null)return{};const c=bn(u),p={x:t,y:o},h=Tt(a),g=Mt(h),_=await i.getDimensions(f),x=h==="y",k=x?"top":"left",$=x?"bottom":"right",B=x?"clientHeight":"clientWidth",D=s.reference[g]+s.reference[h]-p[h]-s.floating[g],L=p[h]-s.reference[h],j=await(i.getOffsetParent==null?void 0:i.getOffsetParent(f));let T=j?j[B]:0;(!T||!await(i.isElement==null?void 0:i.isElement(j)))&&(T=l.floating[B]||s.floating[g]);const H=D/2-L/2,C=T/2-_[g]/2-1,M=Ce(c[k],C),P=Ce(c[$],C),W=M,q=T-_[g]-P,z=T/2-_[g]/2+H,Y=xt(W,z,q),Z=!d.arrow&&Fe(a)!=null&&z!==Y&&s.reference[g]/2-(z<W?M:P)-_[g]/2<0,U=Z?z<W?z-W:z-q:0;return{[h]:p[h]+U,data:{[h]:Y,centerOffset:z-Y-U,...Z&&{alignmentOffset:U}},reset:Z}}}),Fo=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(n){var t,o;const{placement:a,middlewareData:s,rects:i,initialPlacement:l,platform:d,elements:f}=n,{mainAxis:u=!0,crossAxis:c=!0,fallbackPlacements:p,fallbackStrategy:h="bestFit",fallbackAxisSideDirection:g="none",flipAlignment:_=!0,...x}=ge(e,n);if((t=s.arrow)!=null&&t.alignmentOffset)return{};const k=ye(a),$=ue(l),B=ye(l)===l,D=await(d.isRTL==null?void 0:d.isRTL(f.floating)),L=p||(B||!_?[Ze(l)]:Eo(l)),j=g!=="none";!p&&j&&L.push(...Mo(l,_,g,D));const T=[l,...L],H=await Ne(n,x),C=[];let M=((o=s.flip)==null?void 0:o.overflows)||[];if(u&&C.push(H[k]),c){const z=Do(a,i,D);C.push(H[z[0]],H[z[1]])}if(M=[...M,{placement:a,overflows:C}],!C.every(z=>z<=0)){var P,W;const z=(((P=s.flip)==null?void 0:P.index)||0)+1,Y=T[z];if(Y&&(!(c==="alignment"?$!==ue(Y):!1)||M.every(Q=>Q.overflows[0]>0&&ue(Q.placement)===$)))return{data:{index:z,overflows:M},reset:{placement:Y}};let Z=(W=M.filter(U=>U.overflows[0]<=0).sort((U,Q)=>U.overflows[1]-Q.overflows[1])[0])==null?void 0:W.placement;if(!Z)switch(h){case"bestFit":{var q;const U=(q=M.filter(Q=>{if(j){const be=ue(Q.placement);return be===$||be==="y"}return!0}).map(Q=>[Q.placement,Q.overflows.filter(be=>be>0).reduce((be,Jn)=>be+Jn,0)]).sort((Q,be)=>Q[1]-be[1])[0])==null?void 0:q[0];U&&(Z=U);break}case"initialPlacement":Z=l;break}if(a!==Z)return{reset:{placement:Z}}}return{}}}};function Ut(e,n){return{top:e.top-n.height,right:e.right-n.width,bottom:e.bottom-n.height,left:e.left-n.width}}function Gt(e){return Bo.some(n=>e[n]>=0)}const Io=function(e){return e===void 0&&(e={}),{name:"hide",options:e,async fn(n){const{rects:t}=n,{strategy:o="referenceHidden",...a}=ge(e,n);switch(o){case"referenceHidden":{const s=await Ne(n,{...a,elementContext:"reference"}),i=Ut(s,t.reference);return{data:{referenceHiddenOffsets:i,referenceHidden:Gt(i)}}}case"escaped":{const s=await Ne(n,{...a,altBoundary:!0}),i=Ut(s,t.floating);return{data:{escapedOffsets:i,escaped:Gt(i)}}}default:return{}}}}};async function zo(e,n){const{placement:t,platform:o,elements:a}=e,s=await(o.isRTL==null?void 0:o.isRTL(a.floating)),i=ye(t),l=Fe(t),d=ue(t)==="y",f=["left","top"].includes(i)?-1:1,u=s&&d?-1:1,c=ge(n,e);let{mainAxis:p,crossAxis:h,alignmentAxis:g}=typeof c=="number"?{mainAxis:c,crossAxis:0,alignmentAxis:null}:{mainAxis:c.mainAxis||0,crossAxis:c.crossAxis||0,alignmentAxis:c.alignmentAxis};return l&&typeof g=="number"&&(h=l==="end"?g*-1:g),d?{x:h*u,y:p*f}:{x:p*f,y:h*u}}const Ho=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(n){var t,o;const{x:a,y:s,placement:i,middlewareData:l}=n,d=await zo(n,e);return i===((t=l.offset)==null?void 0:t.placement)&&(o=l.arrow)!=null&&o.alignmentOffset?{}:{x:a+d.x,y:s+d.y,data:{...d,placement:i}}}}},No=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(n){const{x:t,y:o,placement:a}=n,{mainAxis:s=!0,crossAxis:i=!1,limiter:l={fn:x=>{let{x:k,y:$}=x;return{x:k,y:$}}},...d}=ge(e,n),f={x:t,y:o},u=await Ne(n,d),c=ue(ye(a)),p=St(c);let h=f[p],g=f[c];if(s){const x=p==="y"?"top":"left",k=p==="y"?"bottom":"right",$=h+u[x],B=h-u[k];h=xt($,h,B)}if(i){const x=c==="y"?"top":"left",k=c==="y"?"bottom":"right",$=g+u[x],B=g-u[k];g=xt($,g,B)}const _=l.fn({...n,[p]:h,[c]:g});return{..._,data:{x:_.x-t,y:_.y-o,enabled:{[p]:s,[c]:i}}}}}},Wo=function(e){return e===void 0&&(e={}),{options:e,fn(n){const{x:t,y:o,placement:a,rects:s,middlewareData:i}=n,{offset:l=0,mainAxis:d=!0,crossAxis:f=!0}=ge(e,n),u={x:t,y:o},c=ue(a),p=St(c);let h=u[p],g=u[c];const _=ge(l,n),x=typeof _=="number"?{mainAxis:_,crossAxis:0}:{mainAxis:0,crossAxis:0,..._};if(d){const B=p==="y"?"height":"width",D=s.reference[p]-s.floating[B]+x.mainAxis,L=s.reference[p]+s.reference[B]-x.mainAxis;h<D?h=D:h>L&&(h=L)}if(f){var k,$;const B=p==="y"?"width":"height",D=["top","left"].includes(ye(a)),L=s.reference[c]-s.floating[B]+(D&&((k=i.offset)==null?void 0:k[c])||0)+(D?0:x.crossAxis),j=s.reference[c]+s.reference[B]+(D?0:(($=i.offset)==null?void 0:$[c])||0)-(D?x.crossAxis:0);g<L?g=L:g>j&&(g=j)}return{[p]:h,[c]:g}}}},Ko=function(e){return e===void 0&&(e={}),{name:"size",options:e,async fn(n){var t,o;const{placement:a,rects:s,platform:i,elements:l}=n,{apply:d=()=>{},...f}=ge(e,n),u=await Ne(n,f),c=ye(a),p=Fe(a),h=ue(a)==="y",{width:g,height:_}=s.floating;let x,k;c==="top"||c==="bottom"?(x=c,k=p===(await(i.isRTL==null?void 0:i.isRTL(l.floating))?"start":"end")?"left":"right"):(k=c,x=p==="end"?"top":"bottom");const $=_-u.top-u.bottom,B=g-u.left-u.right,D=Ce(_-u[x],$),L=Ce(g-u[k],B),j=!n.middlewareData.shift;let T=D,H=L;if((t=n.middlewareData.shift)!=null&&t.enabled.x&&(H=B),(o=n.middlewareData.shift)!=null&&o.enabled.y&&(T=$),j&&!p){const M=J(u.left,0),P=J(u.right,0),W=J(u.top,0),q=J(u.bottom,0);h?H=g-2*(M!==0||P!==0?M+P:J(u.left,u.right)):T=_-2*(W!==0||q!==0?W+q:J(u.top,u.bottom))}await d({...n,availableWidth:H,availableHeight:T});const C=await i.getDimensions(l.floating);return g!==C.width||_!==C.height?{reset:{rects:!0}}:{}}}};function rt(){return typeof window<"u"}function Ae(e){return Lt(e)?(e.nodeName||"").toLowerCase():"#document"}function ee(e){var n;return(e==null||(n=e.ownerDocument)==null?void 0:n.defaultView)||window}function pe(e){var n;return(n=(Lt(e)?e.ownerDocument:e.document)||window.document)==null?void 0:n.documentElement}function Lt(e){return rt()?e instanceof Node||e instanceof ee(e).Node:!1}function se(e){return rt()?e instanceof Element||e instanceof ee(e).Element:!1}function fe(e){return rt()?e instanceof HTMLElement||e instanceof ee(e).HTMLElement:!1}function qt(e){return!rt()||typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof ee(e).ShadowRoot}function Ve(e){const{overflow:n,overflowX:t,overflowY:o,display:a}=re(e);return/auto|scroll|overlay|hidden|clip/.test(n+o+t)&&!["inline","contents"].includes(a)}function Vo(e){return["table","td","th"].includes(Ae(e))}function it(e){return[":popover-open",":modal"].some(n=>{try{return e.matches(n)}catch{return!1}})}function Rt(e){const n=Ft(),t=se(e)?re(e):e;return["transform","translate","scale","rotate","perspective"].some(o=>t[o]?t[o]!=="none":!1)||(t.containerType?t.containerType!=="normal":!1)||!n&&(t.backdropFilter?t.backdropFilter!=="none":!1)||!n&&(t.filter?t.filter!=="none":!1)||["transform","translate","scale","rotate","perspective","filter"].some(o=>(t.willChange||"").includes(o))||["paint","layout","strict","content"].some(o=>(t.contain||"").includes(o))}function jo(e){let n=Oe(e);for(;fe(n)&&!Le(n);){if(Rt(n))return n;if(it(n))return null;n=Oe(n)}return null}function Ft(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function Le(e){return["html","body","#document"].includes(Ae(e))}function re(e){return ee(e).getComputedStyle(e)}function lt(e){return se(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function Oe(e){if(Ae(e)==="html")return e;const n=e.assignedSlot||e.parentNode||qt(e)&&e.host||pe(e);return qt(n)?n.host:n}function wn(e){const n=Oe(e);return Le(n)?e.ownerDocument?e.ownerDocument.body:e.body:fe(n)&&Ve(n)?n:wn(n)}function We(e,n,t){var o;n===void 0&&(n=[]),t===void 0&&(t=!0);const a=wn(e),s=a===((o=e.ownerDocument)==null?void 0:o.body),i=ee(a);if(s){const l=Ot(i);return n.concat(i,i.visualViewport||[],Ve(a)?a:[],l&&t?We(l):[])}return n.concat(a,We(a,[],t))}function Ot(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function _n(e){const n=re(e);let t=parseFloat(n.width)||0,o=parseFloat(n.height)||0;const a=fe(e),s=a?e.offsetWidth:t,i=a?e.offsetHeight:o,l=Xe(t)!==s||Xe(o)!==i;return l&&(t=s,o=i),{width:t,height:o,$:l}}function It(e){return se(e)?e:e.contextElement}function Me(e){const n=It(e);if(!fe(n))return de(1);const t=n.getBoundingClientRect(),{width:o,height:a,$:s}=_n(n);let i=(s?Xe(t.width):t.width)/o,l=(s?Xe(t.height):t.height)/a;return(!i||!Number.isFinite(i))&&(i=1),(!l||!Number.isFinite(l))&&(l=1),{x:i,y:l}}const Uo=de(0);function xn(e){const n=ee(e);return!Ft()||!n.visualViewport?Uo:{x:n.visualViewport.offsetLeft,y:n.visualViewport.offsetTop}}function Go(e,n,t){return n===void 0&&(n=!1),!t||n&&t!==ee(e)?!1:n}function $e(e,n,t,o){n===void 0&&(n=!1),t===void 0&&(t=!1);const a=e.getBoundingClientRect(),s=It(e);let i=de(1);n&&(o?se(o)&&(i=Me(o)):i=Me(e));const l=Go(s,t,o)?xn(s):de(0);let d=(a.left+l.x)/i.x,f=(a.top+l.y)/i.y,u=a.width/i.x,c=a.height/i.y;if(s){const p=ee(s),h=o&&se(o)?ee(o):o;let g=p,_=Ot(g);for(;_&&o&&h!==g;){const x=Me(_),k=_.getBoundingClientRect(),$=re(_),B=k.left+(_.clientLeft+parseFloat($.paddingLeft))*x.x,D=k.top+(_.clientTop+parseFloat($.paddingTop))*x.y;d*=x.x,f*=x.y,u*=x.x,c*=x.y,d+=B,f+=D,g=ee(_),_=Ot(g)}}return Qe({width:u,height:c,x:d,y:f})}function zt(e,n){const t=lt(e).scrollLeft;return n?n.left+t:$e(pe(e)).left+t}function Cn(e,n,t){t===void 0&&(t=!1);const o=e.getBoundingClientRect(),a=o.left+n.scrollLeft-(t?0:zt(e,o)),s=o.top+n.scrollTop;return{x:a,y:s}}function qo(e){let{elements:n,rect:t,offsetParent:o,strategy:a}=e;const s=a==="fixed",i=pe(o),l=n?it(n.floating):!1;if(o===i||l&&s)return t;let d={scrollLeft:0,scrollTop:0},f=de(1);const u=de(0),c=fe(o);if((c||!c&&!s)&&((Ae(o)!=="body"||Ve(i))&&(d=lt(o)),fe(o))){const h=$e(o);f=Me(o),u.x=h.x+o.clientLeft,u.y=h.y+o.clientTop}const p=i&&!c&&!s?Cn(i,d,!0):de(0);return{width:t.width*f.x,height:t.height*f.y,x:t.x*f.x-d.scrollLeft*f.x+u.x+p.x,y:t.y*f.y-d.scrollTop*f.y+u.y+p.y}}function Yo(e){return Array.from(e.getClientRects())}function Xo(e){const n=pe(e),t=lt(e),o=e.ownerDocument.body,a=J(n.scrollWidth,n.clientWidth,o.scrollWidth,o.clientWidth),s=J(n.scrollHeight,n.clientHeight,o.scrollHeight,o.clientHeight);let i=-t.scrollLeft+zt(e);const l=-t.scrollTop;return re(o).direction==="rtl"&&(i+=J(n.clientWidth,o.clientWidth)-a),{width:a,height:s,x:i,y:l}}function Zo(e,n){const t=ee(e),o=pe(e),a=t.visualViewport;let s=o.clientWidth,i=o.clientHeight,l=0,d=0;if(a){s=a.width,i=a.height;const f=Ft();(!f||f&&n==="fixed")&&(l=a.offsetLeft,d=a.offsetTop)}return{width:s,height:i,x:l,y:d}}function Qo(e,n){const t=$e(e,!0,n==="fixed"),o=t.top+e.clientTop,a=t.left+e.clientLeft,s=fe(e)?Me(e):de(1),i=e.clientWidth*s.x,l=e.clientHeight*s.y,d=a*s.x,f=o*s.y;return{width:i,height:l,x:d,y:f}}function Yt(e,n,t){let o;if(n==="viewport")o=Zo(e,t);else if(n==="document")o=Xo(pe(e));else if(se(n))o=Qo(n,t);else{const a=xn(e);o={x:n.x-a.x,y:n.y-a.y,width:n.width,height:n.height}}return Qe(o)}function On(e,n){const t=Oe(e);return t===n||!se(t)||Le(t)?!1:re(t).position==="fixed"||On(t,n)}function Jo(e,n){const t=n.get(e);if(t)return t;let o=We(e,[],!1).filter(l=>se(l)&&Ae(l)!=="body"),a=null;const s=re(e).position==="fixed";let i=s?Oe(e):e;for(;se(i)&&!Le(i);){const l=re(i),d=Rt(i);!d&&l.position==="fixed"&&(a=null),(s?!d&&!a:!d&&l.position==="static"&&!!a&&["absolute","fixed"].includes(a.position)||Ve(i)&&!d&&On(e,i))?o=o.filter(u=>u!==i):a=l,i=Oe(i)}return n.set(e,o),o}function ea(e){let{element:n,boundary:t,rootBoundary:o,strategy:a}=e;const i=[...t==="clippingAncestors"?it(n)?[]:Jo(n,this._c):[].concat(t),o],l=i[0],d=i.reduce((f,u)=>{const c=Yt(n,u,a);return f.top=J(c.top,f.top),f.right=Ce(c.right,f.right),f.bottom=Ce(c.bottom,f.bottom),f.left=J(c.left,f.left),f},Yt(n,l,a));return{width:d.right-d.left,height:d.bottom-d.top,x:d.left,y:d.top}}function ta(e){const{width:n,height:t}=_n(e);return{width:n,height:t}}function na(e,n,t){const o=fe(n),a=pe(n),s=t==="fixed",i=$e(e,!0,s,n);let l={scrollLeft:0,scrollTop:0};const d=de(0);function f(){d.x=zt(a)}if(o||!o&&!s)if((Ae(n)!=="body"||Ve(a))&&(l=lt(n)),o){const h=$e(n,!0,s,n);d.x=h.x+n.clientLeft,d.y=h.y+n.clientTop}else a&&f();s&&!o&&a&&f();const u=a&&!o&&!s?Cn(a,l):de(0),c=i.left+l.scrollLeft-d.x-u.x,p=i.top+l.scrollTop-d.y-u.y;return{x:c,y:p,width:i.width,height:i.height}}function ht(e){return re(e).position==="static"}function Xt(e,n){if(!fe(e)||re(e).position==="fixed")return null;if(n)return n(e);let t=e.offsetParent;return pe(e)===t&&(t=t.ownerDocument.body),t}function kn(e,n){const t=ee(e);if(it(e))return t;if(!fe(e)){let a=Oe(e);for(;a&&!Le(a);){if(se(a)&&!ht(a))return a;a=Oe(a)}return t}let o=Xt(e,n);for(;o&&Vo(o)&&ht(o);)o=Xt(o,n);return o&&Le(o)&&ht(o)&&!Rt(o)?t:o||jo(e)||t}const oa=async function(e){const n=this.getOffsetParent||kn,t=this.getDimensions,o=await t(e.floating);return{reference:na(e.reference,await n(e.floating),e.strategy),floating:{x:0,y:0,width:o.width,height:o.height}}};function aa(e){return re(e).direction==="rtl"}const sa={convertOffsetParentRelativeRectToViewportRelativeRect:qo,getDocumentElement:pe,getClippingRect:ea,getOffsetParent:kn,getElementRects:oa,getClientRects:Yo,getDimensions:ta,getScale:Me,isElement:se,isRTL:aa};function $n(e,n){return e.x===n.x&&e.y===n.y&&e.width===n.width&&e.height===n.height}function ra(e,n){let t=null,o;const a=pe(e);function s(){var l;clearTimeout(o),(l=t)==null||l.disconnect(),t=null}function i(l,d){l===void 0&&(l=!1),d===void 0&&(d=1),s();const f=e.getBoundingClientRect(),{left:u,top:c,width:p,height:h}=f;if(l||n(),!p||!h)return;const g=je(c),_=je(a.clientWidth-(u+p)),x=je(a.clientHeight-(c+h)),k=je(u),B={rootMargin:-g+"px "+-_+"px "+-x+"px "+-k+"px",threshold:J(0,Ce(1,d))||1};let D=!0;function L(j){const T=j[0].intersectionRatio;if(T!==d){if(!D)return i();T?i(!1,T):o=setTimeout(()=>{i(!1,1e-7)},1e3)}T===1&&!$n(f,e.getBoundingClientRect())&&i(),D=!1}try{t=new IntersectionObserver(L,{...B,root:a.ownerDocument})}catch{t=new IntersectionObserver(L,B)}t.observe(e)}return i(!0),s}function ia(e,n,t,o){o===void 0&&(o={});const{ancestorScroll:a=!0,ancestorResize:s=!0,elementResize:i=typeof ResizeObserver=="function",layoutShift:l=typeof IntersectionObserver=="function",animationFrame:d=!1}=o,f=It(e),u=a||s?[...f?We(f):[],...We(n)]:[];u.forEach(k=>{a&&k.addEventListener("scroll",t,{passive:!0}),s&&k.addEventListener("resize",t)});const c=f&&l?ra(f,t):null;let p=-1,h=null;i&&(h=new ResizeObserver(k=>{let[$]=k;$&&$.target===f&&h&&(h.unobserve(n),cancelAnimationFrame(p),p=requestAnimationFrame(()=>{var B;(B=h)==null||B.observe(n)})),t()}),f&&!d&&h.observe(f),h.observe(n));let g,_=d?$e(e):null;d&&x();function x(){const k=$e(e);_&&!$n(_,k)&&t(),_=k,g=requestAnimationFrame(x)}return t(),()=>{var k;u.forEach($=>{a&&$.removeEventListener("scroll",t),s&&$.removeEventListener("resize",t)}),c==null||c(),(k=h)==null||k.disconnect(),h=null,d&&cancelAnimationFrame(g)}}const la=Ho,ua=No,Zt=Fo,da=Ko,ca=Io,fa=Ro,pa=Wo,ma=(e,n,t)=>{const o=new Map,a={platform:sa,...t},s={...a.platform,_c:o};return Lo(e,n,{...a,platform:s})};function ha(e){return e!=null&&typeof e=="object"&&"$el"in e}function kt(e){if(ha(e)){const n=e.$el;return Lt(n)&&Ae(n)==="#comment"?null:n}return e}function Se(e){return typeof e=="function"?e():r(e)}function ga(e){return{name:"arrow",options:e,fn(n){const t=kt(Se(e.element));return t==null?{}:fa({element:t,padding:e.padding}).fn(n)}}}function Bn(e){return typeof window>"u"?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function Qt(e,n){const t=Bn(e);return Math.round(n*t)/t}function ya(e,n,t){t===void 0&&(t={});const o=t.whileElementsMounted,a=A(()=>{var T;return(T=Se(t.open))!=null?T:!0}),s=A(()=>Se(t.middleware)),i=A(()=>{var T;return(T=Se(t.placement))!=null?T:"bottom"}),l=A(()=>{var T;return(T=Se(t.strategy))!=null?T:"absolute"}),d=A(()=>{var T;return(T=Se(t.transform))!=null?T:!0}),f=A(()=>kt(e.value)),u=A(()=>kt(n.value)),c=E(0),p=E(0),h=E(l.value),g=E(i.value),_=oo({}),x=E(!1),k=A(()=>{const T={position:h.value,left:"0",top:"0"};if(!u.value)return T;const H=Qt(u.value,c.value),C=Qt(u.value,p.value);return d.value?{...T,transform:"translate("+H+"px, "+C+"px)",...Bn(u.value)>=1.5&&{willChange:"transform"}}:{position:h.value,left:H+"px",top:C+"px"}});let $;function B(){if(f.value==null||u.value==null)return;const T=a.value;ma(f.value,u.value,{middleware:s.value,placement:i.value,strategy:l.value}).then(H=>{c.value=H.x,p.value=H.y,h.value=H.strategy,g.value=H.placement,_.value=H.middlewareData,x.value=T!==!1})}function D(){typeof $=="function"&&($(),$=void 0)}function L(){if(D(),o===void 0){B();return}if(f.value!=null&&u.value!=null){$=o(f.value,u.value,B);return}}function j(){a.value||(x.value=!1)}return _e([s,i,l,a],B,{flush:"sync"}),_e([f,u],L,{flush:"sync"}),_e(a,j,{flush:"sync"}),ao()&&so(D),{x:De(c),y:De(p),strategy:De(h),placement:De(g),middlewareData:De(_),isPositioned:De(x),floatingStyles:k,update:B}}const va=["INPUT","TEXTAREA"];function ba(e,n,t,o={}){if(!n||o.enableIgnoredElement&&va.includes(n.nodeName))return null;const{arrowKeyOptions:a="both",attributeName:s="[data-reka-collection-item]",itemsArray:i=[],loop:l=!0,dir:d="ltr",preventScroll:f=!0,focus:u=!1}=o,[c,p,h,g,_,x]=[e.key==="ArrowRight",e.key==="ArrowLeft",e.key==="ArrowUp",e.key==="ArrowDown",e.key==="Home",e.key==="End"],k=h||g,$=c||p;if(!_&&!x&&(!k&&!$||a==="vertical"&&$||a==="horizontal"&&k))return null;const B=t?Array.from(t.querySelectorAll(s)):i;if(!B.length)return null;f&&e.preventDefault();let D=null;return $||k?D=Pn(B,n,{goForward:k?g:d==="ltr"?c:p,loop:l}):_?D=B.at(0)||null:x&&(D=B.at(-1)||null),u&&(D==null||D.focus()),D}function Pn(e,n,t,o=e.length){if(--o===0)return null;const a=e.indexOf(n),s=t.goForward?a+1:a-1;if(!t.loop&&(s<0||s>=e.length))return null;const i=(s+e.length)%e.length,l=e[i];return l?l.hasAttribute("disabled")&&l.getAttribute("disabled")!=="false"?Pn(e,l,t,o):l:null}const[ve,wa]=ie("DialogRoot"),_a=v({inheritAttrs:!1,__name:"DialogRoot",props:{open:{type:Boolean,default:void 0},defaultOpen:{type:Boolean,default:!1},modal:{type:Boolean,default:!0}},emits:["update:open"],setup(e,{emit:n}){const t=e,a=Ke(t,"open",n,{defaultValue:t.defaultOpen,passive:t.open===void 0}),s=E(),i=E(),{modal:l}=Re(t);return wa({open:a,modal:l,openModal:()=>{a.value=!0},onOpenChange:d=>{a.value=d},onOpenToggle:()=>{a.value=!a.value},contentId:"",titleId:"",descriptionId:"",triggerElement:s,contentElement:i}),(d,f)=>b(d.$slots,"default",{open:r(a),close:()=>a.value=!1})}}),xa=v({__name:"DialogClose",props:{asChild:{type:Boolean},as:{default:"button"}},setup(e){const n=e;R();const t=ve();return(o,a)=>(y(),w(r(V),S(n,{type:o.as==="button"?"button":void 0,onClick:a[0]||(a[0]=s=>r(t).onOpenChange(!1))}),{default:m(()=>[b(o.$slots,"default")]),_:3},16,["type"]))}}),Ca="menu.itemSelect",$t=["Enter"," "],Oa=["ArrowDown","PageUp","Home"],An=["ArrowUp","PageDown","End"],ka=[...Oa,...An];[...$t],[...$t];function Dn(e){return e?"open":"closed"}function $a(e){const n=te();for(const t of e)if(t===n||(t.focus(),te()!==n))return}function Ba(e,n){const{x:t,y:o}=e;let a=!1;for(let s=0,i=n.length-1;s<n.length;i=s++){const l=n[s].x,d=n[s].y,f=n[i].x,u=n[i].y;d>o!=u>o&&t<(f-l)*(o-d)/(u-d)+l&&(a=!a)}return a}function Pa(e,n){if(!n)return!1;const t={x:e.clientX,y:e.clientY};return Ba(t,n)}function Bt(e){return e.pointerType==="mouse"}const Aa=go(()=>E([]));function Da(){const e=Aa();return{add(n){const t=e.value[0];n!==t&&(t==null||t.pause()),e.value=Jt(e.value,n),e.value.unshift(n)},remove(n){var t;e.value=Jt(e.value,n),(t=e.value[0])==null||t.resume()}}}function Jt(e,n){const t=[...e],o=t.indexOf(n);return o!==-1&&t.splice(o,1),t}function Ea(e){return e.filter(n=>n.tagName!=="A")}const gt="focusScope.autoFocusOnMount",yt="focusScope.autoFocusOnUnmount",en={bubbles:!1,cancelable:!0};function Sa(e,{select:n=!1}={}){const t=te();for(const o of e)if(we(o,{select:n}),te()!==t)return!0}function Ma(e){const n=En(e),t=tn(n,e),o=tn(n.reverse(),e);return[t,o]}function En(e){const n=[],t=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:o=>{const a=o.tagName==="INPUT"&&o.type==="hidden";return o.disabled||o.hidden||a?NodeFilter.FILTER_SKIP:o.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;t.nextNode();)n.push(t.currentNode);return n}function tn(e,n){for(const t of e)if(!Ta(t,{upTo:n}))return t}function Ta(e,{upTo:n}){if(getComputedStyle(e).visibility==="hidden")return!0;for(;e;){if(n!==void 0&&e===n)return!1;if(getComputedStyle(e).display==="none")return!0;e=e.parentElement}return!1}function La(e){return e instanceof HTMLInputElement&&"select"in e}function we(e,{select:n=!1}={}){if(e&&e.focus){const t=te();e.focus({preventScroll:!0}),e!==t&&La(e)&&n&&e.select()}}const Sn=v({__name:"FocusScope",props:{loop:{type:Boolean,default:!1},trapped:{type:Boolean,default:!1},asChild:{type:Boolean},as:{}},emits:["mountAutoFocus","unmountAutoFocus"],setup(e,{emit:n}){const t=e,o=n,{currentRef:a,currentElement:s}=R(),i=E(null),l=Da(),d=dn({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}});ne(u=>{if(!Pe)return;const c=s.value;if(!t.trapped)return;function p(x){if(d.paused||!c)return;const k=x.target;c.contains(k)?i.value=k:we(i.value,{select:!0})}function h(x){if(d.paused||!c)return;const k=x.relatedTarget;k!==null&&(c.contains(k)||we(i.value,{select:!0}))}function g(x){c.contains(i.value)||we(c)}document.addEventListener("focusin",p),document.addEventListener("focusout",h);const _=new MutationObserver(g);c&&_.observe(c,{childList:!0,subtree:!0}),u(()=>{document.removeEventListener("focusin",p),document.removeEventListener("focusout",h),_.disconnect()})}),ne(async u=>{const c=s.value;if(await ae(),!c)return;l.add(d);const p=te();if(!c.contains(p)){const g=new CustomEvent(gt,en);c.addEventListener(gt,_=>o("mountAutoFocus",_)),c.dispatchEvent(g),g.defaultPrevented||(Sa(Ea(En(c)),{select:!0}),te()===p&&we(c))}u(()=>{c.removeEventListener(gt,x=>o("mountAutoFocus",x));const g=new CustomEvent(yt,en),_=x=>{o("unmountAutoFocus",x)};c.addEventListener(yt,_),c.dispatchEvent(g),setTimeout(()=>{g.defaultPrevented||we(p??document.body,{select:!0}),c.removeEventListener(yt,_),l.remove(d)},0)})});function f(u){if(!t.loop&&!t.trapped||d.paused)return;const c=u.key==="Tab"&&!u.altKey&&!u.ctrlKey&&!u.metaKey,p=te();if(c&&p){const h=u.currentTarget,[g,_]=Ma(h);g&&_?!u.shiftKey&&p===_?(u.preventDefault(),t.loop&&we(g,{select:!0})):u.shiftKey&&p===g&&(u.preventDefault(),t.loop&&we(_,{select:!0})):p===h&&u.preventDefault()}}return(u,c)=>(y(),w(r(V),{ref_key:"currentRef",ref:a,tabindex:"-1","as-child":u.asChild,as:u.as,onKeydown:f},{default:m(()=>[b(u.$slots,"default")]),_:3},8,["as-child","as"]))}});function Mn(e,n,t){const o=t.originalEvent.target,a=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:t});n&&o.addEventListener(e,n,{once:!0}),o.dispatchEvent(a)}const Ra="dismissableLayer.pointerDownOutside",Fa="dismissableLayer.focusOutside";function Tn(e,n){const t=n.closest("[data-dismissable-layer]"),o=e.dataset.dismissableLayer===""?e:e.querySelector("[data-dismissable-layer]"),a=Array.from(e.ownerDocument.querySelectorAll("[data-dismissable-layer]"));return!!(t&&(o===t||a.indexOf(o)<a.indexOf(t)))}function Ia(e,n,t=!0){var i;const o=((i=n==null?void 0:n.value)==null?void 0:i.ownerDocument)??(globalThis==null?void 0:globalThis.document),a=E(!1),s=E(()=>{});return ne(l=>{if(!Pe||!Ie(t))return;const d=async u=>{const c=u.target;if(!(!(n!=null&&n.value)||!c)){if(Tn(n.value,c)){a.value=!1;return}if(u.target&&!a.value){let p=function(){Mn(Ra,e,h)};const h={originalEvent:u};u.pointerType==="touch"?(o.removeEventListener("click",s.value),s.value=p,o.addEventListener("click",s.value,{once:!0})):p()}else o.removeEventListener("click",s.value);a.value=!1}},f=window.setTimeout(()=>{o.addEventListener("pointerdown",d)},0);l(()=>{window.clearTimeout(f),o.removeEventListener("pointerdown",d),o.removeEventListener("click",s.value)})}),{onPointerDownCapture:()=>{Ie(t)&&(a.value=!0)}}}function za(e,n,t=!0){var s;const o=((s=n==null?void 0:n.value)==null?void 0:s.ownerDocument)??(globalThis==null?void 0:globalThis.document),a=E(!1);return ne(i=>{if(!Pe||!Ie(t))return;const l=async d=>{if(!(n!=null&&n.value))return;await ae(),await ae();const f=d.target;!n.value||!f||Tn(n.value,f)||d.target&&!a.value&&Mn(Fa,e,{originalEvent:d})};o.addEventListener("focusin",l),i(()=>o.removeEventListener("focusin",l))}),{onFocusCapture:()=>{Ie(t)&&(a.value=!0)},onBlurCapture:()=>{Ie(t)&&(a.value=!1)}}}const me=dn({layersRoot:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),Ht=v({__name:"DismissableLayer",props:{disableOutsidePointerEvents:{type:Boolean,default:!1},asChild:{type:Boolean},as:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","dismiss"],setup(e,{emit:n}){const t=e,o=n,{forwardRef:a,currentElement:s}=R(),i=A(()=>{var g;return((g=s.value)==null?void 0:g.ownerDocument)??globalThis.document}),l=A(()=>me.layersRoot),d=A(()=>s.value?Array.from(l.value).indexOf(s.value):-1),f=A(()=>me.layersWithOutsidePointerEventsDisabled.size>0),u=A(()=>{const g=Array.from(l.value),[_]=[...me.layersWithOutsidePointerEventsDisabled].slice(-1),x=g.indexOf(_);return d.value>=x}),c=Ia(async g=>{const _=[...me.branches].some(x=>x==null?void 0:x.contains(g.target));!u.value||_||(o("pointerDownOutside",g),o("interactOutside",g),await ae(),g.defaultPrevented||o("dismiss"))},s),p=za(g=>{[...me.branches].some(x=>x==null?void 0:x.contains(g.target))||(o("focusOutside",g),o("interactOutside",g),g.defaultPrevented||o("dismiss"))},s);yo("Escape",g=>{d.value===l.value.size-1&&(o("escapeKeyDown",g),g.defaultPrevented||o("dismiss"))});let h;return ne(g=>{s.value&&(t.disableOutsidePointerEvents&&(me.layersWithOutsidePointerEventsDisabled.size===0&&(h=i.value.body.style.pointerEvents,i.value.body.style.pointerEvents="none"),me.layersWithOutsidePointerEventsDisabled.add(s.value)),l.value.add(s.value),g(()=>{t.disableOutsidePointerEvents&&me.layersWithOutsidePointerEventsDisabled.size===1&&(i.value.body.style.pointerEvents=h)}))}),ne(g=>{g(()=>{s.value&&(l.value.delete(s.value),me.layersWithOutsidePointerEventsDisabled.delete(s.value))})}),(g,_)=>(y(),w(r(V),{ref:r(a),"as-child":g.asChild,as:g.as,"data-dismissable-layer":"",style:Je({pointerEvents:f.value?u.value?"auto":"none":void 0}),onFocusCapture:r(p).onFocusCapture,onBlurCapture:r(p).onBlurCapture,onPointerdownCapture:r(c).onPointerDownCapture},{default:m(()=>[b(g.$slots,"default")]),_:3},8,["as-child","as","style","onFocusCapture","onBlurCapture","onPointerdownCapture"]))}}),Ln=v({__name:"DialogContentImpl",props:{forceMount:{type:Boolean},trapFocus:{type:Boolean},disableOutsidePointerEvents:{type:Boolean},asChild:{type:Boolean},as:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","openAutoFocus","closeAutoFocus"],setup(e,{emit:n}){const t=e,o=n,a=ve(),{forwardRef:s,currentElement:i}=R();return a.titleId||(a.titleId=He(void 0,"reka-dialog-title")),a.descriptionId||(a.descriptionId=He(void 0,"reka-dialog-description")),Be(()=>{a.contentElement=i,te()!==document.body&&(a.triggerElement.value=te())}),(l,d)=>(y(),w(r(Sn),{"as-child":"",loop:"",trapped:t.trapFocus,onMountAutoFocus:d[5]||(d[5]=f=>o("openAutoFocus",f)),onUnmountAutoFocus:d[6]||(d[6]=f=>o("closeAutoFocus",f))},{default:m(()=>[O(r(Ht),S({id:r(a).contentId,ref:r(s),as:l.as,"as-child":l.asChild,"disable-outside-pointer-events":l.disableOutsidePointerEvents,role:"dialog","aria-describedby":r(a).descriptionId,"aria-labelledby":r(a).titleId,"data-state":r(Dn)(r(a).open.value)},l.$attrs,{onDismiss:d[0]||(d[0]=f=>r(a).onOpenChange(!1)),onEscapeKeyDown:d[1]||(d[1]=f=>o("escapeKeyDown",f)),onFocusOutside:d[2]||(d[2]=f=>o("focusOutside",f)),onInteractOutside:d[3]||(d[3]=f=>o("interactOutside",f)),onPointerDownOutside:d[4]||(d[4]=f=>o("pointerDownOutside",f))}),{default:m(()=>[b(l.$slots,"default")]),_:3},16,["id","as","as-child","disable-outside-pointer-events","aria-describedby","aria-labelledby","data-state"])]),_:3},8,["trapped"]))}});var Ha=function(e){if(typeof document>"u")return null;var n=Array.isArray(e)?e[0]:e;return n.ownerDocument.body},Ee=new WeakMap,Ue=new WeakMap,Ge={},vt=0,Rn=function(e){return e&&(e.host||Rn(e.parentNode))},Na=function(e,n){return n.map(function(t){if(e.contains(t))return t;var o=Rn(t);return o&&e.contains(o)?o:(console.error("aria-hidden",t,"in not contained inside",e,". Doing nothing"),null)}).filter(function(t){return!!t})},Wa=function(e,n,t,o){var a=Na(n,Array.isArray(e)?e:[e]);Ge[t]||(Ge[t]=new WeakMap);var s=Ge[t],i=[],l=new Set,d=new Set(a),f=function(c){!c||l.has(c)||(l.add(c),f(c.parentNode))};a.forEach(f);var u=function(c){!c||d.has(c)||Array.prototype.forEach.call(c.children,function(p){if(l.has(p))u(p);else try{var h=p.getAttribute(o),g=h!==null&&h!=="false",_=(Ee.get(p)||0)+1,x=(s.get(p)||0)+1;Ee.set(p,_),s.set(p,x),i.push(p),_===1&&g&&Ue.set(p,!0),x===1&&p.setAttribute(t,"true"),g||p.setAttribute(o,"true")}catch(k){console.error("aria-hidden: cannot operate on ",p,k)}})};return u(n),l.clear(),vt++,function(){i.forEach(function(c){var p=Ee.get(c)-1,h=s.get(c)-1;Ee.set(c,p),s.set(c,h),p||(Ue.has(c)||c.removeAttribute(o),Ue.delete(c)),h||c.removeAttribute(t)}),vt--,vt||(Ee=new WeakMap,Ee=new WeakMap,Ue=new WeakMap,Ge={})}},Ka=function(e,n,t){t===void 0&&(t="data-aria-hidden");var o=Array.from(Array.isArray(e)?e:[e]),a=Ha(e);return a?(o.push.apply(o,Array.from(a.querySelectorAll("[aria-live], script"))),Wa(o,a,t,"aria-hidden")):function(){return null}};function Fn(e){let n;_e(()=>mn(e),t=>{t?n=Ka(t):n&&n()}),Dt(()=>{n&&n()})}const Va=v({__name:"DialogContentModal",props:{forceMount:{type:Boolean},trapFocus:{type:Boolean},disableOutsidePointerEvents:{type:Boolean},asChild:{type:Boolean},as:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","openAutoFocus","closeAutoFocus"],setup(e,{emit:n}){const t=e,o=n,a=ve(),s=ot(o),{forwardRef:i,currentElement:l}=R();return Fn(l),(d,f)=>(y(),w(Ln,S({...t,...r(s)},{ref:r(i),"trap-focus":r(a).open.value,"disable-outside-pointer-events":!0,onCloseAutoFocus:f[0]||(f[0]=u=>{var c;u.defaultPrevented||(u.preventDefault(),(c=r(a).triggerElement.value)==null||c.focus())}),onPointerDownOutside:f[1]||(f[1]=u=>{const c=u.detail.originalEvent,p=c.button===0&&c.ctrlKey===!0;(c.button===2||p)&&u.preventDefault()}),onFocusOutside:f[2]||(f[2]=u=>{u.preventDefault()})}),{default:m(()=>[b(d.$slots,"default")]),_:3},16,["trap-focus"]))}}),ja=v({__name:"DialogContentNonModal",props:{forceMount:{type:Boolean},trapFocus:{type:Boolean},disableOutsidePointerEvents:{type:Boolean},asChild:{type:Boolean},as:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","openAutoFocus","closeAutoFocus"],setup(e,{emit:n}){const t=e,a=ot(n);R();const s=ve(),i=E(!1),l=E(!1);return(d,f)=>(y(),w(Ln,S({...t,...r(a)},{"trap-focus":!1,"disable-outside-pointer-events":!1,onCloseAutoFocus:f[0]||(f[0]=u=>{var c;u.defaultPrevented||(i.value||(c=r(s).triggerElement.value)==null||c.focus(),u.preventDefault()),i.value=!1,l.value=!1}),onInteractOutside:f[1]||(f[1]=u=>{var h;u.defaultPrevented||(i.value=!0,u.detail.originalEvent.type==="pointerdown"&&(l.value=!0));const c=u.target;((h=r(s).triggerElement.value)==null?void 0:h.contains(c))&&u.preventDefault(),u.detail.originalEvent.type==="focusin"&&l.value&&u.preventDefault()})}),{default:m(()=>[b(d.$slots,"default")]),_:3},16))}}),Ua=v({__name:"DialogContent",props:{forceMount:{type:Boolean},disableOutsidePointerEvents:{type:Boolean},asChild:{type:Boolean},as:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","openAutoFocus","closeAutoFocus"],setup(e,{emit:n}){const t=e,o=n,a=ve(),s=ot(o),{forwardRef:i}=R();return(l,d)=>(y(),w(r(at),{present:l.forceMount||r(a).open.value},{default:m(()=>[r(a).modal.value?(y(),w(Va,S({key:0,ref:r(i)},{...t,...r(s),...l.$attrs}),{default:m(()=>[b(l.$slots,"default")]),_:3},16)):(y(),w(ja,S({key:1,ref:r(i)},{...t,...r(s),...l.$attrs}),{default:m(()=>[b(l.$slots,"default")]),_:3},16))]),_:3},8,["present"]))}}),Ga=v({__name:"DialogDescription",props:{asChild:{type:Boolean},as:{default:"p"}},setup(e){const n=e;R();const t=ve();return(o,a)=>(y(),w(r(V),S(n,{id:r(t).descriptionId}),{default:m(()=>[b(o.$slots,"default")]),_:3},16,["id"]))}});function bt(e){if(e===null||typeof e!="object")return!1;const n=Object.getPrototypeOf(e);return n!==null&&n!==Object.prototype&&Object.getPrototypeOf(n)!==null||Symbol.iterator in e?!1:Symbol.toStringTag in e?Object.prototype.toString.call(e)==="[object Module]":!0}function Pt(e,n,t=".",o){if(!bt(n))return Pt(e,{},t,o);const a=Object.assign({},n);for(const s in e){if(s==="__proto__"||s==="constructor")continue;const i=e[s];i!=null&&(o&&o(a,s,i,t)||(Array.isArray(i)&&Array.isArray(a[s])?a[s]=[...i,...a[s]]:bt(i)&&bt(a[s])?a[s]=Pt(i,a[s],(t?`${t}.`:"")+s.toString(),o):a[s]=i))}return a}function qa(e){return(...n)=>n.reduce((t,o)=>Pt(t,o,"",e),{})}const Ya=qa(),Xa=hn(()=>{const e=E(new Map),n=E(),t=A(()=>{for(const i of e.value.values())if(i)return!0;return!1}),o=Co({scrollBody:E(!0)});let a=null;const s=()=>{document.body.style.paddingRight="",document.body.style.marginRight="",document.body.style.pointerEvents="",document.documentElement.style.removeProperty("--scrollbar-width"),document.body.style.overflow=n.value??"",Vt&&(a==null||a()),n.value=void 0};return _e(t,(i,l)=>{var c;if(!Pe)return;if(!i){l&&s();return}n.value===void 0&&(n.value=document.body.style.overflow);const d=window.innerWidth-document.documentElement.clientWidth,f={padding:d,margin:0},u=(c=o.scrollBody)!=null&&c.value?typeof o.scrollBody.value=="object"?Ya({padding:o.scrollBody.value.padding===!0?d:o.scrollBody.value.padding,margin:o.scrollBody.value.margin===!0?d:o.scrollBody.value.margin},f):f:{padding:0,margin:0};d>0&&(document.body.style.paddingRight=typeof u.padding=="number"?`${u.padding}px`:String(u.padding),document.body.style.marginRight=typeof u.margin=="number"?`${u.margin}px`:String(u.margin),document.documentElement.style.setProperty("--scrollbar-width",`${d}px`),document.body.style.overflow="hidden"),Vt&&(a=Te(document,"touchmove",p=>Za(p),{passive:!1})),ae(()=>{document.body.style.pointerEvents="none",document.body.style.overflow="hidden"})},{immediate:!0,flush:"sync"}),e});function In(e){const n=Math.random().toString(36).substring(2,7),t=Xa();t.value.set(n,e??!1);const o=A({get:()=>t.value.get(n)??!1,set:a=>t.value.set(n,a)});return vo(()=>{t.value.delete(n)}),o}function zn(e){const n=window.getComputedStyle(e);if(n.overflowX==="scroll"||n.overflowY==="scroll"||n.overflowX==="auto"&&e.clientWidth<e.scrollWidth||n.overflowY==="auto"&&e.clientHeight<e.scrollHeight)return!0;{const t=e.parentNode;return!(t instanceof Element)||t.tagName==="BODY"?!1:zn(t)}}function Za(e){const n=e||window.event,t=n.target;return t instanceof Element&&zn(t)?!1:n.touches.length>1?!0:(n.preventDefault&&n.cancelable&&n.preventDefault(),!1)}const Qa=v({__name:"DialogOverlayImpl",props:{asChild:{type:Boolean},as:{}},setup(e){const n=ve();return In(!0),R(),(t,o)=>(y(),w(r(V),{as:t.as,"as-child":t.asChild,"data-state":r(n).open.value?"open":"closed",style:{"pointer-events":"auto"}},{default:m(()=>[b(t.$slots,"default")]),_:3},8,["as","as-child","data-state"]))}}),Ja=v({__name:"DialogOverlay",props:{forceMount:{type:Boolean},asChild:{type:Boolean},as:{}},setup(e){const n=ve(),{forwardRef:t}=R();return(o,a)=>{var s;return(s=r(n))!=null&&s.modal.value?(y(),w(r(at),{key:0,present:o.forceMount||r(n).open.value},{default:m(()=>[O(Qa,S(o.$attrs,{ref:r(t),as:o.as,"as-child":o.asChild}),{default:m(()=>[b(o.$slots,"default")]),_:3},16,["as","as-child"])]),_:3},8,["present"])):ke("",!0)}}}),Nt=v({__name:"Teleport",props:{to:{default:"body"},disabled:{type:Boolean},defer:{type:Boolean},forceMount:{type:Boolean}},setup(e){const n=bo();return(t,o)=>r(n)||t.forceMount?(y(),w(ro,{key:0,to:t.to,disabled:t.disabled,defer:t.defer},[b(t.$slots,"default")],8,["to","disabled","defer"])):ke("",!0)}}),es=v({__name:"DialogTitle",props:{asChild:{type:Boolean},as:{default:"h2"}},setup(e){const n=e,t=ve();return R(),(o,a)=>(y(),w(r(V),S(n,{id:r(t).titleId}),{default:m(()=>[b(o.$slots,"default")]),_:3},16,["id"]))}}),[Hn,ts]=ie("AvatarRoot"),ns=v({__name:"AvatarRoot",props:{asChild:{type:Boolean},as:{default:"span"}},setup(e){return R(),ts({imageLoadingStatus:E("idle")}),(n,t)=>(y(),w(r(V),{"as-child":n.asChild,as:n.as},{default:m(()=>[b(n.$slots,"default")]),_:3},8,["as-child","as"]))}}),os=v({__name:"AvatarFallback",props:{delayMs:{},asChild:{type:Boolean},as:{default:"span"}},setup(e){const n=e,t=Hn();R();const o=E(n.delayMs===void 0);return ne(a=>{if(n.delayMs&&Pe){const s=window.setTimeout(()=>{o.value=!0},n.delayMs);a(()=>{window.clearTimeout(s)})}}),(a,s)=>o.value&&r(t).imageLoadingStatus.value!=="loaded"?(y(),w(r(V),{key:0,"as-child":a.asChild,as:a.as},{default:m(()=>[b(a.$slots,"default")]),_:3},8,["as-child","as"])):ke("",!0)}});function nn(e,n){return e?n?(e.src!==n&&(e.src=n),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}function as(e,{referrerPolicy:n,crossOrigin:t}={}){const o=E(!1),a=E(null),s=A(()=>o.value?(!a.value&&Pe&&(a.value=new window.Image),a.value):null),i=E(nn(s.value,e.value)),l=d=>()=>{o.value&&(i.value=d)};return Be(()=>{o.value=!0,ne(d=>{const f=s.value;if(!f)return;i.value=nn(f,e.value);const u=l("loaded"),c=l("error");f.addEventListener("load",u),f.addEventListener("error",c),n!=null&&n.value&&(f.referrerPolicy=n.value),typeof(t==null?void 0:t.value)=="string"&&(f.crossOrigin=t.value),d(()=>{f.removeEventListener("load",u),f.removeEventListener("error",c)})})}),Dt(()=>{o.value=!1}),i}const ss=v({__name:"AvatarImage",props:{src:{},referrerPolicy:{},crossOrigin:{},asChild:{type:Boolean},as:{default:"img"}},emits:["loadingStatusChange"],setup(e,{emit:n}){const t=e,o=n,{src:a,referrerPolicy:s,crossOrigin:i}=Re(t);R();const l=Hn(),d=as(a,{referrerPolicy:s,crossOrigin:i});return _e(d,f=>{o("loadingStatusChange",f),f!=="idle"&&(l.imageLoadingStatus.value=f)},{immediate:!0}),(f,u)=>io((y(),w(r(V),{role:"img","as-child":f.asChild,as:f.as,src:r(a),"referrer-policy":r(s)},{default:m(()=>[b(f.$slots,"default")]),_:3},8,["as-child","as","src","referrer-policy"])),[[lo,r(d)==="loaded"]])}}),[Nn,rs]=ie("PopperRoot"),Wn=v({inheritAttrs:!1,__name:"PopperRoot",setup(e){const n=E();return rs({anchor:n,onAnchorChange:t=>n.value=t}),(t,o)=>b(t.$slots,"default")}}),Kn=v({__name:"PopperAnchor",props:{reference:{},asChild:{type:Boolean},as:{}},setup(e){const n=e,{forwardRef:t,currentElement:o}=R(),a=Nn();return cn(()=>{a.onAnchorChange(n.reference??o.value)}),(s,i)=>(y(),w(r(V),{ref:r(t),as:s.as,"as-child":s.asChild},{default:m(()=>[b(s.$slots,"default")]),_:3},8,["as","as-child"]))}}),is={key:0,d:"M0 0L6 6L12 0"},ls={key:1,d:"M0 0L4.58579 4.58579C5.36683 5.36683 6.63316 5.36684 7.41421 4.58579L12 0"},us=v({__name:"Arrow",props:{width:{default:10},height:{default:5},rounded:{type:Boolean},asChild:{type:Boolean},as:{default:"svg"}},setup(e){const n=e;return R(),(t,o)=>(y(),w(r(V),S(n,{width:t.width,height:t.height,viewBox:t.asChild?void 0:"0 0 12 6",preserveAspectRatio:t.asChild?void 0:"none"}),{default:m(()=>[b(t.$slots,"default",{},()=>[t.rounded?(y(),F("path",ls)):(y(),F("path",is))])]),_:3},16,["width","height","viewBox","preserveAspectRatio"]))}});function ds(e){return e!==null}function cs(e){return{name:"transformOrigin",options:e,fn(n){var x,k,$;const{placement:t,rects:o,middlewareData:a}=n,i=((x=a.arrow)==null?void 0:x.centerOffset)!==0,l=i?0:e.arrowWidth,d=i?0:e.arrowHeight,[f,u]=At(t),c={start:"0%",center:"50%",end:"100%"}[u],p=(((k=a.arrow)==null?void 0:k.x)??0)+l/2,h=((($=a.arrow)==null?void 0:$.y)??0)+d/2;let g="",_="";return f==="bottom"?(g=i?c:`${p}px`,_=`${-d}px`):f==="top"?(g=i?c:`${p}px`,_=`${o.floating.height+d}px`):f==="right"?(g=`${-d}px`,_=i?c:`${h}px`):f==="left"&&(g=`${o.floating.width+d}px`,_=i?c:`${h}px`),{data:{x:g,y:_}}}}}function At(e){const[n,t="center"]=e.split("-");return[n,t]}function fs(e){const n=E(),t=A(()=>{var a;return((a=n.value)==null?void 0:a.width)??0}),o=A(()=>{var a;return((a=n.value)==null?void 0:a.height)??0});return Be(()=>{const a=mn(e);if(a){n.value={width:a.offsetWidth,height:a.offsetHeight};const s=new ResizeObserver(i=>{if(!Array.isArray(i)||!i.length)return;const l=i[0];let d,f;if("borderBoxSize"in l){const u=l.borderBoxSize,c=Array.isArray(u)?u[0]:u;d=c.inlineSize,f=c.blockSize}else d=a.offsetWidth,f=a.offsetHeight;n.value={width:d,height:f}});return s.observe(a,{box:"border-box"}),()=>s.unobserve(a)}else n.value=void 0}),{width:t,height:o}}const Vn={side:"bottom",sideOffset:0,align:"center",alignOffset:0,arrowPadding:0,avoidCollisions:!0,collisionBoundary:()=>[],collisionPadding:0,sticky:"partial",hideWhenDetached:!1,positionStrategy:"fixed",updatePositionStrategy:"optimized",prioritizePosition:!1},[ps,ms]=ie("PopperContent"),jn=v({inheritAttrs:!1,__name:"PopperContent",props:fn({side:{},sideOffset:{},align:{},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},positionStrategy:{},updatePositionStrategy:{},disableUpdateOnLayoutShift:{type:Boolean},prioritizePosition:{type:Boolean},reference:{},asChild:{type:Boolean},as:{}},{...Vn}),emits:["placed"],setup(e,{emit:n}){const t=e,o=n,a=Nn(),{forwardRef:s,currentElement:i}=R(),l=E(),d=E(),{width:f,height:u}=fs(d),c=A(()=>t.side+(t.align!=="center"?`-${t.align}`:"")),p=A(()=>typeof t.collisionPadding=="number"?t.collisionPadding:{top:0,right:0,bottom:0,left:0,...t.collisionPadding}),h=A(()=>Array.isArray(t.collisionBoundary)?t.collisionBoundary:[t.collisionBoundary]),g=A(()=>({padding:p.value,boundary:h.value.filter(ds),altBoundary:h.value.length>0})),_=wo(()=>[la({mainAxis:t.sideOffset+u.value,alignmentAxis:t.alignOffset}),t.prioritizePosition&&t.avoidCollisions&&Zt({...g.value}),t.avoidCollisions&&ua({mainAxis:!0,crossAxis:!!t.prioritizePosition,limiter:t.sticky==="partial"?pa():void 0,...g.value}),!t.prioritizePosition&&t.avoidCollisions&&Zt({...g.value}),da({...g.value,apply:({elements:P,rects:W,availableWidth:q,availableHeight:z})=>{const{width:Y,height:Z}=W.reference,U=P.floating.style;U.setProperty("--reka-popper-available-width",`${q}px`),U.setProperty("--reka-popper-available-height",`${z}px`),U.setProperty("--reka-popper-anchor-width",`${Y}px`),U.setProperty("--reka-popper-anchor-height",`${Z}px`)}}),d.value&&ga({element:d.value,padding:t.arrowPadding}),cs({arrowWidth:f.value,arrowHeight:u.value}),t.hideWhenDetached&&ca({strategy:"referenceHidden",...g.value})]),x=A(()=>t.reference??a.anchor.value),{floatingStyles:k,placement:$,isPositioned:B,middlewareData:D}=ya(x,l,{strategy:t.positionStrategy,placement:c,whileElementsMounted:(...P)=>ia(...P,{layoutShift:!t.disableUpdateOnLayoutShift,animationFrame:t.updatePositionStrategy==="always"}),middleware:_}),L=A(()=>At($.value)[0]),j=A(()=>At($.value)[1]);cn(()=>{B.value&&o("placed")});const T=A(()=>{var P;return((P=D.value.arrow)==null?void 0:P.centerOffset)!==0}),H=E("");ne(()=>{i.value&&(H.value=window.getComputedStyle(i.value).zIndex)});const C=A(()=>{var P;return((P=D.value.arrow)==null?void 0:P.x)??0}),M=A(()=>{var P;return((P=D.value.arrow)==null?void 0:P.y)??0});return ms({placedSide:L,onArrowChange:P=>d.value=P,arrowX:C,arrowY:M,shouldHideArrow:T}),(P,W)=>{var q,z,Y;return y(),F("div",{ref_key:"floatingRef",ref:l,"data-reka-popper-content-wrapper":"",style:Je({...r(k),transform:r(B)?r(k).transform:"translate(0, -200%)",minWidth:"max-content",zIndex:H.value,"--reka-popper-transform-origin":[(q=r(D).transformOrigin)==null?void 0:q.x,(z=r(D).transformOrigin)==null?void 0:z.y].join(" "),...((Y=r(D).hide)==null?void 0:Y.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}})},[O(r(V),S({ref:r(s)},P.$attrs,{"as-child":t.asChild,as:P.as,"data-side":L.value,"data-align":j.value,style:{animation:r(B)?void 0:"none"}}),{default:m(()=>[b(P.$slots,"default")]),_:3},16,["as-child","as","data-side","data-align","style"])],4)}}}),hs={top:"bottom",right:"left",bottom:"top",left:"right"},gs=v({inheritAttrs:!1,__name:"PopperArrow",props:{width:{},height:{},rounded:{type:Boolean},asChild:{type:Boolean},as:{default:"svg"}},setup(e){const{forwardRef:n}=R(),t=ps(),o=A(()=>hs[t.placedSide.value]);return(a,s)=>{var i,l,d,f;return y(),F("span",{ref:u=>{r(t).onArrowChange(u)},style:Je({position:"absolute",left:(i=r(t).arrowX)!=null&&i.value?`${(l=r(t).arrowX)==null?void 0:l.value}px`:void 0,top:(d=r(t).arrowY)!=null&&d.value?`${(f=r(t).arrowY)==null?void 0:f.value}px`:void 0,[o.value]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[r(t).placedSide.value],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[r(t).placedSide.value],visibility:r(t).shouldHideArrow.value?"hidden":void 0})},[O(us,S(a.$attrs,{ref:r(n),style:{display:"block"},as:a.as,"as-child":a.asChild,rounded:a.rounded,width:a.width,height:a.height}),{default:m(()=>[b(a.$slots,"default")]),_:3},16,["as","as-child","rounded","width","height"])],4)}}});function ys(e){const n=gn("",1e3);return{search:n,handleTypeaheadSearch:(a,s)=>{n.value=n.value+a;{const i=te(),l=s.map(p=>{var h,g;return{...p,textValue:((h=p.value)==null?void 0:h.textValue)??((g=p.ref.textContent)==null?void 0:g.trim())??""}}),d=l.find(p=>p.ref===i),f=l.map(p=>p.textValue),u=bs(f,n.value,d==null?void 0:d.textValue),c=l.find(p=>p.textValue===u);return c&&c.ref.focus(),c==null?void 0:c.ref}},resetTypeahead:()=>{n.value=""}}}function vs(e,n){return e.map((t,o)=>e[(n+o)%e.length])}function bs(e,n,t){const a=n.length>1&&Array.from(n).every(f=>f===n[0])?n[0]:n,s=t?e.indexOf(t):-1;let i=vs(e,Math.max(s,0));a.length===1&&(i=i.filter(f=>f!==t));const d=i.find(f=>f.toLowerCase().startsWith(a.toLowerCase()));return d!==t?d:void 0}function ws(){const e=E(!1);return Be(()=>{Te("keydown",()=>{e.value=!0},{capture:!0,passive:!0}),Te(["pointerdown","pointermove"],()=>{e.value=!1},{capture:!0,passive:!0})}),e}const _s=hn(ws),[ut,xs]=ie(["MenuRoot","MenuSub"],"MenuContext"),[Wt,Cs]=ie("MenuRoot"),Os=v({__name:"MenuRoot",props:{open:{type:Boolean,default:!1},dir:{},modal:{type:Boolean,default:!0}},emits:["update:open"],setup(e,{emit:n}){const t=e,o=n,{modal:a,dir:s}=Re(t),i=vn(s),l=Ke(t,"open",o),d=E(),f=_s();return xs({open:l,onOpenChange:u=>{l.value=u},content:d,onContentChange:u=>{d.value=u}}),Cs({onClose:()=>{l.value=!1},isUsingKeyboardRef:f,dir:i,modal:a}),(u,c)=>(y(),w(r(Wn),null,{default:m(()=>[b(u.$slots,"default")]),_:3}))}});let wt=0;function ks(){ne(e=>{if(!Pe)return;const n=document.querySelectorAll("[data-reka-focus-guard]");document.body.insertAdjacentElement("afterbegin",n[0]??on()),document.body.insertAdjacentElement("beforeend",n[1]??on()),wt++,e(()=>{wt===1&&document.querySelectorAll("[data-reka-focus-guard]").forEach(t=>t.remove()),wt--})})}function on(){const e=document.createElement("span");return e.setAttribute("data-reka-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}const[Un,$s]=ie("MenuContent"),Gn=v({__name:"MenuContentImpl",props:fn({loop:{type:Boolean},disableOutsidePointerEvents:{type:Boolean},disableOutsideScroll:{type:Boolean},trapFocus:{type:Boolean},side:{},sideOffset:{},align:{},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},positionStrategy:{},updatePositionStrategy:{},disableUpdateOnLayoutShift:{type:Boolean},prioritizePosition:{type:Boolean},reference:{},asChild:{type:Boolean},as:{}},{...Vn}),emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","entryFocus","openAutoFocus","closeAutoFocus","dismiss"],setup(e,{emit:n}){const t=e,o=n,a=ut(),s=Wt(),{trapFocus:i,disableOutsidePointerEvents:l,loop:d}=Re(t);ks(),In(l.value);const f=E(""),u=E(0),c=E(0),p=E(null),h=E("right"),g=E(0),_=E(null),x=E(),{forwardRef:k,currentElement:$}=R(),{handleTypeaheadSearch:B}=ys();_e($,C=>{a.onContentChange(C)}),Dt(()=>{window.clearTimeout(u.value)});function D(C){var P,W;return h.value===((P=p.value)==null?void 0:P.side)&&Pa(C,(W=p.value)==null?void 0:W.area)}async function L(C){var M;o("openAutoFocus",C),!C.defaultPrevented&&(C.preventDefault(),(M=$.value)==null||M.focus({preventScroll:!0}))}function j(C){var U;if(C.defaultPrevented)return;const P=C.target.closest("[data-reka-menu-content]")===C.currentTarget,W=C.ctrlKey||C.altKey||C.metaKey,q=C.key.length===1,z=ba(C,te(),$.value,{loop:d.value,arrowKeyOptions:"vertical",dir:s==null?void 0:s.dir.value,focus:!0,attributeName:"[data-reka-collection-item]:not([data-disabled])"});if(z)return z==null?void 0:z.focus();if(C.code==="Space")return;const Y=((U=x.value)==null?void 0:U.getItems())??[];if(P&&(C.key==="Tab"&&C.preventDefault(),!W&&q&&B(C.key,Y)),C.target!==$.value||!ka.includes(C.key))return;C.preventDefault();const Z=[...Y.map(Q=>Q.ref)];An.includes(C.key)&&Z.reverse(),$a(Z)}function T(C){var M,P;(P=(M=C==null?void 0:C.currentTarget)==null?void 0:M.contains)!=null&&P.call(M,C.target)||(window.clearTimeout(u.value),f.value="")}function H(C){var W;if(!Bt(C))return;const M=C.target,P=g.value!==C.clientX;if((W=C==null?void 0:C.currentTarget)!=null&&W.contains(M)&&P){const q=C.clientX>g.value?"right":"left";h.value=q,g.value=C.clientX}}return $s({onItemEnter:C=>!!D(C),onItemLeave:C=>{var M;D(C)||((M=$.value)==null||M.focus(),_.value=null)},onTriggerLeave:C=>!!D(C),searchRef:f,pointerGraceTimerRef:c,onPointerGraceIntentChange:C=>{p.value=C}}),(C,M)=>(y(),w(r(Sn),{"as-child":"",trapped:r(i),onMountAutoFocus:L,onUnmountAutoFocus:M[7]||(M[7]=P=>o("closeAutoFocus",P))},{default:m(()=>[O(r(Ht),{"as-child":"","disable-outside-pointer-events":r(l),onEscapeKeyDown:M[2]||(M[2]=P=>o("escapeKeyDown",P)),onPointerDownOutside:M[3]||(M[3]=P=>o("pointerDownOutside",P)),onFocusOutside:M[4]||(M[4]=P=>o("focusOutside",P)),onInteractOutside:M[5]||(M[5]=P=>o("interactOutside",P)),onDismiss:M[6]||(M[6]=P=>o("dismiss"))},{default:m(()=>[O(r(Oo),{ref_key:"rovingFocusGroupRef",ref:x,"current-tab-stop-id":_.value,"onUpdate:currentTabStopId":M[0]||(M[0]=P=>_.value=P),"as-child":"",orientation:"vertical",dir:r(s).dir.value,loop:r(d),onEntryFocus:M[1]||(M[1]=P=>{o("entryFocus",P),r(s).isUsingKeyboardRef.value||P.preventDefault()})},{default:m(()=>[O(r(jn),{ref:r(k),role:"menu",as:C.as,"as-child":C.asChild,"aria-orientation":"vertical","data-reka-menu-content":"","data-state":r(Dn)(r(a).open.value),dir:r(s).dir.value,side:C.side,"side-offset":C.sideOffset,align:C.align,"align-offset":C.alignOffset,"avoid-collisions":C.avoidCollisions,"collision-boundary":C.collisionBoundary,"collision-padding":C.collisionPadding,"arrow-padding":C.arrowPadding,"prioritize-position":C.prioritizePosition,"position-strategy":C.positionStrategy,"update-position-strategy":C.updatePositionStrategy,sticky:C.sticky,"hide-when-detached":C.hideWhenDetached,reference:C.reference,onKeydown:j,onBlur:T,onPointermove:H},{default:m(()=>[b(C.$slots,"default")]),_:3},8,["as","as-child","data-state","dir","side","side-offset","align","align-offset","avoid-collisions","collision-boundary","collision-padding","arrow-padding","prioritize-position","position-strategy","update-position-strategy","sticky","hide-when-detached","reference"])]),_:3},8,["current-tab-stop-id","dir","loop"])]),_:3},8,["disable-outside-pointer-events"])]),_:3},8,["trapped"]))}}),Bs=v({inheritAttrs:!1,__name:"MenuItemImpl",props:{disabled:{type:Boolean},textValue:{},asChild:{type:Boolean},as:{}},setup(e){const n=e,t=Un(),{forwardRef:o}=R(),{CollectionItem:a}=ko(),s=E(!1);async function i(d){if(!d.defaultPrevented&&Bt(d)){if(n.disabled)t.onItemLeave(d);else if(!t.onItemEnter(d)){const u=d.currentTarget;u==null||u.focus({preventScroll:!0})}}}async function l(d){await ae(),!d.defaultPrevented&&Bt(d)&&t.onItemLeave(d)}return(d,f)=>(y(),w(r(a),{value:{textValue:d.textValue}},{default:m(()=>[O(r(V),S({ref:r(o),role:"menuitem",tabindex:"-1"},d.$attrs,{as:d.as,"as-child":d.asChild,"aria-disabled":d.disabled||void 0,"data-disabled":d.disabled?"":void 0,"data-highlighted":s.value?"":void 0,onPointermove:i,onPointerleave:l,onFocus:f[0]||(f[0]=async u=>{await ae(),!(u.defaultPrevented||d.disabled)&&(s.value=!0)}),onBlur:f[1]||(f[1]=async u=>{await ae(),!u.defaultPrevented&&(s.value=!1)})}),{default:m(()=>[b(d.$slots,"default")]),_:3},16,["as","as-child","aria-disabled","data-disabled","data-highlighted"])]),_:3},8,["value"]))}}),Ps=v({__name:"MenuItem",props:{disabled:{type:Boolean},textValue:{},asChild:{type:Boolean},as:{}},emits:["select"],setup(e,{emit:n}){const t=e,o=n,{forwardRef:a,currentElement:s}=R(),i=Wt(),l=Un(),d=E(!1);async function f(){const u=s.value;if(!t.disabled&&u){const c=new CustomEvent(Ca,{bubbles:!0,cancelable:!0});o("select",c),await ae(),c.defaultPrevented?d.value=!1:i.onClose()}}return(u,c)=>(y(),w(Bs,S(t,{ref:r(a),onClick:f,onPointerdown:c[0]||(c[0]=()=>{d.value=!0}),onPointerup:c[1]||(c[1]=async p=>{var h;await ae(),!p.defaultPrevented&&(d.value||(h=p.currentTarget)==null||h.click())}),onKeydown:c[2]||(c[2]=async p=>{const h=r(l).searchRef.value!=="";u.disabled||h&&p.key===" "||r($t).includes(p.key)&&(p.currentTarget.click(),p.preventDefault())})}),{default:m(()=>[b(u.$slots,"default")]),_:3},16))}}),As=v({__name:"MenuRootContentModal",props:{loop:{type:Boolean},side:{},sideOffset:{},align:{},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},positionStrategy:{},updatePositionStrategy:{},disableUpdateOnLayoutShift:{type:Boolean},prioritizePosition:{type:Boolean},reference:{},asChild:{type:Boolean},as:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","entryFocus","openAutoFocus","closeAutoFocus"],setup(e,{emit:n}){const t=e,o=n,a=le(t,o),s=ut(),{forwardRef:i,currentElement:l}=R();return Fn(l),(d,f)=>(y(),w(Gn,S(r(a),{ref:r(i),"trap-focus":r(s).open.value,"disable-outside-pointer-events":r(s).open.value,"disable-outside-scroll":!0,onDismiss:f[0]||(f[0]=u=>r(s).onOpenChange(!1)),onFocusOutside:f[1]||(f[1]=pn(u=>o("focusOutside",u),["prevent"]))}),{default:m(()=>[b(d.$slots,"default")]),_:3},16,["trap-focus","disable-outside-pointer-events"]))}}),Ds=v({__name:"MenuRootContentNonModal",props:{loop:{type:Boolean},side:{},sideOffset:{},align:{},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},positionStrategy:{},updatePositionStrategy:{},disableUpdateOnLayoutShift:{type:Boolean},prioritizePosition:{type:Boolean},reference:{},asChild:{type:Boolean},as:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","entryFocus","openAutoFocus","closeAutoFocus"],setup(e,{emit:n}){const a=le(e,n),s=ut();return(i,l)=>(y(),w(Gn,S(r(a),{"trap-focus":!1,"disable-outside-pointer-events":!1,"disable-outside-scroll":!1,onDismiss:l[0]||(l[0]=d=>r(s).onOpenChange(!1))}),{default:m(()=>[b(i.$slots,"default")]),_:3},16))}}),Es=v({__name:"MenuContent",props:{forceMount:{type:Boolean},loop:{type:Boolean},side:{},sideOffset:{},align:{},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},positionStrategy:{},updatePositionStrategy:{},disableUpdateOnLayoutShift:{type:Boolean},prioritizePosition:{type:Boolean},reference:{},asChild:{type:Boolean},as:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","entryFocus","openAutoFocus","closeAutoFocus"],setup(e,{emit:n}){const a=le(e,n),s=ut(),i=Wt();return(l,d)=>(y(),w(r(at),{present:l.forceMount||r(s).open.value},{default:m(()=>[r(i).modal.value?(y(),w(As,X(S({key:0},{...l.$attrs,...r(a)})),{default:m(()=>[b(l.$slots,"default")]),_:3},16)):(y(),w(Ds,X(S({key:1},{...l.$attrs,...r(a)})),{default:m(()=>[b(l.$slots,"default")]),_:3},16))]),_:3},8,["present"]))}}),Ss=v({__name:"MenuGroup",props:{asChild:{type:Boolean},as:{}},setup(e){const n=e;return(t,o)=>(y(),w(r(V),S({role:"group"},n),{default:m(()=>[b(t.$slots,"default")]),_:3},16))}}),Ms=v({__name:"MenuLabel",props:{asChild:{type:Boolean},as:{default:"div"}},setup(e){const n=e;return(t,o)=>(y(),w(r(V),X(oe(n)),{default:m(()=>[b(t.$slots,"default")]),_:3},16))}}),Ts=v({__name:"MenuPortal",props:{to:{},disabled:{type:Boolean},defer:{type:Boolean},forceMount:{type:Boolean}},setup(e){const n=e;return(t,o)=>(y(),w(r(Nt),X(oe(n)),{default:m(()=>[b(t.$slots,"default")]),_:3},16))}}),Ls=v({__name:"MenuSeparator",props:{asChild:{type:Boolean},as:{}},setup(e){const n=e;return(t,o)=>(y(),w(r(V),S(n,{role:"separator","aria-orientation":"horizontal"}),{default:m(()=>[b(t.$slots,"default")]),_:3},16))}}),Rs=v({__name:"MenuAnchor",props:{reference:{},asChild:{type:Boolean},as:{}},setup(e){const n=e;return(t,o)=>(y(),w(r(Kn),X(oe(n)),{default:m(()=>[b(t.$slots,"default")]),_:3},16))}}),Fs=v({__name:"DialogPortal",props:{to:{},disabled:{type:Boolean},defer:{type:Boolean},forceMount:{type:Boolean}},setup(e){const n=e;return(t,o)=>(y(),w(r(Nt),X(oe(n)),{default:m(()=>[b(t.$slots,"default")]),_:3},16))}}),[qn,Is]=ie("DropdownMenuRoot"),zs=v({__name:"DropdownMenuRoot",props:{defaultOpen:{type:Boolean},open:{type:Boolean,default:void 0},dir:{},modal:{type:Boolean,default:!0}},emits:["update:open"],setup(e,{emit:n}){const t=e,o=n;R();const a=Ke(t,"open",o,{defaultValue:t.defaultOpen,passive:t.open===void 0}),s=E(),{modal:i,dir:l}=Re(t),d=vn(l);return Is({open:a,onOpenChange:f=>{a.value=f},onOpenToggle:()=>{a.value=!a.value},triggerId:"",triggerElement:s,contentId:"",modal:i,dir:d}),(f,u)=>(y(),w(r(Os),{open:r(a),"onUpdate:open":u[0]||(u[0]=c=>uo(a)?a.value=c:null),dir:r(d),modal:r(i)},{default:m(()=>[b(f.$slots,"default",{open:r(a)})]),_:3},8,["open","dir","modal"]))}}),Hs=v({__name:"DropdownMenuContent",props:{forceMount:{type:Boolean},loop:{type:Boolean},side:{},sideOffset:{},align:{},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},positionStrategy:{},updatePositionStrategy:{},disableUpdateOnLayoutShift:{type:Boolean},prioritizePosition:{type:Boolean},reference:{},asChild:{type:Boolean},as:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","closeAutoFocus"],setup(e,{emit:n}){const a=le(e,n);R();const s=qn(),i=E(!1);function l(d){d.defaultPrevented||(i.value||setTimeout(()=>{var f;(f=s.triggerElement.value)==null||f.focus()},0),i.value=!1,d.preventDefault())}return s.contentId||(s.contentId=He(void 0,"reka-dropdown-menu-content")),(d,f)=>{var u;return y(),w(r(Es),S(r(a),{id:r(s).contentId,"aria-labelledby":(u=r(s))==null?void 0:u.triggerId,style:{"--reka-dropdown-menu-content-transform-origin":"var(--reka-popper-transform-origin)","--reka-dropdown-menu-content-available-width":"var(--reka-popper-available-width)","--reka-dropdown-menu-content-available-height":"var(--reka-popper-available-height)","--reka-dropdown-menu-trigger-width":"var(--reka-popper-anchor-width)","--reka-dropdown-menu-trigger-height":"var(--reka-popper-anchor-height)"},onCloseAutoFocus:l,onInteractOutside:f[0]||(f[0]=c=>{var _;if(c.defaultPrevented)return;const p=c.detail.originalEvent,h=p.button===0&&p.ctrlKey===!0,g=p.button===2||h;(!r(s).modal.value||g)&&(i.value=!0),(_=r(s).triggerElement.value)!=null&&_.contains(c.target)&&c.preventDefault()})}),{default:m(()=>[b(d.$slots,"default")]),_:3},16,["id","aria-labelledby"])}}}),Ns=v({__name:"DropdownMenuGroup",props:{asChild:{type:Boolean},as:{}},setup(e){const n=e;return R(),(t,o)=>(y(),w(r(Ss),X(oe(n)),{default:m(()=>[b(t.$slots,"default")]),_:3},16))}}),Ws=v({__name:"DropdownMenuItem",props:{disabled:{type:Boolean},textValue:{},asChild:{type:Boolean},as:{}},emits:["select"],setup(e,{emit:n}){const t=e,a=ot(n);return R(),(s,i)=>(y(),w(r(Ps),X(oe({...t,...r(a)})),{default:m(()=>[b(s.$slots,"default")]),_:3},16))}}),Ks=v({__name:"DropdownMenuLabel",props:{asChild:{type:Boolean},as:{}},setup(e){const n=e;return R(),(t,o)=>(y(),w(r(Ms),X(oe(n)),{default:m(()=>[b(t.$slots,"default")]),_:3},16))}}),Vs=v({__name:"DropdownMenuPortal",props:{to:{},disabled:{type:Boolean},defer:{type:Boolean},forceMount:{type:Boolean}},setup(e){const n=e;return(t,o)=>(y(),w(r(Ts),X(oe(n)),{default:m(()=>[b(t.$slots,"default")]),_:3},16))}}),js=v({__name:"DropdownMenuSeparator",props:{asChild:{type:Boolean},as:{}},setup(e){const n=e;return R(),(t,o)=>(y(),w(r(Ls),X(oe(n)),{default:m(()=>[b(t.$slots,"default")]),_:3},16))}}),Us=v({__name:"DropdownMenuTrigger",props:{disabled:{type:Boolean},asChild:{type:Boolean},as:{default:"button"}},setup(e){const n=e,t=qn(),{forwardRef:o,currentElement:a}=R();return Be(()=>{t.triggerElement=a}),t.triggerId||(t.triggerId=He(void 0,"reka-dropdown-menu-trigger")),(s,i)=>(y(),w(r(Rs),{"as-child":""},{default:m(()=>[O(r(V),{id:r(t).triggerId,ref:r(o),type:s.as==="button"?"button":void 0,"as-child":n.asChild,as:s.as,"aria-haspopup":"menu","aria-expanded":r(t).open.value,"aria-controls":r(t).open.value?r(t).contentId:void 0,"data-disabled":s.disabled?"":void 0,disabled:s.disabled,"data-state":r(t).open.value?"open":"closed",onClick:i[0]||(i[0]=async l=>{var d;!s.disabled&&l.button===0&&l.ctrlKey===!1&&((d=r(t))==null||d.onOpenToggle(),await ae(),r(t).open.value&&l.preventDefault())}),onKeydown:i[1]||(i[1]=co(l=>{s.disabled||(["Enter"," "].includes(l.key)&&r(t).onOpenToggle(),l.key==="ArrowDown"&&r(t).onOpenChange(!0),["Enter"," ","ArrowDown"].includes(l.key)&&l.preventDefault())},["enter","space","arrow-down"]))},{default:m(()=>[b(s.$slots,"default")]),_:3},8,["id","type","as-child","as","aria-expanded","aria-controls","data-disabled","disabled","data-state"])]),_:3}))}});function Gs(e,n){const t=gn(!1,300),o=E(null),a=_o();function s(){o.value=null,t.value=!1}function i(l,d){const f=l.currentTarget,u={x:l.clientX,y:l.clientY},c=qs(u,f.getBoundingClientRect()),p=Ys(u,c),h=Xs(d.getBoundingClientRect()),g=Qs([...p,...h]);o.value=g,t.value=!0}return ne(l=>{if(e.value&&n.value){const d=u=>i(u,n.value),f=u=>i(u,e.value);e.value.addEventListener("pointerleave",d),n.value.addEventListener("pointerleave",f),l(()=>{var u,c;(u=e.value)==null||u.removeEventListener("pointerleave",d),(c=n.value)==null||c.removeEventListener("pointerleave",f)})}}),ne(l=>{var d;if(o.value){const f=u=>{var x,k;if(!o.value||!(u.target instanceof HTMLElement))return;const c=u.target,p={x:u.clientX,y:u.clientY},h=((x=e.value)==null?void 0:x.contains(c))||((k=n.value)==null?void 0:k.contains(c)),g=!Zs(p,o.value),_=!!c.closest("[data-grace-area-trigger]");h?s():(g||_)&&(s(),a.trigger())};(d=e.value)==null||d.ownerDocument.addEventListener("pointermove",f),l(()=>{var u;return(u=e.value)==null?void 0:u.ownerDocument.removeEventListener("pointermove",f)})}}),{isPointerInTransit:t,onPointerExit:a.on}}function qs(e,n){const t=Math.abs(n.top-e.y),o=Math.abs(n.bottom-e.y),a=Math.abs(n.right-e.x),s=Math.abs(n.left-e.x);switch(Math.min(t,o,a,s)){case s:return"left";case a:return"right";case t:return"top";case o:return"bottom";default:throw new Error("unreachable")}}function Ys(e,n,t=5){const o=[];switch(n){case"top":o.push({x:e.x-t,y:e.y+t},{x:e.x+t,y:e.y+t});break;case"bottom":o.push({x:e.x-t,y:e.y-t},{x:e.x+t,y:e.y-t});break;case"left":o.push({x:e.x+t,y:e.y-t},{x:e.x+t,y:e.y+t});break;case"right":o.push({x:e.x-t,y:e.y-t},{x:e.x-t,y:e.y+t});break}return o}function Xs(e){const{top:n,right:t,bottom:o,left:a}=e;return[{x:a,y:n},{x:t,y:n},{x:t,y:o},{x:a,y:o}]}function Zs(e,n){const{x:t,y:o}=e;let a=!1;for(let s=0,i=n.length-1;s<n.length;i=s++){const l=n[s].x,d=n[s].y,f=n[i].x,u=n[i].y;d>o!=u>o&&t<(f-l)*(o-d)/(u-d)+l&&(a=!a)}return a}function Qs(e){const n=e.slice();return n.sort((t,o)=>t.x<o.x?-1:t.x>o.x?1:t.y<o.y?-1:t.y>o.y?1:0),Js(n)}function Js(e){if(e.length<=1)return e.slice();const n=[];for(let o=0;o<e.length;o++){const a=e[o];for(;n.length>=2;){const s=n[n.length-1],i=n[n.length-2];if((s.x-i.x)*(a.y-i.y)>=(s.y-i.y)*(a.x-i.x))n.pop();else break}n.push(a)}n.pop();const t=[];for(let o=e.length-1;o>=0;o--){const a=e[o];for(;t.length>=2;){const s=t[t.length-1],i=t[t.length-2];if((s.x-i.x)*(a.y-i.y)>=(s.y-i.y)*(a.x-i.x))t.pop();else break}t.push(a)}return t.pop(),n.length===1&&t.length===1&&n[0].x===t[0].x&&n[0].y===t[0].y?n:n.concat(t)}const er=v({__name:"TooltipArrow",props:{width:{default:10},height:{default:5},asChild:{type:Boolean},as:{default:"svg"}},setup(e){const n=e;return R(),(t,o)=>(y(),w(r(gs),X(oe(n)),{default:m(()=>[b(t.$slots,"default")]),_:3},16))}}),Yn="tooltip.open",[Kt,tr]=ie("TooltipProvider"),nr=v({inheritAttrs:!1,__name:"TooltipProvider",props:{delayDuration:{default:700},skipDelayDuration:{default:300},disableHoverableContent:{type:Boolean,default:!1},disableClosingTrigger:{type:Boolean},disabled:{type:Boolean},ignoreNonKeyboardFocus:{type:Boolean,default:!1}},setup(e){const n=e,{delayDuration:t,skipDelayDuration:o,disableHoverableContent:a,disableClosingTrigger:s,ignoreNonKeyboardFocus:i,disabled:l}=Re(n);R();const d=E(!0),f=E(!1),{start:u,stop:c}=yn(()=>{d.value=!0},o,{immediate:!1});return tr({isOpenDelayed:d,delayDuration:t,onOpen(){c(),d.value=!1},onClose(){u()},isPointerInTransitRef:f,disableHoverableContent:a,disableClosingTrigger:s,disabled:l,ignoreNonKeyboardFocus:i}),(p,h)=>b(p.$slots,"default")}}),[dt,or]=ie("TooltipRoot"),ar=v({__name:"TooltipRoot",props:{defaultOpen:{type:Boolean,default:!1},open:{type:Boolean,default:void 0},delayDuration:{default:void 0},disableHoverableContent:{type:Boolean,default:void 0},disableClosingTrigger:{type:Boolean,default:void 0},disabled:{type:Boolean,default:void 0},ignoreNonKeyboardFocus:{type:Boolean,default:void 0}},emits:["update:open"],setup(e,{emit:n}){const t=e,o=n;R();const a=Kt(),s=A(()=>t.disableHoverableContent??a.disableHoverableContent.value),i=A(()=>t.disableClosingTrigger??a.disableClosingTrigger.value),l=A(()=>t.disabled??a.disabled.value),d=A(()=>t.delayDuration??a.delayDuration.value),f=A(()=>t.ignoreNonKeyboardFocus??a.ignoreNonKeyboardFocus.value),u=Ke(t,"open",o,{defaultValue:t.defaultOpen,passive:t.open===void 0});_e(u,B=>{a.onClose&&(B?(a.onOpen(),document.dispatchEvent(new CustomEvent(Yn))):a.onClose())});const c=E(!1),p=E(),h=A(()=>u.value?c.value?"delayed-open":"instant-open":"closed"),{start:g,stop:_}=yn(()=>{c.value=!0,u.value=!0},d,{immediate:!1});function x(){_(),c.value=!1,u.value=!0}function k(){_(),u.value=!1}function $(){g()}return or({contentId:"",open:u,stateAttribute:h,trigger:p,onTriggerChange(B){p.value=B},onTriggerEnter(){a.isOpenDelayed.value?$():x()},onTriggerLeave(){s.value?k():_()},onOpen:x,onClose:k,disableHoverableContent:s,disableClosingTrigger:i,disabled:l,ignoreNonKeyboardFocus:f}),(B,D)=>(y(),w(r(Wn),null,{default:m(()=>[b(B.$slots,"default",{open:r(u)})]),_:3}))}}),Xn=v({__name:"TooltipContentImpl",props:{ariaLabel:{},asChild:{type:Boolean},as:{},side:{default:"top"},sideOffset:{default:0},align:{default:"center"},alignOffset:{},avoidCollisions:{type:Boolean,default:!0},collisionBoundary:{default:()=>[]},collisionPadding:{default:0},arrowPadding:{default:0},sticky:{default:"partial"},hideWhenDetached:{type:Boolean,default:!1},positionStrategy:{},updatePositionStrategy:{}},emits:["escapeKeyDown","pointerDownOutside"],setup(e,{emit:n}){const t=e,o=n,a=dt(),{forwardRef:s}=R(),i=fo(),l=A(()=>{var u;return(u=i.default)==null?void 0:u.call(i,{})}),d=A(()=>{var p;if(t.ariaLabel)return t.ariaLabel;let u="";function c(h){typeof h.children=="string"&&h.type!==po?u+=h.children:Array.isArray(h.children)&&h.children.forEach(g=>c(g))}return(p=l.value)==null||p.forEach(h=>c(h)),u}),f=A(()=>{const{ariaLabel:u,...c}=t;return c});return Be(()=>{Te(window,"scroll",u=>{const c=u.target;c!=null&&c.contains(a.trigger.value)&&a.onClose()}),Te(window,Yn,a.onClose)}),(u,c)=>(y(),w(r(Ht),{"as-child":"","disable-outside-pointer-events":!1,onEscapeKeyDown:c[0]||(c[0]=p=>o("escapeKeyDown",p)),onPointerDownOutside:c[1]||(c[1]=p=>{var h;r(a).disableClosingTrigger.value&&((h=r(a).trigger.value)!=null&&h.contains(p.target))&&p.preventDefault(),o("pointerDownOutside",p)}),onFocusOutside:c[2]||(c[2]=pn(()=>{},["prevent"])),onDismiss:c[3]||(c[3]=p=>r(a).onClose())},{default:m(()=>[O(r(jn),S({ref:r(s),"data-state":r(a).stateAttribute.value},{...u.$attrs,...f.value},{style:{"--reka-tooltip-content-transform-origin":"var(--reka-popper-transform-origin)","--reka-tooltip-content-available-width":"var(--reka-popper-available-width)","--reka-tooltip-content-available-height":"var(--reka-popper-available-height)","--reka-tooltip-trigger-width":"var(--reka-popper-anchor-width)","--reka-tooltip-trigger-height":"var(--reka-popper-anchor-height)"}}),{default:m(()=>[b(u.$slots,"default"),O(r($o),{id:r(a).contentId,role:"tooltip"},{default:m(()=>[ce(he(d.value),1)]),_:1},8,["id"])]),_:3},16,["data-state"])]),_:3}))}}),sr=v({__name:"TooltipContentHoverable",props:{ariaLabel:{},asChild:{type:Boolean},as:{},side:{},sideOffset:{},align:{},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},positionStrategy:{},updatePositionStrategy:{}},setup(e){const t=st(e),{forwardRef:o,currentElement:a}=R(),{trigger:s,onClose:i}=dt(),l=Kt(),{isPointerInTransit:d,onPointerExit:f}=Gs(s,a);return l.isPointerInTransitRef=d,f(()=>{i()}),(u,c)=>(y(),w(Xn,S({ref:r(o)},r(t)),{default:m(()=>[b(u.$slots,"default")]),_:3},16))}}),rr=v({__name:"TooltipContent",props:{forceMount:{type:Boolean},ariaLabel:{},asChild:{type:Boolean},as:{},side:{default:"top"},sideOffset:{},align:{},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},positionStrategy:{},updatePositionStrategy:{}},emits:["escapeKeyDown","pointerDownOutside"],setup(e,{emit:n}){const t=e,o=n,a=dt(),s=le(t,o),{forwardRef:i}=R();return(l,d)=>(y(),w(r(at),{present:l.forceMount||r(a).open.value},{default:m(()=>[(y(),w(et(r(a).disableHoverableContent.value?Xn:sr),S({ref:r(i)},r(s)),{default:m(()=>[b(l.$slots,"default")]),_:3},16))]),_:3},8,["present"]))}}),ir=v({__name:"TooltipPortal",props:{to:{},disabled:{type:Boolean},defer:{type:Boolean},forceMount:{type:Boolean}},setup(e){const n=e;return(t,o)=>(y(),w(r(Nt),X(oe(n)),{default:m(()=>[b(t.$slots,"default")]),_:3},16))}}),lr=v({__name:"TooltipTrigger",props:{reference:{},asChild:{type:Boolean},as:{default:"button"}},setup(e){const n=e,t=dt(),o=Kt();t.contentId||(t.contentId=He(void 0,"reka-tooltip-content"));const{forwardRef:a,currentElement:s}=R(),i=E(!1),l=E(!1),d=A(()=>t.disabled.value?{}:{click:_,focus:h,pointermove:c,pointerleave:p,pointerdown:u,blur:g});Be(()=>{t.onTriggerChange(s.value)});function f(){setTimeout(()=>{i.value=!1},1)}function u(){t.open&&!t.disableClosingTrigger.value&&t.onClose(),i.value=!0,document.addEventListener("pointerup",f,{once:!0})}function c(x){x.pointerType!=="touch"&&!l.value&&!o.isPointerInTransitRef.value&&(t.onTriggerEnter(),l.value=!0)}function p(){t.onTriggerLeave(),l.value=!1}function h(x){var k,$;i.value||t.ignoreNonKeyboardFocus.value&&!(($=(k=x.target).matches)!=null&&$.call(k,":focus-visible"))||t.onOpen()}function g(){t.onClose()}function _(){t.disableClosingTrigger.value||t.onClose()}return(x,k)=>(y(),w(r(Kn),{"as-child":"",reference:x.reference},{default:m(()=>[O(r(V),S({ref:r(a),"aria-describedby":r(t).open.value?r(t).contentId:void 0,"data-state":r(t).stateAttribute.value,as:x.as,"as-child":n.asChild,"data-grace-area-trigger":""},mo(d.value)),{default:m(()=>[b(x.$slots,"default")]),_:3},16,["aria-describedby","data-state","as","as-child"])]),_:3},8,["reference"]))}}),ur=v({__name:"Sheet",props:{open:{type:Boolean},defaultOpen:{type:Boolean},modal:{type:Boolean}},emits:["update:open"],setup(e,{emit:n}){const a=le(e,n);return(s,i)=>(y(),w(r(_a),S({"data-slot":"sheet"},r(a)),{default:m(()=>[b(s.$slots,"default")]),_:3},16))}});/**
 * @license lucide-vue-next v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const dr=N("AwardIcon",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]]);/**
 * @license lucide-vue-next v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const cr=N("BookOpenIcon",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]]);/**
 * @license lucide-vue-next v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fr=N("Building2Icon",[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z",key:"1b4qmf"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2",key:"i71pzd"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2",key:"10jefs"}],["path",{d:"M10 6h4",key:"1itunk"}],["path",{d:"M10 10h4",key:"tcdvrf"}],["path",{d:"M10 14h4",key:"kelpxr"}],["path",{d:"M10 18h4",key:"1ulq68"}]]);/**
 * @license lucide-vue-next v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const pr=N("CalendarIcon",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]]);/**
 * @license lucide-vue-next v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const an=N("ChartColumnIcon",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]);/**
 * @license lucide-vue-next v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const mr=N("ChevronRightIcon",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]);/**
 * @license lucide-vue-next v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const hr=N("ChevronsUpDownIcon",[["path",{d:"m7 15 5 5 5-5",key:"1hf1tw"}],["path",{d:"m7 9 5-5 5 5",key:"sgt6xg"}]]);/**
 * @license lucide-vue-next v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const gr=N("ClipboardListIcon",[["rect",{width:"8",height:"4",x:"8",y:"2",rx:"1",ry:"1",key:"tgr4d6"}],["path",{d:"M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2",key:"116196"}],["path",{d:"M12 11h4",key:"1jrz19"}],["path",{d:"M12 16h4",key:"n85exb"}],["path",{d:"M8 11h.01",key:"1dfujw"}],["path",{d:"M8 16h.01",key:"18s6g9"}]]);/**
 * @license lucide-vue-next v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const sn=N("CreditCardIcon",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]]);/**
 * @license lucide-vue-next v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _t=N("FileTextIcon",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]);/**
 * @license lucide-vue-next v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const yr=N("FolderIcon",[["path",{d:"M20 20a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.9a2 2 0 0 1-1.69-.9L9.6 3.9A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13a2 2 0 0 0 2 2Z",key:"1kt360"}]]);/**
 * @license lucide-vue-next v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const vr=N("GavelIcon",[["path",{d:"m14.5 12.5-8 8a2.119 2.119 0 1 1-3-3l8-8",key:"15492f"}],["path",{d:"m16 16 6-6",key:"vzrcl6"}],["path",{d:"m8 8 6-6",key:"18bi4p"}],["path",{d:"m9 7 8 8",key:"5jnvq1"}],["path",{d:"m21 11-8-8",key:"z4y7zo"}]]);/**
 * @license lucide-vue-next v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const qe=N("LayoutGridIcon",[["rect",{width:"7",height:"7",x:"3",y:"3",rx:"1",key:"1g98yp"}],["rect",{width:"7",height:"7",x:"14",y:"3",rx:"1",key:"6d4xhi"}],["rect",{width:"7",height:"7",x:"14",y:"14",rx:"1",key:"nxv5o0"}],["rect",{width:"7",height:"7",x:"3",y:"14",rx:"1",key:"1bb6yr"}]]);/**
 * @license lucide-vue-next v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const br=N("LogOutIcon",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]]);/**
 * @license lucide-vue-next v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const wr=N("MapPinIcon",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]]);/**
 * @license lucide-vue-next v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _r=N("PanelLeftIcon",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M9 3v18",key:"fh3hqa"}]]);/**
 * @license lucide-vue-next v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xr=N("SettingsIcon",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-vue-next v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Cr=N("TrophyIcon",[["path",{d:"M6 9H4.5a2.5 2.5 0 0 1 0-5H6",key:"17hqa7"}],["path",{d:"M18 9h1.5a2.5 2.5 0 0 0 0-5H18",key:"lmptdp"}],["path",{d:"M4 22h16",key:"57wxv0"}],["path",{d:"M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22",key:"1nw9bq"}],["path",{d:"M14 14.66V17c0 .55.47.98.97 1.21C16.15 18.75 17 20.24 17 22",key:"1np0yb"}],["path",{d:"M18 2H6v7a6 6 0 0 0 12 0V2Z",key:"u46fv3"}]]);/**
 * @license lucide-vue-next v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Or=N("UploadIcon",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]]);/**
 * @license lucide-vue-next v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const kr=N("UserCheckIcon",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["polyline",{points:"16 11 18 13 22 9",key:"1pwet4"}]]);/**
 * @license lucide-vue-next v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $r=N("UserPlusIcon",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]]);/**
 * @license lucide-vue-next v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ye=N("UsersIcon",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]]);/**
 * @license lucide-vue-next v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Br=N("XIcon",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),Pr=v({__name:"SheetOverlay",props:{forceMount:{type:Boolean},asChild:{type:Boolean},as:{type:[String,Object,Function]},class:{}},setup(e){const n=e,t=A(()=>{const{class:o,...a}=n;return a});return(o,a)=>(y(),w(r(Ja),S({"data-slot":"sheet-overlay",class:r(I)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/80",n.class)},t.value),{default:m(()=>[b(o.$slots,"default")]),_:3},16,["class"]))}}),Ar=v({inheritAttrs:!1,__name:"SheetContent",props:{class:{},side:{default:"right"},forceMount:{type:Boolean},disableOutsidePointerEvents:{type:Boolean},asChild:{type:Boolean},as:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","openAutoFocus","closeAutoFocus"],setup(e,{emit:n}){const t=e,o=n,a=nt(t,"class","side"),s=le(a,o);return(i,l)=>(y(),w(r(Fs),null,{default:m(()=>[O(Pr),O(r(Ua),S({"data-slot":"sheet-content",class:r(I)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500",i.side==="right"&&"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm",i.side==="left"&&"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm",i.side==="top"&&"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b",i.side==="bottom"&&"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t",t.class)},{...r(s),...i.$attrs}),{default:m(()=>[b(i.$slots,"default"),O(r(xa),{class:"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none"},{default:m(()=>[O(r(Br),{class:"size-4"}),l[0]||(l[0]=G("span",{class:"sr-only"},"Close",-1))]),_:1,__:[0]})]),_:3},16,["class"])]),_:3}))}}),Dr=v({__name:"SheetDescription",props:{asChild:{type:Boolean},as:{type:[String,Object,Function]},class:{}},setup(e){const n=e,t=A(()=>{const{class:o,...a}=n;return a});return(o,a)=>(y(),w(r(Ga),S({"data-slot":"sheet-description",class:r(I)("text-muted-foreground text-sm",n.class)},t.value),{default:m(()=>[b(o.$slots,"default")]),_:3},16,["class"]))}}),Er=v({__name:"SheetHeader",props:{class:{}},setup(e){const n=e;return(t,o)=>(y(),F("div",{"data-slot":"sheet-header",class:K(r(I)("flex flex-col gap-1.5 p-4",n.class))},[b(t.$slots,"default")],2))}}),Sr=v({__name:"SheetTitle",props:{asChild:{type:Boolean},as:{type:[String,Object,Function]},class:{}},setup(e){const n=e,t=A(()=>{const{class:o,...a}=n;return a});return(o,a)=>(y(),w(r(es),S({"data-slot":"sheet-title",class:r(I)("text-foreground font-semibold",n.class)},t.value),{default:m(()=>[b(o.$slots,"default")]),_:3},16,["class"]))}}),Mr="sidebar_state",Tr=60*60*24*7,Lr="16rem",Rr="18rem",Fr="3rem",Ir="b",[ct,zr]=ie("Sidebar"),Hr={class:"flex h-full w-full flex-col"},Nr=["data-state","data-collapsible","data-variant","data-side"],Wr={"data-sidebar":"sidebar",class:"bg-sidebar group-data-[variant=floating]:border-sidebar-border flex h-full w-full flex-col group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:shadow-sm"},Kr=v({inheritAttrs:!1,__name:"Sidebar",props:{side:{default:"left"},variant:{default:"sidebar"},collapsible:{default:"offcanvas"},class:{}},setup(e){const n=e,{isMobile:t,state:o,openMobile:a,setOpenMobile:s}=ct();return(i,l)=>i.collapsible==="none"?(y(),F("div",S({key:0,"data-slot":"sidebar",class:r(I)("bg-sidebar text-sidebar-foreground flex h-full w-(--sidebar-width) flex-col",n.class)},i.$attrs),[b(i.$slots,"default")],16)):r(t)?(y(),w(r(ur),S({key:1,open:r(a)},i.$attrs,{"onUpdate:open":r(s)}),{default:m(()=>[O(r(Ar),{"data-sidebar":"sidebar","data-slot":"sidebar","data-mobile":"true",side:i.side,class:"bg-sidebar text-sidebar-foreground w-(--sidebar-width) p-0 [&>button]:hidden",style:Je({"--sidebar-width":r(Rr)})},{default:m(()=>[O(Er,{class:"sr-only"},{default:m(()=>[O(Sr,null,{default:m(()=>l[0]||(l[0]=[ce("Sidebar")])),_:1,__:[0]}),O(Dr,null,{default:m(()=>l[1]||(l[1]=[ce("Displays the mobile sidebar.")])),_:1,__:[1]})]),_:1}),G("div",Hr,[b(i.$slots,"default")])]),_:3},8,["side","style"])]),_:3},16,["open","onUpdate:open"])):(y(),F("div",{key:2,class:"group peer text-sidebar-foreground hidden md:block","data-slot":"sidebar","data-state":r(o),"data-collapsible":r(o)==="collapsed"?i.collapsible:"","data-variant":i.variant,"data-side":i.side},[G("div",{class:K(r(I)("relative w-(--sidebar-width) bg-transparent transition-[width] duration-200 ease-linear","group-data-[collapsible=offcanvas]:w-0","group-data-[side=right]:rotate-180",i.variant==="floating"||i.variant==="inset"?"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4)))]":"group-data-[collapsible=icon]:w-(--sidebar-width-icon)"))},null,2),G("div",S({class:r(I)("fixed inset-y-0 z-10 hidden h-svh w-(--sidebar-width) transition-[left,right,width] duration-200 ease-linear md:flex",i.side==="left"?"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]":"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]",i.variant==="floating"||i.variant==="inset"?"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4))+2px)]":"group-data-[collapsible=icon]:w-(--sidebar-width-icon) group-data-[side=left]:border-r group-data-[side=right]:border-l",n.class)},i.$attrs),[G("div",Wr,[b(i.$slots,"default")])],16)],8,Nr))}}),Vr=v({__name:"SidebarContent",props:{class:{}},setup(e){const n=e;return(t,o)=>(y(),F("div",{"data-slot":"sidebar-content","data-sidebar":"content",class:K(r(I)("flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden",n.class))},[b(t.$slots,"default")],2))}}),jr=v({__name:"SidebarFooter",props:{class:{}},setup(e){const n=e;return(t,o)=>(y(),F("div",{"data-slot":"sidebar-footer","data-sidebar":"footer",class:K(r(I)("flex flex-col gap-2 p-2",n.class))},[b(t.$slots,"default")],2))}}),Zn=v({__name:"SidebarGroup",props:{class:{}},setup(e){const n=e;return(t,o)=>(y(),F("div",{"data-slot":"sidebar-group","data-sidebar":"group",class:K(r(I)("relative flex w-full min-w-0 flex-col p-2",n.class))},[b(t.$slots,"default")],2))}}),Ur=v({__name:"SidebarGroupContent",props:{class:{}},setup(e){const n=e;return(t,o)=>(y(),F("div",{"data-slot":"sidebar-group-content","data-sidebar":"group-content",class:K(r(I)("w-full text-sm",n.class))},[b(t.$slots,"default")],2))}}),Gr=v({__name:"SidebarGroupLabel",props:{asChild:{type:Boolean},as:{type:[String,Object,Function]},class:{}},setup(e){const n=e;return(t,o)=>(y(),w(r(V),{"data-slot":"sidebar-group-label","data-sidebar":"group-label",as:t.as,"as-child":t.asChild,class:K(r(I)("text-sidebar-foreground/70 ring-sidebar-ring flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium outline-hidden transition-[margin,opacity] duration-200 ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0","group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0",n.class))},{default:m(()=>[b(t.$slots,"default")]),_:3},8,["as","as-child","class"]))}}),qr=v({__name:"SidebarHeader",props:{class:{}},setup(e){const n=e;return(t,o)=>(y(),F("div",{"data-slot":"sidebar-header","data-sidebar":"header",class:K(r(I)("flex flex-col gap-2 p-2",n.class))},[b(t.$slots,"default")],2))}}),Yr=v({__name:"SidebarInset",props:{class:{}},setup(e){const n=e;return(t,o)=>(y(),F("main",{"data-slot":"sidebar-inset",class:K(r(I)("bg-background relative flex w-full flex-1 flex-col","md:peer-data-[variant=inset]:m-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow-sm md:peer-data-[variant=inset]:peer-data-[state=collapsed]:ml-0",n.class))},[b(t.$slots,"default")],2))}}),ft=v({__name:"SidebarMenu",props:{class:{}},setup(e){const n=e;return(t,o)=>(y(),F("ul",{"data-slot":"sidebar-menu","data-sidebar":"menu",class:K(r(I)("flex w-full min-w-0 flex-col gap-1",n.class))},[b(t.$slots,"default")],2))}}),Xr=v({__name:"Tooltip",props:{defaultOpen:{type:Boolean},open:{type:Boolean},delayDuration:{},disableHoverableContent:{type:Boolean},disableClosingTrigger:{type:Boolean},disabled:{type:Boolean},ignoreNonKeyboardFocus:{type:Boolean}},emits:["update:open"],setup(e,{emit:n}){const a=le(e,n);return(s,i)=>(y(),w(r(ar),S({"data-slot":"tooltip"},r(a)),{default:m(()=>[b(s.$slots,"default")]),_:3},16))}}),Zr=v({inheritAttrs:!1,__name:"TooltipContent",props:{forceMount:{type:Boolean},ariaLabel:{},asChild:{type:Boolean},as:{},side:{},sideOffset:{default:4},align:{},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},positionStrategy:{},updatePositionStrategy:{},class:{}},emits:["escapeKeyDown","pointerDownOutside"],setup(e,{emit:n}){const t=e,o=n,a=nt(t,"class"),s=le(a,o);return(i,l)=>(y(),w(r(ir),null,{default:m(()=>[O(r(rr),S({"data-slot":"tooltip-content"},{...r(s),...i.$attrs},{class:r(I)("bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit rounded-md px-3 py-1.5 text-xs text-balance",t.class)}),{default:m(()=>[b(i.$slots,"default"),O(r(er),{class:"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]"})]),_:3},16,["class"])]),_:3}))}}),Qr=v({__name:"TooltipTrigger",props:{reference:{},asChild:{type:Boolean},as:{type:[String,Object,Function]}},setup(e){const n=e;return(t,o)=>(y(),w(r(lr),S({"data-slot":"tooltip-trigger"},n),{default:m(()=>[b(t.$slots,"default")]),_:3},16))}}),rn=v({__name:"SidebarMenuButtonChild",props:{variant:{default:"default"},size:{default:"default"},isActive:{type:Boolean},class:{},asChild:{type:Boolean},as:{type:[String,Object,Function],default:"button"}},setup(e){const n=e;return(t,o)=>(y(),w(r(V),S({"data-slot":"sidebar-menu-button","data-sidebar":"menu-button","data-size":t.size,"data-active":t.isActive,class:r(I)(r(ti)({variant:t.variant,size:t.size}),n.class),as:t.as,"as-child":t.asChild},t.$attrs),{default:m(()=>[b(t.$slots,"default")]),_:3},16,["data-size","data-active","class","as","as-child"]))}}),pt=v({inheritAttrs:!1,__name:"SidebarMenuButton",props:{variant:{default:"default"},size:{default:"default"},isActive:{type:Boolean},class:{},asChild:{type:Boolean},as:{type:[String,Object,Function],default:"button"},tooltip:{}},setup(e){const n=e,{isMobile:t,state:o}=ct(),a=A(()=>{const{tooltip:s,...i}=n;return i});return(s,i)=>s.tooltip?(y(),w(r(Xr),{key:1},{default:m(()=>[O(r(Qr),{"as-child":""},{default:m(()=>[O(rn,X(oe({...a.value,...s.$attrs})),{default:m(()=>[b(s.$slots,"default")]),_:3},16)]),_:3}),O(r(Zr),{side:"right",align:"center",hidden:r(o)!=="collapsed"||r(t)},{default:m(()=>[typeof s.tooltip=="string"?(y(),F(xe,{key:0},[ce(he(s.tooltip),1)],64)):(y(),w(et(s.tooltip),{key:1}))]),_:1},8,["hidden"])]),_:3})):(y(),w(rn,X(S({key:0},{...a.value,...s.$attrs})),{default:m(()=>[b(s.$slots,"default")]),_:3},16))}}),mt=v({__name:"SidebarMenuItem",props:{class:{}},setup(e){const n=e;return(t,o)=>(y(),F("li",{"data-slot":"sidebar-menu-item","data-sidebar":"menu-item",class:K(r(I)("group/menu-item relative",n.class))},[b(t.$slots,"default")],2))}}),Jr=v({__name:"SidebarProvider",props:{defaultOpen:{type:Boolean,default:!0},open:{type:Boolean,default:void 0},class:{}},emits:["update:open"],setup(e,{emit:n}){const t=e,o=n,a=xo("(max-width: 768px)"),s=E(!1),i=Ke(t,"open",o,{defaultValue:t.defaultOpen??!1,passive:t.open===void 0});function l(c){i.value=c,document.cookie=`${Mr}=${i.value}; path=/; max-age=${Tr}`}function d(c){s.value=c}function f(){return a.value?d(!s.value):l(!i.value)}Te("keydown",c=>{c.key===Ir&&(c.metaKey||c.ctrlKey)&&(c.preventDefault(),f())});const u=A(()=>i.value?"expanded":"collapsed");return zr({state:u,open:i,setOpen:l,isMobile:a,openMobile:s,setOpenMobile:d,toggleSidebar:f}),(c,p)=>(y(),w(r(nr),{"delay-duration":0},{default:m(()=>[G("div",S({"data-slot":"sidebar-wrapper",style:{"--sidebar-width":r(Lr),"--sidebar-width-icon":r(Fr)},class:r(I)("group/sidebar-wrapper has-data-[variant=inset]:bg-sidebar flex min-h-svh w-full",t.class)},c.$attrs),[b(c.$slots,"default")],16)]),_:3}))}}),ei=v({__name:"SidebarTrigger",props:{class:{}},setup(e){const n=e,{toggleSidebar:t}=ct();return(o,a)=>(y(),w(r(eo),{"data-sidebar":"trigger","data-slot":"sidebar-trigger",variant:"ghost",size:"icon",class:K(r(I)("h-7 w-7",n.class)),onClick:r(t)},{default:m(()=>[O(r(_r)),a[0]||(a[0]=G("span",{class:"sr-only"},"Toggle Sidebar",-1))]),_:1,__:[0]},8,["class","onClick"]))}}),ti=to("peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-hidden ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-data-[sidebar=menu-action]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:size-8! group-data-[collapsible=icon]:pr-2! [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0",{variants:{variant:{default:"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",outline:"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]"},size:{default:"h-8 text-sm",sm:"h-7 text-xs",lg:"h-12 text-sm group-data-[collapsible=icon]:p-0!"}},defaultVariants:{variant:"default",size:"default"}}),ni=v({__name:"AppContent",props:{variant:{},class:{}},setup(e){const n=e,t=A(()=>n.class);return(o,a)=>n.variant==="sidebar"?(y(),w(r(Yr),{key:0,class:K(t.value)},{default:m(()=>[b(o.$slots,"default")]),_:3},8,["class"])):(y(),F("main",{key:1,class:K(["mx-auto flex h-full w-full max-w-7xl flex-1 flex-col gap-4 rounded-xl",t.value])},[b(o.$slots,"default")],2))}}),oi={key:0,class:"flex min-h-screen w-full flex-col"},ai=v({__name:"AppShell",props:{variant:{}},setup(e){const n=tt().props.sidebarOpen;return(t,o)=>t.variant==="header"?(y(),F("div",oi,[b(t.$slots,"default")])):(y(),w(r(Jr),{key:1,"default-open":r(n)},{default:m(()=>[b(t.$slots,"default")]),_:3},8,["default-open"]))}}),si=["href"],ri=v({__name:"NavFooter",props:{items:{},class:{}},setup(e){return(n,t)=>(y(),w(r(Zn),{class:K(`group-data-[collapsible=icon]:p-0 ${n.$props.class||""}`)},{default:m(()=>[O(r(Ur),null,{default:m(()=>[O(r(ft),null,{default:m(()=>[(y(!0),F(xe,null,Et(n.items,o=>(y(),w(r(mt),{key:o.title},{default:m(()=>[O(r(pt),{class:"text-neutral-600 hover:text-neutral-800 dark:text-neutral-300 dark:hover:text-neutral-100","as-child":""},{default:m(()=>[G("a",{href:o.href,target:"_blank",rel:"noopener noreferrer"},[(y(),w(et(o.icon))),G("span",null,he(o.title),1)],8,si)]),_:2},1024)]),_:2},1024))),128))]),_:1})]),_:1})]),_:1},8,["class"]))}}),ii=v({__name:"NavMain",props:{items:{}},setup(e){const n=tt();return(t,o)=>(y(),w(r(Zn),{class:"px-2 py-0"},{default:m(()=>[O(r(Gr),null,{default:m(()=>o[0]||(o[0]=[ce("Platform")])),_:1,__:[0]}),O(r(ft),null,{default:m(()=>[(y(!0),F(xe,null,Et(t.items,a=>(y(),w(r(mt),{key:a.title},{default:m(()=>[O(r(pt),{"as-child":"","is-active":a.href===r(n).url,tooltip:a.title},{default:m(()=>[O(r(ze),{href:a.href},{default:m(()=>[(y(),w(et(a.icon))),G("span",null,he(a.title),1)]),_:2},1032,["href"])]),_:2},1032,["is-active","tooltip"])]),_:2},1024))),128))]),_:1})]),_:1}))}}),li=v({__name:"Avatar",props:{class:{}},setup(e){const n=e;return(t,o)=>(y(),w(r(ns),{"data-slot":"avatar",class:K(r(I)("relative flex size-8 shrink-0 overflow-hidden rounded-full",n.class))},{default:m(()=>[b(t.$slots,"default")]),_:3},8,["class"]))}}),ui=v({__name:"AvatarFallback",props:{delayMs:{},asChild:{type:Boolean},as:{type:[String,Object,Function]},class:{}},setup(e){const n=e,t=A(()=>{const{class:o,...a}=n;return a});return(o,a)=>(y(),w(r(os),S({"data-slot":"avatar-fallback"},t.value,{class:r(I)("bg-muted flex size-full items-center justify-center rounded-full",n.class)}),{default:m(()=>[b(o.$slots,"default")]),_:3},16,["class"]))}}),di=v({__name:"AvatarImage",props:{src:{},referrerPolicy:{},crossOrigin:{},asChild:{type:Boolean},as:{type:[String,Object,Function]}},setup(e){const n=e;return(t,o)=>(y(),w(r(ss),S({"data-slot":"avatar-image"},n,{class:"aspect-square size-full"}),{default:m(()=>[b(t.$slots,"default")]),_:3},16))}});function ci(e){if(!e)return"";const n=e.trim().split(" ");return n.length===0?"":n.length===1?n[0].charAt(0).toUpperCase():`${n[0].charAt(0)}${n[n.length-1].charAt(0)}`.toUpperCase()}function fi(){return{getInitials:ci}}const pi={class:"grid flex-1 text-left text-sm leading-tight"},mi={class:"truncate font-medium"},hi={key:0,class:"truncate text-xs text-muted-foreground"},Qn=v({__name:"UserInfo",props:{user:{default:void 0},showEmail:{type:Boolean,default:!1}},setup(e){const n=e,{getInitials:t}=fi(),o=A(()=>{var a,s;return((a=n.user)==null?void 0:a.avatar)&&((s=n.user)==null?void 0:s.avatar)!==""});return(a,s)=>{var i;return y(),F(xe,null,[O(r(li),{class:"h-8 w-8 overflow-hidden rounded-lg"},{default:m(()=>[o.value?(y(),w(r(di),{key:0,src:a.user.avatar,alt:a.user.name},null,8,["src","alt"])):ke("",!0),O(r(ui),{class:"rounded-lg text-black dark:text-white"},{default:m(()=>{var l;return[ce(he(r(t)((l=a.user)==null?void 0:l.name)),1)]}),_:1})]),_:1}),G("div",pi,[G("span",mi,he((i=a.user)==null?void 0:i.name),1),a.showEmail?(y(),F("span",hi,he(a.user.email),1)):ke("",!0)])],64)}}}),gi=v({__name:"DropdownMenu",props:{defaultOpen:{type:Boolean},open:{type:Boolean},dir:{},modal:{type:Boolean}},emits:["update:open"],setup(e,{emit:n}){const a=le(e,n);return(s,i)=>(y(),w(r(zs),S({"data-slot":"dropdown-menu"},r(a)),{default:m(()=>[b(s.$slots,"default")]),_:3},16))}}),yi=v({__name:"DropdownMenuContent",props:{forceMount:{type:Boolean},loop:{type:Boolean},side:{},sideOffset:{default:4},align:{},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},positionStrategy:{},updatePositionStrategy:{},disableUpdateOnLayoutShift:{type:Boolean},prioritizePosition:{type:Boolean},reference:{},asChild:{type:Boolean},as:{},class:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","closeAutoFocus"],setup(e,{emit:n}){const t=e,o=n,a=A(()=>{const{class:i,...l}=t;return l}),s=le(a,o);return(i,l)=>(y(),w(r(Vs),null,{default:m(()=>[O(r(Hs),S({"data-slot":"dropdown-menu-content"},r(s),{class:r(I)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--reka-dropdown-menu-content-available-height) min-w-[8rem] origin-(--reka-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",t.class)}),{default:m(()=>[b(i.$slots,"default")]),_:3},16,["class"])]),_:3}))}}),vi=v({__name:"DropdownMenuGroup",props:{asChild:{type:Boolean},as:{type:[String,Object,Function]}},setup(e){const n=e;return(t,o)=>(y(),w(r(Ns),S({"data-slot":"dropdown-menu-group"},n),{default:m(()=>[b(t.$slots,"default")]),_:3},16))}}),ln=v({__name:"DropdownMenuItem",props:{disabled:{type:Boolean},textValue:{},asChild:{type:Boolean},as:{},class:{},inset:{type:Boolean},variant:{default:"default"}},setup(e){const n=e,t=nt(n,"inset","variant"),o=st(t);return(a,s)=>(y(),w(r(Ws),S({"data-slot":"dropdown-menu-item","data-inset":a.inset?"":void 0,"data-variant":a.variant},r(o),{class:r(I)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive-foreground data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/40 data-[variant=destructive]:focus:text-destructive-foreground data-[variant=destructive]:*:[svg]:!text-destructive-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",n.class)}),{default:m(()=>[b(a.$slots,"default")]),_:3},16,["data-inset","data-variant","class"]))}}),bi=v({__name:"DropdownMenuLabel",props:{asChild:{type:Boolean},as:{type:[String,Object,Function]},class:{},inset:{type:Boolean}},setup(e){const n=e,t=nt(n,"class","inset"),o=st(t);return(a,s)=>(y(),w(r(Ks),S({"data-slot":"dropdown-menu-label","data-inset":a.inset?"":void 0},r(o),{class:r(I)("px-2 py-1.5 text-sm font-medium data-[inset]:pl-8",n.class)}),{default:m(()=>[b(a.$slots,"default")]),_:3},16,["data-inset","class"]))}}),un=v({__name:"DropdownMenuSeparator",props:{asChild:{type:Boolean},as:{type:[String,Object,Function]},class:{}},setup(e){const n=e,t=A(()=>{const{class:o,...a}=n;return a});return(o,a)=>(y(),w(r(js),S({"data-slot":"dropdown-menu-separator"},t.value,{class:r(I)("bg-border -mx-1 my-1 h-px",n.class)}),null,16,["class"]))}}),wi=v({__name:"DropdownMenuTrigger",props:{disabled:{type:Boolean},asChild:{type:Boolean},as:{type:[String,Object,Function]}},setup(e){const t=st(e);return(o,a)=>(y(),w(r(Us),S({"data-slot":"dropdown-menu-trigger"},r(t)),{default:m(()=>[b(o.$slots,"default")]),_:3},16))}}),_i={class:"flex items-center gap-2 px-1 py-1.5 text-left text-sm"},xi=v({__name:"UserMenuContent",props:{user:{}},setup(e){const n=()=>{ho.flushAll()};return(t,o)=>(y(),F(xe,null,[O(r(bi),{class:"p-0 font-normal"},{default:m(()=>[G("div",_i,[O(Qn,{user:t.user,"show-email":!0},null,8,["user"])])]),_:1}),O(r(un)),O(r(vi),null,{default:m(()=>[O(r(ln),{"as-child":!0},{default:m(()=>[O(r(ze),{class:"block w-full",href:t.route("profile.edit"),prefetch:"",as:"button"},{default:m(()=>[O(r(xr),{class:"mr-2 h-4 w-4"}),o[0]||(o[0]=ce(" Settings "))]),_:1,__:[0]},8,["href"])]),_:1})]),_:1}),O(r(un)),O(r(ln),{"as-child":!0},{default:m(()=>[O(r(ze),{class:"block w-full",method:"post",href:t.route("logout"),onClick:n,as:"button"},{default:m(()=>[O(r(br),{class:"mr-2 h-4 w-4"}),o[1]||(o[1]=ce(" Log out "))]),_:1,__:[1]},8,["href"])]),_:1})],64))}}),Ci=v({__name:"NavUser",setup(e){const t=tt().props.auth.user,{isMobile:o,state:a}=ct();return(s,i)=>(y(),w(r(ft),null,{default:m(()=>[O(r(mt),null,{default:m(()=>[O(r(gi),null,{default:m(()=>[O(r(wi),{"as-child":""},{default:m(()=>[O(r(pt),{size:"lg",class:"data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"},{default:m(()=>[O(Qn,{user:r(t)},null,8,["user"]),O(r(hr),{class:"ml-auto size-4"})]),_:1})]),_:1}),O(r(yi),{class:"w-(--reka-dropdown-menu-trigger-width) min-w-56 rounded-lg",side:r(o)?"bottom":r(a)==="collapsed"?"left":"bottom",align:"end","side-offset":4},{default:m(()=>[O(xi,{user:r(t)},null,8,["user"])]),_:1},8,["side"])]),_:1})]),_:1})]),_:1}))}}),Oi={class:"flex aspect-square size-8 items-center justify-center rounded-md bg-sidebar-primary text-sidebar-primary-foreground"},ki=v({__name:"AppLogo",setup(e){return(n,t)=>(y(),F(xe,null,[G("div",Oi,[O(no,{class:"size-5 fill-current text-white dark:text-black"})]),t[0]||(t[0]=G("div",{class:"ml-1 grid flex-1 text-left text-sm"},[G("span",{class:"mb-0.5 truncate leading-tight font-semibold"},"Laravel Starter Kit")],-1))],64))}}),$i=v({__name:"AppSidebar",setup(e){const t=tt().props.auth.user,o=A(()=>{const s=[];return!t||!t.role||(["superadmin","admin"].includes(t.role)&&s.push({title:"Dashboard",href:"/admin/dashboard",icon:qe},{title:"Manajemen User",href:"/admin/users",icon:kr},{title:"Manajemen Wilayah",href:"/admin/wilayah",icon:wr},{title:"Cabang Lomba",href:"/admin/cabang-lomba",icon:Cr},{title:"Golongan Lomba",href:"/admin/golongan",icon:dr},{title:"Mimbar",href:"/admin/mimbar",icon:fr},{title:"Dewan Hakim",href:"/admin/dewan-hakim",icon:vr},{title:"Pelaksanaan MTQ",href:"/admin/pelaksanaan",icon:pr},{title:"Manajemen Peserta",href:"/admin/peserta",icon:Ye},{title:"Pendaftaran Lomba",href:"/admin/pendaftaran",icon:_t},{title:"Pembayaran",href:"/admin/pembayaran",icon:sn},{title:"Laporan",href:"/admin/laporan",icon:an}),t.role==="admin_daerah"&&s.push({title:"Dashboard",href:"/admin-daerah/dashboard",icon:qe},{title:"Manajemen Peserta",href:"/admin-daerah/peserta",icon:Ye},{title:"Daftar Langsung",href:"/admin-daerah/peserta/create",icon:$r},{title:"Pendaftaran Lomba",href:"/admin-daerah/pendaftaran",icon:_t},{title:"Laporan Daerah",href:"/admin-daerah/laporan",icon:an}),t.role==="peserta"&&s.push({title:"Dashboard",href:"/peserta/dashboard",icon:qe},{title:"Profil Saya",href:"/peserta/profile",icon:Ye},{title:"Pendaftaran Lomba",href:"/peserta/pendaftaran",icon:_t},{title:"Dokumen",href:"/peserta/dokumen",icon:Or},{title:"Pembayaran",href:"/peserta/pembayaran",icon:sn}),t.role==="dewan_hakim"&&s.push({title:"Dashboard",href:"/dewan-hakim/dashboard",icon:qe},{title:"Profil Hakim",href:"/dewan-hakim/profile",icon:Ye},{title:"Sistem Penilaian",href:"/dewan-hakim/penilaian",icon:gr})),s}),a=[{title:"MTQ Lampung",href:"https://mtqlampung.id",icon:yr},{title:"Bantuan",href:"/bantuan",icon:cr}];return(s,i)=>(y(),F("div",null,[O(r(Kr),{collapsible:"icon",variant:"floating"},{default:m(()=>[O(r(qr),null,{default:m(()=>[O(r(ft),null,{default:m(()=>[O(r(mt),null,{default:m(()=>[O(r(pt),{size:"lg","as-child":""},{default:m(()=>[O(r(ze),{href:s.route("dashboard")},{default:m(()=>[O(ki)]),_:1},8,["href"])]),_:1})]),_:1})]),_:1})]),_:1}),O(r(Vr),null,{default:m(()=>[O(ii,{items:o.value},null,8,["items"])]),_:1}),O(r(jr),null,{default:m(()=>[O(ri,{items:a}),O(Ci)]),_:1})]),_:1}),b(s.$slots,"default")]))}}),Bi=v({__name:"Breadcrumb",props:{class:{}},setup(e){const n=e;return(t,o)=>(y(),F("nav",{"aria-label":"breadcrumb","data-slot":"breadcrumb",class:K(n.class)},[b(t.$slots,"default")],2))}}),Pi=v({__name:"BreadcrumbItem",props:{class:{}},setup(e){const n=e;return(t,o)=>(y(),F("li",{"data-slot":"breadcrumb-item",class:K(r(I)("inline-flex items-center gap-1.5",n.class))},[b(t.$slots,"default")],2))}}),Ai=v({__name:"BreadcrumbLink",props:{asChild:{type:Boolean},as:{type:[String,Object,Function],default:"a"},class:{}},setup(e){const n=e;return(t,o)=>(y(),w(r(V),{"data-slot":"breadcrumb-link",as:t.as,"as-child":t.asChild,class:K(r(I)("hover:text-foreground transition-colors",n.class))},{default:m(()=>[b(t.$slots,"default")]),_:3},8,["as","as-child","class"]))}}),Di=v({__name:"BreadcrumbList",props:{class:{}},setup(e){const n=e;return(t,o)=>(y(),F("ol",{"data-slot":"breadcrumb-list",class:K(r(I)("text-muted-foreground flex flex-wrap items-center gap-1.5 text-sm break-words sm:gap-2.5",n.class))},[b(t.$slots,"default")],2))}}),Ei=v({__name:"BreadcrumbPage",props:{class:{}},setup(e){const n=e;return(t,o)=>(y(),F("span",{"data-slot":"breadcrumb-page",role:"link","aria-disabled":"true","aria-current":"page",class:K(r(I)("text-foreground font-normal",n.class))},[b(t.$slots,"default")],2))}}),Si=v({__name:"BreadcrumbSeparator",props:{class:{}},setup(e){const n=e;return(t,o)=>(y(),F("li",{"data-slot":"breadcrumb-separator",role:"presentation","aria-hidden":"true",class:K(r(I)("[&>svg]:size-3.5",n.class))},[b(t.$slots,"default",{},()=>[O(r(mr))])],2))}}),Mi=v({__name:"Breadcrumbs",props:{breadcrumbs:{}},setup(e){return(n,t)=>(y(),w(r(Bi),null,{default:m(()=>[O(r(Di),null,{default:m(()=>[(y(!0),F(xe,null,Et(n.breadcrumbs,(o,a)=>(y(),F(xe,{key:a},[O(r(Pi),null,{default:m(()=>[a===n.breadcrumbs.length-1?(y(),w(r(Ei),{key:0},{default:m(()=>[ce(he(o.title),1)]),_:2},1024)):(y(),w(r(Ai),{key:1,"as-child":""},{default:m(()=>[O(r(ze),{href:o.href??"#"},{default:m(()=>[ce(he(o.title),1)]),_:2},1032,["href"])]),_:2},1024))]),_:2},1024),a!==n.breadcrumbs.length-1?(y(),w(r(Si),{key:0})):ke("",!0)],64))),128))]),_:1})]),_:1}))}}),Ti={class:"flex h-16 shrink-0 items-center gap-2 border-b border-sidebar-border/70 px-6 transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-12 md:px-4"},Li={class:"flex items-center gap-2"},Ri=v({__name:"AppSidebarHeader",props:{breadcrumbs:{default:()=>[]}},setup(e){return(n,t)=>(y(),F("header",Ti,[G("div",Li,[O(r(ei),{class:"-ml-1"}),n.breadcrumbs&&n.breadcrumbs.length>0?(y(),w(Mi,{key:0,breadcrumbs:n.breadcrumbs},null,8,["breadcrumbs"])):ke("",!0)])]))}}),Fi=v({__name:"AppSidebarLayout",props:{breadcrumbs:{default:()=>[]}},setup(e){return(n,t)=>(y(),w(ai,{variant:"sidebar"},{default:m(()=>[O($i),O(ni,{variant:"sidebar",class:"overflow-x-hidden"},{default:m(()=>[O(Ri,{breadcrumbs:n.breadcrumbs},null,8,["breadcrumbs"]),b(n.$slots,"default")]),_:3})]),_:3}))}}),Ii={class:"p-4"},Ki=v({__name:"AppLayout",props:{breadcrumbs:{default:()=>[]}},setup(e){return(n,t)=>(y(),w(Fi,{breadcrumbs:n.breadcrumbs},{default:m(()=>[G("div",Ii,[b(n.$slots,"default")])]),_:3},8,["breadcrumbs"]))}});export{dr as A,cr as B,pr as C,br as D,kr as E,_t as F,vr as G,$r as H,Ye as I,_a as J,Ja as K,qe as L,wr as M,Fs as N,Ua as O,_r as P,xa as Q,Ga as R,xr as S,Cr as T,Or as U,es as V,ve as W,Br as X,Ki as _,gi as a,wi as b,yi as c,ln as d,li as e,ui as f,un as g,Wn as h,jn as i,In as j,Fn as k,ys as l,Sn as m,Ht as n,$a as o,Mn as p,Nt as q,Kn as r,fr as s,an as t,ks as u,mr as v,hr as w,gr as x,sn as y,yr as z};
