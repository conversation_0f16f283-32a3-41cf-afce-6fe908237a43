import{d as M,r as F,W as y,c as g,o as u,w as s,a,b as i,u as t,g as E,e as d,h,F as v,m as x,t as r}from"./app-B_pmlBSQ.js";import{a as L,b as P,c as K,d as q,_ as G}from"./AppLayout.vue_vue_type_script_setup_true_lang-BRf8lUiM.js";import{_ as J}from"./Heading.vue_vue_type_script_setup_true_lang-is4VBSCS.js";import{_}from"./AppLogoIcon.vue_vue_type_script_setup_true_lang-wzz4PWna.js";import{_ as C,a as j}from"./CardContent.vue_vue_type_script_setup_true_lang-ClvLNBXC.js";import{_ as D}from"./index-CMGr3-bt.js";import{_ as O}from"./Input.vue_vue_type_script_setup_true_lang-DB1-RsTn.js";import{_ as w}from"./Label.vue_vue_type_script_setup_true_lang-rj_fhE7R.js";import{_ as $,a as b,b as V,c as S,d as p}from"./SelectValue.vue_vue_type_script_setup_true_lang-IzlIronZ.js";import{_ as m}from"./Icon.vue_vue_type_script_setup_true_lang-BuYzjckr.js";import{l as Q,_ as R}from"./lodash-Cvgo55ys.js";import"./useForwardExpose-CO14IhkA.js";import"./RovingFocusGroup-lsWyU4xZ.js";import"./check-DtmHjdxf.js";import"./useFormControl-mZRLifGx.js";import"./loader-circle-6d8QrWFr.js";import"./sun-CHIV9RJ8.js";import"./TextLink.vue_vue_type_script_setup_true_lang-CC2Og0lP.js";const X={class:"space-y-6"},Y={class:"grid grid-cols-1 md:grid-cols-5 gap-4"},Z={class:"flex items-end"},aa={class:"flex justify-between items-center"},ea={class:"text-sm text-gray-600"},ta={class:"overflow-x-auto"},sa={class:"w-full"},ia={class:"bg-white divide-y divide-gray-200"},la={class:"px-6 py-4 whitespace-nowrap"},na={class:"flex items-center"},da={class:"w-10 h-10 bg-indigo-100 rounded-full flex items-center justify-center mr-3"},ra={class:"text-sm font-medium text-gray-900"},oa={class:"text-sm text-gray-500"},ma={class:"text-sm text-gray-500"},ua={class:"px-6 py-4 whitespace-nowrap"},_a={class:"text-sm text-gray-900"},pa={class:"text-sm text-gray-500"},fa={class:"px-6 py-4 whitespace-nowrap"},ca={class:"space-y-1"},ka={class:"text-sm text-gray-500"},ha={class:"px-6 py-4"},wa={class:"text-sm text-gray-900 max-w-xs truncate"},ya={class:"px-6 py-4 whitespace-nowrap"},ga={class:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium space-x-2"},Fa=M({__name:"Index",props:{dewaHakim:{},filters:{},wilayah:{},tipeHakim:{}},setup(A){const N=[{title:"Dashboard",href:"/admin/dashboard"},{title:"Manajemen Dewan Hakim",href:"/admin/dewan-hakim"}],o=F({...A.filters}),f=Q.debounce(()=>{y.get(route("admin.dewan-hakim.index"),o,{preserveState:!0,replace:!0})},300),T=()=>{o.search="",o.tipe_hakim="",o.status="",o.wilayah="",f()},U=n=>{y.post(route("admin.dewan-hakim.toggle-status",n.id_dewan_hakim),{},{preserveScroll:!0})},W=n=>{confirm(`Apakah Anda yakin ingin menghapus dewan hakim ${n.nama_lengkap}?`)&&y.delete(route("admin.dewan-hakim.destroy",n.id_dewan_hakim))},z=n=>({undangan:"default",kabupaten:"secondary"})[n]||"secondary",I=n=>({aktif:"default",non_aktif:"secondary"})[n]||"secondary",B=n=>({aktif:"Aktif",non_aktif:"Non Aktif"})[n]||n;return(n,e)=>(u(),g(G,{breadcrumbs:N},{default:s(()=>[a(t(E),{title:"Manajemen Dewan Hakim"}),a(J,{title:"Manajemen Dewan Hakim"}),i("div",X,[a(t(C),null,{default:s(()=>[a(t(j),{class:"p-6"},{default:s(()=>[i("div",Y,[i("div",null,[a(t(w),{for:"search"},{default:s(()=>e[5]||(e[5]=[d("Pencarian")])),_:1,__:[5]}),a(t(O),{id:"search",modelValue:o.search,"onUpdate:modelValue":e[0]||(e[0]=l=>o.search=l),placeholder:"Nama, NIK, pekerjaan...",onInput:t(f)},null,8,["modelValue","onInput"])]),i("div",null,[a(t(w),{for:"tipe_hakim"},{default:s(()=>e[6]||(e[6]=[d("Tipe Hakim")])),_:1,__:[6]}),a(t($),{modelValue:o.tipe_hakim,"onUpdate:modelValue":[e[1]||(e[1]=l=>o.tipe_hakim=l),t(f)]},{default:s(()=>[a(t(b),null,{default:s(()=>[a(t(V),{placeholder:"Semua Tipe"})]),_:1}),a(t(S),null,{default:s(()=>[a(t(p),{value:"all"},{default:s(()=>e[7]||(e[7]=[d("Semua Tipe")])),_:1,__:[7]}),(u(!0),h(v,null,x(n.tipeHakim,(l,c)=>(u(),g(t(p),{key:c,value:c},{default:s(()=>[d(r(l),1)]),_:2},1032,["value"]))),128))]),_:1})]),_:1},8,["modelValue","onUpdate:modelValue"])]),i("div",null,[a(t(w),{for:"status"},{default:s(()=>e[8]||(e[8]=[d("Status")])),_:1,__:[8]}),a(t($),{modelValue:o.status,"onUpdate:modelValue":[e[2]||(e[2]=l=>o.status=l),t(f)]},{default:s(()=>[a(t(b),null,{default:s(()=>[a(t(V),{placeholder:"Semua Status"})]),_:1}),a(t(S),null,{default:s(()=>[a(t(p),{value:"all"},{default:s(()=>e[9]||(e[9]=[d("Semua Status")])),_:1,__:[9]}),a(t(p),{value:"aktif"},{default:s(()=>e[10]||(e[10]=[d("Aktif")])),_:1,__:[10]}),a(t(p),{value:"non_aktif"},{default:s(()=>e[11]||(e[11]=[d("Non Aktif")])),_:1,__:[11]})]),_:1})]),_:1},8,["modelValue","onUpdate:modelValue"])]),i("div",null,[a(t(w),{for:"wilayah"},{default:s(()=>e[12]||(e[12]=[d("Wilayah")])),_:1,__:[12]}),a(t($),{modelValue:o.wilayah,"onUpdate:modelValue":[e[3]||(e[3]=l=>o.wilayah=l),t(f)]},{default:s(()=>[a(t(b),null,{default:s(()=>[a(t(V),{placeholder:"Semua Wilayah"})]),_:1}),a(t(S),null,{default:s(()=>[a(t(p),{value:"all"},{default:s(()=>e[13]||(e[13]=[d("Semua Wilayah")])),_:1,__:[13]}),(u(!0),h(v,null,x(n.wilayah,l=>(u(),g(t(p),{key:l.id_wilayah,value:l.id_wilayah.toString()},{default:s(()=>[d(r(l.nama_wilayah),1)]),_:2},1032,["value"]))),128))]),_:1})]),_:1},8,["modelValue","onUpdate:modelValue"])]),i("div",Z,[a(_,{onClick:T,variant:"outline",class:"w-full"},{default:s(()=>[a(m,{name:"x",class:"w-4 h-4 mr-2"}),e[14]||(e[14]=d(" Clear "))]),_:1,__:[14]})])])]),_:1})]),_:1}),i("div",aa,[i("div",ea," Menampilkan "+r(n.dewaHakim.from)+" - "+r(n.dewaHakim.to)+" dari "+r(n.dewaHakim.total)+" dewan hakim ",1),a(_,{onClick:e[4]||(e[4]=l=>n.$inertia.visit(n.route("admin.dewan-hakim.create")))},{default:s(()=>[a(m,{name:"plus",class:"w-4 h-4 mr-2"}),e[15]||(e[15]=d(" Tambah Dewan Hakim "))]),_:1,__:[15]})]),a(t(C),null,{default:s(()=>[a(t(j),{class:"p-0"},{default:s(()=>[i("div",ta,[i("table",sa,[e[17]||(e[17]=i("thead",{class:"bg-gray-50 border-b"},[i("tr",null,[i("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Dewan Hakim "),i("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Pekerjaan "),i("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Tipe & Wilayah "),i("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Spesialisasi "),i("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Status "),i("th",{class:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"}," Aksi ")])],-1)),i("tbody",ia,[(u(!0),h(v,null,x(n.dewaHakim.data,l=>{var c,H;return u(),h("tr",{key:l.id_dewan_hakim,class:"hover:bg-gray-50"},[i("td",la,[i("div",na,[i("div",da,[a(m,{name:"user-check",class:"w-5 h-5 text-indigo-600"})]),i("div",null,[i("div",ra,r(l.nama_lengkap),1),i("div",oa,r(l.nik),1),i("div",ma,r((c=l.user)==null?void 0:c.email),1)])])]),i("td",ua,[i("div",_a,r(l.pekerjaan),1),i("div",pa,r(l.unit_kerja),1)]),i("td",fa,[i("div",ca,[a(t(D),{variant:z(l.tipe_hakim)},{default:s(()=>[d(r(n.tipeHakim[l.tipe_hakim]||l.tipe_hakim),1)]),_:2},1032,["variant"]),i("div",ka,r((H=l.wilayah)==null?void 0:H.nama_wilayah),1)])]),i("td",ha,[i("div",wa,r(l.spesialisasi),1)]),i("td",ya,[a(t(D),{variant:I(l.status)},{default:s(()=>[d(r(B(l.status)),1)]),_:2},1032,["variant"])]),i("td",ga,[a(_,{variant:"ghost",size:"sm",onClick:k=>n.$inertia.visit(n.route("admin.dewan-hakim.show",l.id_dewan_hakim))},{default:s(()=>[a(m,{name:"eye",class:"w-4 h-4"})]),_:2},1032,["onClick"]),a(_,{variant:"ghost",size:"sm",onClick:k=>n.$inertia.visit(n.route("admin.dewan-hakim.profile",l.id_dewan_hakim))},{default:s(()=>[a(m,{name:"user",class:"w-4 h-4"})]),_:2},1032,["onClick"]),a(_,{variant:"ghost",size:"sm",onClick:k=>n.$inertia.visit(n.route("admin.dewan-hakim.edit",l.id_dewan_hakim))},{default:s(()=>[a(m,{name:"edit",class:"w-4 h-4"})]),_:2},1032,["onClick"]),a(_,{variant:"ghost",size:"sm",onClick:k=>U(l)},{default:s(()=>[a(m,{name:l.status==="aktif"?"user-x":"user-check",class:"w-4 h-4"},null,8,["name"])]),_:2},1032,["onClick"]),a(t(L),null,{default:s(()=>[a(t(P),{"as-child":""},{default:s(()=>[a(_,{variant:"ghost",size:"sm"},{default:s(()=>[a(m,{name:"more-vertical",class:"w-4 h-4"})]),_:1})]),_:1}),a(t(K),{align:"end"},{default:s(()=>[a(t(q),{onClick:k=>W(l),class:"text-red-600"},{default:s(()=>[a(m,{name:"trash",class:"w-4 h-4 mr-2"}),e[16]||(e[16]=d(" Hapus "))]),_:2,__:[16]},1032,["onClick"])]),_:2},1024)]),_:2},1024)])])}),128))])])])]),_:1})]),_:1}),a(R,{links:n.dewaHakim.links},null,8,["links"])])]),_:1}))}});export{Fa as default};
