<template>
  <AppLayout :breadcrumbs="breadcrumbItems">
    <Head title="Tambah Pelaksanaan MTQ" />
    <Heading title="Tambah Pelaksanaan MTQ" />

    <div class="max-w-4xl mx-auto">
      <Card>
        <CardHeader>
          <CardTitle>Informasi Pelaksanaan MTQ Baru</CardTitle>
          <CardDescription>
            Lengkapi form di bawah untuk menambahkan pelaksanaan MTQ baru
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form @submit.prevent="submit" class="space-y-8">
            <!-- Basic Information -->
            <div class="space-y-4">
              <h3 class="text-lg font-medium">Informasi Dasar</h3>
              
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label for="tahun">Tahun *</Label>
                  <Input
                    id="tahun"
                    v-model="form.tahun"
                    type="number"
                    required
                    min="2020"
                    max="2050"
                    placeholder="2024"
                    :class="{ 'border-red-500': form.errors.tahun }"
                  />
                  <p v-if="form.errors.tahun" class="text-sm text-red-600 mt-1">
                    {{ form.errors.tahun }}
                  </p>
                </div>

                <div>
                  <Label for="status">Status *</Label>
                  <Select v-model="form.status" required>
                    <SelectTrigger :class="{ 'border-red-500': form.errors.status }">
                      <SelectValue placeholder="Pilih Status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="draft">Draft</SelectItem>
                      <SelectItem value="aktif">Aktif</SelectItem>
                      <SelectItem value="selesai">Selesai</SelectItem>
                    </SelectContent>
                  </Select>
                  <p v-if="form.errors.status" class="text-sm text-red-600 mt-1">
                    {{ form.errors.status }}
                  </p>
                </div>
              </div>

              <div>
                <Label for="tema">Tema MTQ *</Label>
                <Input
                  id="tema"
                  v-model="form.tema"
                  type="text"
                  required
                  placeholder="Contoh: Membangun Generasi Qurani Menuju Indonesia Emas 2045"
                  :class="{ 'border-red-500': form.errors.tema }"
                />
                <p v-if="form.errors.tema" class="text-sm text-red-600 mt-1">
                  {{ form.errors.tema }}
                </p>
              </div>

              <div>
                <Label for="tempat">Tempat Pelaksanaan *</Label>
                <Input
                  id="tempat"
                  v-model="form.tempat"
                  type="text"
                  required
                  placeholder="Contoh: Bandar Lampung, Lampung"
                  :class="{ 'border-red-500': form.errors.tempat }"
                />
                <p v-if="form.errors.tempat" class="text-sm text-red-600 mt-1">
                  {{ form.errors.tempat }}
                </p>
              </div>
            </div>

            <!-- Event Period -->
            <div class="space-y-4">
              <h3 class="text-lg font-medium">Periode Pelaksanaan</h3>
              
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label for="tanggal_mulai">Tanggal Mulai *</Label>
                  <Input
                    id="tanggal_mulai"
                    v-model="form.tanggal_mulai"
                    type="date"
                    required
                    :class="{ 'border-red-500': form.errors.tanggal_mulai }"
                  />
                  <p v-if="form.errors.tanggal_mulai" class="text-sm text-red-600 mt-1">
                    {{ form.errors.tanggal_mulai }}
                  </p>
                </div>

                <div>
                  <Label for="tanggal_selesai">Tanggal Selesai *</Label>
                  <Input
                    id="tanggal_selesai"
                    v-model="form.tanggal_selesai"
                    type="date"
                    required
                    :class="{ 'border-red-500': form.errors.tanggal_selesai }"
                  />
                  <p v-if="form.errors.tanggal_selesai" class="text-sm text-red-600 mt-1">
                    {{ form.errors.tanggal_selesai }}
                  </p>
                </div>
              </div>

              <div v-if="form.tanggal_mulai && form.tanggal_selesai" class="bg-blue-50 p-3 rounded-lg">
                <p class="text-sm text-blue-800">
                  <Icon name="calendar" class="w-4 h-4 inline mr-1" />
                  Durasi pelaksanaan: {{ getDuration() }} hari
                </p>
              </div>
            </div>

            <!-- Registration Period -->
            <div class="space-y-4">
              <h3 class="text-lg font-medium">Periode Pendaftaran</h3>
              
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label for="tanggal_buka_pendaftaran">Tanggal Buka Pendaftaran *</Label>
                  <Input
                    id="tanggal_buka_pendaftaran"
                    v-model="form.tanggal_buka_pendaftaran"
                    type="date"
                    required
                    :class="{ 'border-red-500': form.errors.tanggal_buka_pendaftaran }"
                  />
                  <p v-if="form.errors.tanggal_buka_pendaftaran" class="text-sm text-red-600 mt-1">
                    {{ form.errors.tanggal_buka_pendaftaran }}
                  </p>
                </div>

                <div>
                  <Label for="tanggal_tutup_pendaftaran">Tanggal Tutup Pendaftaran *</Label>
                  <Input
                    id="tanggal_tutup_pendaftaran"
                    v-model="form.tanggal_tutup_pendaftaran"
                    type="date"
                    required
                    :class="{ 'border-red-500': form.errors.tanggal_tutup_pendaftaran }"
                  />
                  <p v-if="form.errors.tanggal_tutup_pendaftaran" class="text-sm text-red-600 mt-1">
                    {{ form.errors.tanggal_tutup_pendaftaran }}
                  </p>
                </div>
              </div>

              <div v-if="form.tanggal_buka_pendaftaran && form.tanggal_tutup_pendaftaran" class="bg-green-50 p-3 rounded-lg">
                <p class="text-sm text-green-800">
                  <Icon name="calendar-check" class="w-4 h-4 inline mr-1" />
                  Durasi pendaftaran: {{ getRegistrationDuration() }} hari
                </p>
              </div>
            </div>

            <!-- Information -->
            <div class="bg-blue-50 p-4 rounded-lg">
              <h4 class="font-medium text-blue-900 mb-2">Informasi Pelaksanaan MTQ</h4>
              <div class="text-sm text-blue-800 space-y-1">
                <p><strong>Status Draft:</strong> Pelaksanaan belum aktif, tidak dapat digunakan untuk pendaftaran</p>
                <p><strong>Status Aktif:</strong> Pelaksanaan sedang berlangsung dan dapat menerima pendaftaran</p>
                <p><strong>Status Selesai:</strong> Pelaksanaan telah selesai, tidak dapat menerima pendaftaran baru</p>
                <p><strong>Catatan:</strong> Hanya satu pelaksanaan yang dapat aktif dalam satu waktu</p>
              </div>
            </div>

            <!-- Validation Summary -->
            <div v-if="hasValidationErrors" class="bg-red-50 border border-red-200 p-4 rounded-lg">
              <div class="flex">
                <Icon name="alert-circle" class="w-5 h-5 text-red-600 mr-2 mt-0.5" />
                <div>
                  <h4 class="font-medium text-red-800">Terdapat kesalahan pada form:</h4>
                  <ul class="text-sm text-red-700 mt-1 list-disc list-inside">
                    <li v-for="(error, field) in form.errors" :key="field">
                      {{ error }}
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            <!-- Actions -->
            <div class="flex justify-end space-x-4 pt-6 border-t">
              <Button
                type="button"
                variant="outline"
                @click="$inertia.visit(route('admin.pelaksanaan.index'))"
              >
                Batal
              </Button>
              <Button type="submit" :disabled="form.processing">
                <Icon v-if="form.processing" name="loader-2" class="w-4 h-4 mr-2 animate-spin" />
                Simpan Pelaksanaan MTQ
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Head, useForm } from '@inertiajs/vue3'
import AppLayout from '@/layouts/AppLayout.vue'
import Heading from '@/components/Heading.vue'
import Button from '@/components/ui/button/Button.vue'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import Icon from '@/components/Icon.vue'
import { type BreadcrumbItem } from '@/types'

const breadcrumbItems: BreadcrumbItem[] = [
  { title: 'Dashboard', href: '/admin/dashboard' },
  { title: 'Manajemen Pelaksanaan MTQ', href: '/admin/pelaksanaan' },
  { title: 'Tambah Pelaksanaan MTQ', href: '/admin/pelaksanaan/create' }
]

const form = useForm({
  tahun: new Date().getFullYear().toString(),
  tema: '',
  tempat: '',
  tanggal_mulai: '',
  tanggal_selesai: '',
  tanggal_buka_pendaftaran: '',
  tanggal_tutup_pendaftaran: '',
  status: 'draft'
})

const hasValidationErrors = computed(() => {
  return Object.keys(form.errors).length > 0
})

const getDuration = () => {
  if (!form.tanggal_mulai || !form.tanggal_selesai) return 0
  
  const start = new Date(form.tanggal_mulai)
  const end = new Date(form.tanggal_selesai)
  const diffTime = Math.abs(end.getTime() - start.getTime())
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1
  return diffDays
}

const getRegistrationDuration = () => {
  if (!form.tanggal_buka_pendaftaran || !form.tanggal_tutup_pendaftaran) return 0
  
  const start = new Date(form.tanggal_buka_pendaftaran)
  const end = new Date(form.tanggal_tutup_pendaftaran)
  const diffTime = Math.abs(end.getTime() - start.getTime())
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1
  return diffDays
}

const submit = () => {
  form.post(route('admin.pelaksanaan.store'), {
    onSuccess: () => {
      // Redirect handled by controller
    }
  })
}
</script>
