import{d as T,c as B,o as g,w as e,a as n,b as a,u as o,g as G,e as m,t as l,h as f,F as K,m as N,f as S}from"./app-B_pmlBSQ.js";import{_ as I}from"./AppLayout.vue_vue_type_script_setup_true_lang-BRf8lUiM.js";import{_ as V}from"./Heading.vue_vue_type_script_setup_true_lang-is4VBSCS.js";import{_ as r}from"./AppLogoIcon.vue_vue_type_script_setup_true_lang-wzz4PWna.js";import{_ as b,a as _}from"./CardContent.vue_vue_type_script_setup_true_lang-ClvLNBXC.js";import{_ as k}from"./CardDescription.vue_vue_type_script_setup_true_lang-DboywBIK.js";import{_ as L,a as h}from"./CardTitle.vue_vue_type_script_setup_true_lang-5HO6oSoB.js";import{_ as $}from"./index-CMGr3-bt.js";import{_ as u}from"./Label.vue_vue_type_script_setup_true_lang-rj_fhE7R.js";import{_ as d}from"./Icon.vue_vue_type_script_setup_true_lang-BuYzjckr.js";import"./useForwardExpose-CO14IhkA.js";import"./RovingFocusGroup-lsWyU4xZ.js";import"./check-DtmHjdxf.js";import"./loader-circle-6d8QrWFr.js";import"./sun-CHIV9RJ8.js";const A={class:"max-w-4xl mx-auto space-y-6"},F={class:"flex justify-between items-start"},E={class:"w-12 h-12 bg-green-100 rounded-full flex items-center justify-center"},M={class:"flex gap-2"},P={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},z={class:"space-y-4"},J={class:"text-sm"},R={class:"text-sm"},U={class:"text-sm"},q={class:"space-y-4"},H={class:"text-sm"},O={class:"text-sm"},Q={class:"text-sm"},W={class:"flex justify-between items-center"},X={key:0,class:"text-center py-8 text-gray-500"},Y={key:1,class:"space-y-4"},Z=["onClick"],aa={class:"flex justify-between items-start"},ta={class:"flex-1"},sa={class:"flex items-center gap-3 mb-2"},na={class:"font-medium"},ea={class:"text-sm text-gray-500 mb-2"},oa={class:"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm"},ia={class:"font-medium"},la={class:"font-medium"},ma={class:"font-medium"},da={class:"font-medium text-green-600"},ra={class:"flex gap-2 ml-4"},ua={class:"grid grid-cols-1 md:grid-cols-3 gap-6"},ga={class:"flex items-center"},ba={class:"flex-shrink-0"},_a={class:"ml-4"},fa={class:"text-2xl font-semibold text-gray-900"},ca={class:"flex items-center"},pa={class:"flex-shrink-0"},va={class:"ml-4"},ya={class:"text-2xl font-semibold text-gray-900"},xa={class:"flex items-center"},ka={class:"flex-shrink-0"},La={class:"ml-4"},ha={class:"text-2xl font-semibold text-gray-900"},$a={class:"flex justify-between"},wa={class:"flex gap-2"},Pa=T({__name:"Show",props:{cabangLomba:{}},setup(w){const c=w,C=[{title:"Dashboard",href:"/admin/dashboard"},{title:"Manajemen Cabang Lomba",href:"/admin/cabang-lomba"},{title:"Detail Cabang Lomba",href:`/admin/cabang-lomba/${c.cabangLomba.id_cabang}`}],y=s=>({aktif:"default",non_aktif:"secondary"})[s]||"secondary",p=s=>({aktif:"Aktif",non_aktif:"Non Aktif"})[s]||s,D=()=>c.cabangLomba.golongan?c.cabangLomba.golongan.reduce((s,t)=>s+t.kuota_max,0):0,x=s=>new Date(s).toLocaleDateString("id-ID",{year:"numeric",month:"long",day:"numeric"}),j=s=>new Intl.NumberFormat("id-ID").format(s);return(s,t)=>(g(),B(I,{breadcrumbs:C},{default:e(()=>[n(o(G),{title:`Detail Cabang Lomba: ${s.cabangLomba.nama_cabang}`},null,8,["title"]),n(V,{title:`Detail Cabang Lomba: ${s.cabangLomba.nama_cabang}`},null,8,["title"]),a("div",A,[n(o(b),null,{default:e(()=>[n(o(L),null,{default:e(()=>[a("div",F,[a("div",null,[n(o(h),{class:"flex items-center gap-3"},{default:e(()=>[a("div",E,[n(d,{name:"trophy",class:"w-6 h-6 text-green-600"})]),m(" "+l(s.cabangLomba.nama_cabang),1)]),_:1}),n(o(k),null,{default:e(()=>[m(l(s.cabangLomba.kode_cabang),1)]),_:1})]),a("div",M,[n(o($),{variant:y(s.cabangLomba.status)},{default:e(()=>[m(l(p(s.cabangLomba.status)),1)]),_:1},8,["variant"])])])]),_:1}),n(o(_),null,{default:e(()=>[a("div",P,[a("div",z,[a("div",null,[n(o(u),{class:"text-sm font-medium text-gray-500"},{default:e(()=>t[5]||(t[5]=[m("Kode Cabang")])),_:1,__:[5]}),a("p",J,l(s.cabangLomba.kode_cabang),1)]),a("div",null,[n(o(u),{class:"text-sm font-medium text-gray-500"},{default:e(()=>t[6]||(t[6]=[m("Nama Cabang")])),_:1,__:[6]}),a("p",R,l(s.cabangLomba.nama_cabang),1)]),a("div",null,[n(o(u),{class:"text-sm font-medium text-gray-500"},{default:e(()=>t[7]||(t[7]=[m("Status")])),_:1,__:[7]}),a("p",U,l(p(s.cabangLomba.status)),1)])]),a("div",q,[a("div",null,[n(o(u),{class:"text-sm font-medium text-gray-500"},{default:e(()=>t[8]||(t[8]=[m("Deskripsi")])),_:1,__:[8]}),a("p",H,l(s.cabangLomba.deskripsi||"-"),1)]),a("div",null,[n(o(u),{class:"text-sm font-medium text-gray-500"},{default:e(()=>t[9]||(t[9]=[m("Dibuat")])),_:1,__:[9]}),a("p",O,l(x(s.cabangLomba.created_at)),1)]),a("div",null,[n(o(u),{class:"text-sm font-medium text-gray-500"},{default:e(()=>t[10]||(t[10]=[m("Diperbarui")])),_:1,__:[10]}),a("p",Q,l(x(s.cabangLomba.updated_at)),1)])])])]),_:1})]),_:1}),n(o(b),null,{default:e(()=>[n(o(L),null,{default:e(()=>[a("div",W,[a("div",null,[n(o(h),null,{default:e(()=>t[11]||(t[11]=[m("Golongan Lomba")])),_:1,__:[11]}),n(o(k),null,{default:e(()=>[m(" Daftar golongan yang tersedia untuk cabang lomba "+l(s.cabangLomba.nama_cabang),1)]),_:1})]),n(r,{onClick:t[0]||(t[0]=i=>s.$inertia.visit(s.route("admin.golongan.create",{cabang:s.cabangLomba.id_cabang})))},{default:e(()=>[n(d,{name:"plus",class:"w-4 h-4 mr-2"}),t[12]||(t[12]=m(" Tambah Golongan "))]),_:1,__:[12]})])]),_:1}),n(o(_),null,{default:e(()=>[!s.cabangLomba.golongan||s.cabangLomba.golongan.length===0?(g(),f("div",X,[n(d,{name:"trophy",class:"w-12 h-12 mx-auto mb-4 text-gray-300"}),t[14]||(t[14]=a("p",null,"Belum ada golongan untuk cabang lomba ini",-1)),n(r,{class:"mt-4",onClick:t[1]||(t[1]=i=>s.$inertia.visit(s.route("admin.golongan.create",{cabang:s.cabangLomba.id_cabang})))},{default:e(()=>t[13]||(t[13]=[m(" Tambah Golongan Pertama ")])),_:1,__:[13]})])):(g(),f("div",Y,[(g(!0),f(K,null,N(s.cabangLomba.golongan,i=>(g(),f("div",{key:i.id_golongan,class:"border rounded-lg p-4 hover:bg-gray-50 cursor-pointer",onClick:v=>s.$inertia.visit(s.route("admin.golongan.show",i.id_golongan))},[a("div",aa,[a("div",ta,[a("div",sa,[a("h4",na,l(i.nama_golongan),1),n(o($),{variant:y(i.status)},{default:e(()=>[m(l(p(i.status)),1)]),_:2},1032,["variant"])]),a("p",ea,l(i.kode_golongan),1),a("div",oa,[a("div",null,[t[15]||(t[15]=a("span",{class:"text-gray-500"},"Jenis Kelamin:",-1)),a("p",ia,l(i.jenis_kelamin==="L"?"Laki-laki":"Perempuan"),1)]),a("div",null,[t[16]||(t[16]=a("span",{class:"text-gray-500"},"Usia:",-1)),a("p",la,l(i.batas_umur_min)+"-"+l(i.batas_umur_max)+" tahun",1)]),a("div",null,[t[17]||(t[17]=a("span",{class:"text-gray-500"},"Kuota:",-1)),a("p",ma,l(i.kuota_max)+" peserta",1)]),a("div",null,[t[18]||(t[18]=a("span",{class:"text-gray-500"},"Biaya:",-1)),a("p",da,"Rp "+l(j(i.biaya_pendaftaran)),1)])])]),a("div",ra,[n(r,{variant:"ghost",size:"sm",onClick:S(v=>s.$inertia.visit(s.route("admin.golongan.edit",i.id_golongan)),["stop"])},{default:e(()=>[n(d,{name:"edit",class:"w-4 h-4"})]),_:2},1032,["onClick"])])])],8,Z))),128))]))]),_:1})]),_:1}),a("div",ua,[n(o(b),null,{default:e(()=>[n(o(_),{class:"p-6"},{default:e(()=>{var i;return[a("div",ga,[a("div",ba,[n(d,{name:"users",class:"h-8 w-8 text-blue-600"})]),a("div",_a,[t[19]||(t[19]=a("p",{class:"text-sm font-medium text-gray-500"},"Total Golongan",-1)),a("p",fa,l(((i=s.cabangLomba.golongan)==null?void 0:i.length)||0),1)])])]}),_:1})]),_:1}),n(o(b),null,{default:e(()=>[n(o(_),{class:"p-6"},{default:e(()=>{var i;return[a("div",ca,[a("div",pa,[n(d,{name:"check-circle",class:"h-8 w-8 text-green-600"})]),a("div",va,[t[20]||(t[20]=a("p",{class:"text-sm font-medium text-gray-500"},"Golongan Aktif",-1)),a("p",ya,l(((i=s.cabangLomba.golongan)==null?void 0:i.filter(v=>v.status==="aktif").length)||0),1)])])]}),_:1})]),_:1}),n(o(b),null,{default:e(()=>[n(o(_),{class:"p-6"},{default:e(()=>[a("div",xa,[a("div",ka,[n(d,{name:"dollar-sign",class:"h-8 w-8 text-yellow-600"})]),a("div",La,[t[21]||(t[21]=a("p",{class:"text-sm font-medium text-gray-500"},"Total Kuota",-1)),a("p",ha,l(D()),1)])])]),_:1})]),_:1})]),a("div",$a,[n(r,{variant:"outline",onClick:t[2]||(t[2]=i=>s.$inertia.visit(s.route("admin.cabang-lomba.index")))},{default:e(()=>[n(d,{name:"arrow-left",class:"w-4 h-4 mr-2"}),t[22]||(t[22]=m(" Kembali "))]),_:1,__:[22]}),a("div",wa,[n(r,{variant:"outline",onClick:t[3]||(t[3]=i=>s.$inertia.visit(s.route("admin.golongan.create",{cabang:s.cabangLomba.id_cabang})))},{default:e(()=>[n(d,{name:"plus",class:"w-4 h-4 mr-2"}),t[23]||(t[23]=m(" Tambah Golongan "))]),_:1,__:[23]}),n(r,{onClick:t[4]||(t[4]=i=>s.$inertia.visit(s.route("admin.cabang-lomba.edit",s.cabangLomba.id_cabang)))},{default:e(()=>[n(d,{name:"edit",class:"w-4 h-4 mr-2"}),t[24]||(t[24]=m(" Edit Cabang Lomba "))]),_:1,__:[24]})])])])]),_:1}))}});export{Pa as default};
